# Wrong Params 问题修复方案

**生成时间**: 2025-08-27
**问题总数**: 99 个
**修复状态**: 已生成修复方案

## 概述

本文档包含所有 wrong_params 类型问题的修复方案。这些问题主要是由于参数类型不匹配、返回值类型不兼容等导致的编译错误。

## 修复方案


## PluginConfiguration - Issue 1

**位置**: PluginConfiguration.java:20
**问题**: 'com.cet.electric.fusion.matrix.v2.dto.business.PluginInfo' 中的 'setPluginname(java.lang.String)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## ClassesSchemeDaoImpl - Issue 1

**位置**: ClassesSchemeDaoImpl.java:35
**问题**: 'com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder' 中的 'of(java.lang.String)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 1

**位置**: TeamEnergyServiceImpl.java:72
**问题**: 不兼容的类型。实际为 com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme'，需要 'SchedulingScheme'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 2

**位置**: TeamEnergyServiceImpl.java:73
**问题**: 'org.apache.commons.collections4.CollectionUtils' 中的 'isEmpty(java.util.Collection<?>)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 4

**位置**: TeamEnergyServiceImpl.java:78
**问题**: 不兼容的类型。实际为 null'，需要 'java.util.List<TeamGroupInfo>'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 6

**位置**: TeamEnergyServiceImpl.java:79
**问题**: 'java.util.stream.Stream' 中的 'map(java.util.function.Function<? super TeamGroupInfo,?>)' 无法应用于 '(<method reference>)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 8

**位置**: TeamEnergyServiceImpl.java:79
**问题**: 不兼容的类型。实际为 java.util.List<java.lang.Object>'，需要 'java.util.List<java.lang.Long>'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 9

**位置**: TeamEnergyServiceImpl.java:82
**问题**: 不兼容的类型。实际为 java.util.List<com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy>'，需要 'java.util.List<TeamGroupEnergy>'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 10

**位置**: TeamEnergyServiceImpl.java:89
**问题**: 'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction<? super TeamGroupEnergy>)' 无法应用于 '(<method reference>)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 12

**位置**: TeamEnergyServiceImpl.java:90
**问题**: 不兼容的类型。实际为 com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO'，需要 'UserDefineUnitDTO'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 13

**位置**: TeamEnergyServiceImpl.java:96
**问题**: 不兼容的类型。实际为 null'，需要 'java.lang.Double'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 15

**位置**: TeamEnergyServiceImpl.java:101
**问题**: 不兼容的类型。实际为 java.util.Map<java.lang.Object,java.util.List<TeamGroupEnergy>>'，需要 'java.util.Map<java.lang.Long,java.util.List<TeamGroupEnergy>>'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 16

**位置**: TeamEnergyServiceImpl.java:101
**问题**: 'java.util.stream.Collectors' 中的 'groupingBy(java.util.function.Function<? super java.lang.Object,?>)' 无法应用于 '(<method reference>)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 18

**位置**: TeamEnergyServiceImpl.java:110
**问题**: 'java.util.Objects' 中的 'equals(java.lang.Object, java.lang.Object)' 无法应用于 '(?, java.lang.Long)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 20

**位置**: TeamEnergyServiceImpl.java:111
**问题**: 'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyCard' 中的 'setTeamGroupName(java.lang.String)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 22

**位置**: TeamEnergyServiceImpl.java:114
**问题**: 'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction<? super TeamGroupEnergy>)' 无法应用于 '(<method reference>)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 24

**位置**: TeamEnergyServiceImpl.java:118
**问题**: 不兼容的类型。实际为 null'，需要 'java.lang.Double'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 26

**位置**: TeamEnergyServiceImpl.java:120
**问题**: 不兼容的类型。实际为 null'，需要 'java.lang.Double'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 28

**位置**: TeamEnergyServiceImpl.java:141
**问题**: 'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyInfoVO' 中的 'setEnergyUnit(java.lang.String)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 30

**位置**: TeamEnergyServiceImpl.java:379
**问题**: 不兼容的类型。实际为 com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme'，需要 'SchedulingScheme'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 31

**位置**: TeamEnergyServiceImpl.java:380
**问题**: 'org.apache.commons.collections4.CollectionUtils' 中的 'isEmpty(java.util.Collection<?>)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 34

**位置**: TeamEnergyServiceImpl.java:387
**问题**: 'org.apache.commons.collections4.CollectionUtils' 中的 'isNotEmpty(java.util.Collection<?>)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 36

**位置**: TeamEnergyServiceImpl.java:388
**问题**: 'java.util.List' 中的 'addAll(java.util.Collection<? extends ClassesConfig>)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 38

**位置**: TeamEnergyServiceImpl.java:395
**问题**: 'java.util.stream.Stream' 中的 'map(java.util.function.Function<? super ClassesConfig,?>)' 无法应用于 '(<method reference>)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 40

**位置**: TeamEnergyServiceImpl.java:396
**问题**: 不兼容的类型。实际为 java.util.List<java.lang.Object>'，需要 'java.util.List<java.lang.Long>'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 41

**位置**: TeamEnergyServiceImpl.java:399
**问题**: 不兼容的类型。实际为 java.util.List<com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy>'，需要 'java.util.List<TeamGroupEnergy>'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 42

**位置**: TeamEnergyServiceImpl.java:406
**问题**: 'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction<? super TeamGroupEnergy>)' 无法应用于 '(<method reference>)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 44

**位置**: TeamEnergyServiceImpl.java:407
**问题**: 不兼容的类型。实际为 com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO'，需要 'UserDefineUnitDTO'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 45

**位置**: TeamEnergyServiceImpl.java:410
**问题**: 不兼容的类型。实际为 java.util.List<com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy>'，需要 'java.util.List<TeamGroupEnergy>'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 46

**位置**: TeamEnergyServiceImpl.java:415
**问题**: 'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction<? super TeamGroupEnergy>)' 无法应用于 '(<method reference>)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 48

**位置**: TeamEnergyServiceImpl.java:417
**问题**: 不兼容的类型。实际为 null'，需要 'java.lang.Double'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 50

**位置**: TeamEnergyServiceImpl.java:419
**问题**: 不兼容的类型。实际为 null'，需要 'java.lang.Double'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 52

**位置**: TeamEnergyServiceImpl.java:427
**问题**: 'com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO' 中的 'setEnergyUnit(java.lang.String)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## SchedulingSchemeDaoImpl - Issue 1

**位置**: SchedulingSchemeDaoImpl.java:85
**问题**: 'com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder' 中的 'of(java.lang.String)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## SchedulingSchemeDaoImpl - Issue 2

**位置**: SchedulingSchemeDaoImpl.java:103
**问题**: 'com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder' 中的 'of(java.lang.String)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## SchedulingSchemeDaoImpl - Issue 3

**位置**: SchedulingSchemeDaoImpl.java:72
**问题**: 'com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder' 中的 'of(java.lang.String)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## SchedulingSchemeDaoImpl - Issue 4

**位置**: SchedulingSchemeDaoImpl.java:47
**问题**: 'com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder' 中的 'of(java.lang.String)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamConfigServiceImpl - Issue 1

**位置**: TeamConfigServiceImpl.java:535
**问题**: 不兼容的类型。实际为 null'，需要 'com.cet.eem.fusion.common.entity.Result&lt;java.util.List&lt;com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo&gt;&gt;'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 54

**位置**: TeamEnergyServiceImpl.java:430
**问题**: 不兼容的类型。实际为 null'，需要 'java.util.List&lt;TeamGroupInfo&gt;'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 56

**位置**: TeamEnergyServiceImpl.java:433
**问题**: 'java.util.List' 中的 'contains(java.lang.Object)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 58

**位置**: TeamEnergyServiceImpl.java:434
**问题**: 'java.util.stream.Stream' 中的 'map(java.util.function.Function&lt;? super TeamGroupInfo,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 60

**位置**: TeamEnergyServiceImpl.java:434
**问题**: 不兼容的类型。实际为 R'，需要 'java.lang.String'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 61

**位置**: TeamEnergyServiceImpl.java:434
**问题**: 'java.util.stream.Stream' 中的 'collect(java.util.stream.Collector&lt;? super java.lang.Object,A,R&gt;)' 无法应用于 '(java.util.stream.Collector&lt;java.lang.CharSequence,capture&lt;?&gt;,java.lang.String&gt;)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 63

**位置**: TeamEnergyServiceImpl.java:441
**问题**: 'org.apache.commons.collections4.CollectionUtils' 中的 'isEmpty(java.util.Collection&lt;?&gt;)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 68

**位置**: TeamEnergyServiceImpl.java:445
**问题**: 'java.util.List' 中的 'contains(java.lang.Object)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 70

**位置**: TeamEnergyServiceImpl.java:445
**问题**: 不兼容的类型。实际为 null'，需要 'java.util.List&lt;ClassesConfig&gt;'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 72

**位置**: TeamEnergyServiceImpl.java:450
**问题**: 'com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO.ClassesName' 中的 'setSchemeName(java.lang.String)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 74

**位置**: TeamEnergyServiceImpl.java:451
**问题**: 'java.util.stream.Stream' 中的 'map(java.util.function.Function&lt;? super ClassesConfig,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 76

**位置**: TeamEnergyServiceImpl.java:451
**问题**: 不兼容的类型。实际为 R'，需要 'java.lang.String'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 77

**位置**: TeamEnergyServiceImpl.java:451
**问题**: 'java.util.stream.Stream' 中的 'collect(java.util.stream.Collector&lt;? super java.lang.Object,A,R&gt;)' 无法应用于 '(java.util.stream.Collector&lt;java.lang.CharSequence,capture&lt;?&gt;,java.lang.String&gt;)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 78

**位置**: TeamEnergyServiceImpl.java:163
**问题**: 不兼容的类型。实际为 com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme'，需要 'SchedulingScheme'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 79

**位置**: TeamEnergyServiceImpl.java:164
**问题**: 'org.apache.commons.collections4.CollectionUtils' 中的 'isEmpty(java.util.Collection&lt;?&gt;)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 81

**位置**: TeamEnergyServiceImpl.java:169
**问题**: 不兼容的类型。实际为 null'，需要 'java.util.List&lt;TeamGroupInfo&gt;'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 83

**位置**: TeamEnergyServiceImpl.java:170
**问题**: 'java.util.stream.Stream' 中的 'map(java.util.function.Function&lt;? super TeamGroupInfo,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 85

**位置**: TeamEnergyServiceImpl.java:170
**问题**: 不兼容的类型。实际为 java.util.List&lt;java.lang.Object&gt;'，需要 'java.util.List&lt;java.lang.Long&gt;'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 86

**位置**: TeamEnergyServiceImpl.java:173
**问题**: 'org.apache.commons.collections4.CollectionUtils' 中的 'isNotEmpty(java.util.Collection&lt;?&gt;)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 96

**位置**: TeamEnergyServiceImpl.java:180
**问题**: 不兼容的类型。实际为 java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy&gt;'，需要 'java.util.List&lt;TeamGroupEnergy&gt;'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 97

**位置**: TeamEnergyServiceImpl.java:187
**问题**: 'java.util.stream.Stream' 中的 'map(java.util.function.Function&lt;? super TeamGroupEnergy,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 99

**位置**: TeamEnergyServiceImpl.java:187
**问题**: 不兼容的类型。实际为 java.util.List&lt;java.lang.Object&gt;'，需要 'java.util.List&lt;java.lang.Double&gt;'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 100

**位置**: TeamEnergyServiceImpl.java:189
**问题**: 不兼容的类型。实际为 com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO'，需要 'UserDefineUnitDTO'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 101

**位置**: TeamEnergyServiceImpl.java:199
**问题**: 'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO' 中的 'setEnergyUnit(java.lang.String)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 103

**位置**: TeamEnergyServiceImpl.java:203
**问题**: 'java.util.Objects' 中的 'equals(java.lang.Object, java.lang.Object)' 无法应用于 '(?, java.lang.Long)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 105

**位置**: TeamEnergyServiceImpl.java:204
**问题**: 不兼容的类型。实际为 java.util.Map&lt;java.lang.Object,java.util.List&lt;TeamGroupEnergy&gt;&gt;'，需要 'java.util.Map&lt;java.lang.Long,java.util.List&lt;TeamGroupEnergy&gt;&gt;'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 106

**位置**: TeamEnergyServiceImpl.java:204
**问题**: 'java.util.stream.Collectors' 中的 'groupingBy(java.util.function.Function&lt;? super java.lang.Object,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 108

**位置**: TeamEnergyServiceImpl.java:214
**问题**: 'java.util.Objects' 中的 'equals(java.lang.Object, java.lang.Object)' 无法应用于 '(?, java.lang.Long)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 110

**位置**: TeamEnergyServiceImpl.java:223
**问题**: 'java.util.Objects' 中的 'equals(java.lang.Object, java.lang.Object)' 无法应用于 '(?, ?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 113

**位置**: TeamEnergyServiceImpl.java:224
**问题**: 'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO.TeamGroupEnergy' 中的 'setTeamGroupNumber(java.lang.Integer)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 115

**位置**: TeamEnergyServiceImpl.java:228
**问题**: 'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO.TeamGroupEnergy' 中的 'setTeamGroupName(java.lang.String)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 117

**位置**: TeamEnergyServiceImpl.java:229
**问题**: 'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO.TeamGroupEnergy' 中的 'setColor(java.lang.String)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 119

**位置**: TeamEnergyServiceImpl.java:231
**问题**: 'com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl' 中的 'unitConversion(java.lang.Double, UserDefineUnitDTO)' 无法应用于 '(?, UserDefineUnitDTO)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 121

**位置**: TeamEnergyServiceImpl.java:237
**问题**: 'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO.TeamGroupEnergy' 中的 'setTeamGroupName(java.lang.String)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 123

**位置**: TeamEnergyServiceImpl.java:238
**问题**: 'com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupEnergyHistogramVO.TeamGroupEnergy' 中的 'setColor(java.lang.String)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 125

**位置**: TeamEnergyServiceImpl.java:241
**问题**: 'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction&lt;? super TeamGroupEnergy&gt;)' 无法应用于 '(&lt;method reference&gt;)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 127

**位置**: TeamEnergyServiceImpl.java:278
**问题**: 不兼容的类型。实际为 com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme'，需要 'SchedulingScheme'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 128

**位置**: TeamEnergyServiceImpl.java:279
**问题**: 'org.apache.commons.collections4.CollectionUtils' 中的 'isEmpty(java.util.Collection&lt;?&gt;)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 131

**位置**: TeamEnergyServiceImpl.java:286
**问题**: 'org.apache.commons.collections4.CollectionUtils' 中的 'isNotEmpty(java.util.Collection&lt;?&gt;)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 133

**位置**: TeamEnergyServiceImpl.java:287
**问题**: 'java.util.List' 中的 'addAll(java.util.Collection&lt;? extends ClassesConfig&gt;)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 135

**位置**: TeamEnergyServiceImpl.java:293
**问题**: 'java.util.stream.Stream' 中的 'map(java.util.function.Function&lt;? super ClassesConfig,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 137

**位置**: TeamEnergyServiceImpl.java:293
**问题**: 不兼容的类型。实际为 java.util.List&lt;java.lang.Object&gt;'，需要 'java.util.List&lt;java.lang.Long&gt;'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 138

**位置**: TeamEnergyServiceImpl.java:297
**问题**: 不兼容的类型。实际为 java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.TeamGroupEnergy&gt;'，需要 'java.util.List&lt;TeamGroupEnergy&gt;'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 139

**位置**: TeamEnergyServiceImpl.java:304
**问题**: 'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction&lt;? super TeamGroupEnergy&gt;)' 无法应用于 '(&lt;method reference&gt;)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 141

**位置**: TeamEnergyServiceImpl.java:305
**问题**: 不兼容的类型。实际为 com.cet.eem.fusion.config.sdk.entity.UserDefineUnitDTO'，需要 'UserDefineUnitDTO'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 142

**位置**: TeamEnergyServiceImpl.java:309
**问题**: 不兼容的类型。实际为 java.util.Map&lt;java.lang.Object,java.util.List&lt;TeamGroupEnergy&gt;&gt;'，需要 'java.util.Map&lt;java.lang.Long,java.util.List&lt;TeamGroupEnergy&gt;&gt;'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 143

**位置**: TeamEnergyServiceImpl.java:309
**问题**: 'java.util.stream.Collectors' 中的 'groupingBy(java.util.function.Function&lt;? super java.lang.Object,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 145

**位置**: TeamEnergyServiceImpl.java:324
**问题**: 'java.util.Objects' 中的 'equals(java.lang.Object, java.lang.Object)' 无法应用于 '(?, java.lang.Long)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 147

**位置**: TeamEnergyServiceImpl.java:325
**问题**: 'com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO.ClassesName' 中的 'setConfigName(java.lang.String)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 155

**位置**: TeamEnergyServiceImpl.java:330
**问题**: 'java.util.Objects' 中的 'equals(java.lang.Object, java.lang.Object)' 无法应用于 '(?, java.lang.Long)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 160

**位置**: TeamEnergyServiceImpl.java:331
**问题**: 不兼容的类型。实际为 null'，需要 'java.lang.String'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 162

**位置**: TeamEnergyServiceImpl.java:338
**问题**: 'java.util.stream.Stream' 中的 'map(java.util.function.Function&lt;? super TeamGroupEnergy,?&gt;)' 无法应用于 '(&lt;method reference&gt;)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 164

**位置**: TeamEnergyServiceImpl.java:341
**问题**: 不兼容的类型。实际为 java.util.List&lt;java.lang.Object&gt;'，需要 'java.util.List&lt;java.lang.Long&gt;'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 168

**位置**: TeamEnergyServiceImpl.java:346
**问题**: 'java.util.List' 中的 'contains(java.lang.Object)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 172

**位置**: TeamEnergyServiceImpl.java:347
**问题**: 不兼容的类型。实际为 null'，需要 'java.lang.String'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 174

**位置**: TeamEnergyServiceImpl.java:352
**问题**: 'java.util.stream.Stream' 中的 'mapToDouble(java.util.function.ToDoubleFunction&lt;? super TeamGroupEnergy&gt;)' 无法应用于 '(&lt;method reference&gt;)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 176

**位置**: TeamEnergyServiceImpl.java:354
**问题**: 不兼容的类型。实际为 null'，需要 'java.lang.Double'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 178

**位置**: TeamEnergyServiceImpl.java:357
**问题**: 不兼容的类型。实际为 null'，需要 'java.lang.Double'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 180

**位置**: TeamEnergyServiceImpl.java:362
**问题**: 'com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesEnergyInfoVO' 中的 'setEnergyUnit(java.lang.String)' 无法应用于 '(?)'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数

---

## TeamEnergyServiceImpl - Issue 182

**位置**: TeamEnergyServiceImpl.java:264
**问题**: 不兼容的类型。实际为 null'，需要 'java.lang.Double'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 185

**位置**: TeamEnergyServiceImpl.java:469
**问题**: 不兼容的类型。实际为 null'，需要 'java.util.List&lt;java.util.Map&lt;java.lang.String,java.lang.Object&gt;&gt;'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---

## TeamEnergyServiceImpl - Issue 187

**位置**: TeamEnergyServiceImpl.java:471
**问题**: 不兼容的类型。实际为 java.util.List&lt;com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingSchemeToNode&gt;'，需要 'java.util.List&lt;SchedulingSchemeToNode&gt;'
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:

```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换

---


## 总结

- 总问题数: 99
- 涉及类数: 5
- 修复方式: 类型转换、参数调整、返回值修复

**注意事项**:
1. 类型转换时要注意空值检查
2. 确保修复后的代码逻辑正确
3. 建议添加适当的异常处理
