package com.cet.eem.solution.common.entity.dto.cetml;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.util.List;

/**
 * 间抽周期数据查询条件
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InterMissivePumping {

    @ApiModelProperty("间抽周期算法平滑因子")
    @JsonProperty("smoothing_factor")
    private Integer smoothingFactor;

    @ApiModelProperty("间抽周期算法空抽比例")
    @JsonProperty("empty_pumping_rate")
    private Double emptyPumpingRate;

    @JsonProperty("prodution_data")
    private List<TimeValue> produtionData;

    @Getter
    @Setter
    @AllArgsConstructor
    public static class TimeValue {
        @ApiModelProperty(value = "时间")
        private Long time;

        @ApiModelProperty(value = "值")
        private Double value;
    }
}
