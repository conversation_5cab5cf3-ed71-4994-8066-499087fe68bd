# 能管解决方案融合版本插件库

## 注意事项

## 相关文档资料

- 能管业务融合jar包使用说明：https://alidocs.dingtalk.com/i/nodes/Y1OQX0akWm3ZRQ0aFEeq0ywrJGlDd3mE
- 融合平台基础配置参考文档：https://cetsoft-svr1/Platforms/PLT-Matterhorn/_wiki/wikis/PLT-Matterhorn.wiki/1315/fusion-matrix%E5%9F%BA%E7%A1%80%E9%85%8D%E7%BD%AE%E6%8F%92%E4%BB%B6%E8%AF%B4%E6%98%8E

## 插件清单说明

| 插件解释     | 插件名                               | 接口前缀                               |
|----------|-----------------------------------|------------------------------------|
| 运维插件     | eem-solution-maintenance          | /eem/solution/maintenance          |
| 设备管理插件   | eem-solution-equipment-manage     | /eem/solution/equipment-manage     |
| 批次能耗插件   | eem-solution-batch-energy         | /eem/solution/batch-energy         |
| 班组能耗     | eem-solution-group-energy         | /eem/solution/group-energy         |
| 设备状态能耗插件 | eem-solution-equipment-energy     | /eem/solution/equipment-energy     |
| 系统运维插件   | eem-solution-system-maintenance   | /eem/solution/system-maintenance   |
| 视频插件     | eem-solution-video                | /eem/solution/video                |
| 制冷系统插件   | eem-solution-refrigeration        | /eem/solution/refrigeration        |
| 空压系统插件   | eem-solution-air-compress         | /eem/solution/air-compress         |
| 变压器能效分析  | eem-solution-transformer          | /eem/solution/transformer          |
| 半导体功能插件  | eem-solution-semiconductor        | /eem/solution/semiconductor        |
| 产量拓展插件   | eem-solution-production-expansion | /eem/solution/production-expansion |
| 复杂能效计算插件 | eem-solution-formula-effect       | /eem/solution/formula-effect       |

## 特殊情况说明
运维插件需要多依赖如下工单的相关jar包
```xml
<dependency>
    <groupId>com.cet.electric</groupId>
    <artifactId>workflow-service-common</artifactId>
    <version>2.0.110.3-SHAPSHOT</version>
    <scope>compile</scope>
</dependency>
<dependency>
    <groupId>com.cet.electric</groupId>
    <artifactId>workflow-service-api</artifactId>
    <version>2.0.110.3-SHAPSHOT</version>
    <scope>compile</scope>
</dependency>
<dependency>
    <groupId>com.cet.electric</groupId>
    <artifactId>workflow-service-feign-spring-boot-starter</artifactId>
    <version>2.0.110.3-SHAPSHOT</version>
</dependency>
```