package com.cet.eem.fusion.maintenance.core.dao.devicecomponent;

import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;
import com.cet.eem.fusion.maintenance.core.entity.po.DeviceComponent;

import java.util.List;

public interface DeviceComponentDao extends BaseModelDao<DeviceComponent> {
    List<DeviceComponent> queryComponentByDevice(String objectLabel, Long objectId);

    DeviceComponent querySingle(String objectLabel, Long objectId, String name, String model);

    DeviceComponent queryWhileEdit(String objectLabel, Long objectId, String name, Long id, String model);

    List<DeviceComponent> queryList(List<String> objectLabels, List<Long> objectIds, String name);

    List<DeviceComponent> queryByProjectId(Long projectId);

    /**
     * 根据管网设备节点查询其关联的零件信息
     *
     * @param baseNode 管网设备节点
     * @return 零件信息
     */
    List<DeviceComponent> queryComponentByDevice(List<BaseVo> baseNode);
}
