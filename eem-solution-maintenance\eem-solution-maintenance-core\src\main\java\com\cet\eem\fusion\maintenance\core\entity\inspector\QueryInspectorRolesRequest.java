package com.cet.eem.fusion.maintenance.core.entity.inspector;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * @ClassName : QueryInspectorRolesRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-14 14:45
 */
@Getter
@Setter
@ApiModel(value = "QueryInspectorRolesRequest", description = "查询巡检人员角色")
public class QueryInspectorRolesRequest {

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空")
    private Long tenantId;

    /**
     * 巡检人员id
     */
    private Long id;
}
