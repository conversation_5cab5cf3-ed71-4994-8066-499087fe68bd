﻿package com.cet.eem.fusion.refrigeration.core.impl;


import com.cet.eem.bll.common.def.quantity.FrequencyDef;
import com.cet.eem.bll.common.def.quantity.PhasorDef;
import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;
import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;
import com.cet.eem.bll.common.model.domain.object.organization.City;
import com.cet.eem.bll.common.model.domain.object.organization.Project;
import com.cet.eem.bll.common.model.domain.subject.energysaving.WeatherPredict;
import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.fusion.refrigeration.core.dao.weather.WeatherPredictDao;
import com.cet.eem.fusion.refrigeration.core.model.weather.*;
import com.cet.eem.fusion.refrigeration.core.service.weather.WeatherCrawlingDataService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.constant.EnergyTypeDef;
import com.cet.eem.common.constant.EnumDataTypeId;
import com.cet.eem.common.constant.EnumOperationType;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.DataLogData;
import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.model.realtime.RealTimeValue;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
import com.cet.eem.quantity.model.quantity.QuantitySearchVo;
import com.cet.eem.quantity.service.QuantityManageService;
import com.cet.eem.weather.dao.WeatherDao;
import com.cet.eem.weather.model.Weather;
import com.cet.eem.weather.model.WeatherValueType;
import com.cet.eem.weather.service.QueryWeatherService;
import com.cet.eem.weather.vo.QueryWeatherParam;
import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : WeatherCrawlingServiceImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-14 10:01
 */
@Service
public class WeatherCrawlingDataServiceImpl implements WeatherCrawlingDataService {
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    QueryWeatherService queryWeatherService;
    @Autowired
    WeatherPredictDao weatherPredictDao;
    @Autowired
    WeatherDao weatherDao;

    @Autowired
    QuantityManageService quantityManageService;
    public final static String METEOROLOGICALMONITOR = "meteorologicalmonitor";
    public final static Integer FIFTEENMINUTE = 18;
    public final static Integer ONE_DAY = 96;
    public final static Integer ONE_HOUR = 4;
    @Autowired
    /**
     * 日志对象
     */
    private static Logger logger = LoggerFactory.getLogger(WeatherCrawlingDataServiceImpl.class);

    /**
     * 气象监测仪直接关联到表计，查询涉及到的节点就是气象监测仪
     *
     * @param queryParam
     * @return
     */
    @Override
    public CurrentWeather queryWeatherCurrentData(QueryParam queryParam,Long projectId) {
        CurrentWeather currentWeather = new CurrentWeather();
        //查询气象监测仪
        List<BaseVo> baseVos = queryNodesByMonitor(projectId);
        //处理查询时间
        handleTime(queryParam);
        Map<Integer, List<RealTimeValue>> integerListMap = quantityManageService.queryRealTimeBath(
                createQuantityDataBatchSearchVoWithRealTime(baseVos, queryParam,
                        Arrays.asList(getCurrentWeatherOfTempQuantitySetting(), getCurrentWeatherOfHumQuantitySetting(),
                                getCurrentWeatherOfPressureQuantitySetting(), getCurrentWeatherOfUviQuantitySetting(), getCurrentWeatherOfRainQuantitySetting(),
                                getCurrentWeatherOfSpeedQuantitySetting(), getCurrentWeatherOfDegQuantitySetting())));
        //取返回结果第一个不为null的值
        currentWeather.setTempAvg(realTimeValue(integerListMap, getCurrentWeatherOfTempQuantitySetting().getId()));
        currentWeather.setHumidity(realTimeValue(integerListMap, getCurrentWeatherOfHumQuantitySetting().getId()));
        currentWeather.setPressure(realTimeValue(integerListMap, getCurrentWeatherOfPressureQuantitySetting().getId()));
        currentWeather.setUvi(realTimeValue(integerListMap, getCurrentWeatherOfUviQuantitySetting().getId()));
        currentWeather.setRain(realTimeValue(integerListMap, getCurrentWeatherOfRainQuantitySetting().getId()));
        currentWeather.setWindSpeed(realTimeValue(integerListMap, getCurrentWeatherOfSpeedQuantitySetting().getId()));
        currentWeather.setWindDeg(realTimeValue(integerListMap, getCurrentWeatherOfDegQuantitySetting().getId()));
        return currentWeather;

    }

    private void handleTime(QueryParam queryParam) {
        long now = System.currentTimeMillis();
        LocalDateTime localDateTime = TimeUtil.timestamp2LocalDateTime(now);
        LocalDate date = localDateTime.toLocalDate();
        LocalTime time = LocalTime.of(localDateTime.getHour(), localDateTime.getMinute());
        LocalDateTime of = LocalDateTime.of(date, time);
        queryParam.setStartTime(of);
        queryParam.setCycle(AggregationCycle.ONE_MINUTE);
        queryParam.setEndTime(of.plusMinutes(1L));
    }

    /**
     * 根据配置的检测名称取查询其关联的节点信息
     *
     * @return
     */
    @Override
    public List<BaseVo> queryNodesByMonitor(Long projectId) {
        QueryCondition condition = new QueryConditionBuilder<>(NodeLabelDef.PROJECT, projectId)
                .leftJoin(METEOROLOGICALMONITOR).queryAsTree().build();
        List<BaseVo> query = modelServiceUtils.query(condition, BaseVo.class);
        List<BaseVo> result = new ArrayList<>();
        for (BaseVo baseVo : query) {
            if (CollectionUtils.isNotEmpty(baseVo.getChildren())) {
                result.addAll(baseVo.getChildren());
            }
        }
        return result;
    }

    private QuantitySearchVo getCurrentWeatherOfTempQuantitySetting() {
        return new QuantitySearchVo(6002006,
                QuantityCategoryDef.TEMP,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ENERGY);
    }

    private QuantitySearchVo getCurrentWeatherOfHumQuantitySetting() {
        return new QuantitySearchVo(6005000,
                QuantityCategoryDef.HUMIDITY,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ENERGY);
    }

    private QuantitySearchVo getCurrentWeatherOfUviQuantitySetting() {
        return new QuantitySearchVo(6000481,
                32,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ENERGY);
    }

    private QuantitySearchVo getCurrentWeatherOfPressureQuantitySetting() {
        return new QuantitySearchVo(6000482,
                13,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ENERGY);
    }

    private QuantitySearchVo getCurrentWeatherOfSpeedQuantitySetting() {
        return new QuantitySearchVo(6000483,
                11,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ENERGY);
    }

    private QuantitySearchVo getCurrentWeatherOfDegQuantitySetting() {
        return new QuantitySearchVo(6000484,
                12,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ENERGY);
    }

    private QuantitySearchVo getCurrentWeatherOfRainQuantitySetting() {
        return new QuantitySearchVo(6000485,
                21,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ENERGY);
    }

    private Double realTimeValue(Map<Integer, List<RealTimeValue>> integerListMapOfTemp, Integer id) {
        if (Objects.nonNull(integerListMapOfTemp)) {
            List<RealTimeValue> realTimeValues = integerListMapOfTemp.get(id);
            List<RealTimeValue> collect = realTimeValues.stream().filter(realTimeValue -> Objects.nonNull(realTimeValue.getValue())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                return collect.get(0).getValue();
            }

        }
        return null;
    }


    @Override
    public void writeWeatherPredict(List<WeatherPredict> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        List<WeatherPredict> sortList = dataList.stream().sorted(Comparator.comparing(WeatherPredict::getLogTime)).collect(Collectors.toList());
        List<WeatherPredict> weatherPredicts = weatherPredictDao.queryWeather(sortList.get(0).getLogTime(), sortList.get(sortList.size() - 1).getLogTime());

        // 匹配数据库中已经有的数据
        for (WeatherPredict weather : dataList) {
            Optional<WeatherPredict> any = weatherPredicts.stream().filter(item -> Objects.equals(item.getProjectId(), weather.getProjectId()) &&
                    Objects.equals(weather.getLogTime(), item.getLogTime()) &&
                    Objects.equals(weather.getAggregationCycle(), item.getAggregationCycle())
            )
                    .findAny();
            any.ifPresent(item -> weather.setId(item.getId()));
        }
        List<WeatherPredict> result = weatherPredictDao.writeOrUpdateData(dataList);
        String string = JsonTransferUtils.toJSONString(result);
        logger.info("{预测的天气数据入库详情：{}", string);


    }

    @Override
    public List<ForecastBasicWeatherDataVo> queryWeather(ForecastBasicWeatherQueryVo query, Long project) throws Exception {
        if (CollectionUtils.isEmpty(query.getProjectIds())) {
            query.setProjectIds(Collections.singletonList(project));
        }
        if (Objects.isNull(query.getStartTimePredict())) {
            query.setStartTimePredict(query.getStartTime());
        }
        if (Objects.isNull(query.getEndTimePredict())) {
            query.setEndTimePredict(query.getEndTime());
        }
        List<Long> longList = queryCityCode(query.getProjectIds());
        //处理查历史的时间  因为需要计算差值
        LocalDateTime firstTimeOfHour = TimeUtil.getFirstTimeOfHour(query.getStartTime());
        LocalDateTime endTimeOfHour = TimeUtil.addDateTimeByCycle(TimeUtil.getFirstTimeOfHour(query.getEndTime()), AggregationCycle.ONE_HOUR, 2);
        List<Weather> historyWeathers = queryOldHistoryWeathers(longList, firstTimeOfHour, endTimeOfHour);
        List<Weather> predictWeathers = queryOldPredictWeathers(longList, query);
        List<MeasureWeather> measureWeathers = queryMeasureWeather(query,project);
        List<ForecastWeather> forecastWeathersOfHis = transDataType(calculateHisWeather(assembleWeather(historyWeathers, firstTimeOfHour, endTimeOfHour, AggregationCycle.ONE_HOUR)));
        List<ForecastWeather> forecastWeathers = transDataType(assembleWeather(predictWeathers, query.getStartTimePredict(), query.getEndTimePredict(), AggregationCycle.FIFTEEN_MINUTES));
        ForecastBasicWeatherDataVo forecastBasicWeatherDataVo = new ForecastBasicWeatherDataVo();
        forecastBasicWeatherDataVo.setForecastPredictWeathers(forecastWeathers);
        forecastBasicWeatherDataVo.setPredictWeathers(forecastWeathers);
        forecastBasicWeatherDataVo.setForecastActualWeathers(filterData(forecastWeathersOfHis, query));

        forecastBasicWeatherDataVo.setMeasureWeathers(measureWeathers);
        forecastBasicWeatherDataVo.setProjectId(project);
        return Collections.singletonList(forecastBasicWeatherDataVo);
    }

    private List<ForecastWeather> filterData(List<ForecastWeather> forecastWeathersOfHis, ForecastBasicWeatherQueryVo query) {
        List<ForecastWeather> result = new ArrayList<>();
        for (ForecastWeather forecastWeather : forecastWeathersOfHis) {
            if ((forecastWeather.getLogTime().isEqual(query.getStartTime()) || forecastWeather.getLogTime().isAfter(query.getStartTime())) && forecastWeather.getLogTime().isBefore(query.getEndTime())) {
                result.add(forecastWeather);
            }
        }
        return result;
    }

    private List<Weather> assembleWeather(List<Weather> totalWeathers, LocalDateTime st, LocalDateTime et, Integer cycle) {
        List<LocalDateTime> timeRange = TimeUtil.getTimeRange(st, et, cycle);
        List<Weather> before = new ArrayList<>();
        for (LocalDateTime time : timeRange) {
            Weather weather = totalWeathers.stream().filter(weather1 -> Objects.equals(weather1.getLogTime(), TimeUtil.localDateTime2timestamp(time)))
                    .findFirst().orElse(new Weather());
            if (Objects.isNull(weather.getLogTime())) {
                weather.setLogTime(TimeUtil.localDateTime2timestamp(time));
            }
            before.add(weather);
        }
        return before;
    }

    private List<Weather> assembleWeather(List<Weather> totalWeathers, LocalDateTime nowTime, Boolean isBefore) {
        List<LocalDateTime> oneDayBeforeTime;
        if (Boolean.TRUE.equals(isBefore)) {
            oneDayBeforeTime = getOneDayBeforeTime(nowTime);
        } else {
            oneDayBeforeTime = createNextFIfTime(nowTime);
        }
        List<Weather> before = new ArrayList<>();
        for (LocalDateTime time : oneDayBeforeTime) {
            Weather weather = totalWeathers.stream().filter(weather1 -> Objects.equals(weather1.getLogTime(), TimeUtil.localDateTime2timestamp(time)))
                    .findFirst().orElse(new Weather());
            if (Objects.isNull(weather.getLogTime())) {
                weather.setLogTime(TimeUtil.localDateTime2timestamp(time));
            }
            before.add(weather);
        }
        return before;
    }


    private List<Weather> calculateHisWeather(List<Weather> totalWeathers) {

        if (CollectionUtils.isEmpty(totalWeathers)) {
            return Collections.emptyList();
        }
        List<Weather> result = new ArrayList<>();
        if (Objects.equals(totalWeathers.size(), 1)) {
            createMinuteWeather(totalWeathers.get(0), totalWeathers.get(0), result);
        } else {
            for (int i = 0; i < totalWeathers.size() - 1; i++) {
                createMinuteWeather(totalWeathers.get(i), totalWeathers.get(i + 1), result);
            }
        }
        return result;

    }

    private List<LocalDateTime> getOneDayBeforeTime(LocalDateTime now) {
        List<LocalDateTime> result = new ArrayList<>();
        LocalDateTime nowHour = TimeUtil.getFirstTimeOfHour(now);
        LocalDateTime endHour = TimeUtil.addDateTimeByCycle(nowHour, AggregationCycle.ONE_HOUR, 1);
        LocalDateTime startHour = TimeUtil.addDateTimeByCycle(nowHour, AggregationCycle.ONE_DAY, -1);
        List<LocalDateTime> timeRange = TimeUtil.getTimeRange(startHour, endHour, AggregationCycle.FIFTEEN_MINUTES);
        List<LocalDateTime> times = timeRange.stream().sorted(Comparator.comparing(LocalDateTime::toLocalTime).reversed()).collect(Collectors.toList());
        for (LocalDateTime time : times) {
            if (Boolean.TRUE.equals(time.isBefore(now))) {
                result.add(time);
            }
            if (Objects.equals(ONE_DAY, result.size())) {
                result.add(time);
            }
        }
        return result.stream().sorted(Comparator.comparing(LocalDateTime::toLocalTime)).collect(Collectors.toList());
    }

    private List<LocalDateTime> createNextFIfTime(LocalDateTime now) {
        LocalDateTime firstTimeOfHour = TimeUtil.getFirstTimeOfHour(now);
        List<LocalDateTime> timeRangeForFifteen = TimeUtil.getTimeRange(firstTimeOfHour, TimeUtil.addDateTimeByCycle(firstTimeOfHour, AggregationCycle.ONE_HOUR, 2), AggregationCycle.FIFTEEN_MINUTES);
        List<LocalDateTime> result = new ArrayList<>();
        for (LocalDateTime time : timeRangeForFifteen) {
            if (Boolean.TRUE.equals(time.isAfter(now))) {
                result.add(time);
            }
            if (Objects.equals(result.size(), ONE_HOUR)) {
                return result;
            }
        }
        return result;
    }

    private void createMinuteWeather(Weather weather, Weather weatherOther, List<Weather> result) {
        List<Long> timeRange = new ArrayList<>();
        LocalDateTime j;
        for (j = TimeUtil.timestamp2LocalDateTime(weather.getLogTime()); j.isBefore(TimeUtil.timestamp2LocalDateTime(weatherOther.getLogTime())); j = j.plusMinutes(15L)) {
            timeRange.add(TimeUtil.localDateTime2timestamp(j));
        }
        int size = timeRange.size();
        for (int i = 0; i < size; i++) {
            Weather item = new Weather();
            BeanUtils.copyProperties(weather, item);
            item.setLogTime(timeRange.get(i));
            item.setAggregationCycle(AggregationCycle.FIFTEEN_MINUTES);
            item.setHumidity(calc(weatherOther.getHumidity(), weather.getHumidity(), i, size));
            item.setTempAvg(calc(weatherOther.getTempAvg(), weather.getTempAvg(), i, size));
            result.add(item);
        }

    }

    private Double calc(Double d1, Double d2, int i, int size) {
        Double aDouble = CommonUtils.calcDouble(d1, d2, EnumOperationType.SUBTRACT.getId());
        Double bDouble = CommonUtils.calcDouble(aDouble, Double.parseDouble(String.valueOf(size)), EnumOperationType.DIVISION.getId());
        Double cDouble = CommonUtils.calcDouble(bDouble, Double.parseDouble(String.valueOf(i)), EnumOperationType.MULTIPLICATION.getId());
        return CommonUtils.calcDouble(cDouble, d2, EnumOperationType.ADD.getId());

    }

    private List<ForecastWeather> transDataType(List<Weather> weathers) {
        if (CollectionUtils.isEmpty(weathers)) {
            return Collections.emptyList();
        }
        ArrayList<Weather> collect = weathers.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(
                        Comparator.comparing(
                                Weather::getLogTime))), ArrayList::new));

        List<ForecastWeather> forecastWeathers = new ArrayList<>();
        for (Weather weather : collect) {
            ForecastWeather forecastWeather = new ForecastWeather();
            BeanUtils.copyProperties(weather, forecastWeather);
            forecastWeather.setLogTime(TimeUtil.timestamp2LocalDateTime(weather.getLogTime()));
            forecastWeather.setTemp(weather.getTempAvg());
            forecastWeather.setCycle(weather.getAggregationCycle());
            forecastWeathers.add(forecastWeather);
        }
        return forecastWeathers.stream().sorted(Comparator.comparing(ForecastWeather::getLogTime)).collect(Collectors
                .toList());
    }

    private List<MeasureWeather> queryMeasureWeather(ForecastBasicWeatherQueryVo query,Long projectId) throws Exception {
        List<BaseVo> baseVos = queryNodesByMonitor(projectId);

        QueryParam queryParam = new QueryParam(query.getStartTime(), query.getEndTime(), query.getCycle());
        return querySingleDataLog(baseVos, queryParam);
    }

    private QuantityDataBatchSearchVo createQuantityDataBatchSearchVo(List<BaseVo> deviceNodes, QueryParam query, List<QuantitySearchVo> quantitySearchVo) {
        QuantityDataBatchSearchVo aggregationDataBatch = new QuantityDataBatchSearchVo();
        aggregationDataBatch.setDataTypeId(EnumDataTypeId.REALTIME.getId());
        aggregationDataBatch.setStartTime(TimeUtil.localDateTime2timestamp(query.getStartTime()));
        aggregationDataBatch.setEndTime(TimeUtil.localDateTime2timestamp(query.getEndTime()));
        aggregationDataBatch.setQuantitySettings((quantitySearchVo));
        aggregationDataBatch.setNodes(deviceNodes);
        aggregationDataBatch.setAggregationCycle(query.getCycle());
        return aggregationDataBatch;
    }

    private List<LocalDateTime> getTimeRangeForFifteen(LocalDateTime st, LocalDateTime et) {
        List<LocalDateTime> timeRange = new ArrayList<>();
        LocalDateTime j;
        for (j = st; j.isBefore(et); j = j.plusMinutes(15L)) {
            timeRange.add((j));
        }
        return timeRange;
    }

    private List<MeasureWeather> querySingleDataLog(List<BaseVo> baseVos, QueryParam queryParam)  {
        Map<Integer, List<TrendDataVo>> dataLogResult = quantityManageService.queryDataLogBatch(
                createQuantityDataBatchSearchVo(baseVos, queryParam, Arrays.asList(getCurrentWeatherOfTempQuantitySetting(), getCurrentWeatherOfHumQuantitySetting())));
        // 取出设备数据
        List<TrendDataVo> dataVoListOfTemp = dataLogResult.get(getCurrentWeatherOfTempQuantitySetting().getId());
        List<TrendDataVo> dataVoListOfHum = dataLogResult.get(getCurrentWeatherOfHumQuantitySetting().getId());
        List<DatalogValue> dataListOfTemp = new ArrayList<>();
        List<DatalogValue> dataListOfHum = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dataVoListOfTemp)) {
            dataListOfTemp = dataVoListOfTemp.get(0).getDataList();
        }
        if (CollectionUtils.isNotEmpty(dataVoListOfHum)) {
            dataListOfHum = dataVoListOfHum.get(0).getDataList();
        }
        List<MeasureWeather> result = new ArrayList<>();
        List<LocalDateTime> timeRange;
        if (Objects.equals(queryParam.getCycle(), FIFTEENMINUTE)) {
            timeRange = getTimeRangeForFifteen(queryParam.getStartTime(), queryParam.getEndTime());
        } else {
            timeRange = TimeUtil.getTimeRange(queryParam.getStartTime(), queryParam.getEndTime(), queryParam.getCycle());
        }
        for (LocalDateTime time : timeRange) {
            MeasureWeather measureWeather = new MeasureWeather();

            List<DatalogValue> hum = dataListOfHum.stream().filter(dataLogData -> Objects.equals(TimeUtil.timestamp2LocalDateTime(dataLogData.getTime()), time)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hum)) {
                measureWeather.setHumidity(hum.get(0).getValue());
            }
            List<DatalogValue> temp = dataListOfTemp.stream().filter(dataLogData -> Objects.equals(TimeUtil.timestamp2LocalDateTime(dataLogData.getTime()), time)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(temp)) {
                measureWeather.setTemp(temp.get(0).getValue());
            }
            measureWeather.setLogTime(time);
            result.add(measureWeather);
        }

        return result;
    }

    private QuantityDataBatchSearchVo createQuantityDataBatchSearchVoWithRealTime(List<BaseVo> deviceNodes, QueryParam query, List<QuantitySearchVo> quantitySettings) {
        QuantityDataBatchSearchVo aggregationDataBatch = new QuantityDataBatchSearchVo();
        aggregationDataBatch.setDataTypeId(EnumDataTypeId.REALTIME.getId());
        aggregationDataBatch.setStartTime(TimeUtil.localDateTime2timestamp(query.getStartTime()));
        aggregationDataBatch.setEndTime(TimeUtil.localDateTime2timestamp(query.getEndTime()));
        aggregationDataBatch.setQuantitySettings(quantitySettings);
        aggregationDataBatch.setNodes(deviceNodes);
        return aggregationDataBatch;
    }


    /**
     * 查询城市预测天气数据
     *
     * @param cities
     * @param query
     * @return
     */
    private List<Weather> queryOldPredictWeathers(List<Long> cities, ForecastBasicWeatherQueryVo query) {
        QueryWeatherParam queryParam = new QueryWeatherParam();
        queryParam.setCodes(cities);
        queryParam.setStarttime(TimeUtil.localDateTime2timestamp(query.getStartTimePredict()));
        queryParam.setEndtime(TimeUtil.localDateTime2timestamp(query.getEndTimePredict()));
        queryParam.setType(WeatherValueType.PREDICT.getId());
        return weatherDao.queryWeather(queryParam).getData();
    }

    /**
     * 历史数据
     *
     * @param cities
     * @param st
     * @param et
     * @return
     */
    private List<Weather> queryOldHistoryWeathers(List<Long> cities, LocalDateTime st, LocalDateTime et) {
        QueryWeatherParam queryParam = new QueryWeatherParam();
        queryParam.setCodes(cities);
        queryParam.setStarttime(TimeUtil.localDateTime2timestamp(st));
        queryParam.setEndtime(TimeUtil.localDateTime2timestamp(et));
        queryParam.setType(WeatherValueType.ACTUAL.getId());
        return weatherDao.queryWeather(queryParam).getData();
    }

    private List<Long> queryCityCode(List<Long> projectIds) {
        List<Project> projects = queryProject(projectIds);
        List<String> cityNames = projects.stream().map(Project::getCity).distinct().collect(Collectors.toList());
        List<City> cities = queryCity(cityNames);
        return cities.stream().map(City::getCode).collect(Collectors.toList());
    }

    private List<Project> queryProject(List<Long> projectIds) {
        QueryCondition condition = new QueryConditionBuilder<>(NodeLabelDef.PROJECT).in(ColumnDef.ID, projectIds).queryAsTree().build();
        List<Map<String, Object>> query = modelServiceUtils.query(condition);
        return JsonTransferUtils.transferList(query, Project.class);

    }

    private List<City> queryCity(List<String> cityNames) {
        QueryCondition queryCondition = new QueryConditionBuilder<>(ModelLabelDef.CITY).in(ColumnDef.NAME, cityNames)
                .queryAsTree().build();
        List<Map<String, Object>> queryCitys = modelServiceUtils.query(queryCondition);
        return JsonTransferUtils.transferList(queryCitys, City.class);

    }


}
