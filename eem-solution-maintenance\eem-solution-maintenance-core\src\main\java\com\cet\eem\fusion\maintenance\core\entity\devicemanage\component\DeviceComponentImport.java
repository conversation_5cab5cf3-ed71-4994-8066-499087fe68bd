package com.cet.eem.fusion.maintenance.core.entity.devicemanage.component;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * @ClassName : DeviceComponentImport
 * @Description : 导入零件数据
 * <AUTHOR> Administrator
 * @Date: 2021-05-17 14:30
 */
@Data
public class DeviceComponentImport {
    @ExcelProperty(value = "流入节点", index = 0)
    private String device;
    @ExcelProperty(value = "零件名称", index = 1)
    private String name;
    @ExcelProperty(value = "零件规格", index = 2)
    private String model;
    @ExcelProperty(value = "零件厂家", index = 3)
    private String brand;
    @ExcelProperty(value = "零件数量", index = 4)
    private Double number;
    @ExcelProperty(value = "零件单位", index = 5)
    private String unit;


}