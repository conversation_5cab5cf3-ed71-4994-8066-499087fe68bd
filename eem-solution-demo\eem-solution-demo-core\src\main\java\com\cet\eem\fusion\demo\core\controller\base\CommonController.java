package com.cet.eem.fusion.demo.core.controller.base;

import com.cet.eem.fusion.config.sdk.controller.BaseEemCommonController;
import com.cet.eem.solution.common.def.common.PluginInfoDef;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 描述：公共基础操作
 *
 * <AUTHOR>
 * @date 2020/2/24 13:48
 */
@Api(value = PluginInfoDef.Demo.INTERFACE_PREFIX + "/v1/common", tags = "公共管理服务")
@RequestMapping(value = PluginInfoDef.Demo.INTERFACE_PREFIX + "/v1/common")
@RestController
public class CommonController extends BaseEemCommonController {
}
