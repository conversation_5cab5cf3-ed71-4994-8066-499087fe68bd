﻿package com.cet.eem.fusion.refrigeration.core.service;

import com.cet.eem.fusion.refrigeration.core.model.dataentryquery.DataQueryParam;

import java.time.LocalDateTime;

/**
 * @ClassName : ColdPredictDataService
 * @Description : 写入coldpredict表的
 * <AUTHOR> jiang<PERSON><PERSON><PERSON>
 * @Date: 2022-07-26 14:12
 */
public interface ColdPredictDataService {
    /**
     * 转存末端数据
     */
    void saveEndColdPredictData();


    /**
     * 转存管损
     */
    void savePipeLineLossPredictData();

    /**
     * 转存系统总功率
     */
    void saveTotalSystemPowerPredictData();

    /**
     * 拼接过去一天和未来一小时的查询时间
     *
     * @param now
     * @return
     */
    DataQueryParam assembleTime(LocalDateTime now);
}
