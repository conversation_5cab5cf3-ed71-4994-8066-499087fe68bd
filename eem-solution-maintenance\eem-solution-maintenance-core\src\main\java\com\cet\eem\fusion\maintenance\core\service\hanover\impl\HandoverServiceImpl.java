package com.cet.eem.fusion.maintenance.core.service.hanover.impl;

import com.cet.eem.auth.service.AuthUtils;
import com.cet.eem.fusion.config.sdk.def.OperationLogType;
import com.cet.eem.bll.common.log.service.CommonUtilsService;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.bll.maintenance.dao.handover.HandOverDao;
import com.cet.eem.bll.maintenance.def.DutyStatusDef;
import com.cet.eem.bll.maintenance.model.handover.*;
import com.cet.eem.bll.maintenance.model.workorder.OperationUser;
import com.cet.eem.bll.maintenance.service.hanover.HandoverService;
import com.cet.eem.bll.maintenance.service.inspection.InspectorService;
import com.cet.eem.bll.maintenance.utils.InspectorUserCheckUtils;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.common.utils.ParamUtils;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.exception.ValidationException;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 交接班
 *
 * <AUTHOR>
 * @date 2020年6月22日 14:22:10
 */
@Service
public class HandoverServiceImpl implements HandoverService {

    @Autowired
    HandOverDao handOverDao;

    @Autowired
    AuthUtils authUtils;

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    CommonUtilsService commonUtilsService;

    @Autowired
    InspectorService inspectorService;

    @Autowired
    InspectorUserCheckUtils inspectorUserCheckUtils;
    private static final String MESSAGE = "项目未指定！";

    @Override
    public ResultWithTotal<List<ShiftingDutyVo>> getHandoverList(ShiftingDutySearchDto dto) {
        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        dto.setTeamId(inspectorUserCheckUtils.getAndCheckTeamId(dto.getTeamId(), user));
        if (!ParamUtils.checkPrimaryKeyValid(dto.getProjectId())) {
            dto.setProjectId(GlobalInfoUtils.getTenantId());
        }
        ResultWithTotal<List<Map<String, Object>>> result = handOverDao.queryShiftingDuty(dto);
        List<ShiftingDutyVo> dataList = JsonTransferUtils.transferList(result.getData(), ShiftingDutyVo.class);
        assemblyShiftingDuty(dataList, dto.getTenantId());

        return ResultWithTotal.ok(dataList, result.getTotal());
    }

    /**
     * 组装数据
     *
     * @param shiftingDutyPos
     * @param tenantId
     */
    public void assemblyShiftingDuty(List<ShiftingDutyVo> shiftingDutyPos, Long tenantId) {
        if (CollectionUtils.isEmpty(shiftingDutyPos)) {
            return;
        }

        Map<Integer, String> dutyStatusMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.DUTY_STATUS);
        Map<Long, String> userMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        Map<Long, String> groupMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        authUtils.getUserAndGroupMsg(tenantId, userMap, groupMap);

        for (ShiftingDutyVo shiftingDutyPo : shiftingDutyPos) {
            shiftingDutyPo.setDutyStatusName(dutyStatusMap.get(shiftingDutyPo.getDutyStatus()));
            shiftingDutyPo.setTeamName(groupMap.get(shiftingDutyPo.getTeamId()));
            shiftingDutyPo.setHandoverFromStaffName(userMap.get(shiftingDutyPo.getHandoverFromStaff()));
            shiftingDutyPo.setHandoverStaffName(userMap.get(shiftingDutyPo.getHandoverStaff()));
            shiftingDutyPo.setDutyOfficerName(userMap.get(shiftingDutyPo.getDutyOfficer()));
        }
    }

    @Override
    public ShiftingDutyVo getHandoverDetailInfo(Long id, Long tenantId) {
        ShiftingDutyPo shiftingDutyPo = handOverDao.selectById(id);
        List<ShiftingDutyVo> shiftingDutyVos = JsonTransferUtils.transferList(Collections.singletonList(shiftingDutyPo), ShiftingDutyVo.class);
        assemblyShiftingDuty(shiftingDutyVos, tenantId);
        return shiftingDutyVos.get(0);
    }

    @Override
    public ShiftingDutyPo handOverToNext(ShiftingDutyUpdateVo shiftingDutyUpdateVo) {
        // 获取交班用户信息
        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        Long teamId = authUtils.getRelativeGroup(user);
        checkInspectUser(user);

        Long projectId = GlobalInfoUtils.getTenantId();
        if (!ParamUtils.checkPrimaryKeyValid(projectId)) {
            throw new ValidationException(MESSAGE);
        }

        // 判断是否允许交班
        ShiftingDutyPo shiftingDutyPoResult = handOverDao.getLastOnDutyRecord(teamId, GlobalInfoUtils.getTenantId(), user.getId());
        if (shiftingDutyPoResult == null) {
            throw new ValidationException("当前没有需要交班的工作！");
        }

        if (!shiftingDutyPoResult.getHandoverStaff().equals(user.getId())) {
            throw new ValidationException("非值班人员交班！");
        }

        shiftingDutyPoResult.setHandoverMatter(shiftingDutyUpdateVo.getHandoverMatter());
        shiftingDutyPoResult.setDutyLog(shiftingDutyUpdateVo.getDutyLog());
        shiftingDutyPoResult.setDutyStatus(DutyStatusDef.OFF_DUTY);
        shiftingDutyPoResult.setEndTime(LocalDateTime.now());
        long duration = Duration.between(shiftingDutyPoResult.getStartTime(), shiftingDutyPoResult.getEndTime()).toMillis();
        shiftingDutyPoResult.setDutyTime(duration);

        List<ShiftingDutyPo> shiftingDutyPos = modelServiceUtils.writeData(Collections.singletonList(shiftingDutyPoResult), ShiftingDutyPo.class);
        commonUtilsService.writeUpdateOperationLogs(EEMOperationLogType.HANDOVER, "交班", shiftingDutyPos);
        return shiftingDutyPos.get(0);
    }

    @Override
    public ShiftingDutyPo succession(SuccessionDto successionDto) {
        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        Long teamId = authUtils.getRelativeGroup(user);
        checkInspectUser(user);

        ShiftingDutyPo shiftingDutyPo = new ShiftingDutyPo();
        Assert.isTrue(CollectionUtils.isNotEmpty(successionDto.getDutyStaffList()), "值班人员不允许为空！");

        Long projectId = GlobalInfoUtils.getTenantId();
        if (!ParamUtils.checkPrimaryKeyValid(projectId)) {
            throw new ValidationException(MESSAGE);
        }

        // 当前班组没有任何交接班记录或者最新一条值班记录中已经填写了交接班注意事项才允许接班
        ShiftingDutyPo onDutyRecord = handOverDao.queryLastShiftDuty(teamId, projectId);
        if (onDutyRecord != null && user.getId().equals(onDutyRecord.getHandoverStaff())) {
            throw new ValidationException("交接班不允许由同一个用户完成！");

        }

        shiftingDutyPo.setDutyStatus(DutyStatusDef.ON_DUTY);
        shiftingDutyPo.setStartTime(LocalDateTime.now());
        shiftingDutyPo.setHandoverStaff(user.getId());
        shiftingDutyPo.setDutyOfficer(user.getId());
        shiftingDutyPo.setTeamId(teamId);
        shiftingDutyPo.setProjectId(projectId);
        shiftingDutyPo.setDutyStaff(JsonTransferUtils.toJSONString(successionDto.getDutyStaffList()));

        List<ShiftingDutyPo> dataList = new ArrayList<>();
        dataList.add(shiftingDutyPo);
        if (onDutyRecord != null) {
            shiftingDutyPo.setHandoverFromStaff(onDutyRecord.getHandoverStaff());
            onDutyRecord.setEndTime(LocalDateTime.now());
            long duration = Duration.between(onDutyRecord.getStartTime(), onDutyRecord.getEndTime()).toMillis();
            onDutyRecord.setDutyTime(duration);
            dataList.add(onDutyRecord);
        }

        List<ShiftingDutyPo> shiftingDutyPos = modelServiceUtils.writeData(dataList, ShiftingDutyPo.class);
        if (onDutyRecord == null) {
            return shiftingDutyPos.get(0);
        }

        Long lastShiftId = onDutyRecord.getId();
        List<ShiftingDutyPo> result = shiftingDutyPos.stream().filter(it -> !it.getId().equals(lastShiftId)).collect(Collectors.toList());
        commonUtilsService.writeUpdateOperationLogs(EEMOperationLogType.HANDOVER, "接班", result);
        return result.get(0);
    }

    @Override
    public HandoverMatterVo getHandoverMatter(Long tenantId) {
        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        Long teamId = authUtils.getRelativeGroup(user);
        checkInspectUser(user);

        Long projectId = GlobalInfoUtils.getTenantId();
        if (!ParamUtils.checkPrimaryKeyValid(projectId)) {
            throw new ValidationException(MESSAGE);
        }

        Map<Long, String> userMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        Map<Long, String> groupMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        authUtils.getUserAndGroupMsg(tenantId, userMap, groupMap);

        HandoverMatterVo handoverMatterVo = new HandoverMatterVo();
        handoverMatterVo.setTeamId(teamId);
        handoverMatterVo.setTeamName(groupMap.get(teamId));
        handoverMatterVo.setDutyOfficer(user.getId());
        handoverMatterVo.setDutyOfficerName(userMap.get(user.getId()));
        handoverMatterVo.setFirstHandOver(false);

        ShiftingDutyVo onDutyRecord = handOverDao.queryLastShiftDuty(teamId, null, projectId, null, ShiftingDutyVo.class);
        if (onDutyRecord == null) {
            handoverMatterVo.setAllowChooseUsers(queryAllowChooseUsers(null));
            handoverMatterVo.setFirstHandOver(true);
        } else {
            if (user.getId().equals(onDutyRecord.getHandoverStaff())) {
                throw new ValidationException("当前人员在上一班次已经值班，不允许重复接班！");
            }

            List<OperationUser> dutyStaffList = onDutyRecord.getDutyStaffList();
            handoverMatterVo.setAllowChooseUsers(queryAllowChooseUsers(dutyStaffList));
            handoverMatterVo.setOnDutyOfficer(onDutyRecord.getHandoverStaff());
            handoverMatterVo.setOnDutyOfficerName(userMap.get(onDutyRecord.getHandoverStaff()));
            handoverMatterVo.setHandoverMatter(onDutyRecord.getHandoverMatter());
        }

        return handoverMatterVo;
    }

    private List<OperationUser> queryAllowChooseUsers(List<OperationUser> dutyStaffList) {
        List<UserVo> userVos = inspectorService.querySameGroupMember();
        if (dutyStaffList == null) {
            dutyStaffList = Collections.emptyList();
        }

        Set<Long> userIds = dutyStaffList.stream().map(OperationUser::getUserId).collect(Collectors.toSet());
        return userVos.stream().filter(it -> !userIds.contains(it.getId())).map(it -> {
            OperationUser operationUser = new OperationUser();
            operationUser.setUserId(it.getId());
            operationUser.setUserName(it.getName());
            return operationUser;
        }).collect(Collectors.toList());
    }

    private void checkInspectUser(UserVo user) {
        boolean inspectorUser = inspectorService.isMaintenanceUser(user);
        if (!inspectorUser) {
            throw new ValidationException("非班组用户！");
        }
    }

    @Override
    public ShiftingDutyVo queryOnDutyRecord(Long tenantId) {
        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        Long teamId = authUtils.getRelativeGroup(user);
        checkInspectUser(user);

        Long projectId = GlobalInfoUtils.getTenantId();
        if (!ParamUtils.checkPrimaryKeyValid(projectId)) {
            throw new ValidationException(MESSAGE);
        }

        Map<Long, String> userMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        Map<Long, String> groupMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        authUtils.getUserAndGroupMsg(tenantId, userMap, groupMap);

        HandoverMatterVo handoverMatterVo = new HandoverMatterVo();
        handoverMatterVo.setTeamId(teamId);
        handoverMatterVo.setTeamName(groupMap.get(teamId));
        handoverMatterVo.setDutyOfficer(user.getId());
        handoverMatterVo.setDutyOfficerName(userMap.get(user.getId()));
        handoverMatterVo.setFirstHandOver(false);

        ShiftingDutyPo onDutyRecord = handOverDao.queryLastShiftDuty(teamId, DutyStatusDef.ON_DUTY, projectId, user.getId(), ShiftingDutyPo.class);
        if (onDutyRecord == null) {
            throw new ValidationException("当前用户未在值班！");
        }

        List<ShiftingDutyVo> shiftingDutyVos = JsonTransferUtils.transferList(Collections.singletonList(onDutyRecord), ShiftingDutyVo.class);
        assemblyShiftingDuty(shiftingDutyVos, tenantId);

        return shiftingDutyVos.get(0);
    }

    @Override
    public String getUserWorkingStatus() {
        UserVo user = authUtils.queryUser(GlobalInfoUtils.getUserId());
        List<Long> relativeUserGroup = user.getRelativeUserGroup();
        Map<Integer, String> dutyStatusMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.DUTY_STATUS);
        if (CollectionUtils.isEmpty(relativeUserGroup)) {
            return dutyStatusMap.get(DutyStatusDef.OFF_DUTY);
        }
        ShiftingDutyPo ShiftingDutyPo = handOverDao.getHandoverPo(relativeUserGroup.get(0), GlobalInfoUtils.getTenantId());
        if (ShiftingDutyPo == null) {
            return dutyStatusMap.get(DutyStatusDef.OFF_DUTY);
        }
        if (user.getId().equals(ShiftingDutyPo.getHandoverStaff())) {
            return dutyStatusMap.get(DutyStatusDef.ON_DUTY);
        }
        return dutyStatusMap.get(DutyStatusDef.OFF_DUTY);
    }
}
