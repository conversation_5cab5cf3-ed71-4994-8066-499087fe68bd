package com.cet.eem.fusion.maintenance.core.service.device;

import com.cet.eem.bll.common.model.domain.object.physicalquantity.MeasuredbyVo;
import com.cet.eem.bll.maintenance.model.devicemanage.*;
import com.cet.eem.bll.maintenance.model.devicemanage.template.RunningParamGroup;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.commons.ApiResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 设备管理
 *
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-04-14
 */
public interface DeviceManagerService {

    /**
     * 返回二维码
     *
     * @param userId
     * @param response
     * @return
     * @throws Exception
     */
    Result<String> qrCode(
            Long userId,
            EquipmentSearchDto queryCondition,
            HttpServletResponse response) throws IOException;

    /**
     * 导出二维码
     *
     * @param userId
     * @param info
     * @param response
     * @return
     * @throws Exception
     */
    Result<String> exportQrCodees(
            Long userId,
            EquipmentExportInfo info,
            HttpServletResponse response) ;

    /**
     * 查询设备的技术参数值，如果参数值库中没有显示模板中技术参数
     *
     * @param searchDto
     * @return
     */
    List<TechParamValue> queryTechParam(EquipmentSearchDto searchDto);


    /**
     * 插入更新技术参数
     *
     * @param list
     */
    void writeTechParamValue(List<TechParamValue> list);


    /**
     * 查看设备运行参数分组
     *
     * @param searchDto
     * @return
     */
    List<RunningParamGroup> queryEquipmentRunningParam(EquipmentSearchDto searchDto);





    /**
     * 通过模板信息和设备信息查询模板名称
     *
     * @param dtoList
     * @return
     */
    List<BaseWithTemplate> queryTemplateName(BaseVo dtoList);


    /**
     * 获取测点信息
     *
     * @param groupId
     * @param measuredby
     * @return
     */
    List<RunningParamValue> dataServiceParam(Long groupId, List<MeasuredbyVo> measuredby);


    /**
     * 获取节点以及子节点的信息
     * @param request
     * @param userId
     * @return
     */
    ResultWithTotal<List<Map<String, Object>>> getNodeData(DeviceInfoRequest request, Long userId);

    /**
     * 获取节点以及子节点的信息,传参由前端控制
     *
     * @param deviceInfoRequestControlByFront
     * @param userId
     * @return 返回对应的节点树信息
     */
    ResultWithTotal<List<Map<String, Object>>> getNodeData(DeviceInfoRequestControlByFront deviceInfoRequestControlByFront, Long userId);


    /**判断是否是配电柜返回所属抽屉柜信息
     * @param baseVo
     * @return
     */
    List<BaseVo> queryBaseVo(BaseVo baseVo);


    /**
     * 导出技术参数
     * @param baseVo
     * @param response
     */
    void exportTechParamByChoose(BaseVo baseVo, HttpServletResponse response);


    /**
     * 导入技术参数
     * @param file
     */
    void importTechParams(MultipartFile file) throws IOException;
}
