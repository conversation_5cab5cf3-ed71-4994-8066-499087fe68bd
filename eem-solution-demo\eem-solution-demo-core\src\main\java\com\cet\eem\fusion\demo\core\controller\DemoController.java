package com.cet.eem.fusion.demo.core.controller;

import com.cet.eem.fusion.common.entity.Result;
import com.cet.eem.fusion.demo.core.entity.dto.DemoDTO;
import com.cet.eem.fusion.demo.core.entity.vo.DemoVO;
import com.cet.eem.fusion.demo.core.service.DemoService;
import com.cet.eem.solution.common.def.common.PluginInfoDef;
import com.cet.electric.commons.ApiResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>  (2025/7/17 15:40)
 */
@Validated
@RestController
@Api(value = PluginInfoDef.Demo.INTERFACE_PREFIX + "/v1/demo", tags = "样例接口")
@RequestMapping(value = PluginInfoDef.Demo.PLUGIN_NAME_PREFIX + "/v1/demo")
public class DemoController {

    @Resource
    private DemoService demoService;

    @ApiOperation("样例方法")
    @PostMapping("/demoMethod")
    public ApiResult<DemoVO> demoMethod(@RequestBody DemoDTO demoDTO) {
        return Result.ok(demoService.demoMethod(demoDTO));
    }
}
