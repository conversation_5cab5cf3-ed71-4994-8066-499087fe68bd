package com.cet.eem.fusion.maintenance.core.service.inspection;

import com.cet.eem.bll.maintenance.model.inspector.*;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.common.model.auth.user.RoleVo;
import com.cet.eem.common.model.auth.user.UserGroupVo;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.util.List;

public interface InspectorService {

    /**
     * 查询巡检人员
     *
     * @param queryInspectorRequest
     * @return
     */
    ResultWithTotal<List<UserVo>> queryMaintenanceUser(QueryInspectorRequest queryInspectorRequest);

    /**
     * 巡检人员注册
     * @deprecated 废弃
     * @param inspectorRegistryRequest 巡检人员注册请求
     * @return
     */
    @Deprecated
    UserVo inspectorRegistry(InspectorRegistryRequest inspectorRegistryRequest);

    /**
     * 创建巡检用户
     *
     * @param inspectorRegistryRequest
     * @return
     */
    UserVo inspectorRegistrySecurity(InspectorRegistryRequest inspectorRegistryRequest);

    /**
     * 巡检人员修改密码
     * @deprecated 废弃
     * @param inspectorPasswordChangeRequest
     */
    @Deprecated
    void inspectorPasswordChange(InspectorPasswordChangeRequest inspectorPasswordChangeRequest);

    /**
     * 巡检人员修改密码
     *
     * @param inspectorPasswordChangeRequest
     */
    void inspectorPasswordChangeSecurity(InspectorPasswordChangeRequest inspectorPasswordChangeRequest);

    /**
     * 修改确认用密码
     *
     * @param inspectorPasswordChangeRequest
     */
    void checkPasswordChange(InspectorPasswordChangeRequest inspectorPasswordChangeRequest);

    /**
     * 修改确认用密码，加密后
     *
     * @param inspectorPasswordChangeRequest
     */
    void checkPasswordChangeSecurity(InspectorPasswordChangeRequest inspectorPasswordChangeRequest);


    /**
     * 查询巡检人员角色信息
     *
     * @param tenantId
     */
    QueryInspectorRolesResult queryInspectorRoleInfo(QueryInspectorRolesRequest tenantId);

    /**
     * 查询巡检班组
     *
     * @param tenantId
     * @return
     */
    List<UserGroupVo> queryInspectorTeam(Long tenantId);

    /**
     * 查询巡检班组
     *
     * @param tenantId
     * @return
     */
    List<UserGroupVo> queryInspectorTeamWithoutUser(Long tenantId);

    /**
     * 查询巡检用户
     *
     * @param groupId 用户组
     * @return 巡检用户信息
     */
    List<UserVo> queryMaintenanceUser(Long groupId);

    /**
     * 根据用户信息，判断指定用户是否为运维用户
     *
     * @param userVo
     * @return
     */
    boolean isMaintenanceUser(UserVo userVo);

    /**
     * 判断用户是否为巡检用户
     *
     * @param userVo
     * @return
     */
    boolean isInspectUser(@NotNull UserVo userVo);

    /**
     * 判断指定用户是否为运维用户
     *
     * @return
     */
    boolean isInspectorUserByUserId();

    /**
     * 新增运维班组
     *
     * @param addInspectorTeamRequest
     */
    Long addInspectorTeam(AddInspectorTeamRequest addInspectorTeamRequest);

    /**
     * 编辑运维班组
     *
     * @param editInspectorTeamRequest
     */
    void editInspectorTeam(EditInspectorTeamRequest editInspectorTeamRequest);


    /**
     * 查询巡检角色
     *
     * @param tenantId
     * @return
     */
    List<RoleVo> queryInspectorRoles(Long tenantId);

    /**
     * 编辑巡检人员
     *
     * @param editInspectorUserRequest
     * @return
     */
    UserVo editInspectorUser(EditInspectorUserRequest editInspectorUserRequest);

    /**
     * 删除巡检人员
     */
    void deleteInspectorUser(Long id, String name, Long tenantId);

    /**
     * 删除用户组
     *
     * @param id
     * @param name
     * @param tenantId
     */
    void deleteInspectorTeam(Long id, String name, Long tenantId);

    /**
     * 查询用户同组成员
     *
     * @return
     */
    List<UserVo> querySameGroupMember();

    /**
     * 校验巡检用户的口令，非登录用的密码
     *
     * @param inspectorPasswordChangeRequest
     * @return
     */
    boolean checkPassword(InspectorPasswordChangeRequest inspectorPasswordChangeRequest);

    /**
     * 校验巡检用户的口令，非登录用的密码，MD5加密之后的
     *
     * @param inspectorPasswordChangeRequest
     * @return
     */
    boolean checkPasswordSecurity(InspectorPasswordCheckRequest inspectorPasswordChangeRequest);

    /**
     * 下载巡检人员导入模板
     * @param response
     */
    void downloadTemplate(HttpServletResponse response);

    /**
     * 导入巡检人员数据
     * @param file
     * @param tenantId
     */
    void importItem(MultipartFile file,Long tenantId) throws IOException;
}
