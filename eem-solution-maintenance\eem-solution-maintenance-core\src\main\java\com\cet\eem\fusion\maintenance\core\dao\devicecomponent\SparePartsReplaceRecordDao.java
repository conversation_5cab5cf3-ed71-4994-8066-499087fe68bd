package com.cet.eem.fusion.maintenance.core.dao.devicecomponent;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SparePartsReplaceRecord;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * @ClassName : SparePartsReplaceRecordDao
 * @Description : 备件更换记录
 * <AUTHOR> jiangzixuan
 * @Date: 2021-05-17 08:45
 */
public interface SparePartsReplaceRecordDao extends BaseModelDao<SparePartsReplaceRecord> {
    /**
     * 根据时间，备件系统id，备件分类类型进行查询备件更换记录
     * @param st
     * @param et
     * @param ids
     * @param page
     * @param deviceSystemId
     * @param objectLabels
     * @return
     */
    ResultWithTotal<List<SparePartsReplaceRecord>> queryByTimeAndId(Long st, Long et, Set<Long> ids, Page page,List<Long> deviceSystemId,List<String> objectLabels);

    /**
     * 根据时间，备件id来查询备件更换记录
     * @param st
     * @param et
     * @param ids
     * @param page
     * @return
     */
    ResultWithTotal<List<SparePartsReplaceRecord>> querySparePartsReplace(Long st, Long et, Set<Long> ids, Page page);

    /**
     * 根据设备信息来查询备件更换记录
     * @param objectLabel
     * @param objectId
     * @return
     */
    List<SparePartsReplaceRecord> queryByDevice(String objectLabel, Long objectId);

    /**
     * 根据工单id查询备件更换记录
     *
     * @param workOrderId
     * @return
     */
    List<SparePartsReplaceRecord> queryByWorkOderId(Collection<Long> workOrderId);
    ResultWithTotal<List<SparePartsReplaceRecord>> querySparePartsReplaceByDevice(Long st, Long et, String objectLabel, Page page);
}