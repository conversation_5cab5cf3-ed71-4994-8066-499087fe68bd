package com.cet.eem.solution.common.entity.vo.peccore;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/23 20:05
 */
@Data
public class DeviceInfo {

    @ApiModelProperty("节点类型")
    private String nodeType;

    @ApiModelProperty("节点id")
    private Long nodeId;

    @ApiModelProperty("节点名称")
    private String nodeName;

    @ApiModelProperty("父节点类型")
    private String parentNodeType;

    @ApiModelProperty("父节点id")
    private Long parentNodeId;

    private String typeId;

    private String typeName;

    private String ip;

    private String domainName;

    private String port;

    private String communicationId;

    private String measureNodeCount;
}

