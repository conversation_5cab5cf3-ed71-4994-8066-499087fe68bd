﻿package com.cet.eem.fusion.refrigeration.core.dao;

import com.cet.eem.bll.common.model.domain.subject.energysaving.ColdActual;
import com.cet.eem.dao.BaseModelDao;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/22
 */
public interface ColdActualDao extends BaseModelDao<ColdActual> {
    /**
     * 写入制冷实际数据
     *
     * @param coldActualDataList 需要写入的数据
     */
    void writeColdActualDataList(List<ColdActual> coldActualDataList);

    /**
     * 查询制冷实际数据
     *
     * @param coldLoadTypes 负载端类型
     * @param dataTypes     数据类型
     * @param logTimes      时间
     * @param roomIds       系统id
     * @return 查询到的制冷数据
     */
    List<ColdActual> queryColdActualData(Collection<Integer> coldLoadTypes, Collection<Integer> dataTypes, Collection<Long> logTimes,
                                         Collection<Long> roomIds);

    /**
     * 查询制冷实际数据
     * @param coldLoadTypes 负载端类型
     * @param dataTypes  数据类型
     * @param st 时间
     * @param et 时间
     * @param roomId 系统id
     * @return 查询到的制冷数据
     */
    List<ColdActual> queryColdActualData(Collection<Integer> coldLoadTypes, Collection<Integer> dataTypes, LocalDateTime st,
                                         LocalDateTime et,
                                         Long roomId);
    /**
     * 查询制冷实际数据
     * @param coldLoadTypes 负载端类型
     * @param dataTypes  数据类型
     * @param st 时间
     * @param et 时间
     * @param roomId 系统id
     * @return 查询到的制冷数据
     */
    List<ColdActual> queryColdActualData(Collection<Integer> coldLoadTypes, Collection<Integer> dataTypes, LocalDateTime st,
                                         LocalDateTime et,
                                         Collection<Long> roomId);
}

