package com.cet.eem.fusion.maintenance.core.service;

import com.cet.eem.bll.maintenance.model.workorder.*;
import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.bll.maintenance.model.workorder.inspection.WorkOrderBatchReviewVo;
import com.cet.eem.bll.maintenance.model.workorder.inspection.WorkOrderReviewVo;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.workflow.common.model.node.config.UserTaskConfig;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/31
 */
public interface WorkOrderService {
    /**
     * 查询工单
     *
     * @param searchVo 查询条件
     * @return 工单
     */
    ResultWithTotal<List<InspectionWorkOrderDto>> queryWorkOrderList(WorkOrderSearchVo searchVo);

    /**
     * 查询工单数量
     *
     * @param dto 查询条件
     * @return 工单数量
     */
    List<WOCountByTaskTypeVo> queryWorkOrderCount(WorkOrderSearchVo dto);

    /**
     * 查询允许操作的工单数量
     *
     * @param dto
     * @return
     */
    List<WOCountByTaskTypeVo> queryRuntimeWorkOrderCount(WorkOrderSearchVo dto);

    /**
     * 根据工单查询流程图
     *
     * @param code 工单号
     */
    void getProcessDiagram(String code, Boolean isLightStyle) throws Exception;

    /**
     * 查询正在运行的工单
     *
     * @param code 工单code
     * @return 工单
     */
    InspectionWorkOrderDto queryRuntimeWorkOrder(String code);

    /**
     * 查询工单
     *
     * @param code 工单code
     * @return 工单
     */
    InspectionWorkOrderDto queryWorkOrder(String code);

    /**
     * 审核工单
     *
     * @param workOrderReviewVo 工单审核信息
     */
    void reviewForm(WorkOrderReviewVo workOrderReviewVo);

    /**
     * 批量审核工单
     *
     * @param workOrderReviewVo 工单审核信息
     */
    void reviewFormBatch(WorkOrderBatchReviewVo workOrderReviewVo);

    /**
     * 保存审核工单信息
     *
     * @param workOrderReviewVo 工单审核信息
     */
    void saveReviewForm(WorkOrderReviewVo workOrderReviewVo);

    /**
     * 查询工单确认暂存信息
     *
     * @param code 工单号
     * @return 工单暂存的确认信息
     */
    WorkOrderCheckInfoVo queryWorkOrderCheckInfo(String code);

    /**
     * 校验任务节点是否匹配
     *
     * @param code
     * @param nodeLabel
     */
    void checkTaskNodes(String code, String nodeLabel);

    /**
     * 查询工单所处节点
     *
     * @param code
     * @return
     */
    UserTaskConfig queryTaskConfig(String code);

    /**
     * 公共提交表单数据
     *
     * @param submitParam
     */
    void submitFormData(WorkOrderFormSubmitParam submitParam);

    /**
     * 公共批量提交表单数据
     *
     * @param submitParam
     */
    void submitFormDataBatch(WorkOrderFormBatchSubmitParam submitParam);

    /**
     * 校验是否具备操作权限
     *
     * @param code
     * @return
     */
    boolean checkAuth(String code);

}
