package com.cet.eem.fusion.maintenance.core.entity.devicemanage.template;

import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.maintenance.core.entity.po.MeasureNode;
import com.cet.eem.fusion.maintenance.core.entity.po.RunningParamNode;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-14
 */
@Data
public class NodeWithMeasureNode extends RunningParamNode {
    @JsonProperty(ModelLabelDef.MEASURE_NODE+"_model")
    private List<MeasureNode> params;
}
