﻿package com.cet.eem.fusion.refrigeration.core.impl;

import com.cet.eem.bll.common.dao.node.EnergySupplyDao;
import com.cet.eem.bll.common.dao.node.NodeDao;
import com.cet.eem.bll.common.dao.pipenetwork.PipeNetworkConnectionModelDao;
import com.cet.eem.bll.common.model.domain.object.entitymap.PipeNetworkConnectionModel;
import com.cet.eem.bll.common.model.domain.subject.energy.EnergyConsumption;
import com.cet.eem.bll.common.model.domain.subject.energysaving.ColdPredict;
import com.cet.eem.bll.common.model.node.relations.EnergySupplyToPo;
import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.bll.energy.dao.consumption.EnergyConsumptionDao;
import com.cet.eem.fusion.refrigeration.core.dao.aioptimization.AiStartStopStrategyDao;
import com.cet.eem.fusion.refrigeration.core.dao.aioptimization.StrategyObjectMapDao;
import com.cet.eem.fusion.refrigeration.core.dao.aioptimization.WorkSectionDao;
import com.cet.eem.fusion.refrigeration.core.dao.weather.*;
import com.cet.eem.fusion.refrigeration.core.model.aiconsumption.AiConsumptionSearchVo;
import com.cet.eem.fusion.refrigeration.core.model.aiconsumption.AiTbHbEnergyVo;
import com.cet.eem.fusion.refrigeration.core.model.aiconsumption.AnalysisTypeDef;
import com.cet.eem.fusion.refrigeration.core.model.aioptimization.*;
import com.cet.eem.fusion.refrigeration.core.model.config.PumpFunctionType;
import com.cet.eem.fusion.refrigeration.core.model.config.system.RefrigeratingSystem;
import com.cet.eem.fusion.refrigeration.core.model.def.*;
import com.cet.eem.fusion.refrigeration.core.model.weather.*;
import com.cet.eem.fusion.refrigeration.core.service.aioptimization.AIEnergyConsumptionAndCopService;
import com.cet.eem.fusion.refrigeration.core.service.aioptimization.AiOptimizationService;
import com.cet.eem.fusion.refrigeration.core.service.trend.ModelConfigurationService;
import com.cet.eem.fusion.refrigeration.core.service.trend.OperationTrendService;
import com.cet.eem.fusion.refrigeration.core.service.weather.WeatherCrawlingDataService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.constant.EnergyTypeDef;
import com.cet.eem.common.constant.EnumDataTypeId;
import com.cet.eem.common.constant.EnumOperationType;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.ResultWithTotal;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.model.realtime.RealTimeValue;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.base.ConditionBlock;
import com.cet.eem.model.base.ConditionBlockCompose;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.base.SingleModelConditionDTO;
import com.cet.eem.model.model.AbstractModelEntity;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import com.cet.eem.quantity.dao.QuantityAggregationDataDao;
import com.cet.eem.quantity.dao.QuantityObjectDao;
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
import com.cet.eem.quantity.model.quantity.QuantitySearchVo;
import com.cet.eem.quantity.service.QuantityManageService;
import com.cet.eem.weather.model.Weather;
import com.ibm.icu.text.DecimalFormat;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : AiOptimizationServiceImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-04-18 13:51
 */
@Service
public class AiOptimizationServiceImpl implements AiOptimizationService {


    @Autowired
    WeatherCrawlingDataService weatherCrawlingDataService;
    @Autowired
    OperationTrendService operationTrendService;
    @Autowired
    ColdPredictDao coldPredictDao;
    @Autowired
    WeatherPredictDao weatherPredictDao;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    PipeNetworkConnectionModelDao connectionModelDao;
    @Autowired
    QuantityManageService quantityManageService;
    @Autowired
    AiStartStopStrategyDao aiStartStopStrategyDao;
    @Autowired
    StrategyObjectMapDao strategyObjectMapDao;
    @Autowired
    NodeDao nodeDao;
    @Autowired
    RefrigeratingRunningStrategyDao refrigeratingRunningStrategyDao;
    @Autowired
    EnergySupplyDao energySupplyDao;
    @Autowired
    ModelConfigurationService modelConfigurationService;
    @Autowired
    WorkSectionDao workSectionDao;
    @Autowired
    QuantityAggregationDataDao quantityAggregationDataDao;
    @Autowired
    QuantityObjectDao quantityObjectDao;
    @Autowired
    EnergyConsumptionDao consumptionDao;
    @Autowired
    RefrigeratingSystemDao refrigeratingSystemDao;
    public static final Integer SCREW = 1;
    public static final Integer CENTRIFUGAL = 2;
    public static final String SCREW_MACHINE = "螺杆式";
    public static final String CENTRIFUGAL_MACHINE = "离心式";
    public static final String START = "开启";
    public static final String STOP = "关闭";
    public static final String OPTIMIZATION = "冷冻水出水温度设定";
    public static final String TEMP = "℃";
    public static final Integer QUERY_TYPE = 3;
    public static final Double UP = 1.0;
    //gj转kw
    public static final Double UNIT = 0.0036D;
    public static final Double RATE = 0.125;
    @Autowired
    DeviceChainDao deviceChainDao;
    @Autowired
    AIEnergyConsumptionAndCopService aiEnergyConsumptionAndCopService;

    /**
     * 第一条数据来源于实时值，后面三条来源于华科预测值
     * 显示的间隔是15分钟的，预测的结果--暂定1小时的整点数据，需要计算--等差数列的逻辑
     * --其中系统冷负荷和系统功率需要修改成末端加管损，冷机功率
     *
     * @param roomId--房间id
     * @return
     */
    @Override
    public List<WeatherAndLoadPredictVo> queryWeatherAndLoadPredictVo(Long roomId) {
        QueryParam queryParam = new QueryParam(roomId, NodeLabelDef.ROOM);
        //先计算现在时间的
        CurrentWeather currentWeather = weatherCrawlingDataService.queryWeatherCurrentData(queryParam,
                GlobalInfoUtils.getProjectId());
        //当前时间冷负荷
        OperationTrendVo operationTrendVo = operationTrendService.querySystemCoolingWaterSingData(queryParam);
        List<LocalDateTime> nextFIfTime = createNextFIfTime();
        LocalDateTime st = TimeUtil.getFirstTimeOfHour(LocalDateTime.now());
        LocalDateTime et = TimeUtil.addDateTimeByCycle(st, AggregationCycle.ONE_HOUR, 1);
        LocalDateTime endTime = TimeUtil.addDateTimeByCycle(et, AggregationCycle.ONE_HOUR, 1);
        //末端冷量和管损预测值--修改成，总冷负荷预测和功率预测值
        List<ColdPredict> predicts = coldPredictDao.
                queryColdPredictData(Collections.singletonList(ColdLoadType.TOTAL),
                        Arrays.asList(PredictDataType.COLD_LOAD, PredictDataType.POWER),
                        st, endTime, roomId);
        List<WeatherAndLoadPredictVo> result = new ArrayList<>();
        WeatherAndLoadPredictVo current = new WeatherAndLoadPredictVo(System.currentTimeMillis(), currentWeather.getTempAvg(), currentWeather.getHumidity(),
                operationTrendVo.getCoolingLoad(), operationTrendVo.getTotalSystemPower(), operationTrendVo.getDifference());
        result.add(current);
        //天气预测数据-小时数据算15分钟数据
        List<Weather> weathers = operationTrendService.queryWeather(st, endTime, AggregationCycle.FIFTEEN_MINUTES, GlobalInfoUtils.getProjectId());
        result.addAll(assemblePredictWithTime(weathers, predicts, nextFIfTime));
        return result;
    }

    private List<WeatherAndLoadPredictVo> assemblePredictWithTime(List<Weather> weathers, List<ColdPredict> predicts, List<LocalDateTime> nextFIfTime) {
        List<WeatherAndLoadPredictVo> result = new ArrayList<>();
        for (LocalDateTime time : nextFIfTime) {
            WeatherAndLoadPredictVo predictVo = new WeatherAndLoadPredictVo();
            ColdPredict coldPredict1 = filterData(time, ColdLoadType.TOTAL, PredictDataType.COLD_LOAD, predicts);
            ColdPredict coldPredict3 = filterData(time, ColdLoadType.TOTAL, PredictDataType.POWER, predicts);
            Weather weatherPredict1 = weathers.stream().filter(weatherPredict -> Objects.equals(TimeUtil.timestamp2LocalDateTime(weatherPredict.getLogTime()), time))
                    .findAny().orElse(new Weather());
            predictVo.setLogTime(TimeUtil.localDateTime2timestamp(time));
            predictVo.setHumidity(weatherPredict1.getHumidity());
            predictVo.setTemp(weatherPredict1.getTempAvg());
            predictVo.setColdLoad(coldPredict1.getValue());
            predictVo.setElectricPower(coldPredict3.getValue());
            predictVo.setCopData(CommonUtils.calcDouble(predictVo.getColdLoad(), predictVo.getElectricPower(), EnumOperationType.DIVISION.getId()));
            result.add(predictVo);
        }
        return result;
    }


    private ColdPredict filterData(LocalDateTime time, Integer coldType, Integer predictType, List<ColdPredict> predicts) {
        return predicts.stream().filter(coldActual ->
                Objects.equals(coldActual.getColdLoadType(), coldType)
                        && Objects.equals(coldActual.getPredictDataType(), predictType)
                        && Objects.equals(time, coldActual.getLogTime())).findAny().orElse(new ColdPredict());
    }


    private Double addTwoValue(Double endActual, Double totalActual) {
        if (Objects.isNull(endActual)) {
            return totalActual;
        }
        if (Objects.isNull(totalActual)) {
            return endActual;
        }

        return totalActual + endActual;
    }

    /**
     * 查询冷机的xx温度和冷站--总管的xx温度
     *
     * @param roomId
     * @return
     */
    @Override
    public WaterTempVo queryWaterTempVo(Long roomId) {
        WaterTempVo waterTempVo = new WaterTempVo();
        QueryParam queryParam = new QueryParam(roomId, NodeLabelDef.ROOM);
        List<BaseVo> baseVos = queryEquipmentGroupDevice(queryParam);
        //过滤出冷水主机
        List<BaseVo> coldDevice = baseVos.stream().filter(baseVo -> Objects.equals(baseVo.getModelLabel(), NodeLabelDef.COLD_WATER_MAINENGINE))
                .collect(Collectors.toList());
        List<PipeNetworkConnectionModel> connectionModels = connectionModelDao.getTopOrDownBatchWithNodes(coldDevice, false, GlobalInfoUtils.getProjectId());
        List<BaseVo> pipeLines = connectionModels.stream().filter(model -> Objects.equals(model.getOutflowLabel(), NodeLabelDef.PIPELINE))
                .map(model -> new BaseVo(model.getOutflowId(), model.getOutflowLabel()))
                .collect(Collectors.toList());
        //冷冻水供回水压差,末端供水和回水
        getDiffData(pipeLines, queryParam, waterTempVo);
        return waterTempVo;
    }

    /**
     * @param roomId
     * @param refrigeratingSystemId
     * @return
     * @deprecated
     */
    @Deprecated
    @Override
    public List<AiStartStopStrategyVo> queryAiStartStopStrategyVo(Long roomId, Long refrigeratingSystemId) {
        List<AiStartStopStrategy> strategies = aiStartStopStrategyDao.queryAiStartStopStrategy(refrigeratingSystemId,
                Arrays.asList(StrategyTypeDef.start, StrategyTypeDef.stop));
        List<Long> ids = strategies.stream().map(AiStartStopStrategy::getId).collect(Collectors.toList());
        List<StrategyObjectMap> objectMaps = strategyObjectMapDao.queryStrategyObjectMap(ids);
        List<BaseVo> nodes = objectMaps.stream().map(strategyObjectMap -> new BaseVo(strategyObjectMap.getObjectId(), strategyObjectMap.getObjectLabel()))
                .distinct().collect(Collectors.toList());
        List<NodeWithPumpType> nodeWithPumpTypes = nodeDao.queryNodes(nodes, NodeWithPumpType.class);
        //分清冷却泵和冷冻泵
        AiStartStopStrategyVo stopStrategyVo = assembleStrategyVo(nodeWithPumpTypes, objectMaps, strategies, StrategyTypeDef.start);
        AiStartStopStrategyVo stopStrategyVo1 = assembleStrategyVo(nodeWithPumpTypes, objectMaps, strategies, StrategyTypeDef.stop);
        return Arrays.asList(stopStrategyVo, stopStrategyVo1);
    }

    /**
     * 根据房间查询底下设备，查询相关测点--分组返回
     *
     * @param roomId
     * @return
     */
    @Override
    public List<DeviceCurrentStatus> queryDeviceCurrentStatus(Long roomId) {
        List<PumpVo> pumpVos = modelConfigurationService.queryChainDevice(roomId);
        List<BaseVo> machines = pumpVos.stream().filter(pumpVo -> Objects.equals(pumpVo.getModelLabel(), NodeLabelDef.COLD_WATER_MAINENGINE))
                .map(pumpVo -> new BaseVo(pumpVo.getId(), pumpVo.getModelLabel())).collect(Collectors.toList());
        List<EnergySupplyToPo> energySupplyToPos = energySupplyDao.queryEnergySupplyTo(machines, System.currentTimeMillis(), EnergySupplyToPo.class);
        List<EnergySupplyToPo> supplyToPos = energySupplyToPos.stream().filter(energySupplyToPo -> Objects.equals(NodeLabelDef.LINE_SEGMENT_WITH_SWITCH, energySupplyToPo.getObjectlabel()))
                .collect(Collectors.toList());
        //和采集设备关联的节点
        List<BaseVo> allNodes = pumpVos.stream()
                .map(pumpVo -> new BaseVo(pumpVo.getId(), pumpVo.getModelLabel())).collect(Collectors.toList());
        allNodes.addAll(supplyToPos.stream().map(energySupplyToPo -> new BaseVo(energySupplyToPo.getObjectid(), energySupplyToPo.getObjectlabel()))
                .collect(Collectors.toList()));
        Map<Integer, List<RealTimeValue>> statusMap = quantityManageService.queryRealTimeBath(
                createQuantityDataBatchSearchVo(allNodes, new QueryParam(),
                        Arrays.asList(QuantityDef.getDeviceStatus(), QuantityDef.getMainPowerQuantitySetting(),
                                QuantityDef.getDeviceFrequency(), QuantityDef.getFreezingWaterForSupplyQuantitySetting(),
                                QuantityDef.getFreezingWaterForReturnQuantitySetting(), QuantityDef.getFreezingDeviceFrequency(),
                                QuantityDef.getCoolingWaterForReturnQuantitySetting(), QuantityDef.getCoolingWaterForSupplyQuantitySetting(),
                                QuantityDef.getDeviceTowerEnergy())));
        //房间下的全部节点
        List<BaseVo> nodes = pumpVos.stream()
                .map(pumpVo -> new BaseVo(pumpVo.getId(), pumpVo.getModelLabel(), pumpVo.getName())).collect(Collectors.toList());
        List<DeviceCurrentStatus> deviceCurrentStatuses = assembleStatus(statusMap, nodes);
        //添加设备负载率
        Map<BaseVo, List<EnergySupplyToPo>> baseVoListMap = supplyToPos.stream()
                .collect(Collectors.groupingBy(
                        energySupplyToPo -> new BaseVo(energySupplyToPo.getSupplytoid(), energySupplyToPo.getSupplytolabel())));
        //频率
        assembleFrequency(statusMap, deviceCurrentStatuses, pumpVos, baseVoListMap);
        return deviceCurrentStatuses;
    }

    /**
     * 查询冷机的工作范围，和相关的实时数据
     *
     * @param roomId
     * @return
     */
    @Override
    public WorkSectionListVo queryDeviceWorkSection(Long roomId) {
        List<PumpVo> baseVos = queryNodesByParent(NodeLabelDef.COLD_WATER_MAINENGINE, roomId, NodeLabelDef.ROOM);
        WorkSectionListVo result = new WorkSectionListVo();
        if (CollectionUtils.isEmpty(baseVos)) {
            result.setColdWater(Collections.emptyList());
            result.setMachine(Collections.emptyList());
            result.setFreezingWater(Collections.emptyList());
            return result;
        }
        List<BaseVo> nodeList = baseVos.stream().map(pumpVo -> new BaseVo(pumpVo.getId(), pumpVo.getModelLabel())).distinct().collect(Collectors.toList());
        //查询 根据冷机id查
        List<WorkSection> workSections = workSectionDao.queryWorkSections(nodeList);
        //查询冷机关联的一段线
        List<BaseVo> collect = baseVos.stream().map(pumpVo -> new BaseVo(pumpVo.getId(), pumpVo.getModelLabel())).collect(Collectors.toList());
        List<EnergySupplyToPo> energySupplyToPos = energySupplyDao.queryEnergySupplyTo(collect, System.currentTimeMillis(), EnergySupplyToPo.class);
        List<EnergySupplyToPo> supplyToPos = energySupplyToPos.stream().filter(energySupplyToPo -> Objects.equals(NodeLabelDef.LINE_SEGMENT_WITH_SWITCH, energySupplyToPo.getObjectlabel()))
                .collect(Collectors.toList());
        List<BaseVo> nodes = supplyToPos.stream().map(energySupplyToPo -> new BaseVo(energySupplyToPo.getObjectid(), energySupplyToPo.getObjectlabel()))
                .collect(Collectors.toList());

        collect.addAll(nodes);
        //查询物理量数据
        Map<Integer, List<RealTimeValue>> statusMap = quantityManageService.queryRealTimeBath(
                createQuantityDataBatchSearchVo(collect, new QueryParam(),
                        Arrays.asList(QuantityDef.getMainPowerQuantitySetting(),
                                QuantityDef.getFreezingWaterForSupplyQuantitySetting(),
                                QuantityDef.getFreezingWaterForReturnQuantitySetting(),
                                QuantityDef.getCoolingWaterForReturnQuantitySetting(), QuantityDef.getCoolingWaterForSupplyQuantitySetting()
                        )));
        Map<BaseVo, List<EnergySupplyToPo>> baseVoListMap = supplyToPos.stream()
                .collect(Collectors.groupingBy(
                        energySupplyToPo -> new BaseVo(energySupplyToPo.getSupplytoid(), energySupplyToPo.getSupplytolabel())));
        //负载
        List<WorkSectionVo> workSectionVos = assembleLoadAndTemp(statusMap, baseVos, baseVoListMap, workSections);
        //温差 |冷冻水供水温度（6007206） - 冷冻水回水温度（6007207）| 代替
        List<RealTimeValue> realTimeValues1 = statusMap.get(QuantityDef.getFreezingWaterForSupplyQuantitySetting().getId());
        List<RealTimeValue> realTimeValues2 = statusMap.get(QuantityDef.getFreezingWaterForReturnQuantitySetting().getId());
        //|6007204 - 6007203|
        List<RealTimeValue> realTimeValues3 = statusMap.get(QuantityDef.getCoolingWaterForSupplyQuantitySetting().getId());
        List<RealTimeValue> realTimeValues4 = statusMap.get(QuantityDef.getCoolingWaterForReturnQuantitySetting().getId());
        //拼接温度
        List<WorkSectionVo> workSectionVos1 = assembleTemp(realTimeValues1, realTimeValues2, baseVos, workSections, SectionDataType.CHILLED_WATER_TEMP);
        List<WorkSectionVo> workSectionVos2 = assembleTemp(realTimeValues3, realTimeValues4, baseVos, workSections, SectionDataType.COOLING_WATER_TEMP);
        result.setColdWater(workSectionVos2);
        result.setMachine(workSectionVos);
        result.setFreezingWater(workSectionVos1);
        return result;
    }

    /**
     * 需要查出冷量和电量的日数据，以及同环比的月数据计算出结果，其中还需要华科预测的冷负荷数据
     * 今日实际COPE改为今日冷机用电量，AI优化后预计今日COPE改为AI优化后预计今日冷机用电量，今日冷机用电量改成自己算的
     * 能效提升率改为节能率，
     *
     * @param roomId
     * @return
     */
    @Override
    public EnergySavingEffectVo queryEnergySavingEffectVo(Long roomId) {
        EnergySavingEffectVo savingEffectVo = new EnergySavingEffectVo();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime thisDay = TimeUtil.getFirstTimeOfDay(now);
        //查询冷机
        List<BaseVo> baseVos = operationTrendService.queryColdWaterMainEngine(new QueryParam(roomId, NodeLabelDef.ROOM), NodeLabelDef.COLD_WATER_MAINENGINE);
        //查询日数据
        List<EnergyConsumption> consumptionsOfDay = consumptionDao.queryEnergyConsumption(baseVos,
                TimeUtil.localDateTime2timestamp(thisDay),
                TimeUtil.addDateTimeByCycle(TimeUtil.localDateTime2timestamp(thisDay), AggregationCycle.ONE_DAY, 1),
                AggregationCycle.ONE_DAY, Collections.singletonList(EnergyTypeDef.ELECTRIC));

        //本日实际cop--改成实际电量
        if (CollectionUtils.isNotEmpty(consumptionsOfDay)) {
            savingEffectVo.setRealTimeValue(consumptionsOfDay.stream().filter(energyConsumption -> Objects.nonNull(energyConsumption.getUsage()))
                    .mapToDouble(EnergyConsumption::getUsage).sum());
        }
        //拼接优化后cop和能效提升率
        assembleEnergySavingEffect(thisDay, roomId, savingEffectVo);
        //拼接本月，和同环比月份的数据
        assembleCopData(getCopTbHb(roomId), savingEffectVo);
        return savingEffectVo;
    }

    private List<AiTbHbEnergyVo> getCopTbHb(Long roomId) {
        AiConsumptionSearchVo searchVo = new AiConsumptionSearchVo();
        searchVo.setAnalysisIndicatorType(AnalysisTypeDef.cop);
        searchVo.setCycle(AggregationCycle.ONE_MONTH);
        searchVo.setStartTime(TimeUtil.timestamp2LocalDateTime(TimeUtil.getFirstDayOfThisMonth(System.currentTimeMillis())));
        searchVo.setEndTime(TimeUtil.addDateTimeByCycle(searchVo.getStartTime(), AggregationCycle.ONE_MONTH, 1));
        searchVo.setProjectId(GlobalInfoUtils.getProjectId());
        searchVo.setQueryType(QUERY_TYPE);
        searchVo.setNodes(Collections.singletonList(new AbstractModelEntity(roomId, NodeLabelDef.ROOM)));
        return aiEnergyConsumptionAndCopService.getTbHbEnergy(searchVo);
    }

    private void assembleCopData(List<AiTbHbEnergyVo> aiTbHbEnergyVos, EnergySavingEffectVo savingEffectVo) {
        if (CollectionUtils.isEmpty(aiTbHbEnergyVos)) {
            return;
        }
        AiTbHbEnergyVo aiTbHbEnergyVo = aiTbHbEnergyVos.get(0);
        savingEffectVo.setMonthValue(aiTbHbEnergyVo.getValue());
        savingEffectVo.setTbValue(aiTbHbEnergyVo.getTbValue());
        savingEffectVo.setHbValue(aiTbHbEnergyVo.getHbValue());
        savingEffectVo.setHbPercent(CommonUtils.calcDouble(savingEffectVo.getMonthValue(), savingEffectVo.getHbValue(), EnumOperationType.RATE.getId()));
        savingEffectVo.setTbPercent(CommonUtils.calcDouble(savingEffectVo.getMonthValue(), savingEffectVo.getTbValue(), EnumOperationType.RATE.getId()));
    }

    private void assembleCopData(List<EnergyConsumption> consumptions, EnergySavingEffectVo savingEffectVo,
                                 LocalDateTime thisMonth, LocalDateTime hbTime, LocalDateTime tbTime) {
        //本月
        EnergyConsumption data = consumptions.stream().filter(quantityAggregationData1 -> Objects.equals(quantityAggregationData1.getLogtime(), TimeUtil.localDateTime2timestamp(thisMonth))
                && Objects.nonNull(quantityAggregationData1.getUsage()) && Objects.equals(quantityAggregationData1.getEnergytype(), EnergyTypeDef.ELECTRIC))
                .findAny().orElse(new EnergyConsumption());
        EnergyConsumption dataCold = consumptions.stream().filter(quantityAggregationData1 -> Objects.equals(quantityAggregationData1.getLogtime(), TimeUtil.localDateTime2timestamp(thisMonth))
                && Objects.nonNull(quantityAggregationData1.getUsage()) && Objects.equals(quantityAggregationData1.getEnergytype(), EnergyTypeDef.COLD))
                .findAny().orElse(new EnergyConsumption());
        //环比
        EnergyConsumption data1 = consumptions.stream().filter(quantityAggregationData1 -> Objects.equals(quantityAggregationData1.getLogtime(), TimeUtil.localDateTime2timestamp(hbTime))
                && Objects.nonNull(quantityAggregationData1.getUsage()) && Objects.equals(quantityAggregationData1.getEnergytype(), EnergyTypeDef.ELECTRIC))
                .findAny().orElse(new EnergyConsumption());
        EnergyConsumption dataCold1 = consumptions.stream().filter(quantityAggregationData1 -> Objects.equals(quantityAggregationData1.getLogtime(), TimeUtil.localDateTime2timestamp(hbTime))
                && Objects.nonNull(quantityAggregationData1.getUsage()) && Objects.equals(quantityAggregationData1.getEnergytype(), EnergyTypeDef.COLD))
                .findAny().orElse(new EnergyConsumption());
        EnergyConsumption data2 = consumptions.stream().filter(quantityAggregationData1 -> Objects.equals(quantityAggregationData1.getLogtime(), TimeUtil.localDateTime2timestamp(tbTime))
                && Objects.nonNull(quantityAggregationData1.getUsage()) && Objects.equals(quantityAggregationData1.getEnergytype(), EnergyTypeDef.ELECTRIC))
                .findAny().orElse(new EnergyConsumption());
        EnergyConsumption dataCold2 = consumptions.stream().filter(quantityAggregationData1 -> Objects.equals(quantityAggregationData1.getLogtime(), TimeUtil.localDateTime2timestamp(tbTime))
                && Objects.nonNull(quantityAggregationData1.getUsage()) && Objects.equals(quantityAggregationData1.getEnergytype(), EnergyTypeDef.COLD))
                .findAny().orElse(new EnergyConsumption());
        savingEffectVo.setMonthValue(CommonUtils.calcDouble(CommonUtils.calcDouble(dataCold.getUsage(), UNIT, EnumOperationType.DIVISION.getId()), data.getUsage(), EnumOperationType.DIVISION.getId()));
        savingEffectVo.setHbValue(CommonUtils.calcDouble(CommonUtils.calcDouble(dataCold1.getUsage(), UNIT, EnumOperationType.DIVISION.getId()), data1.getUsage(), EnumOperationType.DIVISION.getId()));
        savingEffectVo.setTbValue(CommonUtils.calcDouble(CommonUtils.calcDouble(dataCold2.getUsage(), UNIT, EnumOperationType.DIVISION.getId()), data2.getUsage(), EnumOperationType.DIVISION.getId()));
        savingEffectVo.setHbPercent(CommonUtils.calcDouble(savingEffectVo.getMonthValue(), savingEffectVo.getHbValue(), EnumOperationType.RATE.getId()));
        savingEffectVo.setTbPercent(CommonUtils.calcDouble(savingEffectVo.getMonthValue(), savingEffectVo.getTbValue(), EnumOperationType.RATE.getId()));
    }

    private void assembleEnergySavingEffect(LocalDateTime thisDay, Long roomId, EnergySavingEffectVo savingEffectVo) {
        //修改，不存入能耗的数据，根据功率的数据来进行计算(P1+(P2+...+Pn-1)*2+Pn)*0.25/2 （单位：kWh）
        //截止时间小于等于当前的时间的整点时间
        List<ColdPredict> result = coldPredictDao.queryColdPredictData(Collections.singletonList(ColdLoadType.TOTAL),
                Collections.singletonList(PredictDataType.POWER),
                thisDay, LocalDateTime.now(), roomId);
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        result = result.stream().sorted(Comparator.comparing(ColdPredict::getLogTime)).collect(Collectors.toList());
        if (Objects.equals(result.size(), 1)) {
            savingEffectVo.setAfterOptimizationValue(result.get(0).getValue());
            //实际值-预测值  /实际值
            Double efficiencyRate = CommonUtils.calcDouble(savingEffectVo.getRealTimeValue(), savingEffectVo.getAfterOptimizationValue(), EnumOperationType.SUBTRACT.getId());
            savingEffectVo.setEfficiencyRate(CommonUtils.calcDouble(efficiencyRate, savingEffectVo.getRealTimeValue(), EnumOperationType.DIVISION.getId()));
            return;
        }
        ColdPredict first = result.get(0);
        ColdPredict last = result.get(result.size() - 1);
        result.remove(first);
        result.remove(last);
        double sum = result.stream().filter(coldPredict -> Objects.nonNull(coldPredict.getValue())).mapToDouble(ColdPredict::getValue).sum();
        Double afterOptimizationValue = CommonUtils.calcDouble(CommonUtils.calcDouble(first.getValue(),
                last.getValue(), EnumOperationType.ADD.getId()), sum * 2, EnumOperationType.ADD.getId());
        if (Objects.nonNull(afterOptimizationValue)) {
            savingEffectVo.setAfterOptimizationValue(afterOptimizationValue * RATE);
        }
        Double efficiencyRate = CommonUtils.calcDouble(savingEffectVo.getRealTimeValue(), savingEffectVo.getAfterOptimizationValue(), EnumOperationType.SUBTRACT.getId());
        savingEffectVo.setEfficiencyRate(CommonUtils.calcDouble(efficiencyRate, savingEffectVo.getRealTimeValue(), EnumOperationType.DIVISION.getId()));

    }


    /**
     * 查询策略--开启或者关闭，获得策略关联的节点--获得这个房间下的连锁，整理出开启或者关闭的连锁所包含的全部设备
     * 根据建议赋值状态，冷机出水温度是实时值
     *
     * @param roomId
     * @return
     */
    @Override
    public StrategyAdviceVo queryStrategyAdvice(Long roomId) {
        StrategyAdviceVo result = new StrategyAdviceVo();
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime thisDay = TimeUtil.getFirstTimeOfDay(now);
        List<PumpVo> mains = queryNodesByParent(NodeLabelDef.COLD_WATER_MAINENGINE, roomId, NodeLabelDef.ROOM);
        //根据房间id查询关联的系统
        List<RefrigeratingSystem> refrigeratingSystems = refrigeratingSystemDao.querySystemByRoomId(roomId, GlobalInfoUtils.getProjectId());
        //查询策略建议
        List<AiStartStopStrategy> aiStartStopStrategies = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(refrigeratingSystems)) {
            aiStartStopStrategies = aiStartStopStrategyDao.queryAiStartStopStrategy(refrigeratingSystems.get(0).getId(),
                    Arrays.asList(StrategyTypeDef.start, StrategyTypeDef.stop, StrategyTypeDef.optimization), thisDay, TimeUtil.addDateTimeByCycle(thisDay, AggregationCycle.ONE_DAY, 1));
        }
        //获得最新时间的策略，需要分开启和关闭的，分开处理
        List<AiStartStopStrategy> startOrStopStrategy = getStartOrStopStrategy(aiStartStopStrategies);
        List<AiStartStopStrategy> aiStartStrategy = startOrStopStrategy.stream().filter(aiStartStopStrategy ->
                Objects.equals(StrategyTypeDef.start, aiStartStopStrategy.getStrategyType())).collect(Collectors.toList());
        List<AiStartStopStrategy> aiStopStrategy = startOrStopStrategy.stream().filter(aiStartStopStrategy ->
                Objects.equals(StrategyTypeDef.stop, aiStartStopStrategy.getStrategyType())).collect(Collectors.toList());
        List<StrategyObjectMap> strategyObjectMaps = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(startOrStopStrategy)) {
            result.setTime(startOrStopStrategy.get(0).getUpdateTime());
            List<Long> ids = startOrStopStrategy.stream().map(AiStartStopStrategy::getId).distinct().collect(Collectors.toList());
            //查询策略详情，和连锁信息
            strategyObjectMaps = strategyObjectMapDao.queryStrategyObjectMap(ids);
        }
        //同一连锁下的设备建议状态相同
        Set<BaseVo> startBaseVos = addStatusByAdvice(aiStartStrategy, strategyObjectMaps);
        Set<BaseVo> stopBaseVos = addStatusByAdvice(aiStopStrategy, strategyObjectMaps);
        ArrayList<BaseVo> baseVos = new ArrayList<>(startBaseVos);
        baseVos.addAll(new ArrayList<>(stopBaseVos));
        //补充名称和冷机类型
        List<PumpVo> pumpVos = nodeDao.queryNodes(baseVos, PumpVo.class);
        addStrategyAdvices(result, pumpVos, strategyObjectMaps);
        //添加建议
        result.setString(setAdviceString(startBaseVos, stopBaseVos, pumpVos, roomId,
                aiStartStrategy, aiStopStrategy, mains));
        //添加状态
        setStatus(result, startBaseVos, stopBaseVos);
        //添加策略是否是新策略
        setIsNew(startOrStopStrategy, result);
        return result;


    }

    /**
     * 判断是否要提醒策略不是新的,用建议时间
     *
     * @param startOrStopStrategy
     * @param result
     */
    private void setIsNew(List<AiStartStopStrategy> startOrStopStrategy, StrategyAdviceVo result) {
        if (CollectionUtils.isEmpty(startOrStopStrategy)) {
            result.setIsNew(false);
            return;
        }
        Long operationTime = startOrStopStrategy.get(0).getOperationTime();
        long now = System.currentTimeMillis();
        result.setIsNew(operationTime > now);
    }

    /**
     * 拼接策略优化的建议说明
     *
     * @param startBaseVos
     * @param stopBaseVos
     * @param pumpVos
     * @param roomId
     * @param aiStartStrategys
     * @param aiStopStrategys
     * @return
     */
    private String setAdviceString(Set<BaseVo> startBaseVos, Set<BaseVo> stopBaseVos, List<PumpVo> pumpVos, Long roomId,
                                   List<AiStartStopStrategy> aiStartStrategys, List<AiStartStopStrategy> aiStopStrategys, List<PumpVo> mains) {
        AiStartStopStrategy aiStartStrategy = new AiStartStopStrategy();
        if (CollectionUtils.isNotEmpty(aiStartStrategys)) {
            aiStartStrategy = aiStartStrategys.get(0);
        }
        AiStartStopStrategy aiStopStrategy = new AiStartStopStrategy();
        if (CollectionUtils.isNotEmpty(aiStopStrategys)) {
            aiStopStrategy = aiStopStrategys.get(0);
        }
        String adviceStringFirst = createAdviceStringFirst(mains, roomId);
        List<String> nameOfStart = pumpVos.stream().filter(
                pumpVo -> startBaseVos.contains(new BaseVo(pumpVo.getId(), pumpVo.getModelLabel()))).map(PumpVo::getName).collect(Collectors.toList());
        List<String> nameOfStop = pumpVos.stream().filter(
                pumpVo -> stopBaseVos.contains(new BaseVo(pumpVo.getId(), pumpVo.getModelLabel()))).map(PumpVo::getName).collect(Collectors.toList());
        String adviceStringStart = "";
        if (CollectionUtils.isNotEmpty(nameOfStart)) {
            adviceStringStart = createString(aiStartStrategy, nameOfStart);
            adviceStringFirst = adviceStringFirst + "，" + adviceStringStart;
        }
        String adviceStringStop = "";
        if (CollectionUtils.isNotEmpty(nameOfStop)) {
            adviceStringStop = createStringStop(aiStopStrategy, nameOfStop);
            adviceStringFirst = adviceStringFirst + adviceStringStop;
        }

        return adviceStringFirst;
    }

    /**
     * 拼接冷机出水温度和冷机类型
     *
     * @param result
     * @param pumpVos
     * @param strategyObjectMaps
     */
    private void addStrategyAdvices(StrategyAdviceVo result, List<PumpVo> pumpVos, List<StrategyObjectMap> strategyObjectMaps) {


        //房间下的全部节点 冷机冷冻水出水温度  策略值
        List<StrategyAdvice> strategyAdvices = assembleStatusWithPump(strategyObjectMaps, pumpVos);
        result.setStrategyAdvices(strategyAdvices);
    }

    /**
     * 获得最新的一批策略
     *
     * @param aiStartStopStrategies
     * @return
     */
    private List<AiStartStopStrategy> getStartOrStopStrategy(List<AiStartStopStrategy> aiStartStopStrategies) {
        if (CollectionUtils.isEmpty(aiStartStopStrategies)) {
            return Collections.emptyList();
        }
        List<AiStartStopStrategy> strategies = aiStartStopStrategies.stream()
                .sorted(Comparator.comparing(AiStartStopStrategy::getOperationTime).reversed())
                .collect(Collectors.toList());
        Long operationTime = strategies.get(0).getOperationTime();
        return strategies.stream().filter(aiStartStopStrategy -> Objects.equals(operationTime, aiStartStopStrategy.getOperationTime()))
                .collect(Collectors.toList());

    }

    @Override
    public ResultWithTotal<List<StrategyInfoVo>> queryStrategyInfo(StrategyQueryParam param) {
        //查询系统
        RefrigeratingSystem refrigeratingSystem = refrigeratingSystemDao.selectById(param.getSystemId());
        if (Objects.isNull(refrigeratingSystem)) {
            return ResultWithTotal.ok();
        }
        ResultWithTotal<List<AiStartStopStrategy>> aiStartStopStrategies;
        if (Objects.equals(param.getStrategyType(), 0)) {
            aiStartStopStrategies = aiStartStopStrategyDao.queryAiStartStopStrategyWithPage(param.getSystemId(),
                    Collections.emptyList(), param.getStartTime(), param.getEndTime(), param.getPage());
        } else {
            aiStartStopStrategies = aiStartStopStrategyDao.queryAiStartStopStrategyWithPage(param.getSystemId(),
                    Collections.singletonList(param.getStrategyType()), param.getStartTime(), param.getEndTime(), param.getPage());
        }
        List<AiStartStopStrategy> data = aiStartStopStrategies.getData();
        //查询策略
        if (CollectionUtils.isEmpty(data)) {
            return ResultWithTotal.ok();
        }
        List<Long> strategyIds = data.stream().map(AiStartStopStrategy::getId).collect(Collectors.toList());
        //查询策略详情
        List<StrategyObjectMap> strategyObjectMaps = strategyObjectMapDao.queryStrategyObjectMap(strategyIds);
        List<StrategyInfoVo> result = new ArrayList<>();
        //查询系统下的节点
        List<PumpVo> pumpVos = modelConfigurationService.queryChainDevice(refrigeratingSystem.getRoomId());
        for (AiStartStopStrategy strategy : data) {
            StrategyInfoVo strategyInfoVo = new StrategyInfoVo();
            Set<BaseVo> baseVos;
            String adviceString;
            List<BaseVo> needNodes = strategyObjectMaps.stream().filter(strategyObjectMap -> Objects.equals(strategyObjectMap.getStrategyId(), strategy.getId()))
                    .map(strategyObjectMap -> new BaseVo(strategyObjectMap.getObjectId(), strategyObjectMap.getObjectLabel()))
                    .distinct().collect(Collectors.toList());
            if (Objects.equals(strategy.getStrategyType(), StrategyTypeDef.optimization)) {
                needNodes = nodeDao.queryNodeName(needNodes);
                adviceString = createString(strategy, needNodes, strategyObjectMaps);
            } else {
                baseVos = new HashSet<>(needNodes);
                List<String> names = pumpVos.stream().filter(pumpVo -> baseVos.contains(new BaseVo(pumpVo.getId(), pumpVo.getModelLabel()))).map(PumpVo::getName).collect(Collectors.toList());

                adviceString = createString(strategy, names);
            }
            strategyInfoVo.setAdvice(adviceString);
            strategyInfoVo.setTime(strategy.getUpdateTime());
            strategyInfoVo.setStrategyType(strategy.getStrategyType());
            strategyInfoVo.setTypeName(StrategyTypeEnum.valueOf(strategy.getStrategyType()));
            result.add(strategyInfoVo);
        }

        return ResultWithTotal.ok(result, aiStartStopStrategies.getTotal());
    }

    /**
     * 根据策略详情里的设备信息，和连锁信息获得状态相同的设备的列表，
     *
     * @param aiStartStopStrategy
     * @param strategyObjectMaps
     * @return
     */
    private Set<BaseVo> addStatusByAdvice(List<AiStartStopStrategy> aiStartStopStrategy,
                                          List<StrategyObjectMap> strategyObjectMaps) {
        Set<Long> ids = aiStartStopStrategy.stream().map(AiStartStopStrategy::getId).collect(Collectors.toSet());
        return strategyObjectMaps.stream().filter(strategyObjectMap -> ids.contains(strategyObjectMap.getStrategyId()))
                .map(strategyObjectMap -> new BaseVo(strategyObjectMap.getObjectId(), strategyObjectMap.getObjectLabel()))
                .collect(Collectors.toSet());

    }

    private ColdPredict getLastColdPredict(List<ColdPredict> result, Integer coldLoadType, Integer predictDataType) {
        if (CollectionUtils.isEmpty(result)) {
            return new ColdPredict();
        }
        List<ColdPredict> coldPredicts = result.stream().filter(coldActual -> Objects.equals(coldActual.getColdLoadType(), coldLoadType) && Objects.equals(coldActual.getPredictDataType(),
                predictDataType)).sorted(Comparator.comparing(ColdPredict::getLogTime).reversed()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(coldPredicts)) {
            return coldPredicts.get(0);
        }
        return new ColdPredict();
    }

    /**
     * 拼接策略优化建议前面一段
     *
     * @param pumpVos
     * @param roomId
     * @return
     */
    private String createAdviceStringFirst(List<PumpVo> pumpVos, Long roomId) {
        List<BaseVo> mains = pumpVos.stream()
                .filter(pumpVo -> Objects.equals(pumpVo.getModelLabel(), NodeLabelDef.COLD_WATER_MAINENGINE))
                .map(pumpVo -> new BaseVo(pumpVo.getId(), pumpVo.getModelLabel()))
                .collect(Collectors.toList());
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime st = TimeUtil.getFirstTimeOfHour(now);
        LocalDateTime et = TimeUtil.addDateTimeByCycle(st, AggregationCycle.ONE_HOUR, 2);
        //冷负荷实时值--做处理
        Double aDouble = operationTrendService.querySystemCold(mains);
        if (Objects.nonNull(aDouble)) {
            aDouble = operationTrendService.handleDoubleData(aDouble) * 1.01;
        }
        //冷量需求未来值
        Double predict = null;
        List<ColdPredict> result = coldPredictDao.queryColdPredictData(Collections.singletonList(ColdLoadType.TOTAL), Collections.singletonList(PredictDataType.COLD_LOAD),
                st, et, roomId);
        if (CollectionUtils.isNotEmpty(result)) {
            result = result.stream().sorted(Comparator.comparing(ColdPredict::getLogTime).reversed()).collect(Collectors.toList());
            predict = Math.abs(result.get(0).getValue());
        }
        String actual = handleNoneData(aDouble);
        String predictValue = handleNoneData(predict);
        String percent = CommonUtils.BLANK_STR;
        Double percentValue = CommonUtils.calcDouble(predict, aDouble, EnumOperationType.RATE.getId());
        if (Objects.nonNull(percentValue)) {
            double abs = Math.abs(percentValue) * 100;
            percent = handleNoneData(abs);
        }
        String timeHour = CommonUtils.BLANK_STR;
        if (CollectionUtils.isNotEmpty(result)) {
            String format = TimeUtil.format(result.get(0).getLogTime(), TimeUtil.MINUTE_TIME_FORMAT);
            String[] s = format.split(" ");
            timeHour = s[1];
        }
        return "当前冷负荷" + actual + "kW" + "，预计" + timeHour + "冷负荷需求为" + predictValue + "kW，变化" + percent + "%";

    }

    /**
     * 赋值状态
     *
     * @param strategyAdviceVo
     * @param startBaseVos
     * @param stopBaseVos
     */
    private void setStatus(StrategyAdviceVo strategyAdviceVo, Set<BaseVo> startBaseVos, Set<BaseVo> stopBaseVos) {

        List<StrategyAdvice> strategyAdvices = strategyAdviceVo.getStrategyAdvices();
        for (StrategyAdvice strategy : strategyAdvices) {
            if (startBaseVos.contains(new BaseVo(strategy.getId(), strategy.getModelLabel()))) {
                strategy.setIsStart(true);
            } else if (stopBaseVos.contains(new BaseVo(strategy.getId(), strategy.getModelLabel()))) {
                strategy.setIsStart(false);
            }
        }
        List<StrategyAdvice> advices = strategyAdvices.stream().filter(strategyAdvice -> Objects.nonNull(strategyAdvice.getIsStart()))
                .collect(Collectors.toList());
        strategyAdviceVo.setStrategyAdvices(advices);
    }

    /**
     * 开启和关闭的建议逻辑
     *
     * @param aiStartStopStrategy
     * @param names
     * @return
     */
    private String createString(AiStartStopStrategy aiStartStopStrategy, List<String> names) {
        if (Objects.isNull(aiStartStopStrategy.getOperationTime())) {
            return StringUtils.EMPTY;
        }
        String result = "建议";
        String format = TimeUtil.format(aiStartStopStrategy.getOperationTime(), TimeUtil.MINUTE_TIME_FORMAT);
        String[] s = format.split(" ");
        String s1 = s[1];
        String operation;
        if (Objects.equals(aiStartStopStrategy.getStrategyType(), StrategyTypeDef.start)) {
            operation = START;
        } else {
            operation = STOP;
        }
        String join = StringUtils.join(names, "、");
        if (CollectionUtils.isEmpty(names)) {
            join = CommonUtils.BLANK_STR;
        }
        return result + s1 + operation + join;
    }

    /**
     * 优化建议逻辑
     *
     * @param aiStartStopStrategy
     * @param nodes
     * @param maps
     * @return
     */
    private String createString(AiStartStopStrategy aiStartStopStrategy, List<BaseVo> nodes, List<StrategyObjectMap> maps) {

        String result = "建议";
        String format = TimeUtil.format(aiStartStopStrategy.getOperationTime(), TimeUtil.MINUTE_TIME_FORMAT);
        String[] s = format.split(" ");
        String s1 = s[1];
        StringBuilder builder = new StringBuilder();
        builder.append(result);
        builder.append(s1).append(" ");
        nodes = nodes.stream().filter(baseVo -> Objects.equals(baseVo.getModelLabel(), NodeLabelDef.COLD_WATER_MAINENGINE)).collect(Collectors.toList());
        for (int i = 0; i < nodes.size(); i++) {
            BaseVo baseVo = nodes.get(i);
            StrategyObjectMap map = maps.stream().filter(strategyObjectMap -> Objects.equals(baseVo.getModelLabel(), strategyObjectMap.getObjectLabel())
                    && Objects.equals(baseVo.getId(), strategyObjectMap.getObjectId()) && Objects.equals(strategyObjectMap.getStrategyId(), aiStartStopStrategy.getId()))
                    .findAny().orElse(new StrategyObjectMap());
            builder.append(baseVo.getName());
            builder.append(OPTIMIZATION);
            builder.append(handleNoneData(map.getTemp()));
            builder.append(TEMP);
            if (Objects.equals(i, nodes.size() - 1)) {
                builder.append("。");
            } else {
                builder.append("，");
            }
        }
        return builder.toString();
    }

    private String createStringStop(AiStartStopStrategy aiStartStopStrategy, List<String> names) {

        String operation;
        if (Objects.equals(aiStartStopStrategy.getStrategyType(), StrategyTypeDef.start)) {
            operation = START;
        } else {
            operation = STOP;
        }
        String join = StringUtils.join(names, "、");
        if (CollectionUtils.isEmpty(names)) {
            return "，" + operation + CommonUtils.BLANK_STR;
        }
        return "，" + operation + join;
    }

    private String handleNoneData(Double value) {
        if (Objects.isNull(value)) {
            return CommonUtils.BLANK_STR;
        }
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(value);
    }


    private List<WorkSectionVo> assembleLoadAndTemp(Map<Integer, List<RealTimeValue>> statusMap, List<PumpVo> pumpVos, Map<BaseVo, List<EnergySupplyToPo>> baseVoListMap,
                                                    List<WorkSection> workSections) {
        //负载率
        List<RealTimeValue> realTimeValues = statusMap.get(QuantityDef.getMainPowerQuantitySetting().getId());

        List<WorkSectionVo> machine = new ArrayList<>();
        for (PumpVo node : pumpVos) {
            WorkSectionVo workSectionVo = new WorkSectionVo();
            workSectionVo.setObjectId(node.getId());
            workSectionVo.setObjectLabel(node.getModelLabel());
            workSectionVo.setObjectName(node.getName());
            Double aDouble = calculateLoad(node.getId(), node.getModelLabel(), baseVoListMap, realTimeValues, pumpVos);
            workSectionVo.setPower(aDouble);
            addWorkSectionData(workSectionVo, workSections, SectionDataType.LOAD_RATE);
            machine.add(workSectionVo);
        }
        return machine;
    }

    private List<WorkSectionVo> assembleTemp(List<RealTimeValue> realTimeValues1, List<RealTimeValue> realTimeValues2, List<PumpVo> pumpVos
            , List<WorkSection> workSections, Integer type) {
        List<WorkSectionVo> result = new ArrayList<>();
        for (PumpVo node : pumpVos) {
            WorkSection normal = workSections.stream().filter(workSection -> Objects.equals(workSection.getObjectId(), node.getId())
                    && Objects.equals(workSection.getObjectLabel(), node.getModelLabel())
                    && Objects.equals(type, workSection.getSectionDataType()) && Objects.equals(SectionType.NORMAL, workSection.getSectionType()))
                    .findAny().orElse(new WorkSection());
            WorkSection optimal = workSections.stream().filter(workSection -> Objects.equals(workSection.getObjectId(), node.getId())
                    && Objects.equals(workSection.getObjectLabel(), node.getModelLabel())
                    && Objects.equals(type, workSection.getSectionDataType()) && Objects.equals(SectionType.OPTIMAL, workSection.getSectionType()))
                    .findAny().orElse(new WorkSection());
            WorkSectionVo workSectionVo = new WorkSectionVo();
            workSectionVo.setObjectId(node.getId());
            workSectionVo.setObjectLabel(node.getModelLabel());
            workSectionVo.setObjectName(node.getName());
            RealTimeValue value = realTimeValues1.stream().filter(realTimeValue -> Objects.equals(realTimeValue.getMonitoredId(), node.getId())
                    && Objects.equals(realTimeValue.getMonitoredLabel(), node.getModelLabel())
                    && Objects.nonNull(realTimeValue.getValue())).findAny().orElse(new RealTimeValue());
            RealTimeValue value1 = realTimeValues2.stream().filter(realTimeValue -> Objects.equals(realTimeValue.getMonitoredId(), node.getId())
                    && Objects.equals(realTimeValue.getMonitoredLabel(), node.getModelLabel())
                    && Objects.nonNull(realTimeValue.getValue())).findAny().orElse(new RealTimeValue());
            workSectionVo.setSupplyTemp(value.getValue());
            workSectionVo.setReturnTemp(value1.getValue());
            workSectionVo.setOptimalMax(optimal.getMax());
            workSectionVo.setOptimalMin(optimal.getMin());
            workSectionVo.setNormalMax(normal.getMax());
            workSectionVo.setNormalMin(normal.getMin());
            result.add(workSectionVo);
        }
        return result;
    }

    private void addWorkSectionData(WorkSectionVo workSectionVo, List<WorkSection> workSections, Integer sectionDataType) {
        List<WorkSection> workSection1 = workSections.stream().filter(workSection -> Objects.equals(workSection.getObjectId(), workSectionVo.getObjectId())
                && Objects.equals(workSection.getObjectLabel(), workSectionVo.getObjectLabel())
                && Objects.equals(sectionDataType, workSection.getSectionDataType())
        ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workSection1)) {
            return;
        }
        WorkSection normal = workSection1.stream().filter(workSection -> Objects.equals(workSection.getSectionType(), SectionType.NORMAL))
                .findAny().orElse(null);
        WorkSection optimal = workSection1.stream().filter(workSection -> Objects.equals(workSection.getSectionType(), SectionType.OPTIMAL))
                .findAny().orElse(null);
        if (Objects.nonNull(normal)) {
            workSectionVo.setNormalMax(normal.getMax());
            workSectionVo.setNormalMin(normal.getMin());
        }
        if (Objects.nonNull(optimal)) {
            workSectionVo.setOptimalMax(optimal.getMax());
            workSectionVo.setOptimalMin(optimal.getMin());
        }
    }

    private List<RealTimeValue> getDataWithEmpty(Map<Integer, List<RealTimeValue>> statusMap, Integer id) {
        List<RealTimeValue> realTimeValues = statusMap.get(id);
        if (CollectionUtils.isEmpty(realTimeValues)) {
            return Collections.emptyList();
        }
        return realTimeValues;
    }

    private void assembleFrequency(Map<Integer, List<RealTimeValue>> statusMap, List<DeviceCurrentStatus> deviceCurrentStatuses
            , List<PumpVo> pumpVos, Map<BaseVo, List<EnergySupplyToPo>> baseVoListMap) {
        //频率
        List<RealTimeValue> realTimeValuesOfTower = getDataWithEmpty(statusMap, QuantityDef.getDeviceTowerEnergy().getId());
        List<RealTimeValue> realTimeValuesOfPumpCold = getDataWithEmpty(statusMap, QuantityDef.getFreezingDeviceFrequency().getId());
        List<RealTimeValue> realTimeValuesOfPumpFreezing = getDataWithEmpty(statusMap, QuantityDef.getDeviceFrequency().getId());
        //负载率
        List<RealTimeValue> realTimeValues = getDataWithEmpty(statusMap, QuantityDef.getMainPowerQuantitySetting().getId());
        //温差 |冷冻水供水温度（6007206） - 冷冻水回水温度（6007207）| 代替
        List<RealTimeValue> realTimeValues1 = getDataWithEmpty(statusMap, QuantityDef.getFreezingWaterForSupplyQuantitySetting().getId());
        List<RealTimeValue> realTimeValues2 = getDataWithEmpty(statusMap, QuantityDef.getFreezingWaterForReturnQuantitySetting().getId());
        //|6007204 - 6007203|
        List<RealTimeValue> realTimeValues3 = getDataWithEmpty(statusMap, QuantityDef.getCoolingWaterForReturnQuantitySetting().getId());
        List<RealTimeValue> realTimeValues4 = getDataWithEmpty(statusMap, QuantityDef.getCoolingWaterForSupplyQuantitySetting().getId());
        //出塔水温dataID=6007204	没找到，用“流入冷却水泵的水温””代替 冷却塔的
        for (DeviceCurrentStatus status : deviceCurrentStatuses) {
            if (Objects.equals(status.getModelLabel(), NodeLabelDef.COLD_WATER_MAINENGINE)) {
                Double aDouble = calculateLoadRate(status.getId(), status.getModelLabel(), baseVoListMap, realTimeValues, pumpVos);
                calculateTemp(realTimeValues3, realTimeValues4, status, true);
                calculateTemp(realTimeValues1, realTimeValues2, status, false);
                status.setLoadRate(aDouble);
                setMainType(status, pumpVos);
            } else if (Objects.equals(status.getModelLabel(), NodeLabelDef.COOLING_TOWER)) {
                RealTimeValue value1 = realTimeValuesOfTower.stream().filter(realTimeValue -> Objects.equals(realTimeValue.getMonitoredId(), status.getId())
                        && Objects.equals(realTimeValue.getMonitoredLabel(), status.getModelLabel())
                        && Objects.nonNull(realTimeValue.getValue())).findAny().orElse(new RealTimeValue());
                status.setFrequency(value1.getValue());
            } else if (Objects.equals(status.getModelLabel(), NodeLabelDef.PUMP)) {
                PumpVo pumpVo1 = pumpVos.stream().filter(pumpVo -> Objects.equals(pumpVo.getFunctionType(), PumpFunctionType.COOLING_PUMP)
                        && Objects.equals(pumpVo.getId(), status.getId()) && Objects.equals(pumpVo.getModelLabel(), status.getModelLabel()))
                        .findAny().orElse(null);

                if (Objects.nonNull(pumpVo1)) {
                    status.setFunctionType(pumpVo1.getFunctionType());
                    RealTimeValue value1 = filter(realTimeValuesOfPumpCold, status.getId(), status.getModelLabel());
                    status.setFrequency(value1.getValue());
                    continue;
                }
                pumpVo1 = pumpVos.stream().filter(pumpVo -> Objects.equals(pumpVo.getFunctionType(), PumpFunctionType.REFRIGERATING_PUMP)
                        && Objects.equals(pumpVo.getId(), status.getId()) && Objects.equals(pumpVo.getModelLabel(), status.getModelLabel()))
                        .findAny().orElse(null);
                if (Objects.nonNull(pumpVo1)) {
                    status.setFunctionType(pumpVo1.getFunctionType());
                    RealTimeValue value1 = filter(realTimeValuesOfPumpFreezing, status.getId(), status.getModelLabel());
                    status.setFrequency(value1.getValue());
                }
            }
        }
    }

    private void setMainType(DeviceCurrentStatus status, List<PumpVo> pumpVos) {
        PumpVo pumpVo1 = pumpVos.stream().filter(pumpVo ->
                Objects.equals(pumpVo.getId(), status.getId()) && Objects.equals(pumpVo.getModelLabel(), status.getModelLabel())
                        && Objects.nonNull(pumpVo.getRatedMotorPower())).findAny().orElse(new PumpVo());
        if (Objects.equals(pumpVo1.getEngineAttr(), SCREW)) {
            status.setMainType(SCREW_MACHINE);
        } else if (Objects.equals(pumpVo1.getEngineAttr(), CENTRIFUGAL)) {
            status.setMainType(CENTRIFUGAL_MACHINE);
        }
    }

    private RealTimeValue filter(List<RealTimeValue> values, Long id, String label) {
        return values.stream().filter(realTimeValue -> Objects.equals(realTimeValue.getMonitoredId(), id)
                && Objects.equals(realTimeValue.getMonitoredLabel(), label)
                && Objects.nonNull(realTimeValue.getValue())).findAny().orElse(new RealTimeValue());
    }

    private void calculateTemp(List<RealTimeValue> realTimeValues1, List<RealTimeValue> realTimeValues2, DeviceCurrentStatus status, Boolean isCooling) {
        RealTimeValue realTimeValue1 = realTimeValues1.stream().filter(realTimeValue -> Objects.nonNull(realTimeValue.getValue())
                && Objects.equals(realTimeValue.getMonitoredId(), status.getId())
                && Objects.equals(realTimeValue.getMonitoredLabel(), status.getModelLabel()))
                .findAny().orElse(new RealTimeValue());
        RealTimeValue realTimeValue2 = realTimeValues2.stream().filter(realTimeValue -> Objects.nonNull(realTimeValue.getValue())
                && Objects.equals(realTimeValue.getMonitoredId(), status.getId())
                && Objects.equals(realTimeValue.getMonitoredLabel(), status.getModelLabel()))
                .findAny().orElse(new RealTimeValue());
        if (Boolean.TRUE.equals(isCooling)) {
            Double aDouble = CommonUtils.calcDouble(realTimeValue1.getValue(), realTimeValue2.getValue(), EnumOperationType.SUBTRACT.getId());
            if (Objects.nonNull(aDouble)) {
                status.setCoolingWaterTemp(Math.abs(aDouble));
            }
        } else {
            Double aDouble = CommonUtils.calcDouble(realTimeValue1.getValue(), realTimeValue2.getValue(), EnumOperationType.SUBTRACT.getId());
            if (Objects.nonNull(aDouble)) {
                status.setFreezingWaterTemp(Math.abs(aDouble));
            }
        }
    }

    private Double calculateLoad(Long objectId, String label, Map<BaseVo, List<EnergySupplyToPo>> baseVoListMap, List<RealTimeValue> realTimeValues, List<PumpVo> pumpVos) {
        PumpVo pumpVo1 = pumpVos.stream().filter(pumpVo -> Objects.equals(pumpVo.getId(), objectId) && Objects.equals(pumpVo.getModelLabel(), label)
                && Objects.nonNull(pumpVo.getRatedMotorPower())).findAny().orElse(new PumpVo());
        if (Objects.isNull(pumpVo1.getRatedMotorPower())) {
            return null;
        }
        List<EnergySupplyToPo> energySupplyToPos = baseVoListMap.get(new BaseVo(objectId, label));
        if (CollectionUtils.isEmpty(energySupplyToPos)) {
            return null;
        }
        Set<BaseVo> collect = energySupplyToPos.stream().map(energySupplyToPo -> new BaseVo(energySupplyToPo.getObjectid(), energySupplyToPo.getObjectlabel()))
                .collect(Collectors.toSet());
        Map<BaseVo, List<RealTimeValue>> collect1 = realTimeValues.stream().filter(realTimeValue -> collect.contains(new BaseVo(realTimeValue.getMonitoredId(), realTimeValue.getMonitoredLabel()))
                && Objects.nonNull(realTimeValue.getValue())).collect(Collectors.groupingBy(realTimeValue -> new BaseVo(realTimeValue.getMonitoredId(), realTimeValue.getMonitoredLabel())));
        List<Double> result = new ArrayList<>();
        for (Map.Entry<BaseVo, List<RealTimeValue>> entry : collect1.entrySet()) {
            List<RealTimeValue> value = entry.getValue();
            if (CollectionUtils.isNotEmpty(value)) {
                result.add(value.get(0).getValue());
            }
        }
        if (CollectionUtils.isNotEmpty(result)) {
            return result.stream().mapToDouble(Double::doubleValue).sum();
        }
        return null;
    }

    private Double calculateLoadRate(Long objectId, String label, Map<BaseVo, List<EnergySupplyToPo>> baseVoListMap, List<RealTimeValue> realTimeValues, List<PumpVo> pumpVos) {
        PumpVo pumpVo1 = pumpVos.stream().filter(pumpVo -> Objects.equals(pumpVo.getId(), objectId) && Objects.equals(pumpVo.getModelLabel(), label)
                && Objects.nonNull(pumpVo.getRatedMotorPower())).findAny().orElse(new PumpVo());
        if (Objects.isNull(pumpVo1.getRatedMotorPower())) {
            return null;
        }
        List<EnergySupplyToPo> energySupplyToPos = baseVoListMap.get(new BaseVo(objectId, label));
        if (CollectionUtils.isEmpty(energySupplyToPos)) {
            return null;
        }
        Set<BaseVo> collect = energySupplyToPos.stream().map(energySupplyToPo -> new BaseVo(energySupplyToPo.getObjectid(), energySupplyToPo.getObjectlabel()))
                .collect(Collectors.toSet());
        Map<BaseVo, List<RealTimeValue>> collect1 = realTimeValues.stream().filter(realTimeValue -> collect.contains(new BaseVo(realTimeValue.getMonitoredId(), realTimeValue.getMonitoredLabel()))
                && Objects.nonNull(realTimeValue.getValue())).collect(Collectors.groupingBy(realTimeValue -> new BaseVo(realTimeValue.getMonitoredId(), realTimeValue.getMonitoredLabel())));
        List<Double> result = new ArrayList<>();
        for (Map.Entry<BaseVo, List<RealTimeValue>> entry : collect1.entrySet()) {
            List<RealTimeValue> value = entry.getValue();
            if (CollectionUtils.isNotEmpty(value)) {
                result.add(value.get(0).getValue());
            }
        }
        if (CollectionUtils.isNotEmpty(result)) {
            double sum = result.stream().mapToDouble(Double::doubleValue).sum();
            return CommonUtils.calcDouble(sum, pumpVo1.getRatedMotorPower(), EnumOperationType.DIVISION.getId());
        }
        return null;
    }

    /**
     * 设备状态
     *
     * @param statusMap
     * @param nodes
     * @return
     */
    private List<DeviceCurrentStatus> assembleStatus(Map<Integer, List<RealTimeValue>> statusMap, List<BaseVo> nodes) {
        List<RealTimeValue> realTimeValues = statusMap.get(QuantityDef.getDeviceStatus().getId());
        List<DeviceCurrentStatus> result = new ArrayList<>();
        for (BaseVo baseVo : nodes) {
            DeviceCurrentStatus status = new DeviceCurrentStatus();
            BeanUtils.copyProperties(baseVo, status);
            if (CollectionUtils.isNotEmpty(realTimeValues)) {
                Optional<RealTimeValue> any = realTimeValues.stream().filter(realTimeValue -> Objects.equals(realTimeValue.getMonitoredId(), baseVo.getId())
                        && Objects.equals(realTimeValue.getMonitoredLabel(), baseVo.getModelLabel())
                        && Objects.equals(realTimeValue.getValue(), UP)).findAny();
                status.setIsStart(any.isPresent());
            }
            result.add(status);
        }
        return result;
    }

    /**
     * 设备状态
     *
     * @param strategyObjectMaps
     * @param nodes
     * @return
     */
    private List<StrategyAdvice> assembleStatusWithPump(List<StrategyObjectMap> strategyObjectMaps, List<PumpVo> nodes) {
        List<StrategyAdvice> result = new ArrayList<>();
        for (PumpVo baseVo : nodes) {
            StrategyAdvice status = new StrategyAdvice();
            BeanUtils.copyProperties(baseVo, status);
            if (Objects.equals(baseVo.getEngineAttr(), SCREW)) {
                status.setMainType(SCREW_MACHINE);
            } else if (Objects.equals(baseVo.getEngineAttr(), CENTRIFUGAL)) {
                status.setMainType(CENTRIFUGAL_MACHINE);
            }
            if (CollectionUtils.isNotEmpty(strategyObjectMaps) && Objects.equals(baseVo.getModelLabel(), NodeLabelDef.COLD_WATER_MAINENGINE)) {
                StrategyObjectMap value = strategyObjectMaps.stream().filter(realTimeValue -> Objects.equals(realTimeValue.getObjectId(), baseVo.getId())
                        && Objects.equals(realTimeValue.getObjectLabel(), baseVo.getModelLabel())
                        && Objects.nonNull(realTimeValue.getTemp())).findAny().orElse(new StrategyObjectMap());
                status.setOutWaterTemp(value.getTemp());
            }
            result.add(status);
        }
        return result;
    }


    private AiStartStopStrategyVo assembleStrategyVo(List<NodeWithPumpType> nodeWithPumpTypes, List<StrategyObjectMap> objectMaps, List<AiStartStopStrategy> strategies
            , Integer type) {
        AiStartStopStrategy strategy = strategies.stream().filter(aiStartStopStrategy -> Objects.equals(type, aiStartStopStrategy.getStrategyType()))
                .findAny().orElse(null);
        AiStartStopStrategyVo strategyVo = new AiStartStopStrategyVo();
        strategyVo.setStrategyType(type);
        if (Objects.isNull(strategy)) {
            return strategyVo;
        }
        Map<String, List<StrategyObjectMap>> map = objectMaps.stream().filter(strategyObjectMap -> Objects.equals(strategy.getId(), strategyObjectMap.getStrategyId())
                && Objects.equals(type, strategyObjectMap.getStrategyType()))
                .collect(Collectors.groupingBy(StrategyObjectMap::getObjectLabel));
        for (Map.Entry<String, List<StrategyObjectMap>> entry : map.entrySet()) {
            String key = entry.getKey();
            List<StrategyObjectMap> value = entry.getValue();
            handleStrategyVoWithObjectLabel(strategyVo, key, value, nodeWithPumpTypes);
        }
        return strategyVo;
    }

    private void handleStrategyVoWithObjectLabel(AiStartStopStrategyVo strategyVo, String key, List<StrategyObjectMap> value, List<NodeWithPumpType> nodeWithPumpTypes) {
        List<BaseVo> collect = value.stream().map(strategyObjectMap -> new BaseVo(strategyObjectMap.getObjectId(), strategyObjectMap.getObjectLabel()))
                .distinct().collect(Collectors.toList());
        if (Objects.equals(key, NodeLabelDef.PUMP)) {

            //区分冷冻和冷却
            Set<BaseVo> coldNodes = nodeWithPumpTypes.stream().filter(nodeWithPumpType -> Objects.equals(nodeWithPumpType.getFunctionType(), PumpFunctionType.COOLING_PUMP))
                    .map(nodeWithPumpType -> new BaseVo(nodeWithPumpType.getId(), nodeWithPumpType.getModelLabel(), nodeWithPumpType.getName())).collect(Collectors.toSet());
            List<StrategyObjectMap> strategyObjectMap1 = value.stream().filter(strategyObjectMap ->
                    coldNodes.contains(new BaseVo(strategyObjectMap.getObjectId(), strategyObjectMap.getObjectLabel()))).collect(Collectors.toList());
            Set<BaseVo> nodes = nodeWithPumpTypes.stream().filter(nodeWithPumpType -> Objects.equals(nodeWithPumpType.getFunctionType(), PumpFunctionType.REFRIGERATING_PUMP))
                    .map(nodeWithPumpType -> new BaseVo(nodeWithPumpType.getId(), nodeWithPumpType.getModelLabel(), nodeWithPumpType.getName())).collect(Collectors.toSet());
            List<StrategyObjectMap> map = value.stream().filter(strategyObjectMap ->
                    nodes.contains(new BaseVo(strategyObjectMap.getObjectId(), strategyObjectMap.getObjectLabel()))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(strategyObjectMap1)) {
                strategyVo.setCoolingPumpFrequency(strategyObjectMap1.get(0).getFrequency());
                List<BaseVo> collect1 = strategyObjectMap1.stream().map(strategyObjectMap -> new BaseVo(strategyObjectMap.getObjectId(), strategyObjectMap.getObjectLabel()))
                        .distinct().collect(Collectors.toList());
                assembleNodesName(collect1, nodeWithPumpTypes);
                strategyVo.setCoolingPumps(new ArrayList<>(collect1));
            }
            if (CollectionUtils.isNotEmpty(map)) {
                strategyVo.setRefrigerationPumpFrequency(map.get(0).getFrequency());
                List<BaseVo> collect1 = map.stream().map(strategyObjectMap -> new BaseVo(strategyObjectMap.getObjectId(), strategyObjectMap.getObjectLabel()))
                        .distinct().collect(Collectors.toList());
                assembleNodesName(collect1, nodeWithPumpTypes);
                strategyVo.setFreezingPumps(new ArrayList<>(collect1));
            }
        } else if (Objects.equals(key, NodeLabelDef.COLD_WATER_MAINENGINE)) {
            assembleNodesName(collect, nodeWithPumpTypes);
            strategyVo.setColdWaterMainEngines(collect);
            StrategyObjectMap map = value.stream().filter(strategyObjectMap -> Objects.nonNull(strategyObjectMap.getTemp())).findAny().orElse(new StrategyObjectMap());
            strategyVo.setWaterOutTemp(map.getTemp());
            StrategyObjectMap map1 = value.stream().filter(strategyObjectMap -> Objects.nonNull(strategyObjectMap.getPressureDiff())).findAny().orElse(new StrategyObjectMap());
            strategyVo.setWaterReturnPressureDiff(map1.getPressureDiff());
        } else if (Objects.equals(key, NodeLabelDef.WINDSET)) {
            StrategyObjectMap map = value.stream().filter(strategyObjectMap -> Objects.nonNull(strategyObjectMap.getFrequency())).findAny().orElse(new StrategyObjectMap());
            strategyVo.setFanFrequency(map.getFrequency());
        } else if (Objects.equals(key, NodeLabelDef.COOLING_TOWER)) {
            assembleNodesName(collect, nodeWithPumpTypes);
            strategyVo.setCoolingTowers(collect);
        } else if (Objects.equals(key, NodeLabelDef.PLATE_HEAT_EXCHANGER)) {
            assembleNodesName(collect, nodeWithPumpTypes);
            strategyVo.setPlateHeatExChangers(collect);
        }
    }

    private void assembleNodesName(List<BaseVo> collect, List<NodeWithPumpType> nodeWithPumpTypes) {
        for (BaseVo baseVo : collect) {
            NodeWithPumpType nodeWithPumpType1 = nodeWithPumpTypes.stream().filter(nodeWithPumpType -> Objects.equals(nodeWithPumpType.getId(), baseVo.getId())
                    && Objects.equals(nodeWithPumpType.getModelLabel(), baseVo.getModelLabel()))
                    .findAny().orElse(new NodeWithPumpType());
            baseVo.setName(nodeWithPumpType1.getName());
        }
    }


    /**
     * 查询子节点
     *
     * @return
     */
    @Override
    public List<PumpVo> queryNodesByParent(String queryLabel, Long id, String modelLabel) {
        QueryCondition condition = new QueryConditionBuilder<>(modelLabel, id)
                .leftJoin(queryLabel).queryAsTree().build();
        List<PumpVo> query = modelServiceUtils.query(condition, PumpVo.class);
        List<PumpVo> result = new ArrayList<>();
        for (PumpVo baseVo : query) {
            if (CollectionUtils.isNotEmpty(baseVo.getChildren())) {
                result.addAll(baseVo.getChildren());
            }
        }
        return result;
    }

    private void getDiffData(List<BaseVo> pipeLines, QueryParam queryParam, WaterTempVo waterTempVo) {
        Map<Integer, List<RealTimeValue>> integerListMap = quantityManageService.queryRealTimeBath(
                createQuantityDataBatchSearchVo(pipeLines, queryParam, Arrays.asList(QuantityDef.getDifferentialPressureSetting(),
                        QuantityDef.getEndSupplyTemp(), QuantityDef.getEndReturnTemp(), QuantityDef.getFreezingWaterPipelineForSupplyStandardQuantitySetting())));
        List<RealTimeValue> realTimeValues = integerListMap.get(QuantityDef.getDifferentialPressureSetting().getId());
        List<RealTimeValue> supplyTemp = integerListMap.get(QuantityDef.getEndSupplyTemp().getId());
        List<RealTimeValue> returnTemp = integerListMap.get(QuantityDef.getEndReturnTemp().getId());
        List<RealTimeValue> pipelineSupplyTemp = integerListMap.get(QuantityDef.getFreezingWaterPipelineForSupplyStandardQuantitySetting().getId());
        if (CollectionUtils.isNotEmpty(realTimeValues)) {
            RealTimeValue value = realTimeValues.stream().filter(realTimeValue -> Objects.nonNull(realTimeValue.getValue())).findAny().orElse(new RealTimeValue());
            waterTempVo.setPressureSettingValue(value.getValue());
        }
        if (CollectionUtils.isNotEmpty(supplyTemp)) {
            RealTimeValue value = supplyTemp.stream().filter(realTimeValue -> Objects.nonNull(realTimeValue.getValue())).findAny().orElse(new RealTimeValue());
            waterTempVo.setSupplyTemp(value.getValue());
        }
        if (CollectionUtils.isNotEmpty(returnTemp)) {
            RealTimeValue value = returnTemp.stream().filter(realTimeValue -> Objects.nonNull(realTimeValue.getValue())).findAny().orElse(new RealTimeValue());
            waterTempVo.setReturnTemp(value.getValue());
        }
        if (CollectionUtils.isNotEmpty(pipelineSupplyTemp)) {
            RealTimeValue value = pipelineSupplyTemp.stream().filter(realTimeValue -> Objects.nonNull(realTimeValue.getValue())).findAny().orElse(new RealTimeValue());
            waterTempVo.setOutletWaterTempSettingValue(value.getValue());
        }
    }


    private QuantityDataBatchSearchVo createQuantityDataBatchSearchVo(List<BaseVo> deviceNodes, QueryParam
            query, List<QuantitySearchVo> quantitySearchVo) {
        QuantityDataBatchSearchVo aggregationDataBatch = new QuantityDataBatchSearchVo();
        aggregationDataBatch.setDataTypeId(EnumDataTypeId.REALTIME.getId());
        if (Objects.nonNull(query.getStartTime())) {
            aggregationDataBatch.setStartTime(TimeUtil.localDateTime2timestamp(query.getStartTime()));
            aggregationDataBatch.setEndTime(TimeUtil.localDateTime2timestamp(query.getEndTime()));
        }
        aggregationDataBatch.setQuantitySettings((quantitySearchVo));
        aggregationDataBatch.setNodes(deviceNodes);
        aggregationDataBatch.setAggregationCycle(query.getCycle());
        return aggregationDataBatch;
    }

    public List<BaseVo> queryEquipmentGroupDevice(QueryParam queryParam) {

        SingleModelConditionDTO singleModelConditionDTO = new SingleModelConditionDTO(NodeLabelDef.PUMP);
        List<ConditionBlock> filters = new ArrayList<>();
        filters.add(new ConditionBlock(ColdOptimizationLabelDef.FUNCTION_TYPE, ConditionBlock.OPERATOR_EQ, PumpFunctionType.COOLING_PUMP));
        singleModelConditionDTO.setFilter(new ConditionBlockCompose(filters));
        SingleModelConditionDTO singleModelConditionDTO1 = new SingleModelConditionDTO(NodeLabelDef.COLD_WATER_MAINENGINE);
        QueryCondition condition = new QueryConditionBuilder<>(queryParam.getObjectLabel(), queryParam.getObjectId())
                .leftJoinCondition(Arrays.asList(singleModelConditionDTO, singleModelConditionDTO1))
                .queryAsTree()
                .build();
        List<BaseVo> query = modelServiceUtils.query(condition, BaseVo.class);
        List<BaseVo> result = new ArrayList<>();
        for (BaseVo baseVo : query) {
            if (CollectionUtils.isNotEmpty(baseVo.getChildren())) {
                result.addAll(baseVo.getChildren());
            }
        }
        return result;
    }


    private List<LocalDateTime> createNextFIfTime() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime firstTimeOfHour = TimeUtil.getFirstTimeOfHour(now);
        List<LocalDateTime> timeRangeForFifteen = getTimeRangeForFifteen(firstTimeOfHour, TimeUtil.addDateTimeByCycle(firstTimeOfHour, AggregationCycle.ONE_HOUR, 2));
        List<LocalDateTime> result = new ArrayList<>();
        for (LocalDateTime time : timeRangeForFifteen) {
            if (Boolean.TRUE.equals(time.isAfter(now))) {
                result.add(time);
            }
            if (Objects.equals(result.size(), 3)) {
                return result;
            }
        }
        return result;
    }

    private List<LocalDateTime> getTimeRangeForFifteen(LocalDateTime st, LocalDateTime et) {
        List<LocalDateTime> timeRange = new ArrayList<>();
        LocalDateTime j;
        for (j = st; j.isBefore(et); j = j.plusMinutes(15L)) {
            timeRange.add((j));
        }
        return timeRange;
    }


}
