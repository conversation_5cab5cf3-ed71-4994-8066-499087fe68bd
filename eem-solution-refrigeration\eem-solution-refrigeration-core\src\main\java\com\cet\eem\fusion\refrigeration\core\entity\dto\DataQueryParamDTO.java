﻿package com.cet.eem.fusion.refrigeration.core.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName : DataQueryParam
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-04-08 14:21
 */
@Getter
@Setter
public class DataQueryParamDTO {
    @ApiModelProperty("实际数据开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("实际数据结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty("周期")
    private Integer cycle;

    @ApiModelProperty("项目id")
    private Long projectId;
    @ApiModelProperty("系统id")
    private List<Long> systemIds;
    @ApiModelProperty("预测数据开始时间")
    private LocalDateTime startTimePredict;

    @ApiModelProperty("预测数据结束时间")
    private LocalDateTime endTimePredict;
}
