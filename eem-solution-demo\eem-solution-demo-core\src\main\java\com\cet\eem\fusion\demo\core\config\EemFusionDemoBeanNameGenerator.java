package com.cet.eem.fusion.demo.core.config;

import com.cet.eem.solution.common.def.common.PluginInfoDef;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanNameGenerator;
import org.springframework.context.annotation.AnnotationBeanNameGenerator;

/**
 * bean name 生成器
 * <AUTHOR>  (2025/7/17 15:18)
 */
public class EemFusionDemoBeanNameGenerator implements BeanNameGenerator {
    @Override
    public String generateBeanName(BeanDefinition definition, BeanDefinitionRegistry registry) {
        AnnotationBeanNameGenerator annotationBeanNameGenerator =  new AnnotationBeanNameGenerator();
        return PluginInfoDef.Demo.PLUGIN_NAME_PREFIX + annotationBeanNameGenerator.generateBeanName(definition, registry);
    }
}
