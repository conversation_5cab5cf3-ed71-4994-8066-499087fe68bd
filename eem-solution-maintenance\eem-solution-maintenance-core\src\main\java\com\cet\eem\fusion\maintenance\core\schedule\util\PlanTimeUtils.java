package com.cet.eem.fusion.maintenance.core.schedule.util;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.PlanSheet;

import java.util.Objects;

/**
 * 描述：计划时间工具类
 *
 * <AUTHOR>
 * @date 2025/4/1
 */
public class PlanTimeUtils {
    /**
     * 计算计划开始时间
     *
     * @param planSheet 计划
     * @return 计划开始时间
     */
    public static Long getRealStartTime(PlanSheet planSheet) {
        if (Objects.isNull(planSheet.getAheadDuration())) {
            return planSheet.getExecuteTime();
        }
        return planSheet.getExecuteTime() - planSheet.getAheadDuration() * 60000;
    }
}
