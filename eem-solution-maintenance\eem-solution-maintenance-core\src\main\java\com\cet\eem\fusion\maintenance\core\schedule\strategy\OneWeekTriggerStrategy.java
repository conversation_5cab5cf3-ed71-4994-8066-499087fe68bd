package com.cet.eem.fusion.maintenance.core.schedule.strategy;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.PlanSheet;
import org.quartz.ScheduleBuilder;
import org.quartz.SimpleScheduleBuilder;
import org.quartz.SimpleTrigger;
import org.springframework.stereotype.Component;

/**
 * @ClassName : OneWeekTriggerStrategy
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-26 10:37
 */
@Component(PlanSheetTriggerStrategyKey.ONE_WEEK)
public class OneWeekTriggerStrategy implements PlanSheetTriggerStrategy<SimpleTrigger> {

    private static final int ONE_WEEK_HOURS = 7 * 24;

    @Override
    public ScheduleBuilder<SimpleTrigger> buildSchedule(PlanSheet planSheet) {
        SimpleScheduleBuilder simpleScheduleBuilder = SimpleScheduleBuilder.simpleSchedule().withMisfireHandlingInstructionNextWithExistingCount();
        simpleScheduleBuilder.withIntervalInHours(ONE_WEEK_HOURS);
        simpleScheduleBuilder.repeatForever();
        return simpleScheduleBuilder;
    }
}
