﻿package com.cet.eem.fusion.refrigeration.core.dao;

import com.cet.eem.fusion.refrigeration.core.model.aioptimization.AiStartStopStrategy;
import com.cet.eem.common.model.Page;
import com.cet.eem.common.model.ResultWithTotal;
import com.cet.eem.dao.BaseModelDao;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName : AiStartStopStrategyDao
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-04-19 10:41
 */
public interface AiStartStopStrategyDao extends BaseModelDao<AiStartStopStrategy> {
    /**
     * 根据制冷系统id查策略
     * @param refrigeratingSystemId
     * @param type
     * @return
     */
    List<AiStartStopStrategy>  queryAiStartStopStrategy(Long refrigeratingSystemId,List<Integer> type);
    List<AiStartStopStrategy>  queryAiStartStopStrategy(Long refrigeratingSystemId, List<Integer> type, LocalDateTime st,LocalDateTime et);

    /**
     * 根据制冷系统id查策略
     * @param refrigeratingSystemId
     * @param type
     * @param st
     * @param et
     * @return
     */
    List<AiStartStopStrategy>  queryAiStartStopStrategy(List<Long> refrigeratingSystemId, List<Integer> type, LocalDateTime st,LocalDateTime et);

    /**
     * 分页查询
     * @param refrigeratingSystemId
     * @param type
     * @param st
     * @param et
     * @param page
     * @return
     */
    ResultWithTotal<List<AiStartStopStrategy>> queryAiStartStopStrategyWithPage(Long refrigeratingSystemId, List<Integer> type, LocalDateTime st, LocalDateTime et, Page page);
}

