package com.cet.eem.fusion.maintenance.core.controller.bff.maintenance;

import com.cet.eem.fusion.config.sdk.service.log.OperationLog;
import com.cet.eem.fusion.config.sdk.def.OperationLogType;
import com.cet.eem.fusion.common.utils.EnumOperationSubType;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.PlanSheet;
import com.cet.eem.bll.maintenance.model.plan.AddMaintenancePlanRequest;
import com.cet.eem.bll.maintenance.model.plan.EditMaintenancePlanRequest;
import com.cet.eem.bll.maintenance.model.plan.MaintenancePlanSheetVo;
import com.cet.eem.bll.maintenance.model.plan.QueryMaintenancePlanRequest;
import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.bll.maintenance.service.maintenance.MaintenancePlanService;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.commons.ApiResult;
import io.swagger.annotations.ApiOperation;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @ClassName : MaintenancePlanBffController
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-21 14:19
 */

public class MaintenancePlanBffController {

    @Autowired
    private MaintenancePlanService maintenancePlanService;

    @ApiOperation(value = "新增维保计划")
    @OperationLog(operationType = EEMOperationLogType.MAINTENANCE_PLAN, subType = EnumOperationSubType.ADD, description = "【新增维保计划】")
    @PostMapping
    public ApiResult<PlanSheet> addMaintenancePlan(@RequestBody AddMaintenancePlanRequest addMaintenancePlanRequest) throws SchedulerException {
        return Result.ok(maintenancePlanService.addMaintenancePlan(addMaintenancePlanRequest));
    }

    @ApiOperation(value = "编辑维保计划")
    @OperationLog(operationType = EEMOperationLogType.MAINTENANCE_PLAN, subType = EnumOperationSubType.UPDATE, description = "【更新维保计划】")
    @PatchMapping
    public ApiResult<PlanSheet> editMaintenancePlan(@RequestBody EditMaintenancePlanRequest editMaintenancePlanRequest) throws SchedulerException {
        return Result.ok(maintenancePlanService.editMaintenancePlan(editMaintenancePlanRequest));
    }

    @ApiOperation(value = "查询当前项目维保计划")
    @PostMapping("/query")
    public ResultWithTotal<List<MaintenancePlanSheetVo>> queryMaintenancePlan(@RequestBody QueryMaintenancePlanRequest queryInspectionPlanRequest) {
        return maintenancePlanService.queryMaintenancePlan(queryInspectionPlanRequest);
    }

    @ApiOperation(value = "查询维保计划工单信息")
    @GetMapping("/order")
    public ApiResult<List<InspectionWorkOrderDto>> queryPlanWorkOrder(@RequestParam Long workSheetId) {
        return Result.ok(maintenancePlanService.queryPlanWorkOrder(workSheetId));
    }

    @ApiOperation(value = "启用维保计划")
    @OperationLog(operationType = EEMOperationLogType.MAINTENANCE_PLAN, subType = EnumOperationSubType.UPDATE, description = "【启用维保计划】")
    @PostMapping("/enable")
    public ApiResult<Void> enablePlanSheet(@RequestBody List<Long> ids) throws SchedulerException {
        maintenancePlanService.enablePlanSheet(ids);
        return Result.ok();
    }

    @ApiOperation(value = "禁用维保计划")
    @OperationLog(operationType = EEMOperationLogType.MAINTENANCE_PLAN, subType = EnumOperationSubType.UPDATE, description = "【禁用维保计划】")
    @PostMapping("disable")
    public ApiResult<Void> updatePlanSheetBatch(@RequestBody List<Long> ids) throws SchedulerException {
        maintenancePlanService.disablePlanSheet(ids);
        return Result.ok();
    }

}