"""
全局匹配问题检查器
基于关键字在项目的所有文件中进行搜索匹配，保证大小写一致
"""
import json
import os
from pathlib import Path
from typing import List, Dict, Any, Set
from datetime import datetime


class GlobalMatcher:
    """全局匹配问题检查器"""
    
    def __init__(self, rules_file: str = "global_match_rules.json"):
        """
        初始化全局匹配器
        
        Args:
            rules_file: 全局匹配规则配置文件路径
        """
        self.rules_file = rules_file
        self.issues = []
        self.rules = self._load_rules()
        self.supported_extensions = self._get_supported_extensions()
    
    def _load_rules(self) -> Dict[str, Any]:
        """加载全局匹配规则"""
        try:
            with open(self.rules_file, 'r', encoding='utf-8') as f:
                rules = json.load(f)
                return rules if rules else {}
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"警告: 无法加载全局匹配规则文件 {self.rules_file}: {e}")
            print("跳过全局匹配检查")
            return {}

    
    def _get_supported_extensions(self) -> Set[str]:
        """获取支持的文件扩展名"""
        return set(self.rules.get("file_extensions", [".java"]))
    
    def scan_project(self, project_path: str) -> List[Dict[str, Any]]:
        """
        扫描项目中的所有文件进行关键字匹配

        Args:
            project_path: 项目路径

        Returns:
            匹配问题列表
        """
        # 如果没有加载到规则，跳过检查
        if not self.rules:
            print("跳过全局匹配扫描（未加载到规则文件）")
            return []

        print("开始全局匹配扫描...")
        self.issues = []

        project_dir = Path(project_path)
        if not project_dir.exists():
            raise FileNotFoundError(f"项目目录不存在: {project_path}")

        # 获取所有需要扫描的文件
        files_to_scan = self._get_files_to_scan(project_dir)
        print(f"找到 {len(files_to_scan)} 个文件需要扫描")

        # 扫描每个文件
        for file_path in files_to_scan:
            self._scan_file(file_path)

        print(f"全局匹配扫描完成，发现 {len(self.issues)} 个匹配项")
        return self.issues
    
    def _get_files_to_scan(self, project_dir: Path) -> List[str]:
        """
        获取需要扫描的文件列表
        
        Args:
            project_dir: 项目目录
            
        Returns:
            文件路径列表
        """
        files_to_scan = []
        exclude_dirs = set(self.rules.get("exclude_directories", []))
        
        for file_path in project_dir.rglob("*"):
            if file_path.is_file():
                # 检查是否在排除目录中
                if any(exclude_dir in file_path.parts for exclude_dir in exclude_dirs):
                    continue
                
                # 检查文件扩展名
                if file_path.suffix in self.supported_extensions:
                    files_to_scan.append(str(file_path))
        
        return files_to_scan
    
    def _scan_file(self, file_path: str) -> None:
        """
        扫描单个文件

        Args:
            file_path: 文件路径
        """
        try:
            with open(file_path, 'r', encoding='utf-8-sig', errors='ignore') as f:
                content = f.read()

            # 移除可能的BOM字符
            if content.startswith('\ufeff'):
                content = content[1:]

            lines = content.split('\n')

            # 对每一行进行关键字匹配（保持向后兼容）
            for line_number, line in enumerate(lines, 1):
                self._check_line_for_keywords(file_path, line_number, line.rstrip())

            # 对整个文件内容进行跨行匹配
            self._check_content_for_cross_line_keywords(file_path, content, lines)

        except Exception as e:
            print(f"警告: 无法读取文件 {file_path}: {str(e)}")

    def _check_content_for_cross_line_keywords(self, file_path: str, content: str, lines: List[str]) -> None:
        """
        检查文件内容是否包含跨行关键字

        Args:
            file_path: 文件路径
            content: 文件完整内容
            lines: 文件行列表
        """
        import re

        keywords_config = self.rules.get("keywords", {})

        for category, config in keywords_config.items():
            severity = config.get("severity", "info")
            keywords = config.get("keywords", [])
            case_sensitive = config.get("case_sensitive", True)
            cross_line = config.get("cross_line", False)  # 新增配置项
            suggest = config.get("suggest", "")  # 获取建议信息
            problemtype = config.get("problemtype", "")  # 获取问题类型

            # 只处理启用了跨行匹配的关键字
            if not cross_line:
                continue

            for keyword in keywords:
                # 直接在原始内容中进行跨行匹配
                flags = re.MULTILINE | re.DOTALL
                if not case_sensitive:
                    flags |= re.IGNORECASE

                # 构建更智能的正则表达式模式
                pattern = self._build_keyword_pattern(keyword)

                # 查找所有匹配
                for match in re.finditer(pattern, content, flags):
                    match_start = match.start()
                    match_end = match.end()

                    # 计算匹配位置的行号
                    lines_before_match = content[:match_start].count('\n')
                    lines_in_match = content[match_start:match_end].count('\n')

                    start_line = lines_before_match + 1
                    end_line = lines_before_match + lines_in_match + 1

                    # 提取实际匹配的内容用于显示
                    match_content = match.group(0).strip()

                    # 检查是否在注释或字符串中（简单检查）
                    if self._is_in_comment_or_string(content, match_start, match_end):
                        continue

                    # 特殊处理注解：如果匹配内容是注解且只在单行，则只返回单行行号
                    is_annotation = keyword.startswith('@') or match_content.strip().startswith('@')
                    is_single_line = start_line == end_line

                    if is_annotation and is_single_line:
                        # 对于单行注解，只返回精确的行号
                        self.issues.append({
                            "file": file_path,
                            "type": "global_match",
                            "category": category,
                            "severity": severity,
                            "message": f"发现关键字 '{keyword}' (行: {start_line})",
                            "line": start_line,
                            "keyword": keyword,
                            "match_content": match_content,
                            "suggest": suggest,  # 添加建议信息
                            "error_type": category,  # 使用 category 作为 error_type
                            "error_code": problemtype,  # 使用 problemtype 作为 error_code
                            "problemtype": problemtype,  # 保留原字段
                            "cross_line": False,  # 注解标记为非跨行
                            "annotation_precise_line": True  # 标记为注解精确行号
                        })
                    else:
                        # 对于非注解或跨行内容，保持原有逻辑
                        self.issues.append({
                            "file": file_path,
                            "type": "global_match",
                            "category": category,
                            "severity": severity,
                            "message": f"发现关键字 '{keyword}' (行: {start_line}-{end_line})",
                            "line": start_line,
                            "end_line": end_line,
                            "keyword": keyword,
                            "match_content": match_content,
                            "suggest": suggest,  # 添加建议信息
                            "error_type": category,  # 使用 category 作为 error_type
                            "error_code": problemtype,  # 使用 problemtype 作为 error_code
                            "problemtype": problemtype,  # 保留原字段
                            "cross_line": True
                        })

    def _remove_comments_and_strings(self, content: str) -> str:
        """
        移除代码中的注释和字符串字面量，避免在注释或字符串中误匹配

        Args:
            content: 源代码内容

        Returns:
            清理后的代码
        """
        import re

        # 简单的清理：移除单行注释、多行注释和字符串字面量
        # 注意：这是一个简化的实现，完整的解析需要更复杂的词法分析

        # 移除多行注释 /* ... */
        content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)

        # 移除单行注释 //
        content = re.sub(r'//.*?$', '', content, flags=re.MULTILINE)

        # 移除字符串字面量 "..." 和 '...'
        content = re.sub(r'"(?:[^"\\]|\\.)*"', '""', content)
        content = re.sub(r"'(?:[^'\\]|\\.)*'", "''", content)

        return content

    def _is_in_comment_or_string(self, content: str, start: int, end: int) -> bool:
        """
        检查匹配位置是否在注释或字符串字面量中

        Args:
            content: 源代码内容
            start: 匹配开始位置
            end: 匹配结束位置

        Returns:
            True if the match is inside a comment or string literal
        """
        import re

        # 简单的检查：查找匹配位置前后的注释和字符串
        # 这是一个简化的实现，完整的解析需要更复杂的词法分析

        # 检查是否在多行注释中 /* ... */
        before_text = content[:start]
        after_text = content[end:]

        # 查找最近的多行注释开始和结束
        last_comment_start = before_text.rfind('/*')
        last_comment_end = before_text.rfind('*/')

        if last_comment_start > last_comment_end:
            # 在多行注释中
            next_comment_end = after_text.find('*/')
            if next_comment_end != -1:
                return True

        # 检查是否在单行注释中
        # 找到匹配位置所在的行
        line_start = content.rfind('\n', 0, start) + 1
        line_end = content.find('\n', end)
        if line_end == -1:
            line_end = len(content)

        line_content = content[line_start:line_end]
        comment_pos = line_content.find('//')
        if comment_pos != -1:
            match_pos_in_line = start - line_start
            if match_pos_in_line >= comment_pos:
                return True

        # 检查是否在字符串字面量中（简化检查）
        # 这个检查不够完善，但对于大多数情况足够了
        quote_count_double = before_text.count('"') - before_text.count('\\"')
        quote_count_single = before_text.count("'") - before_text.count("\\'")

        if quote_count_double % 2 == 1 or quote_count_single % 2 == 1:
            return True

        return False

    def _build_keyword_pattern(self, keyword: str) -> str:
        """
        构建智能的关键字匹配模式，支持极端跨行情况

        Args:
            keyword: 要匹配的关键字

        Returns:
            正则表达式模式字符串
        """
        import re

        # 特殊处理注解：对于@开头的关键字，使用精确匹配
        if keyword.startswith('@'):
            # 对于注解，使用精确匹配，不允许@和注解名之间有空白
            return re.escape(keyword)

        # 特殊处理：将关键字按照非字母数字字符分割，然后用更灵活的匹配连接
        # 这样可以处理极端的跨行情况
        parts = re.split(r'(\W+)', keyword)  # 保留分隔符

        pattern_parts = []
        for i, part in enumerate(parts):
            if not part:  # 跳过空字符串
                continue

            if re.match(r'\w+', part):  # 字母数字部分
                escaped_part = re.escape(part)
                pattern_parts.append(escaped_part)
            else:  # 非字母数字部分（空格、标点等）
                if re.match(r'\s+', part):  # 空白字符
                    # 使用非常灵活的空白匹配，允许任意数量的空白字符、换行符、注释等
                    pattern_parts.append(r'[\s\n]*(?:/\*.*?\*/[\s\n]*|//.*?[\n\r][\s\n]*)*[\s\n]*')
                else:  # 其他标点符号
                    escaped_part = re.escape(part)
                    # 在标点符号前后也允许空白字符
                    pattern_parts.append(r'[\s\n]*' + escaped_part + r'[\s\n]*')

        # 组合所有部分
        pattern = ''.join(pattern_parts)

        # 智能添加单词边界
        start_needs_boundary = keyword and keyword[0].isalnum()
        end_needs_boundary = keyword and keyword[-1].isalnum()

        if start_needs_boundary:
            pattern = r'\b' + pattern
        if end_needs_boundary:
            pattern = pattern + r'\b'

        return pattern

    def _check_line_for_keywords(self, file_path: str, line_number: int, line_content: str) -> None:
        """
        检查行内容是否包含关键字
        
        Args:
            file_path: 文件路径
            line_number: 行号
            line_content: 行内容
        """
        keywords_config = self.rules.get("keywords", {})
        
        for category, config in keywords_config.items():
            severity = config.get("severity", "info")
            keywords = config.get("keywords", [])
            case_sensitive = config.get("case_sensitive", True)
            cross_line = config.get("cross_line", False)
            suggest = config.get("suggest", "")  # 获取建议信息
            problemtype = config.get("problemtype", "")  # 获取问题类型

            # 如果启用了跨行匹配，跳过单行匹配避免重复
            # 注意：跨行匹配逻辑应该能够处理单行情况，所以这里跳过单行匹配
            if cross_line:
                continue

            for keyword in keywords:
                if self._line_contains_keyword(line_content, keyword, case_sensitive):
                    self.issues.append({
                        "file": file_path,
                        "type": "global_match",
                        "category": category,
                        "severity": severity,
                        "keyword": keyword,
                        "message": f"发现关键字 '{keyword}' (类别: {category})",
                        "line": line_number,
                        "line_content": line_content.strip(),
                        "match_type": "exact" if case_sensitive else "case_insensitive",
                        "suggest": suggest,  # 添加建议信息
                        "error_type": category,  # 使用 category 作为 error_type
                        "error_code": problemtype,  # 使用 problemtype 作为 error_code
                        "problemtype": problemtype,  # 保留原字段
                        "cross_line": False
                    })
    
    def _line_contains_keyword(self, line_content: str, keyword: str, case_sensitive: bool = True) -> bool:
        """
        检查行内容是否包含关键字
        
        Args:
            line_content: 行内容
            keyword: 关键字
            case_sensitive: 是否大小写敏感
            
        Returns:
            是否包含关键字
        """
        if case_sensitive:
            return keyword in line_content
        else:
            return keyword.lower() in line_content.lower()
    
    def search_custom_keywords(self, project_path: str, custom_keywords: List[str], 
                             case_sensitive: bool = True) -> List[Dict[str, Any]]:
        """
        使用自定义关键字进行搜索
        
        Args:
            project_path: 项目路径
            custom_keywords: 自定义关键字列表
            case_sensitive: 是否大小写敏感
            
        Returns:
            匹配结果列表
        """
        print(f"开始自定义关键字搜索: {custom_keywords}")
        custom_issues = []
        
        project_dir = Path(project_path)
        if not project_dir.exists():
            raise FileNotFoundError(f"项目目录不存在: {project_path}")
        
        # 获取所有需要扫描的文件
        files_to_scan = self._get_files_to_scan(project_dir)
        
        # 扫描每个文件
        for file_path in files_to_scan:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                
                # 对每一行进行关键字匹配
                for line_number, line in enumerate(lines, 1):
                    line_content = line.rstrip()
                    
                    for keyword in custom_keywords:
                        if self._line_contains_keyword(line_content, keyword, case_sensitive):
                            custom_issues.append({
                                "file": file_path,
                                "type": "custom_search",
                                "category": "custom",
                                "severity": "info",
                                "keyword": keyword,
                                "message": f"找到自定义关键字 '{keyword}'",
                                "line": line_number,
                                "line_content": line_content.strip(),
                                "match_type": "exact" if case_sensitive else "case_insensitive"
                            })
                            
            except Exception as e:
                print(f"警告: 无法读取文件 {file_path}: {str(e)}")
        
        print(f"自定义关键字搜索完成，找到 {len(custom_issues)} 个匹配项")
        return custom_issues
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取匹配统计信息
        
        Returns:
            统计信息字典
        """
        stats = {
            "total_matches": len(self.issues),
            "by_category": {},
            "by_severity": {},
            "by_file_extension": {},
            "top_keywords": {}
        }
        
        # 按类别统计
        for issue in self.issues:
            category = issue.get("category", "unknown")
            stats["by_category"][category] = stats["by_category"].get(category, 0) + 1
        
        # 按严重程度统计
        for issue in self.issues:
            severity = issue.get("severity", "unknown")
            stats["by_severity"][severity] = stats["by_severity"].get(severity, 0) + 1
        
        # 按文件扩展名统计
        for issue in self.issues:
            file_path = issue.get("file", "")
            ext = Path(file_path).suffix if file_path else "unknown"
            stats["by_file_extension"][ext] = stats["by_file_extension"].get(ext, 0) + 1
        
        # 关键字频率统计
        for issue in self.issues:
            keyword = issue.get("keyword", "")
            stats["top_keywords"][keyword] = stats["top_keywords"].get(keyword, 0) + 1
        
        return stats
    
    def export_issues(self, output_file: str = "global_match_issues.json") -> None:
        """
        导出全局匹配问题到文件
        
        Args:
            output_file: 输出文件路径
        """
        print(f"导出全局匹配问题到文件: {output_file}")
        
        # 获取统计信息
        stats = self.get_statistics()
        
        # 构建输出数据
        output_data = {
            "scan_info": {
                "timestamp": datetime.now().isoformat(),
                "total_matches": len(self.issues),
                "scan_type": "global_match",
                "statistics": stats
            },
            "matches": self.issues
        }
        
        # 写入JSON文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        print(f"全局匹配检查完成！结果已保存到 {output_file}")
        print(f"匹配统计:")
        print(f"  - 总匹配数: {len(self.issues)}")
        for category, count in stats["by_category"].items():
            print(f"  - {category}: {count}")
        for severity, count in stats["by_severity"].items():
            print(f"  - {severity}: {count}")
