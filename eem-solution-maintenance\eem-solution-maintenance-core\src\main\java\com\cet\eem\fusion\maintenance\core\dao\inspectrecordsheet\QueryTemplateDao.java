package com.cet.eem.fusion.maintenance.core.dao.inspectrecordsheet;

import com.cet.eem.bll.maintenance.model.workorder.inspection.recordsheet.QueryTemplate;
import com.cet.eem.bll.maintenance.model.workorder.inspection.recordsheet.QueryTemplateWithLayer;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;

import java.util.Collection;
import java.util.List;

/**
 * @ClassName : QueryTemplateDao
 * @Description : 模板表的dao
 * <AUTHOR> jiangzixuan
 * @Date: 2022-10-13 16:27
 */
public interface QueryTemplateDao extends BaseModelDao<QueryTemplate> {
    /**
     * 分系统查询全部模板
     * @param projectId
     * @return
     */
    List<QueryTemplateWithLayer> queryTemplate(Long projectId);

    /**
     * 查询名称重复的
     * @param id
     * @param name
     * @return
     */
    List<QueryTemplate> querySameNameTemplate(Long id,String name);

    /**
     * 查询子节点下同名的
     * @param parentId
     * @param id
     * @param name
     * @return
     */
    List<QueryTemplate> queryChildrenData(Long parentId,Long id,String name);

    /**
     * 根据id列表查询模板
     * @param templateIds
     * @return
     */
    List<QueryTemplateWithLayer> queryTemplate(Collection<Long> templateIds);
}
