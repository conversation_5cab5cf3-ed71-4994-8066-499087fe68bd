package com.cet.eem.fusion.maintenance.core.schedule.job;

import com.cet.eem.bll.common.task.TaskSchedule;
import com.cet.eem.bll.maintenance.service.repair.RepairWorkOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/5/16
 */
@Component
@Slf4j
public class CheckRepairWorkOrderOverTimeSchedule implements TaskSchedule {
    @Autowired
    RepairWorkOrderService repairWorkOrderService;

    /**
     * 判断时间分析是否在运行
     */
    private boolean isPecCoreRunning = false;


    @Scheduled(cron = "${cet.eem.work-order.repair.check-over-time.interval}")
    @Override
    public void execute() {
        if (isPecCoreRunning) {
            return;
        }

        isPecCoreRunning = true;
        try {
            repairWorkOrderService.updateOverTimeStatus();
        } catch (Exception ex) {
            log.error("检查维修工单是否处于异常状态任务发生异常:", ex);
        }

        isPecCoreRunning = false;
    }
}
