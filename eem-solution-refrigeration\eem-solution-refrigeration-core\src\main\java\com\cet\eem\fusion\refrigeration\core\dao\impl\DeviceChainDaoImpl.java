﻿package com.cet.eem.fusion.refrigeration.core.impl;

import com.cet.eem.bll.common.model.domain.subject.energysaving.DeviceChain;
import com.cet.eem.bll.common.model.ext.subject.energysaving.DeviceChainWithSubLayer;
import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.fusion.refrigeration.core.dao.weather.DeviceChainDao;
import com.cet.eem.fusion.refrigeration.core.model.config.ParameterConfig;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;

/**
 * @ClassName : DeviceChainDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-17 16:34
 */
@Repository
public class DeviceChainDaoImpl extends ModelDaoImpl<DeviceChain> implements DeviceChainDao {

    @Override
    public List<DeviceChain> queryDeviceChain(Long roomId,Long projectId) {
        LambdaQueryWrapper<DeviceChain> wrapper = LambdaQueryWrapper.of(DeviceChain.class);
        wrapper.eq(DeviceChain::getRoomId, roomId)
                .eq(DeviceChain::getProjectId, projectId);
        return this.selectList(wrapper);
    }

    @Override
    public List<DeviceChainWithSubLayer> queryDeviceChainWithDetail(List<Long> chainIds,Long projectId) {
        LambdaQueryWrapper<DeviceChain> wrapper = LambdaQueryWrapper.of(DeviceChain.class);
        wrapper.in(DeviceChain::getId, chainIds)
                .eq(DeviceChain::getProjectId,projectId );
        return this.selectRelatedList(DeviceChainWithSubLayer.class, wrapper);
    }

    @Override
    public DeviceChain queryDeivceChain(Long roomId, String name, Long chainId,Long projectId) {
        LambdaQueryWrapper<DeviceChain> wrapper = LambdaQueryWrapper.of(DeviceChain.class);
        wrapper.eq(DeviceChain::getRoomId, roomId)
                .eq(DeviceChain::getProjectId, projectId)
                .eq(DeviceChain::getName, name);
        if (Objects.nonNull(chainId)) {
            wrapper.ne(DeviceChain::getId, chainId);
        }
        List<DeviceChain> deviceChains = this.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(deviceChains)) {
            return deviceChains.get(0);
        }
        return null;
    }

    @Override
    public List<DeviceChainWithSubLayer> queryDeviceChainDetail(List<Long> roomIds) {
        LambdaQueryWrapper<DeviceChain> wrapper = LambdaQueryWrapper.of(DeviceChain.class);
        wrapper.in(DeviceChain::getRoomId, roomIds);
        return this.selectRelatedList(DeviceChainWithSubLayer.class, wrapper);
    }
}
