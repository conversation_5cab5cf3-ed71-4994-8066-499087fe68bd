package com.cet.eem.solution.common.entity.vo.cetml;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 机采优化-模型训练返回参数
 *
 * <AUTHOR>
 * @date 2023/12/22 11:13
 */
@Data
public class CetmlModelTrainVO {
    @JsonProperty("model_path")
    @ApiModelProperty("训练后模型地址")
    public String modelPath;

    @ApiModelProperty("冲次相关参数")
    public Double impacttimes;

    @ApiModelProperty("平衡度相关参数")
    public Double unbalance;

    @ApiModelProperty("冲程相关参数")
    public Double stroke;

    @ApiModelProperty("最大载荷相关参数")
    @JsonProperty("max_load")
    public Double maxLoad;

    @ApiModelProperty("最小载荷相关参数")
    @JsonProperty("min_load")
    public Double minLoad;

    @ApiModelProperty("油压相关参数")
    @JsonProperty("oil_pressure")
    public Double oilPressure;

    @ApiModelProperty("套压相关参数")
    @JsonProperty("casing_pressure")
    public Double casingPressure;

    @ApiModelProperty("能耗相关参数")
    public Double energyconsumption;

    public Double getValueByKey(String key) {
        switch (key) {
            case "impacttimes":
                return this.impacttimes;
            case "unbalance":
                return this.unbalance;
            case "stroke":
                return this.stroke;
            case "max_load":
                return this.maxLoad;
            case "min_load":
                return this.minLoad;
            case "oil_pressure":
                return this.oilPressure;
            case "casing_pressure":
                return this.casingPressure;
            case "energyconsumption":
                return this.energyconsumption;
            default:
                return 0D;
        }
    }
}