package com.cet.eem.fusion.maintenance.core.service.inspection.app;

import com.cet.eem.bll.maintenance.model.wo.MobileWorkOrderQueryVO;
import com.cet.eem.bll.maintenance.model.workorder.WoStatusCountDTO;
import com.cet.eem.bll.maintenance.model.workorder.app.InspectionWorkOrderDetailDto;
import com.cet.eem.bll.maintenance.model.workorder.app.SignPointWithWorkOrder;
import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.fusion.common.model.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/20
 */
public interface InspectionWorkOrderMobileService {
    /**
     * 查询巡检工单首页详情
     *
     * @return
     */
    InspectionWorkOrderDetailDto queryWorkOrderDetail(Page page);

    /**
     * 根据工单状态查询工单数量
     *
     * @return
     */
    List<WoStatusCountDTO> queryWoStatusCount(Long userId);

    List<InspectionWorkOrderDto> queryWorkOrderList(MobileWorkOrderQueryVO query);

    List<SignPointWithWorkOrder> querySignPoint(Long userId);
}
