package com.cet.eem.fusion.maintenance.core.schedule.event;

import org.springframework.context.ApplicationEvent;

/**
 * @ClassName : CheckInspectionPlanIsOverEvent
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-05-07 08:35
 */
public class CheckPlanIsOverEvent extends ApplicationEvent {

    public CheckPlanIsOverEvent(CheckInspectionPlanIsOverCommand checkInspectionPlanCommand) {
        super(checkInspectionPlanCommand);
    }

    public CheckInspectionPlanIsOverCommand getCommand() {
        return (CheckInspectionPlanIsOverCommand) getSource();
    }
}
