Result.SUCCESS_CODE
ErrorCode.SUCCESS_CODE

EemCommonUtils.BLANK_STR
StringFormatUtils.BLANK_STR

CommonUtils.BLANK_STR
StringFormatUtils.BLANK_STR

CommonUtils.APPLICATION_MSEXCEL
ContentTypeDef.APPLICATION_MSEXCEL

CommonUtils.APPLICATION_MS_EXCEL_07
ContentTypeDef.APPLICATION_MS_EXCEL_07

CommonUtils.DOUBLE_CONVERSION_COEFFICIENT
NumberCalcUtils.DOUBLE_CONVERSION_COEFFICIENT

extends BaseEntity {
extends EntityWithName {

EemCommonUtils.PRECISION_2
StringFormatUtils.PRECISION_2

GlobalInfoUtils.getProjectId(
GlobalInfoUtils.getTenantId(

CommonUtils.calcDouble(
NumberCalcUtils.calcDouble(

CommonUtils.parseInteger(
NumberUtils.parseInteger(

CommonUtils.formatDoubleWithOutScientificNotation(
StringFormatUtils.formatDoubleWithOutScientificNotation(

EemCommonUtils.formatDouble(
StringFormatUtils.formatDouble(

new QueryConditionBuilder<>
ParentQueryConditionBuilder.of

new QueryConditionBuilder<BaseEntity>(
ParentQueryConditionBuilder.of(

new QueryConditionBuilder(
ParentQueryConditionBuilder.of(

CommonUtils.getMax(
NumberUtils.getMax(

@ModelLabel(TableNameDef.
@ModelLabel(ModelLabelDef.

public Result<
public ApiResult<

this.modelLabel = TableNameDef.
this.modelLabel = ModelLabelDef.

import com.cet.eem.common.model.BaseVo;
import com.cet.eem.fusion.common.model.BaseVo;

import com.cet.eem.fusion.common.util.GlobalInfoUtils;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;

import com.cet.eem.auth.aspect.EnumAndOr;
import com.cet.electric.matterhorn.cloud.authservice.sdk.common.enums.EnumAndOr;

import com.cet.eem.auth.aspect.OperationPermission;
import com.cet.electric.matterhorn.cloud.authservice.sdk.common.annotation.OperationPermission;

import com.cet.eem.bll.common.def.OperationAuthDef;
import com.cet.eem.fusion.common.def.OperationAuthDef;

import com.cet.piem.common.constant.TableNameDef;
import com.cet.eem.solution.common.def.common.label.ModelLabelDef;

import com.cet.piem.common.constant.TableColumnNameDef;
import com.cet.eem.solution.common.def.common.label.TableColumnNameDef;

import com.cet.eem.common.service.RedisService;
import com.cet.eem.fusion.common.service.RedisService;

import com.cet.eem.common.model.event.RedisEventKey;
import com.cet.eem.fusion.common.model.event.RedisEventKey;

import com.cet.eem.bll.common.util.DataValidationUtils;
import com.cet.eem.fusion.energy.sdk.util.DataValidationUtils;

import com.cet.eem.bll.common.model.node.relations.EnergySupplyToPo;
import com.cet.electric.baseconfig.common.entity.EnergySupplyTo;

import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;

import com.cet.eem.model.tool.QueryConditionBuilder;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;

import com.cet.eem.model.tool.ParentQueryConditionBuilder;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;

import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;

import com.cet.piem.common.utils.ProgressUpdaterUtils;
import com.cet.eem.solution.common.utils.ProgressUpdaterUtils;

import com.cet.piem.common.def.AggregationCycle;
import com.cet.eem.fusion.common.model.objective.physicalquantity.AggregationCycle;

import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.fusion.common.model.objective.physicalquantity.AggregationCycle;

import com.cet.eem.bll.common.service.impl.EnergySupplyService;
import com.cet.eem.fusion.energy.sdk.service.EnergySupplyService;

import com.cet.eem.common.model.realtime.RealTimeValue;
import com.cet.eem.fusion.common.model.realtime.RealTimeValue;

import com.cet.eem.quantity.model.quantity.QuantitySearchVo;
import com.cet.eem.fusion.common.model.quantity.QuantitySearchVo;

import com.cet.eem.common.exception.BusinessBaseException;
import com.cet.eem.fusion.common.exception.BusinessBaseException;

import com.cet.eem.common.utils.TimeUtil;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;

import com.cet.eem.dao.BaseModelDao;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;

import com.cet.eem.quantity.dao.QuantityAggregationDataDao;
import com.cet.eem.fusion.energy.sdk.dao.QuantityAggregationDataDao;

import com.cet.piem.common.utils.DoubleUtils;
import com.cet.eem.solution.common.utils.DoubleUtils;

import com.cet.eem.bll.common.model.domain.object.event.SystemEvent;
import com.cet.electric.modelsdk.event.model.SystemEvent;

import com.cet.piem.service.feign.PiemConfigServerService;
import com.cet.eem.solution.common.feign.ConfigServerService;

import com.cet.eem.bll.common.dao.project.ProductDao;
import com.cet.eem.fusion.config.sdk.dao.ProductDao;

import com.cet.eem.common.CommonUtils;
import com.cet.eem.fusion.common.utils.CommonUtils;

import com.cet.eem.common.definition.LoginDef;
import com.cet.eem.fusion.common.def.auth.LoginDef;

import com.cet.eem.common.definition.exception.WorkOrderErrorCodeEnum;
import com.cet.eem.fusion.common.def.exception.WorkOrderErrorCodeEnum;

import com.cet.eem.common.constant.EnumDataTypeId;
import com.cet.eem.fusion.common.def.base.EnumDataTypeId;

import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.fusion.common.def.common.ColumnDef;

import com.cet.eem.common.constant.EnumOperationType;
import com.cet.eem.fusion.common.def.common.EnumOperationType;

import com.cet.eem.bll.common.def.quantity.PhasorDef;
import com.cet.eem.fusion.common.def.quantity.PhasorDef;

import com.cet.eem.bll.common.def.quantity.FrequencyDef;
import com.cet.eem.fusion.common.def.quantity.FrequencyDef;

import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;
import com.cet.eem.fusion.common.def.quantity.QuantityCategoryDef;

import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;
import com.cet.eem.fusion.common.def.quantity.QuantityTypeDef;

import com.cet.eem.common.model.Page;
import com.cet.eem.fusion.common.model.Page;

import com.cet.eem.common.page.PageUtils;
import com.cet.eem.fusion.common.utils.page.PageUtils;

import com.cet.eem.model.base.Order;
import com.cet.eem.fusion.common.modelutils.model.base.Order;

import com.cet.eem.common.file.FileUtils;
import com.cet.eem.fusion.common.utils.file.FileUtils;

import com.cet.eem.common.model.auth.user.UserVo;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;

import com.cet.eem.bll.common.model.domain.object.physicalquantity.ProjectUnitClassify;
import com.cet.eem.fusion.common.def.base.ProjectUnitClassify;

import com.cet.eem.bll.common.model.domain.object.physicalquantity.UserDefineUnit;
import com.cet.electric.baseconfig.common.entity.UserDefineUnit;

import com.cet.eem.bll.common.model.peccore.PecCoreTreeSearchVo;
import com.cet.eem.fusion.config.sdk.entity.peccore.PecCoreTreeSearchVo;

import com.cet.eem.common.constant.EnumSystemEventType;
import com.cet.eem.fusion.common.def.energy.EnumSystemEventType;

import com.cet.eem.common.model.peccore.Meter;
import com.cet.eem.fusion.common.model.peccore.Meter;

import com.cet.eem.bll.common.log.annotation.OperationLog;
import com.cet.eem.fusion.config.sdk.service.log.OperationLog;

import com.cet.eem.bll.common.log.constant.EEMOperationLogType;
import com.cet.eem.fusion.config.sdk.def.OperationLogType;

import com.cet.eem.bll.common.log.constant.EnumOperationSubType;
import com.cet.eem.fusion.common.utils.EnumOperationSubType;

import com.cet.eem.model.tool.QueryResultContentTaker;
import com.cet.eem.fusion.common.modelutils.model.tool.QueryResultContentTaker;

import com.cet.eem.common.constant.PecsNodeType;
import com.cet.eem.fusion.common.def.pec.PecsNodeType;

import com.cet.eem.bll.common.def.MessageTypeDef;
import com.cet.eem.fusion.common.def.MessageTypeDef;

import com.cet.eem.common.utils.PoiExcelUtils;
import com.cet.eem.fusion.common.utils.excel.PoiExcelUtils;

import com.cet.eem.common.constant.ExcelType;
import com.cet.eem.fusion.common.def.base.ExcelType;

import com.cet.eem.common.ErrorUtils;
import com.cet.eem.fusion.common.utils.ErrorUtils;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;

import com.cet.eem.common.definition.SplitCharDef;
import com.cet.eem.fusion.common.def.common.SplitCharDef;

import com.cet.eem.common.constant.EnumOperationType;
import com.cet.eem.fusion.common.def.common.EnumOperationType;

import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityAggregationData;
import com.cet.electric.modelsdk.quantity.model.QuantityAggregationData;

import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityObject;
import com.cet.electric.baseconfig.common.entity.QuantityObject;

import com.cet.eem.auth.service.InnerAuthService;
import com.cet.electric.matterhorn.cloud.authservice.api.UserRestApi;

import com.cet.eem.node.TopologyUtils;
import com.cet.eem.fusion.common.utils.excel.PoiExcelUtils;

import com.cet.eem.bll.common.util.ExcelValidationUtils;
import com.cet.eem.fusion.common.utils.excel.ExcelValidationUtils;

import com.cet.eem.bll.common.dao.node.NodeDao;
import com.cet.eem.fusion.config.sdk.service.EemNodeService;

import com.cet.eem.common.ParamUtils;
import com.cet.eem.fusion.common.utils.ParamUtils;

import com.cet.eem.bll.common.dao.poi.EemPoiRecordDao;
import com.cet.eem.fusion.energy.sdk.dao.EemPoiRecordDao;

import com.cet.eem.bll.common.model.domain.perception.logicaldevice.EemPoiRecord;
import com.cet.eem.fusion.energy.sdk.model.EemPoiRecord;

import com.cet.eem.common.constant.QueryType;
import com.cet.eem.fusion.common.def.base.QueryType;

import com.cet.eem.bll.common.util.EnergyExportUtils;
import com.cet.eem.fusion.energy.sdk.util.EnergyExportUtils;

import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;

import com.cet.eem.model.tool.SubConditionBuilder;
import com.cet.eem.fusion.common.modelutils.model.tool.SubConditionBuilder;

import com.cet.eem.common.exception.ValidationException;
import com.cet.eem.fusion.common.exception.ValidationException;

import com.cet.eem.dao.ModelDaoImpl;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;

import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;

import com.cet.eem.model.base.ModelSingeWriteVo;
import com.cet.eem.fusion.common.modelutils.model.base.ModelSingeWriteVo;

import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;

import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.fusion.common.def.label.NodeLabelDef;

import com.cet.eem.common.constant.AggregationType;
import com.cet.eem.fusion.energy.sdk.def.AggregationType;

import com.cet.eem.common.constant.EnumRoomType;
import com.cet.eem.fusion.common.def.base.EnumRoomType;

import com.cet.eem.common.constant.EnergyTypeDef;
import com.cet.eem.fusion.common.def.base.EnergyTypeDef;

import com.cet.eem.conditions.update.LambdaUpdateWrapper;
import com.cet.eem.fusion.common.modelutils.conditions.update.LambdaUpdateWrapper;

import com.cet.eem.bll.common.model.domain.subject.generalrules.UnnaturalSetVo;
import com.cet.eem.fusion.config.sdk.entity.unnatural.UnnaturalSetVo;

import com.cet.eem.bll.common.model.domain.subject.generalrules.FeeScheme;
import com.cet.eem.fusion.energy.sdk.model.generalrules.FeeScheme;

import com.cet.eem.bll.common.model.domain.object.architecture.RoomVo;
import com.cet.eem.fusion.config.sdk.model.node.RoomVo;

import com.cet.eem.bll.common.model.domain.object.architecture.BuildingVo;
import com.cet.eem.fusion.config.sdk.model.node.BuildingVo;

import com.cet.eem.bll.energy.dao.SystemEventDao;
import com.cet.eem.fusion.energy.sdk.dao.SystemEventDao;

import com.cet.eem.bll.common.model.domain.subject.energy.EnergyConsumption;
import com.cet.eem.fusion.energy.sdk.model.EnergyConsumption;

import com.cet.eem.bll.common.config.EnergyConsumptionConfig;
import com.cet.eem.fusion.energy.sdk.config.EnergyConsumptionConfig;

import com.cet.eem.common.utils.performance.annotation.ExecuteIndexAnnotation;
import com.cet.eem.fusion.common.utils.performance.annotation.ExecuteIndexAnnotation;

import com.cet.eem.common.constant.QueryType;
import com.cet.eem.fusion.common.def.base.QueryType;

import com.cet.eem.bll.common.model.CompareResult;
import com.cet.eem.fusion.energy.sdk.model.result.CompareResult;

import com.cet.eem.common.model.Result;
import com.cet.electric.commons.ApiResult;

import com.cet.eem.common.model.ResultWithTotal;
import com.cet.electric.commons.ApiResult;

import com.cet.eem.bll.common.util.DataCompareUtils;
import com.cet.eem.fusion.energy.sdk.util.DataCompareUtils;

import com.cet.eem.common.model.datalog.DataLogData;
import com.cet.eem.fusion.common.model.datalog.DataLogData;

import com.cet.eem.bll.energy.dao.consumption.EnergyConsumptionDao;
import com.cet.eem.fusion.energy.sdk.dao.energy.EnergyConsumptionDao;

import com.cet.eem.bll.common.dao.poi.EemPoiRecordDao;
import com.cet.eem.fusion.energy.sdk.dao.EemPoiRecordDao;

import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;

import com.cet.eem.bll.common.model.topology.vo.PointNode;
import com.cet.eem.fusion.common.model.topology.bo.PointNode;

import com.cet.eem.bll.common.model.domain.object.entitymap.PipeNetworkConnectionModel;
import com.cet.electric.baseconfig.common.entity.PipeNetworkConnectionModel;

import com.cet.eem.bll.common.model.topology.vo.LinkNode;
import com.cet.eem.fusion.common.model.topology.bo.LinkNode;

import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.fusion.common.model.datalog.TrendDataVo;

import com.cet.eem.model.base.ConditionBlock;
import com.cet.eem.fusion.common.modelutils.model.base.ConditionBlock;

import com.cet.eem.model.model.BaseEntity;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;

import com.cet.eem.quantity.dao.QuantityAggregationDataDao;
import com.cet.eem.fusion.energy.sdk.dao.QuantityAggregationDataService;

import com.cet.eem.common.constant.PoiTypeEnum;
import com.cet.eem.fusion.common.def.base.PoiTypeEnum;

import com.cet.eem.common.model.AggregationResult;
import com.cet.eem.fusion.common.model.AggregationResult;

import com.cet.eem.toolkit.CollectionUtils;
import org.apache.commons.collections4.CollectionUtils;

import com.cet.eem.bll.common.model.domain.object.organization.Project;
import com.cet.electric.baseconfig.common.entity.Project;

import com.cet.eem.bll.common.service.UnnaturalTimeService;
import com.cet.eem.fusion.config.sdk.service.UnnaturalTimeService;

import com.cet.eem.common.model.auth.user.RoleVo;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.RoleVo;