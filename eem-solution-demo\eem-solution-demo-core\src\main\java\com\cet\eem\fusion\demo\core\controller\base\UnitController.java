package com.cet.eem.fusion.demo.core.controller.base;

import com.cet.eem.fusion.config.sdk.controller.BaseEemUnitController;
import com.cet.eem.solution.common.def.common.PluginInfoDef;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>  (2025/8/26 9:59)
 */
@Api(value = PluginInfoDef.Demo.INTERFACE_PREFIX + "/v1/unit", tags = "单位")
@RequestMapping(value = PluginInfoDef.Demo.INTERFACE_PREFIX + "/v1/unit")
@RestController
@Validated
public class UnitController extends BaseEemUnitController {
}
