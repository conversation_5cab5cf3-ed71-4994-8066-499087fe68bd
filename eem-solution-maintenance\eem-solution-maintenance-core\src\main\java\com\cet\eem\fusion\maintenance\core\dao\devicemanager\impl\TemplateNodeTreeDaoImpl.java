package com.cet.eem.fusion.maintenance.core.dao.devicemanager.impl;

import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.bll.maintenance.dao.devicemanager.TemplateNodeTreeDao;
import com.cet.eem.bll.maintenance.model.devicemanage.template.*;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-11
 */
@Service
public class TemplateNodeTreeDaoImpl extends ModelDaoImpl<EquipmentNodeTreeDto> implements TemplateNodeTreeDao {
    @Override
    public List<NodeWithTemplate> queryNodeTemplate(List<Long> ids) {
        LambdaQueryWrapper<EquipmentNodeTreeDto> queryWrapper = LambdaQueryWrapper.of(EquipmentNodeTreeDto.class)
                .in(EquipmentNodeTreeDto::getId, ids);
        return this.selectRelatedList(NodeWithTemplate.class, queryWrapper,
                Arrays.asList(LambdaQueryWrapper.of(AttributeTemplate.class)));
    }

    @Override
    public List<NodeWithTemplate> queryNodeWithTemplates(Long id) {
        LambdaQueryWrapper<EquipmentNodeTreeDto> queryWrapper = LambdaQueryWrapper.of(EquipmentNodeTreeDto.class)
                .eq(EquipmentNodeTreeDto::getId, id);
        return this.selectRelatedList(NodeWithTemplate.class, queryWrapper,
                Arrays.asList(LambdaQueryWrapper.of(AttributeTemplate.class),
                        LambdaQueryWrapper.of(RunningParamGroup.class),
                        LambdaQueryWrapper.of(RunningParam.class),
                        LambdaQueryWrapper.of(TechParam.class)));
    }

    @Override
    public ResultWithTotal<List<NodeWithTemplate>> queryNodeWithTemplates(Long id, Page page) {
        ParentQueryConditionBuilder builder = ParentQueryConditionBuilder.of(ModelLabelDef.TEMPLATE_NODE_TREE)
                .eq(ColumnDef.ID, id)
                .leftJoin(ModelLabelDef.NODE_TEMPLATE)
                .leftJoin(ModelLabelDef.RUNNING_PARAM_TEMPLATE_GROUP)
                .leftJoin(ModelLabelDef.RUNNING_PARAM)
                .leftJoin(ModelLabelDef.TECH_PARAM_TEMPLATE);
        return modelServiceUtils.queryWithTotal(builder.build(), NodeWithTemplate.class);
    }

    @Override
    public NodeWithTemplate NodeWithTemplateName(AttributeTemplate template, Long parentId) {
        LambdaQueryWrapper<AttributeTemplate> queryWrapper = LambdaQueryWrapper.of(AttributeTemplate.class);
        queryWrapper.eq(AttributeTemplate::getName, template.getName());
        return this.selectRelatedById(NodeWithTemplate.class, parentId, Collections.singletonList(queryWrapper));
    }

    @Override
    public EquipmentNodeTreeDto queryNodeByIdAndName(Long id, String name) {
        LambdaQueryWrapper<EquipmentNodeTreeDto> queryWrapper = LambdaQueryWrapper.of(EquipmentNodeTreeDto.class);
        queryWrapper
                .ne(EquipmentNodeTreeDto::getId, id)
                .eq(EquipmentNodeTreeDto::getName, name);
        return this.selectOne(queryWrapper);
    }

    @Override
    public EquipmentNodeTreeDto selectByNameAndParentId(EquipmentNodeTreeDto data) {
        LambdaQueryWrapper<EquipmentNodeTreeDto> queryWrapper = LambdaQueryWrapper.of(EquipmentNodeTreeDto.class);
        queryWrapper.eq(EquipmentNodeTreeDto::getName, data.getName())
                .eq(EquipmentNodeTreeDto::getParentId, data.getParentId());
        return this.selectOne(queryWrapper);
    }

    @Override
    public List<EquipmentNodeTreeDto> selectByParentIds(List<Long> parentIds) {
        //in条件，防止parentIds为空查询整张表
        if (CollectionUtils.isEmpty(parentIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<EquipmentNodeTreeDto> queryWrapper = LambdaQueryWrapper.of(EquipmentNodeTreeDto.class);
        queryWrapper.in(EquipmentNodeTreeDto::getParentId, parentIds);
        return this.selectList(queryWrapper);
    }

    @Override
    public List<EquipmentNodeTreeDto> selectByProjectId(Long projectId) {
        LambdaQueryWrapper<EquipmentNodeTreeDto> queryWrapper = LambdaQueryWrapper.of(EquipmentNodeTreeDto.class);
        queryWrapper.eq(EquipmentNodeTreeDto::getProjectId, projectId);
        return this.selectList(queryWrapper);
    }

    @Override
    public NodeWithTemplate checkRepeat(AttributeTemplate template, Long parentId) {
        LambdaQueryWrapper<AttributeTemplate> queryWrapper = LambdaQueryWrapper.of(AttributeTemplate.class)
                .eq(AttributeTemplate::getName, template.getName())
                .ne(AttributeTemplate::getId, template.getId());
        return this.selectRelatedById(NodeWithTemplate.class, parentId, Collections.singletonList(queryWrapper));

    }

    @Override
    public List<NodeWithTemplate> queryNodeTemplateByProject(@NotNull Long projectId) {
        LambdaQueryWrapper<EquipmentNodeTreeDto> eq = LambdaQueryWrapper.of(EquipmentNodeTreeDto.class)
                .eq(EquipmentNodeTreeDto::getProjectId, GlobalInfoUtils.getTenantId());
        return this.selectRelatedList(NodeWithTemplate.class, eq);
    }

    @Override
    public EquipmentNodeTreeDto selectByNameAndParentIdAndId(EquipmentNodeTreeDto data) {
        LambdaQueryWrapper<EquipmentNodeTreeDto> queryWrapper = LambdaQueryWrapper.of(EquipmentNodeTreeDto.class);
        queryWrapper.eq(EquipmentNodeTreeDto::getName, data.getName())
                .ne(EquipmentNodeTreeDto::getId, data.getId())
                .eq(EquipmentNodeTreeDto::getParentId, data.getParentId());
        return this.selectOne(queryWrapper);
    }


}
