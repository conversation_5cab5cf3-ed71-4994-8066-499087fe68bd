package com.cet.eem.fusion.maintenance.core.entity.devicemanage;

import com.cet.eem.fusion.common.model.Page;
import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 设备信息请求，传参由前端控制
 * @date 2024/7/4 11:06
 */
@NoArgsConstructor
@Getter
public class DeviceInfoRequestControlByFront {
    /**
     * 关键字过滤
     */
    private String keyword;

    /**
     * 查询条件
     */
    private QueryCondition queryCondition;

    /**
     * 返回分页
     */
    private Page page;
}
