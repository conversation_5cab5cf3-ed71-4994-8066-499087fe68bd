package com.cet.eem.fusion.maintenance.core.controller.inspect.app;

import com.cet.eem.maintenanceservice.controller.bff.app.SignInStatisticsTableBffMobileController;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021-04-02 10:08
 */
@Api(value = "/eem/v1/mobile/signIn", tags = "APP:签到点")
@RequestMapping(value = "/eem/v1/mobile/signIn")
@RestController
@Validated
public class SignInStatisticsTableMobileController extends SignInStatisticsTableBffMobileController {

}
