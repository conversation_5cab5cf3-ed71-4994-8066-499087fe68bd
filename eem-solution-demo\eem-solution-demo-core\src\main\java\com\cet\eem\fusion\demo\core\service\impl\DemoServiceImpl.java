package com.cet.eem.fusion.demo.core.service.impl;

import com.cet.eem.fusion.demo.core.dao.DemoDao;
import com.cet.eem.fusion.demo.core.entity.bo.DemoBO;
import com.cet.eem.fusion.demo.core.entity.dto.DemoDTO;
import com.cet.eem.fusion.demo.core.entity.po.Demo;
import com.cet.eem.fusion.demo.core.entity.vo.DemoVO;
import com.cet.eem.fusion.demo.core.service.DemoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>  (2025/7/24 9:37)
 */
@Service
public class DemoServiceImpl implements DemoService {
    @Resource
    private DemoDao demoDao;

    @Override
    public DemoVO demoMethod(DemoDTO demoDTO) {
        DemoBO demoBO = new DemoBO(demoDTO);
        List<Demo> demoList = demoDao.querydemoMethod(demoBO);
        DemoVO demoVo = new DemoVO();
        demoVo.setDemoList(demoList);
        return demoVo;
    }
}
