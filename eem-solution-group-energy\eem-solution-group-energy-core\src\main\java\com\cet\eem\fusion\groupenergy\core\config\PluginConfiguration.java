package com.cet.eem.fusion.groupenergy.core.config;

import com.cet.eem.solution.common.def.common.PluginInfoDef;
import com.cet.electric.fusion.matrix.v2.client.register.JarPluginRegister;
import com.cet.electric.fusion.matrix.v2.dto.business.PluginRuntimeInfo;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> (2025/8/11)
 * @description: 班组能耗插件的路由
 */
@Configuration
public class PluginConfiguration extends JarPluginRegister {
    @Override
    public PluginRuntimeInfo getPluginRuntimeInfo() {
        PluginRuntimeInfo pluginRuntimeInfo = new PluginRuntimeInfo();
        //设置插件的url前缀,用于路由,url前缀需要与插件唯一标识保持一致
        pluginRuntimeInfo.setPluginUrlPrefex("eemsolutiongroupenergy" + "/**");
        //插件唯一标识
        pluginRuntimeInfo.setPluginname("eemsolutiongroupenergy");
        //插件的产品线
        pluginRuntimeInfo.setProductname("eem");
        return pluginRuntimeInfo;
    }
}
