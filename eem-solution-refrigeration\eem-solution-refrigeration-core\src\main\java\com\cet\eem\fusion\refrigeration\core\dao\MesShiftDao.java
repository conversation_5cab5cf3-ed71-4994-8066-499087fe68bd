﻿package com.cet.eem.fusion.refrigeration.core.dao;

import com.cet.eem.fusion.refrigeration.core.model.config.MesShift;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.dao.BaseModelDao;

import java.util.List;

/**
 * @ClassName : MesShiftDao
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-08-25 10:00
 */
public interface MesShiftDao extends BaseModelDao<MesShift> {
    /**
     * 查询
     * @param baseVo
     * @return
     */
    List<MesShift> queryMesShift(BaseVo baseVo);

    /**
     * 查询
     * @param baseVo
     * @return
     */
    List<MesShift> queryMesShift(List<BaseVo> baseVo);
}
