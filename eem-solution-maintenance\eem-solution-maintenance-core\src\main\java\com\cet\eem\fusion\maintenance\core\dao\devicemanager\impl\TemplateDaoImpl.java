package com.cet.eem.fusion.maintenance.core.dao.devicemanager.impl;

import com.cet.eem.bll.maintenance.dao.devicemanager.TemplateDao;
import com.cet.eem.bll.maintenance.model.devicemanage.template.*;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-12
 */
@Service
public class TemplateDaoImpl extends ModelDaoImpl<AttributeTemplateDto> implements TemplateDao {
    @Override
    public List<AttributeTemplate> getTemplates(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) return Collections.EMPTY_LIST;
        LambdaQueryWrapper templateWrapper = LambdaQueryWrapper.of(AttributeTemplate.class)
                .in(AttributeTemplate::getId, ids);
        return this.selectRelatedList(AttributeTemplate.class, templateWrapper, Arrays.asList(
                LambdaQueryWrapper.of(RunningParamGroup.class),
                LambdaQueryWrapper.of(RunningParam.class),
                LambdaQueryWrapper.of(TechParam.class)));
    }

    @Override
    public List<AttributeTemplate> queryNodeTemplates(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<AttributeTemplateDto> wrapper = LambdaQueryWrapper.of(AttributeTemplateDto.class)
                .in(AttributeTemplateDto::getId, ids);
        return this.selectRelatedList(AttributeTemplate.class, wrapper);
    }
}
