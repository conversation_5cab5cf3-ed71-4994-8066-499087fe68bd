package com.cet.eem.solution.common.entity.dto.cetml;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/12
 */
@Data
public class DeleteData {
    @ApiModelProperty("功图标识")
    private List<String> code;

    @ApiModelProperty("功图时间")
    private List<Long> logtime;

    @ApiModelProperty("功图类别")
    private List<Integer> classes;
}