package com.cet.eem.fusion.refrigeration.core.config;

import com.cet.eem.common.utils.TimeUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021/8/19
 */
@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "cet.eem.task.energy-saving.end-cold-capacity")
@Slf4j
public class ColdEndCapacityConfig {
    /**
     * 全局开始时间
     */
    private String overallStartTime;

    /**
     * 单位转换系数
     */
    private Double coef;

    /**
     * 比容值
     */
    private Double specificVolume;

    /**
     * 最大查询跨度天数
     */
    private Integer maxQueryDay;

    /**
     * 解析全局开始时间
     *
     * @return 全局开始时间
     */
    public LocalDateTime parseOverallStartTime() {
        if (StringUtils.isBlank(overallStartTime)) {
            log.error("末端空调制冷需求量配置全局开始时间为空！");
            return null;
        }
        log.info("末端空调制冷需求量配置全局开始时间为[{}]", overallStartTime);
        return TimeUtil.parse(overallStartTime, TimeUtil.LONG_TIME_FORMAT);
    }


}
