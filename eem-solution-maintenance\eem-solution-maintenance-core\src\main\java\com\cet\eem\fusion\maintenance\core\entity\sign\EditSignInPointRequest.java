package com.cet.eem.fusion.maintenance.core.entity.sign;

import com.cet.eem.fusion.maintenance.core.entity.po.SignInEquipment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName : EditSignInGroupRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-03-12 14:24
 */
@Getter
@Setter
@ApiModel(value = "EditSignInPointRequest", description = "编辑签到点")
public class EditSignInPointRequest {
    /**
     * id
     */
    @NotNull(message = "要编辑的签到点id不能为空")
    @ApiModelProperty("要编辑的签到点id")
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty("签到点名称")
    private String name;

    /**
     * 签到点地址
     */
    @ApiModelProperty("签到点地址")
    private String address;

    /**
     * 签到点时间间隔
     */
    @ApiModelProperty("签到点时间间隔")
    private Integer interval;

    /**
     * 签到点nfc
     */
    @ApiModelProperty("签到点nfc")
    private String nfc;

    /**
     * 签到点图片
     */
    @ApiModelProperty("签到点图片")
    private String image;

    /**
     * 签到组id
     */
    @NotNull(message = "签到组id不能为空")
    @ApiModelProperty("签到组id")
    private List<Long> signInGroupIds;

    /**
     * 签到点
     */
    private List<SignInEquipment> children;
}
