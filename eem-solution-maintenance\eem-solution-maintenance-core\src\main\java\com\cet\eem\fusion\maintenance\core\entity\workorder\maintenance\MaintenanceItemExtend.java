package com.cet.eem.fusion.maintenance.core.entity.workorder.maintenance;

import com.cet.eem.fusion.maintenance.core.entity.po.MaintenanceItem;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : MaintenanceItemExtend
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-05-21 15:41
 */
@Getter
@Setter
public class MaintenanceItemExtend extends MaintenanceItem {
    /**
     * 执行人
     */
    private List<Long> executor;

    /**
     * 维保结果
     */
    private String maintenanceResult;
    /**
     * 零部件名称
     */
    private String sparePartName;
    /**
     * 维保方式名称
     */
    private String maintenanceTypeName;
    /**
     * 维保项目组id
     */
    private Long groupId;
    /**
     * 执行人姓名
     */
    private List<String> executorName;
    /**
     * 维保项目组名称
     */
    private String groupName;
    /**
     * 单位
     */
    private String unit;

    /**
     * 消耗零部件数量
     */
    private Double consumeAmount;
}
