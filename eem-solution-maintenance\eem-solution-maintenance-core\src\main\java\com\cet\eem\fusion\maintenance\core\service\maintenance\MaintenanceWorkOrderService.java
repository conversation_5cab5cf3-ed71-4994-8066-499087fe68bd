package com.cet.eem.fusion.maintenance.core.service.maintenance;

import com.cet.eem.bll.maintenance.model.workorder.WorkOrderCountDto;
import com.cet.eem.bll.maintenance.model.workorder.inspection.WorkOrderBatchReviewVo;
import com.cet.eem.bll.maintenance.model.workorder.inspection.WorkOrderReviewVo;
import com.cet.eem.bll.maintenance.model.workorder.maintenance.*;
import com.cet.eem.bll.maintenance.model.workorder.maintenance.MaintenanceWorkOrderDto;
import com.cet.eem.bll.maintenance.schedule.event.CreateOrderCommand;
import com.cet.eem.bll.maintenance.service.WorkOrderServiceCallBackParam;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.workflow.common.model.ProcessInstanceResponse;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface MaintenanceWorkOrderService {

    /**
     * 查询维保工单
     *
     * @param queryMaintenanceWorkOrderRequest
     * @return
     */
    ResultWithTotal<List<MaintenanceWorkOrderDto>> queryMaintenanceWorkOrderList(QueryMaintenanceWorkOrderRequest queryMaintenanceWorkOrderRequest);

    /**
     * 自动创建维保工单
     *
     * @param createOrderCommand
     */
    void createWorkOrderAutomatically(CreateOrderCommand createOrderCommand);

    /**
     * 批量手动创建维保工单
     *
     * @param addMaintenanceWorkOrderRequest
     * @return
     */
    List<ProcessInstanceResponse> createWorkOrderManuallyBatch(AddMaintenanceWorkOrderRequest addMaintenanceWorkOrderRequest);

    /**
     * 手动创建维保工单
     * @deprecated 手动创建废弃
     * @param addMaintenanceWorkOrderRequest
     * @return
     */
    @Deprecated
    ProcessInstanceResponse createWorkOrderManually(AddMaintenanceWorkOrderRequest addMaintenanceWorkOrderRequest);

    /**
     * 保存录入维保工单信息
     *
     * @param inputMaintenanceWorkOrderRequest
     */
    void saveInputWorkOrder(InputMaintenanceWorkOrderRequest inputMaintenanceWorkOrderRequest);

    /**
     * 提交录入维保工单信息
     *
     * @param inputMaintenanceWorkOrderRequest
     */
    void submitInputWorkOrder(InputMaintenanceWorkOrderRequest inputMaintenanceWorkOrderRequest);

    /**
     * 查询维保工单统计信息
     *
     * @param queryMaintenanceWorkOrderCountRequest
     * @return
     */
    List<WorkOrderCountDto> queryMaintenanceWorkOrderCount(QueryMaintenanceWorkOrderCountRequest queryMaintenanceWorkOrderCountRequest);

    /**
     * 查询工单详情
     *
     * @param id
     * @param tenantId
     * @return
     */
    MaintenanceWorkOrderDetail queryMaintenanceWorkOrderDetail(Long id, Long tenantId);

    /**
     * 工单导出
     *
     * @param queryMaintenanceWorkOrderRequest
     * @param response
     * @throws Exception
     */
    void exportWorkOrder(QueryMaintenanceWorkOrderRequest queryMaintenanceWorkOrderRequest, HttpServletResponse response);

    void assemblyCommonData(List<MaintenanceWorkOrderDto> workOrderList, Long tenantId);

    MaintenanceWorkOrderDto queryMaintenanceWorkOrderByCode(String code);

    /**
     * 删除备件记录
     * @param workOrderIds
     */
    void deleteSparePartsRecord(List<Long> workOrderIds);

    /**
     * 判断是否删除
     * @param workOrderReviewVo
     */
    void judgeDeleteSparePartsRecord(WorkOrderReviewVo workOrderReviewVo);
    void judgeDeleteSparePartsRecord(WorkOrderBatchReviewVo workOrderReviewVo);

    /**
     * 写入备件记录
     * @param param
     */
    void insertSparePartsReplaceRecord(WorkOrderServiceCallBackParam param);

    /**
     * 根据设备运行时长生成维保工单
     */
    void createWorkOrderByDeviceWorkTime();
}