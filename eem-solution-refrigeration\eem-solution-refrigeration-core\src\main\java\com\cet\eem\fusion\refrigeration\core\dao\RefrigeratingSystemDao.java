﻿package com.cet.eem.fusion.refrigeration.core.dao;


import com.cet.eem.fusion.refrigeration.core.model.config.system.RefrigeratingSystem;
import com.cet.eem.dao.BaseModelDao;

import java.util.List;

/**
 * @ClassName : RefrigeratingSystemDao
 * @Description : 制冷系统
 * <AUTHOR> jiang<PERSON><PERSON>uan
 * @Date: 2021-12-17 14:03
 */
public interface RefrigeratingSystemDao extends BaseModelDao<RefrigeratingSystem> {
    List<RefrigeratingSystem> querySystemByIds(List<Long> ids,Long projectId);
    List<RefrigeratingSystem> querySystemByRoomId(Long id,Long projectId);
    List<RefrigeratingSystem> querySystem(Long id);
}

