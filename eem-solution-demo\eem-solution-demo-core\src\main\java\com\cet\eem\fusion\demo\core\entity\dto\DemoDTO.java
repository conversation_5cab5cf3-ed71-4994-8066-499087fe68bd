package com.cet.eem.fusion.demo.core.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>  (2025/7/17 15:48)
 */
@Data
@ApiModel(value = "SchedulingSchemeAddUpdateDTO", description = "新增修改排班方案dto")
public class DemoDTO {

    @ApiModelProperty(value = "方案id" ,required = false)
    private Long id;

    @ApiModelProperty(value = "方案名称" ,required = true)
    private String name;

    @ApiModelProperty(value = "班组类型（1-运维，2-生产）" ,required = true)
    private Integer classTeamType;

    @ApiModelProperty(value = "创建时间" ,required = true)
    private Long createTime;

    @ApiModelProperty(value = "创建人id" ,required = true)
    private Long operator;
}
