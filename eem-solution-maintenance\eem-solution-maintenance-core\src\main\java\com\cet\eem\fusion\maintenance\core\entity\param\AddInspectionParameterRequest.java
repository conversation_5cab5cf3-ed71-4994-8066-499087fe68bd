package com.cet.eem.fusion.maintenance.core.entity.param;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @ClassName : AddInspectionParameterRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-14 09:16
 */
@Getter
@Setter
@ApiModel(value = "AddInspectionParameterRequest", description = "创建巡检参数")
public class AddInspectionParameterRequest {

    /**
     * 名称
     */
    @NotEmpty(message = "巡检参数名称不能为空")
    private String name;

    /**
     * 参数类型
     * 1 状态量
     * 2 模拟量
     */
    @NotNull(message = "巡检参数类型不能为空")
    private Integer type;

}
