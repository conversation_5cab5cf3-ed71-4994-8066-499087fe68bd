package com.cet.eem.fusion.maintenance.core.schedule.domain;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.ExecuteStrategyType;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.PlanSheet;
import com.cet.eem.bll.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.PlanSheetWithSubLayer;
import com.cet.eem.bll.maintenance.config.MaintenanceConfig;
import com.cet.eem.bll.maintenance.dao.PlanSheetDao;
import com.cet.eem.bll.maintenance.schedule.util.PlanSheetConverter;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.common.util.JsonUtil;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : SheetPlanDomain
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-23 09:41
 */
@Component
@Slf4j
public class PlanSheetDomain {

    @Autowired
    private PlanSheetDao planSheetDao;

    @Autowired
    private Scheduler scheduler;

    @Autowired
    private PlanSheetConverter planSheetConverter;

    @Autowired
    MaintenanceConfig maintenanceConfig;

    /**
     * 加载数据库中的执行计划
     */
    @PostConstruct
    public void loadDataBasePlanSheet() {
        if (!maintenanceConfig.isLoadPlanSheet()) {
            return;
        }

        List<PlanSheet> planSheetList = planSheetDao.queryUnFinishedPlan();
        if (CollectionUtils.isNotEmpty(planSheetList)) {
            for (PlanSheet planSheet : planSheetList) {
                if (!isCreateWorkOrderJob(planSheet)) {
                    continue;
                }

                try {
                    this.startInspectionPlan(planSheet);
                } catch (SchedulerException e) {
                    log.error("巡检计划:{}开启失败,请检查配置", JsonUtil.toJSONString(planSheet));
                }
            }
        }
    }

    /**
     * 判断是否需要启动创建工单的job
     *
     * @param planSheet
     * @return
     */
    private boolean isCreateWorkOrderJob(PlanSheet planSheet) {
        if (planSheet.getWorkSheetType() == null) {
            return false;
        }

        if (planSheet.getWorkSheetType() != WorkSheetTaskType.MAINTENANCE) {
            return true;
        }

        return planSheet.getExecuteStrategy() == null || planSheet.getExecuteStrategy() != ExecuteStrategyType.OPERATING_PERIOD;
    }

    public Map<Long, Trigger> queryTriggers(Collection<Long> ids) {
        Map<Long, Trigger> triggers = new HashMap<>(ids.size());
        for (Long id : ids) {
            TriggerKey triggerKey = new TriggerKey(id.toString(), ModelLabelDef.PLAN_SHEET);
            try {
                Trigger trigger = scheduler.getTrigger(triggerKey);
                triggers.put(id, trigger);
            } catch (SchedulerException e) {
                log.error("查询定时器异常", e);
            }
        }
        return triggers;
    }

    public Trigger.TriggerState queryTriggerState(TriggerKey triggerKey) throws SchedulerException {
        return scheduler.getTriggerState(triggerKey);
    }

    /**
     * 开启一个巡检计划
     */
    public void startInspectionPlan(PlanSheet planSheet) throws SchedulerException {
        if (!isCreateWorkOrderJob(planSheet)) {
            return;
        }

        scheduler.scheduleJob(planSheetConverter.sheetPlanConvertToJobInstance(planSheet), planSheetConverter.sheetPlanConvertToTrigger(planSheet));
        if (BooleanUtils.isFalse(planSheet.getEnabled())) {
            pausePlan(Collections.singleton(planSheet.getId()));
        }
    }

    /**
     * 重新调度job
     *
     * @param planSheet
     * @throws SchedulerException
     */
    public void reschedulePlan(PlanSheet planSheet) throws SchedulerException {
        if (!isCreateWorkOrderJob(planSheet)) {
            return;
        }

        TriggerKey triggerKey = new TriggerKey(planSheet.getId().toString(), ModelLabelDef.PLAN_SHEET);
        scheduler.rescheduleJob(triggerKey, planSheetConverter.sheetPlanConvertToTrigger(planSheet));
        if (BooleanUtils.isFalse(planSheet.getEnabled())) {
            pausePlan(Collections.singleton(planSheet.getId()));
        }
    }

    /**
     * 暂停巡检计划
     *
     * @param planSheetIds
     * @throws SchedulerException
     */
    public void pausePlan(Collection<Long> planSheetIds) throws SchedulerException {
        for (Long id : planSheetIds) {
            JobKey jobKey = new JobKey(id.toString());
            scheduler.pauseJob(jobKey);
        }
    }

    /**
     * 恢复巡检计划
     *
     * @param planSheetIds
     * @throws SchedulerException
     */
    public void resumePlan(Collection<Long> planSheetIds) throws SchedulerException {
        // 先删除，然后重新创建
        List<JobKey> jobKeyList = planSheetIds.stream().map(it -> new JobKey(it.toString())).collect(Collectors.toList());
        scheduler.deleteJobs(jobKeyList);

        LambdaQueryWrapper<PlanSheet> wrapper = LambdaQueryWrapper.of(PlanSheet.class)
                .in(BaseEntity::getId, planSheetIds);
        List<PlanSheetWithSubLayer> planSheetWithSubLayers = planSheetDao.selectRelatedList(PlanSheetWithSubLayer.class, wrapper);
        for (PlanSheetWithSubLayer planSheetWithSubLayer : planSheetWithSubLayers) {
            startInspectionPlan(planSheetWithSubLayer);
        }
    }

    /**
     * 删除巡检计划
     *
     * @param planSheetIds
     * @throws SchedulerException
     */
    public void deletePlan(Collection<Long> planSheetIds) throws SchedulerException {
        List<JobKey> jobKeyList = planSheetIds.stream().map(s -> new JobKey(s.toString())).collect(Collectors.toList());
        scheduler.deleteJobs(jobKeyList);
    }

    /**
     * 根据计划id查询触发器
     *
     * @param id
     * @return
     * @throws SchedulerException
     */
    public List<Trigger> queryTriggerByJob(Long id) throws SchedulerException {
        JobKey jobKey = new JobKey(id.toString());
        return (List<Trigger>) scheduler.getTriggersOfJob(jobKey);
    }

    /**
     * 查询执行计划
     *
     * @param id
     * @param size
     * @return
     */
    public List<Long> queryExecutePlan(Long id, int size) {
        TriggerKey triggerKey = new TriggerKey(id.toString(), ModelLabelDef.PLAN_SHEET);
        List<Long> list = new ArrayList<>();
        try {
            Trigger trigger = scheduler.getTrigger(triggerKey);
            Date nextFireTime = trigger.getNextFireTime();
            if (nextFireTime == null) {
                return list;
            }
            list.add(nextFireTime.getTime());
            for (int i = 0; i < size - 1; i++) {
                nextFireTime = trigger.getFireTimeAfter(nextFireTime);
                if (nextFireTime == null) {
                    break;
                }

                list.add(nextFireTime.getTime());
            }
        } catch (SchedulerException e) {
            log.error("查询定时器异常", e);
        }

        return list;
    }


}