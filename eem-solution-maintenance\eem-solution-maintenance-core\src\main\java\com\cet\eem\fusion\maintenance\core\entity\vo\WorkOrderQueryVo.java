package com.cet.eem.fusion.maintenance.core.entity.vo;

import com.cet.eem.fusion.common.model.Page;
import com.cet.eem.fusion.common.modelutils.model.base.Order;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 描述：查询工单入参条件
 *
 * <AUTHOR>
 * @date 2023/2/15
 */
@Getter
@Setter
public class WorkOrderQueryVo {
    @NotEmpty
    private List<Integer> workOrderStatus;

    @NotNull
    private Integer taskType;

    @NotNull
    private LocalDateTime startTime;

    @NotNull
    private LocalDateTime endTime;

    @NotNull
    private Page page;

    private List<Order> orders;

    @Null
    private Long teamId;

    @NotNull
    private Long userId;

    @Null
    private Long signPointId;
}
