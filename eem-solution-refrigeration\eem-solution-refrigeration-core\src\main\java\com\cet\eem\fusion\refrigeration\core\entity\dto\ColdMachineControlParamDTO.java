﻿package com.cet.eem.fusion.refrigeration.core.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : ColdMachineControlParam
 * @Description : 冷机优化控制
 * <AUTHOR> jiang<PERSON><PERSON><PERSON>
 * @Date: 2023-06-08 09:28
 */
@Getter
@Setter
public class ColdMachineControlParamDTO {
    @JsonProperty("project_id")
    private Long projectId;

    @JsonProperty("predict_data")
    private List<ColdLoadParam> coldLoadParams;
    @JsonProperty("switch_limit")
    private SwitchLimit switchLimit;

}
