package com.cet.eem.fusion.maintenance.core.entity.workorder.repair;

import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.cet.eem.fusion.maintenance.core.entity.bo.Attachment;
import com.cet.eem.fusion.maintenance.core.entity.workorder.MaintenanceContent;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/5/7
 */
@Getter
@Setter
@ApiModel(description = "维修工单参数")
public class RepairParamsWriteVo {
    @ApiModelProperty("工单编号")
    private String code;

    @ApiModelProperty("根据实际需求，可以不传")
    private Object taskResult;

    @ApiModelProperty("实际开始时间")
    @JsonProperty(WorkOrderDef.EXECUTE_TIME)
    private LocalDateTime executeTime;

    @ApiModelProperty("实际结束时间")
    @JsonProperty(WorkOrderDef.FINISH_TIME)
    private LocalDateTime finishTime;

    @ApiModelProperty("工单附件")
    private List<Attachment> attachmentList;

    @ApiModelProperty("处理描述")
    @JsonProperty(WorkOrderDef.HANDLE_DESCRIPTION)
    private String handleDescription;

    @ApiModelProperty("工单内容")
    private MaintenanceContent maintenanceContent;

    @ApiModelProperty("表单数据，对缺少的字段进行补充")
    private Map<String, Object> formData;

}
