package com.cet.eem.fusion.maintenance.core.entity.plan;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.PlanSheet;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

/**
 * @ClassName : PlanSheetVo
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-22 09:58
 */
@Getter
@Setter
public class InspectionPlanSheetVo extends PlanSheetBaseVo {

    /**
     * 签到组（巡检路线）名
     */
    private String signInGroupName;

    /**
     * 巡检方案名
     */
    private String inspectionSchemeName;


    public InspectionPlanSheetVo(PlanSheet planSheet) {
        BeanUtils.copyProperties(planSheet, this);
    }
}
