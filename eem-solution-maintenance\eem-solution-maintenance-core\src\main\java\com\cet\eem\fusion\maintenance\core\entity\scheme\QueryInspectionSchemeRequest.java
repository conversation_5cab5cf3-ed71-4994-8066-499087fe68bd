package com.cet.eem.fusion.maintenance.core.entity.scheme;

import com.cet.eem.fusion.common.model.Page;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * @ClassName : QueryInspectionSchemeRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-14 16:11
 */
@Getter
@Setter
@ApiModel(value = "QueryInspectionSchemeRequest", description = "查询巡检方案")
public class QueryInspectionSchemeRequest {

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空")
    private Long tenantId;

    /**
     * 方案名称
     */
    private String name;

    /**
     * 分页参数
     */
    private Page page;
}
