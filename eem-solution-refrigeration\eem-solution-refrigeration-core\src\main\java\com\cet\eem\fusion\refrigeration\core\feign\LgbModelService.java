﻿package com.cet.eem.fusion.refrigeration.core.feign;

import cn.hutool.json.JSONObject;
import com.cet.eem.fusion.refrigeration.core.model.dataentryquery.*;
import com.cet.eem.fusion.refrigeration.core.model.predict.WeatherReturn;
import com.cet.eem.fusion.refrigeration.core.model.weather.ForecastBasicWeatherDataVo;
import com.cet.eem.common.model.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * @ClassName : LgbModelService
 * @Description : 算法服务
 * <AUTHOR> jiangzixuan
 * @Date: 2022-07-12 09:55
 */
@FeignClient(value = "lgbmodel-service", url = "${cet.eem.service.url.lgbmodel-service:''}")
public interface LgbModelService {

    @PostMapping("/humidity")
    WeatherReturn getHumidityPredict(String s);

    @PostMapping("/temperature")
    WeatherReturn getTemperaturePredict(String s);

    @PostMapping("/humidity")
    DataLogDataWrite getWeatherPredict(Result<List<ForecastBasicWeatherDataVo>> ok);

    @PostMapping("/temperature")
    DataLogDataWrite getWeatherPredictOfTemp(Result<List<ForecastBasicWeatherDataVo>> ok);

    @PostMapping("/endCold")
    DataLogDataWrite getEndColdPredictData(Result<List<EndColdDataEntry>> entries);

    @PostMapping("/piplineCold")
    DataLogDataWrite getPipelineLossColdPredictData(Result<List<PipelineLossColdDataEntry>> entries);

    @PostMapping("/returnWater")
    DataLogDataWrite getOptimizationOfRefrigeratorWaterPredict(Result<List<OptimizationOfRefrigeratorWaterEntry>> entries);

    @PostMapping("/systemPower")
    DataLogDataWrite getTotalSystemPowerPredict(Result<List<TotalSystemPowerEntry>> entries);
}

