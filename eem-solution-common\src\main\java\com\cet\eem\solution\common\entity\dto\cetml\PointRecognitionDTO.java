package com.cet.eem.solution.common.entity.dto.cetml;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/26 19:48
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PointRecognitionDTO {
    @ApiModelProperty("功图id")
    @JsonProperty("dynamometercard_id")
    private Long dynamometerCardId;

    @ApiModelProperty("功图信息")
    @JsonProperty("dynamometercard_info")
    private String dynamometerCardInfo;

    @ApiModelProperty("载荷修正系数")
    @JsonProperty("load_correction")
    private Double loadCorrection;

    @ApiModelProperty("冲程修正系数")
    @JsonProperty("stroke_correction")
    private Double strokeCorrection;

    @ApiModelProperty("拐点曲率系数")
    @JsonProperty("point_curvature")
    private Double pointCurvature;
}
