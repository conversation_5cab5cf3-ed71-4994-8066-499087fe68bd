#!/usr/bin/env python3
"""
分析遗漏问题的脚本
比较原始数据和已分类数据，找出遗漏的问题
"""

import json
import sys
from pathlib import Path

def load_json_file(file_path):
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def extract_issues_from_original(data):
    """从原始数据中提取所有问题"""
    all_issues = []
    
    for class_name, issues in data.get('issues_by_class', {}).items():
        for issue in issues:
            # 创建唯一标识符：类名 + issue_id
            unique_id = f"{class_name}_{issue['issue_id']}"
            all_issues.append({
                'unique_id': unique_id,
                'class': class_name,
                'issue_id': issue['issue_id'],
                'error_code': issue['error_code'],
                'description': issue['description'],
                'line': issue['line']
            })
    
    return all_issues

def extract_issues_from_classified(miss_method_data, wrong_params_data):
    """从已分类数据中提取所有问题"""
    classified_issues = []
    
    # 处理 miss_method 问题
    if miss_method_data:
        for issue in miss_method_data:
            unique_id = f"{issue['class']}_{issue['issue_id']}"
            classified_issues.append({
                'unique_id': unique_id,
                'class': issue['class'],
                'issue_id': issue['issue_id'],
                'error_code': 'miss_method',
                'source': 'miss_method.json'
            })
    
    # 处理 wrong_params 问题
    if wrong_params_data:
        for issue in wrong_params_data:
            unique_id = f"{issue['class']}_{issue['issue_id']}"
            classified_issues.append({
                'unique_id': unique_id,
                'class': issue['class'],
                'issue_id': issue['issue_id'],
                'error_code': 'wrong_params',
                'source': 'wrong_params.json'
            })
    
    return classified_issues

def find_missing_issues(original_issues, classified_issues):
    """找出遗漏的问题"""
    classified_ids = {issue['unique_id'] for issue in classified_issues}
    missing_issues = []
    
    for issue in original_issues:
        if issue['unique_id'] not in classified_ids:
            missing_issues.append(issue)
    
    return missing_issues

def analyze_missing_by_class(missing_issues):
    """按类分析遗漏问题"""
    by_class = {}
    
    for issue in missing_issues:
        class_name = issue['class']
        if class_name not in by_class:
            by_class[class_name] = []
        by_class[class_name].append(issue)
    
    return by_class

def main():
    # 文件路径
    base_path = Path('output')
    original_file = base_path / 'method_issues_report.json'
    miss_method_file = base_path / 'miss_method.json'
    wrong_params_file = base_path / 'wrong_params.json'
    
    # 加载数据
    print("加载数据文件...")
    original_data = load_json_file(original_file)
    miss_method_data = load_json_file(miss_method_file)
    wrong_params_data = load_json_file(wrong_params_file)
    
    if not original_data:
        print("无法加载原始数据文件")
        return
    
    # 提取问题
    print("分析问题...")
    original_issues = extract_issues_from_original(original_data)
    classified_issues = extract_issues_from_classified(miss_method_data, wrong_params_data)
    
    # 找出遗漏问题
    missing_issues = find_missing_issues(original_issues, classified_issues)
    
    # 统计信息
    print(f"\n=== 完整性分析结果 ===")
    print(f"原始问题总数: {len(original_issues)}")
    print(f"已分类问题数: {len(classified_issues)}")
    print(f"遗漏问题数: {len(missing_issues)}")
    print(f"完整性比例: {len(classified_issues)/len(original_issues)*100:.1f}%")
    
    # 按类分析遗漏问题
    missing_by_class = analyze_missing_by_class(missing_issues)
    
    print(f"\n=== 遗漏问题详情 ===")
    for class_name, issues in missing_by_class.items():
        print(f"\n{class_name}: {len(issues)}个遗漏问题")
        for issue in issues[:5]:  # 只显示前5个
            print(f"  - ID: {issue['issue_id']}, 类型: {issue['error_code']}, 行: {issue['line']}")
            print(f"    描述: {issue['description'][:80]}...")
        if len(issues) > 5:
            print(f"  ... 还有 {len(issues) - 5} 个问题")
    
    # 生成遗漏问题报告
    report_file = base_path / 'missing_issues_analysis.json'
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump({
            'summary': {
                'total_original': len(original_issues),
                'total_classified': len(classified_issues),
                'total_missing': len(missing_issues),
                'completeness_rate': len(classified_issues)/len(original_issues)*100
            },
            'missing_issues': missing_issues,
            'missing_by_class': {k: len(v) for k, v in missing_by_class.items()}
        }, f, ensure_ascii=False, indent=2)
    
    print(f"\n详细分析结果已保存到: {report_file}")

if __name__ == "__main__":
    main()
