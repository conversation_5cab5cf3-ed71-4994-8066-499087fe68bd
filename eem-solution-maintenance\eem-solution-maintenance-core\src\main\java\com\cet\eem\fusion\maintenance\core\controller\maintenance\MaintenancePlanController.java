package com.cet.eem.fusion.maintenance.core.controller.maintenance;

import com.cet.eem.maintenanceservice.controller.bff.maintenance.MaintenancePlanBffController;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName : InspectionPlanController
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-22 09:19
 */
@Api(value = "/eem/v1/maintenance/plan", tags = "维保计划管理")
@RequestMapping(value = "/eem/v1/maintenance/plan")
@RestController
@Validated
public class MaintenancePlanController extends MaintenancePlanBffController {

}
