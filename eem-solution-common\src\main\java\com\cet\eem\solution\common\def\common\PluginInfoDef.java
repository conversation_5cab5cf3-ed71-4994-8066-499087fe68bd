package com.cet.eem.solution.common.def.common;

/**
 * <AUTHOR>  (2025/7/17 15:20)
 */
public class PluginInfoDef {
    /**
     * 产品名称
     */
    public static final String PRODUCT_NAME = "eem-solution";

    /**
     * demo插件相关常量定义
     */
    public static class Demo {
        /**
         * demo插件名称前缀
         */
        public static final String PLUGIN_NAME_PREFIX = "eem-solution-demo";
        /**
         * demo模块接口路径前缀
         */
        public static final String INTERFACE_PREFIX = "/eem/solution/demo";
    }

    /**
     * 制冷系统插件相关常量定义
     */
    public static class Refrigeration {
        /**
         * 制冷系统插件名称前缀
         */
        public static final String PLUGIN_NAME_PREFIX = "eem-solution-refrigeration";
        /**
         * 制冷系统模块接口路径前缀
         */
        public static final String INTERFACE_PREFIX = "/eem/solution/refrigeration";
    }

    /**
     * 班组能耗插件相关常量定义
     */
    public static class GroupEnergy {
        /**
         * 班组能耗插件名称前缀
         */
        public static final String PLUGIN_NAME_PREFIX = "eem-solution-group-energy";
        /**
         * 班组能耗模块接口路径前缀
         */
        public static final String INTERFACE_PREFIX = "/eem/solution/group-energy";
    }

    /**
     * 变压器能效分析插件相关常量定义
     */
    public static class Transformer {
        /**
         * 变压器能效分析插件名称前缀
         */
        public static final String PLUGIN_NAME_PREFIX = "eem-solution-transformer";
        /**
         * 变压器能效分析模块接口路径前缀
         */
        public static final String INTERFACE_PREFIX = "/eem/solution/transformer";
    }

    /**
     * 运维插件相关常量定义
     */
    public static class Maintenance {
        /**
         * 运维插件名称前缀
         */
        public static final String PLUGIN_NAME_PREFIX = "eem-solution-maintenance";
        /**
         * 运维模块接口路径前缀
         */
        public static final String INTERFACE_PREFIX = "/eem/solution/maintenance";
    }
}
