package com.cet.eem.fusion.maintenance.core.entity.devicemanage.component;

import com.cet.eem.fusion.maintenance.core.entity.po.DeviceComponent;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : ComponentWithModel
 * @Description : 继承deviceCompont新增设备的型号这一参数
 * <AUTHOR> jzx
 * @Date: 2021-05-20 10:26
 */
@Getter
@Setter
public class ComponentWithModel extends DeviceComponent {
    /**
     * 设备型号
     */
    private String deviceModel;

    /**
     * 备件设备id
     */
    private Long sparePartsDeviceId;
}