"""
向量库初始化脚本
专门用于初始化向量库
"""

import sys
import logging
import argparse
from service.vector_service import VectorService
# 不再导入DEFAULT_PROJECT_PATH，项目路径必须通过参数传入

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Java代码向量库初始化工具')
    parser.add_argument(
        '--project-path', '-p',
        type=str,
        required=True,
        help='项目路径 (必需参数)'
    )
    parser.add_argument(
        '--recreate', '-r',
        action='store_true',
        help='重新创建向量库（删除现有数据）'
    )
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出'
    )
    parser.add_argument(
        '--collection-name', '-c',
        type=str,
        help='指定向量库集合名称 (可选，默认使用配置文件中的名称)'
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 显示配置信息
    print("=" * 60)
    print("Java代码向量库初始化工具")
    print("=" * 60)
    print(f"项目路径: {args.project_path}")
    print(f"重新创建: {'是' if args.recreate else '否'}")
    print(f"详细输出: {'是' if args.verbose else '否'}")
    print(f"集合名称: {args.collection_name or '使用默认配置'}")
    print("=" * 60)
    
    # 确认操作
    if args.recreate:
        confirm = input("⚠️  警告：重新创建将删除现有向量数据，是否继续？(y/N): ").strip().lower()
        if confirm != 'y':
            print("操作已取消")
            return
    
    # 初始化向量服务
    vector_service = VectorService()
    
    try:
        # 执行初始化
        result = vector_service.initialize_vector_database(
            project_path=args.project_path,
            recreate=args.recreate,
            collection_name=args.collection_name
        )
        
        # 显示结果
        if result["success"]:
            print("\n" + "=" * 60)
            print("✅ 初始化成功！")
            print("=" * 60)
            print(f"处理文件数: {result['processed_files']}")
            stats = result.get('stats', {})
            if stats:
                print(f"向量库记录数: {stats.get('row_count', 'N/A')}")
                print(f"集合名称: {stats.get('collection_name', 'N/A')}")
            print("=" * 60)
            print("\n现在可以使用以下命令进行检索:")
            print("python search_file.py --file your_query_file.java")
            print("python main.py --directory your_search_directory")
        else:
            print("\n" + "=" * 60)
            print("❌ 初始化失败！")
            print("=" * 60)
            print(f"错误信息: {result['message']}")
            if 'error' in result:
                print(f"详细错误: {result['error']}")
            print("=" * 60)
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"初始化过程中发生异常: {e}")
        print(f"\n❌ 初始化失败: {e}")
        sys.exit(1)
    finally:
        vector_service.cleanup()


if __name__ == "__main__":
    main()
