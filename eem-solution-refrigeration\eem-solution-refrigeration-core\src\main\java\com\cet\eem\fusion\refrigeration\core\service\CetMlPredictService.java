﻿package com.cet.eem.fusion.refrigeration.core.service;

import com.cet.eem.fusion.refrigeration.core.model.cetml.ColdLoadQueryParam;
import com.cet.eem.fusion.refrigeration.core.model.cetml.ColdMachineControlParam;
import com.cet.eem.fusion.refrigeration.core.model.cetml.CopQueryParam;
import com.cet.eem.fusion.refrigeration.core.model.cetml.PumpFitParam;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName : CetMlPredictService
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-06-08 17:10
 */
public interface CetMlPredictService {
    /**
     * 查询冷负荷预测需要的入参
     * @param time
     * @param roomId
     * @param projectId
     * @return
     */
    List<ColdLoadQueryParam> getColdPredictQueryParam(LocalDateTime time ,Long roomId,Long projectId) throws Exception;

    /**
     * 查询冷机控制的入参
     * @param time
     * @param roomId
     * @param projectId
     * @return
     */
    List<ColdMachineControlParam> getColdMainControlQueryParams(LocalDateTime time ,Long roomId,Long projectId) throws Exception;

    /**
     * 查询cop拟合的入参
     * @param time
     * @param roomId
     * @param projectId
     * @return
     */
    List<CopQueryParam> getCopQueryParams(LocalDateTime time ,Long roomId,Long projectId) throws InstantiationException, IllegalAccessException;

    /**
     * 查询泵和流量拟合的入参
     * @param time
     * @param roomId
     * @param projectId
     * @return
     */
    List<PumpFitParam> getPumpFitParam(LocalDateTime time ,Long roomId,Long projectId);
    /**
     * 查询冷机控制的入参
     * @param time
     * @param roomId
     * @param projectId
     * @return
     */
    List<ColdMachineControlParam> getColdMainControlQueryParamsBatch(LocalDateTime time ,LocalDateTime endTime,Long roomId,Long projectId) throws Exception;
    /**
     * 查询冷负荷预测需要的入参
     * @param time
     * @param roomId
     * @param projectId
     * @return
     */
    List<ColdLoadQueryParam> getColdPredictQueryParamBatch(LocalDateTime time ,LocalDateTime endTime,Long roomId,Long projectId) throws Exception;
}

