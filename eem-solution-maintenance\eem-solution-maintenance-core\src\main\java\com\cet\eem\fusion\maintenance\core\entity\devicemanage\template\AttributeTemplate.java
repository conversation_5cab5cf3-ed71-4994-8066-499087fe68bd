package com.cet.eem.fusion.maintenance.core.entity.devicemanage.template;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-13
 */
@Data
@AllArgsConstructor
@ModelLabel(ModelLabelDef.NODE_TEMPLATE)
public class AttributeTemplate extends AttributeTemplateDto {

    @JsonProperty("runningparamgroup_model")
    private List<RunningParamGroup> runningParam;

    @JsonProperty("techparamtemplate_model")
    private List<TechParam> techParams;

    public AttributeTemplate(String templatename, Boolean isopenprotect, List<RunningParamGroup> runningParam, List<TechParam> techParams) {
        super(templatename, isopenprotect);
        this.runningParam = runningParam;
        this.techParams = techParams;
    }

    public AttributeTemplate() {
        this.modelLabel = ModelLabelDef.NODE_TEMPLATE;
    }
}
