﻿package com.cet.eem.fusion.refrigeration.core.dao;

import com.cet.eem.fusion.refrigeration.core.model.config.system.ControlScheme;
import com.cet.eem.dao.BaseModelDao;

import java.util.List;

/**
 * @ClassName : ControlSchemeDao
 * @Description : 控制方案
 * <AUTHOR> jiang<PERSON><PERSON><PERSON>
 * @Date: 2022-06-28 19:03
 */
public interface ControlSchemeDao extends BaseModelDao<ControlScheme> {
    /**
     * 根据系统id查询
     * @param systemId
     * @return
     */
    List<ControlScheme> queryControlSchemeBySystemId(Long systemId);
}

