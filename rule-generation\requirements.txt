# 基础依赖
numpy>=1.21.0

# Hugging Face 生态（CodeBERT 模型加载与推理）
transformers==4.55.2
torch==2.8.0  # PyTorch，CodeBERT 依赖的深度学习框架
tokenizers>=0.12.1  # 用于高效分词

# 向量数据库
pymilvus==2.5.3  # Milvus Python SDK

# 代码解析工具（可选，用于提取Java代码结构信息）
javalang==0.13.0 # 解析Java代码的AST（抽象语法树）
tree-sitter>=0.20.0  # 更高效的代码解析器
tree-sitter-java>=0.20.0  # Java语言的tree-sitter绑定

# 其他辅助工具
tqdm>=4.64.0  # 显示进度条，用于显示处理进度
pathlib>=1.0.1  # 路径处理工具

# 可选的向量数据库（备选方案）
# faiss-cpu>=1.7.2  # 轻量型向量检索库（CPU版本）
# faiss-gpu>=1.7.2  # 若有GPU，可替换为GPU版本
# chromadb>=0.3.21  # 简单易用的向量数据库（带内存模式）