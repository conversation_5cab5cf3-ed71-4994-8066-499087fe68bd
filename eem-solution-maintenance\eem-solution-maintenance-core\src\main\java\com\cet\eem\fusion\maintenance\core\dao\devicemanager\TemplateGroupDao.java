package com.cet.eem.fusion.maintenance.core.dao.devicemanager;

import com.cet.eem.bll.maintenance.model.devicemanage.template.GroupWithRunningParam;
import com.cet.eem.bll.maintenance.model.devicemanage.template.TemplateGroupDto;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-12
 */
public interface TemplateGroupDao extends BaseModelDao<TemplateGroupDto> {

    GroupWithRunningParam  getRunningParam(Long groupId);
}
