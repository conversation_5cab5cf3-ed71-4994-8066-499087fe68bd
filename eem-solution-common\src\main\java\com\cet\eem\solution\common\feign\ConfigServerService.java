package com.cet.eem.solution.common.feign;

import com.cet.eem.fusion.common.entity.Result;
import com.cet.eem.solution.common.entity.vo.peccore.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;
import java.util.Map;

@Service
@FeignClient(value = "configServer", url = "${cet.eem.service.url.config-server:''}")
public interface ConfigServerService {

    /**
     * 查询指定设备下的事故报警点
     */
    @GetMapping(value = "/api/pecnode/v1/warn/point/nodes")
    Result<List<EmergencyAlarmPoint>> getEmergencyAlarmPointByDeviceIds(List<Long> deviceId);

    /**
     * 多个设备下所有定时记录测点信息
     */
    @PostMapping(value = "/api/pecnode/v1/devices/datalogpoints")
    Result<Map<Long, List<DatalogPoints>>> getMeasureInfoByDeviceId(List<Long> deviceId);

    /**
     * 获取指定设备的测点信息
     */
    @GetMapping(value = "/api/pecnode/v1/device/{deviceId}/measures")
    Result<List<MeasureInfo>> getMeasureByDeviceId(@PathVariable(value = "deviceId") Long deviceId);

    /**
     * 获取指定通道下的事故报警点
     */
    @GetMapping(value = "/api/pecnode/v1/channels/{channelId}/setpoints")
    Result<List<EmergencyAlarmPoint>> getMeasureByChannelId(@PathVariable(value = "channelId") Long channelId);

    /**
     * 根据设备id获取设备信息
     */
    @PostMapping(value = "/api/pecnode/v1/devices")
    Result<List<DeviceInfo>> getMeasureByDeviceId(List<Long> deviceIdList);

    /**
     * 获取单个厂站下所有设备信息
     */
    @GetMapping(value = "/api/pecnode/v1/stations/{stationId}/devices")
    Result<List<DeviceInfo>> getDeviceInfoByStation(@PathVariable(value = "stationId") Long stationId);

    /**
     * 获取所有厂站配置
     */
    @GetMapping(value = "/api/pecnode/v1/stations")
    Result<List<StationsInfo>> getAllStations();
}