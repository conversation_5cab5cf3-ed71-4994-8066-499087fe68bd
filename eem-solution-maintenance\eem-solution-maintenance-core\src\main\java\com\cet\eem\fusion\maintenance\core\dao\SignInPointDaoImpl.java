package com.cet.eem.fusion.maintenance.core.dao;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInGroup;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInPoint;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInPointSequence;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.SignInGroupWithAllSubLayer;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.SignInGroupWithSubLayer;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName : SignInPointDaoImpl
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-03-12 14:09
 */
@Repository
public class SignInPointDaoImpl extends ModelDaoImpl<SignInPoint> implements SignInPointDao {

    @Autowired
    private SignInGroupDao signInGroupDao;

    @Override
    public List<SignInPoint> queryAllSignInPointInProject(Long projectId) {
        LambdaQueryWrapper<SignInGroup> queryWrapper = LambdaQueryWrapper.of(SignInGroup.class);
        queryWrapper.eq(SignInGroup::getProjectId, projectId);
        List<SignInGroupWithSubLayer> signInGroupWithSubLayers = signInGroupDao.selectRelatedList(SignInGroupWithSubLayer.class, queryWrapper, Collections.singletonList(LambdaQueryWrapper.of(SignInPoint.class)));
        List<SignInPoint> signInPointList = new ArrayList<>();
        for (SignInGroupWithSubLayer signInGroupWithSubLayer : signInGroupWithSubLayers) {
            if (CollectionUtils.isEmpty(signInGroupWithSubLayer.getSignInPointList())) {
                continue;
            }
            signInPointList.addAll(signInGroupWithSubLayer.getSignInPointList());
        }
        return signInPointList;
    }

    @Override
    public List<SignInGroupWithAllSubLayer> querySignInPoint(List<Long> signPointIds, List<Long> signGroupIds) {
        if (CollectionUtils.isEmpty(signPointIds) || CollectionUtils.isEmpty(signGroupIds)) {
            return Collections.emptyList();
        }

        LambdaQueryWrapper<SignInGroup> signGroupQueryWrapper = LambdaQueryWrapper.of(SignInGroup.class)
                .in(SignInGroup::getId, signGroupIds);

        LambdaQueryWrapper<SignInPoint> signInPointQueryWrapper = LambdaQueryWrapper.of(SignInPoint.class)
                .in(SignInPoint::getId, signPointIds);

        LambdaQueryWrapper<SignInPointSequence> signInPointSequenceQueryWrapper = LambdaQueryWrapper.of(SignInPointSequence.class);

        return signInGroupDao.selectRelatedList(SignInGroupWithAllSubLayer.class, signGroupQueryWrapper,
                Arrays.asList(signInPointQueryWrapper, signInPointSequenceQueryWrapper));
    }
}
