package com.cet.eem.fusion.maintenance.core.utils;

import com.cet.eem.auth.service.AuthUtils;
import com.cet.eem.fusion.common.def.OperationAuthDef;
import com.cet.eem.bll.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.bll.maintenance.service.inspection.InspectorService;
import com.cet.eem.fusion.common.utils.ParamUtils;
import com.cet.eem.fusion.common.exception.ValidationException;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;
import com.cet.electric.workflow.common.model.node.config.UserTaskConfig;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/6/20
 */
@Service
public class InspectorUserCheckUtils {
    @Autowired
    InspectorService inspectorService;

    @Autowired
    AuthUtils authUtils;

    /**
     * 获取并校验班组id
     *
     * @param teamId
     * @param user
     * @return
     */
    public Long getAndCheckTeamId(Long teamId, UserVo user) {
        boolean inspectorUser = inspectorService.isMaintenanceUser(user);
        if (!inspectorUser) {
            return teamId;
        }

        Long group = authUtils.getRelativeGroup(user);
        if (!ParamUtils.checkPrimaryKeyValid(teamId)) {
            return group;
        } else {
            if (!Objects.equals(teamId, group)) {
                throw new ValidationException("当前用户无查询其他班组权限！");
            }
        }

        return teamId;
    }

    /**
     * 校验
     * @param user
     * @param workOrder
     * @param userTaskConfig
     * @return
     */
    public boolean checkRepairWorkOrderAuth(UserVo user, InspectionWorkOrderDto workOrder, UserTaskConfig userTaskConfig) {
        if(userTaskConfig == null) {
            return false;
        }

        Long group = getAndCheckTeamId(null, user);
        if (!BooleanUtils.isTrue(userTaskConfig.getAuthorized()) || group == null) {
            return userTaskConfig.getAuthorized();
        }

        // 针对维修工单的运值人员审核需要做特殊处理
        if (Objects.equals(workOrder.getTaskType(), WorkSheetTaskType.REPAIR) &&
                Objects.equals(userTaskConfig.getNodeModel(), OperationAuthDef.INSPECTOR_WORK_ORDER_MONITOR_CHECK)) {
            boolean flag = Objects.equals(group, workOrder.getInspectTeamId());
            userTaskConfig.setAuthorized(flag);
            return flag;
        }

        return userTaskConfig.getAuthorized();
    }
}
