package com.cet.eem.fusion.maintenance.core.entity.po;

import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import lombok.Getter;
import lombok.Setter;

/**
 * 备件信息
 *
 * @Author: jiangzixuan
 * @Description:
 * @Data: Created in 2021-05-12
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.SPARE_PARTS_STORAGE)
public class SpareParts extends EntityWithName {

    /**
     * 备件规格型号
     */
    private String model;
    /**
     * 备件厂家
     */
    private String brand;
    /**
     * 备件单位
     */
    private String unit;

    public SpareParts() {
        this.modelLabel = ModelLabelDef.SPARE_PARTS_STORAGE;
    }

    public SpareParts(String name, String model, String brand, String unit) {
        this.name = name;
        this.model = model;
        this.brand = brand;
        this.unit = unit;
        this.modelLabel = ModelLabelDef.SPARE_PARTS_STORAGE;
    }

}
