package com.cet.eem.fusion.demo.core.dao.impl;

import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.fusion.demo.core.dao.DemoDao;
import com.cet.eem.fusion.demo.core.entity.bo.DemoBO;
import com.cet.eem.fusion.demo.core.entity.po.Demo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>  (2025/7/24 9:54)
 */
@Component
public class DemoDaoImpl extends ModelDaoImpl<Demo> implements DemoDao {
    @Autowired
    private ModelServiceUtils modelServiceUtils;

    @Override
    public List<Demo> querydemoMethod(DemoBO demoBO) {
        return null;
    }
}
