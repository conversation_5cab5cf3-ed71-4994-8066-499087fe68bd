package com.cet.eem.fusion.maintenance.core.controller.inspect;

import com.cet.eem.maintenanceservice.controller.bff.inspect.InspectionSchemeBffController;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName : InspectionSchemeController
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-14 14:29
 */
@Api(value = "/eem/v1/inspect/scheme", tags = "巡检方案管理")
@RequestMapping(value = "/eem/v1/inspect/scheme")
@RestController
@Validated
public class InspectionSchemeController extends InspectionSchemeBffController {

}
