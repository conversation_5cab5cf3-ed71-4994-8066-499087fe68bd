# inspect_method.py 使用说明

## 功能简介

这个Python脚本用于自动执行inspect.bat代码检查工具，并从生成的XML报告中提取"方法解析问题"，最终生成易读的Markdown格式报告。

## 前置要求

- Python 3.6+
- inspect.bat工具可执行，在系统换变量中配置
- 使用idea打开项目路径并更新maven依赖

## 基本使用

### 1. 使用默认参数（推荐）

```
python inspect_method.py
```

默认参数：

- 项目路径: `E:\ai-x\code\energy-solution-fusion\eem-solution-group-energy`
- 配置文件: `E:\ai-x\code\energy-solution-fusion\inspect\ai_method.xml`
- 输出目录: 当前目录

### 2. 自定义参数

```
python inspect_method.py --project-path "你的项目路径" --inspection-profile "配置文件路径" --output-path "输出目录"
```

### 3. 参数说明

- `--project-path`: 要检查的Java项目根目录
- `--inspection-profile`: inspect工具的配置文件路径（.xml格式）
- `--output-path`: 报告输出目录

## 报告

报告method_issues_report.md会生成在当前目录

格式：

```
# 方法扫描问题报告

总问题数: 1004

## SchedulingSchemeVO

### 问题 1
error_code: "method_issues"
module: "eem-solution-group-energy-core"
package: "com.cet.eem.fusion.groupenergy.core.entity.vo"
class: "SchedulingSchemeVO"
missing_method: "getId()"
description: "无法解析方法 'getId()'"
line: [33]

### 问题 2
error_code: "method_issues"
module: "eem-solution-group-energy-core"
package: "com.cet.eem.fusion.groupenergy.core.entity.vo"
class: "SchedulingSchemeVO"
missing_method: "getName()"
description: "无法解析方法 'getName()'"
line: [34]
```

