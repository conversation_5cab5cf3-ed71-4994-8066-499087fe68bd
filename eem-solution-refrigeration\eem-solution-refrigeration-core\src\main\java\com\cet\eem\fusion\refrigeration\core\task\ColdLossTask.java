﻿package com.cet.eem.fusion.refrigeration.core.task;


import com.cet.eem.bll.common.task.TaskSchedule;
import com.cet.eem.fusion.refrigeration.core.handle.LossActualColdHandle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2021/12/23
 */
@Component
@Slf4j
public class ColdLossTask implements TaskSchedule {
    @Autowired
    LossActualColdHandle lossActualColdHandle;

    @Scheduled(cron = "${cet.eem.task.energy-saving.loss.interval}")
    @Override
    public void execute() throws IOException, InstantiationException, IllegalAccessException {
        lossActualColdHandle.calcLossData();
    }
}

