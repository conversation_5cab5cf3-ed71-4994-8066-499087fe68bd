package com.cet.eem.fusion.maintenance.core.controller.inspect;

import com.cet.eem.maintenanceservice.controller.bff.inspect.InspectorWorkOrderBffController;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021-04-02 10:08
 */
@Api(value = "/eem/v1/workorder/inspector", tags = "工单：巡检工单")
@RequestMapping(value = "/eem/v1/workorder/inspector")
@RestController
@Validated
public class InspectorWorkOrderController extends InspectorWorkOrderBffController {

}
