package com.cet.eem.fusion.maintenance.core.entity.workorder.inspection;

import com.cet.electric.workflow.common.model.params.ReviewManyTaskParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 工单审核
 *
 * <AUTHOR>
 * @date 2021/5/7
 */
@Getter
@Setter
@ApiModel(description = "工单审核")
public class WorkOrderBatchReviewVo {
    @ApiModelProperty("工单编号")
    @NotEmpty(message = "工单编号不允许为空！")
    private List<String> codes;

    @ApiModelProperty("参数")
    @NotEmpty(message = "审核参数不允许为空！")
    private ReviewManyTaskParams params;
}
