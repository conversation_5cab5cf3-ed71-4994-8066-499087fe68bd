package com.cet.eem.fusion.maintenance.core.dao.devicecomponent.impl;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.DeviceSystem;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SparePartsDevice;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.DeviceSystemWithSubLayer;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.bll.maintenance.dao.devicecomponent.DeviceSystemDao;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Repository
public class DeviceSystemDaoImpl extends ModelDaoImpl<DeviceSystem> implements DeviceSystemDao {


    @Override
    public List<DeviceSystem> queryAllSystem() {
        LambdaQueryWrapper<DeviceSystem> wrapper = LambdaQueryWrapper.of(DeviceSystem.class)
                .eq(DeviceSystem::getProjectId, GlobalInfoUtils.getTenantId());

        return this.selectList(wrapper);
    }

    @Override
    public List<Map<String, Object>> queryDeviceTree() {
        QueryConditionBuilder<BaseEntity> queryConditionBuilder = ParentQueryConditionBuilder.of(ModelLabelDef.DEVICE_SYSTEM);
        queryConditionBuilder.selectChildByLabels(Arrays.asList(ModelLabelDef.SPARE_PARTS_DEVICE, ModelLabelDef.SPARE_PARTS_STORAGE));
        queryConditionBuilder.queryAsTree();
        ResultWithTotal<List<Map<String, Object>>> query = eemModelDataService.query(queryConditionBuilder.build());
        return query.getData();
    }

    @Override
    public List<Map<String, Object>> queryDeviceTreeByProjectId(Long projectId) {
        QueryConditionBuilder<BaseEntity> queryConditionBuilder = ParentQueryConditionBuilder.of(ModelLabelDef.DEVICE_SYSTEM)
                .eq(ColumnDef.PROJECT_ID,projectId);
        queryConditionBuilder.selectChildByLabels(Arrays.asList(ModelLabelDef.SPARE_PARTS_DEVICE, ModelLabelDef.SPARE_PARTS_STORAGE));
        queryConditionBuilder.queryAsTree();
        ResultWithTotal<List<Map<String, Object>>> query = eemModelDataService.query(queryConditionBuilder.build());
        return query.getData();
    }

    @Override
    public DeviceSystem queryByName(String name) {
        LambdaQueryWrapper<DeviceSystem> queryWrapper = LambdaQueryWrapper.of(DeviceSystem.class)
                .eq(DeviceSystem::getName, name)
                .eq(DeviceSystem::getProjectId, GlobalInfoUtils.getTenantId());
        return this.selectOne(queryWrapper);
    }

    @Override
    public DeviceSystem queryByNameAndId(String name, Long id) {
        LambdaQueryWrapper<DeviceSystem> queryWrapper = LambdaQueryWrapper.of(DeviceSystem.class)
                .eq(DeviceSystem::getName, name)
                .ne(DeviceSystem::getId, id)
                .eq(DeviceSystem::getProjectId, GlobalInfoUtils.getTenantId());
        return this.selectOne(queryWrapper);
    }

    @Override
    public List<DeviceSystemWithSubLayer> queryBySystemId(Long id) {
        LambdaQueryWrapper<DeviceSystem> queryWrapper = LambdaQueryWrapper.of(DeviceSystem.class)
                .eq(DeviceSystem::getId, id)
                .eq(DeviceSystem::getProjectId, GlobalInfoUtils.getTenantId());
        return this.selectRelatedList(DeviceSystemWithSubLayer.class, queryWrapper);
    }

    @Override
    public List<DeviceSystemWithSubLayer> queryBySystemIds(List<Long> ids) {
        LambdaQueryWrapper<DeviceSystem> queryWrapper = LambdaQueryWrapper.of(DeviceSystem.class)
                .in(DeviceSystem::getId, ids)
                .eq(DeviceSystem::getProjectId, GlobalInfoUtils.getTenantId());
        return this.selectRelatedList(DeviceSystemWithSubLayer.class, queryWrapper);
    }

    @Override
    public DeviceSystemWithSubLayer queryByDeviceModelAndLabel(String model, String objectLabel) {
        LambdaQueryWrapper<SparePartsDevice> wrapper = LambdaQueryWrapper.of(SparePartsDevice.class)
                .eq(SparePartsDevice::getModel, model).eq(SparePartsDevice::getObjectLabel, objectLabel);
        List<DeviceSystemWithSubLayer> deviceSystemWithSubLayers = this.selectRelatedList(DeviceSystemWithSubLayer.class, null, Collections.singletonList(wrapper));
        if (CollectionUtils.isEmpty(deviceSystemWithSubLayers)) {
            return null;
        }
        for (DeviceSystemWithSubLayer deviceSystemWithSubLayer : deviceSystemWithSubLayers) {
            if (deviceSystemWithSubLayer != null) {
                return deviceSystemWithSubLayer;
            }
        }
        return null;
    }
}


