package com.cet.eem.fusion.maintenance.core.entity.po;

import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @Author: jiangzixuan
 * @Description:
 * @Data: Created in 2021-05-12
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.SPAREPARTS_REPLACERECORD)
@ApiModel("备件更换记录")
public class SparePartsReplaceRecord extends EntityWithName {

    @ApiModelProperty("更换时间")
    private Long logtime;

    @ApiModelProperty("更换数量")
    private Double number;

    @ApiModelProperty("设备id")
    @JsonProperty("objectid")
    private Long objectId;

    @ApiModelProperty("设备表示")
    @JsonProperty("objectlabel")
    private String objectLabel;

    @JsonProperty("sparepartsstorageid")
    @ApiModelProperty("备件id")
    private Long sparePartsStorageId;

    @JsonProperty("workorderid")
    @ApiModelProperty("工单id")
    private Long workOrderId;

    @JsonProperty("devicesystemid")
    @ApiModelProperty("备件所属系统id")
    private Long deviceSystemId;

    public SparePartsReplaceRecord(Double number, String objectLabel) {
        this.number = number;
        this.objectLabel = objectLabel;
        this.modelLabel = ModelLabelDef.SPAREPARTS_REPLACERECORD;
    }

    public SparePartsReplaceRecord(Double number, String objectLabel, Long objectId) {
        this.number = number;
        this.objectLabel = objectLabel;
        this.objectId = objectId;
        this.modelLabel = ModelLabelDef.SPAREPARTS_REPLACERECORD;
    }

    public SparePartsReplaceRecord() {
        this.modelLabel = ModelLabelDef.SPAREPARTS_REPLACERECORD;
    }
}
