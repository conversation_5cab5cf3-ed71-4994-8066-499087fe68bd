package com.cet.eem.fusion.maintenance.core.schedule.strategy;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.PlanSheet;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import org.quartz.CronScheduleBuilder;
import org.quartz.CronTrigger;
import org.quartz.ScheduleBuilder;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * @ClassName : OnlyOnceTriggerStrategy
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-23 15:08
 */
@Component(PlanSheetTriggerStrategyKey.ONLY_ONCE)
public class OnlyOnceTriggerStrategy implements PlanSheetTriggerStrategy<CronTrigger> {

    /**
     * 秒 分钟 小时 日 月 ？ 年
     */
    private static final MessageFormat ONLY_ONCE_CRON = new MessageFormat("{0} {1} {2} {3} {4} ? {5,number,#}");

    @Override
    public ScheduleBuilder<CronTrigger> buildSchedule(PlanSheet planSheet) {
        LocalDateTime startTime = TimeUtil.timestamp2LocalDateTime(planSheet.getExecuteTime());
        if (Objects.nonNull(planSheet.getAheadDuration())) {
            startTime = startTime.minusMinutes(planSheet.getAheadDuration());
        }
        String cron = ONLY_ONCE_CRON.format(new Object[]{startTime.getSecond(), startTime.getMinute(), startTime.getHour(), startTime.getDayOfMonth(), startTime.getMonthValue(), startTime.getYear()});
        return CronScheduleBuilder.cronSchedule(cron).withMisfireHandlingInstructionDoNothing();
    }
}
