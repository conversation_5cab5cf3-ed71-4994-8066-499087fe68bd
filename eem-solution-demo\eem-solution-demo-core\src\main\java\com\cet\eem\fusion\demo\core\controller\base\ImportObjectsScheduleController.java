package com.cet.eem.fusion.demo.core.controller.base;

import com.cet.eem.fusion.config.sdk.controller.BaseImportObjectsScheduleController;
import com.cet.eem.solution.common.def.common.PluginInfoDef;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>  (2025/8/26 10:00)
 */
@Api(value = "ImportObjectsScheduleController", tags = "导入相关信息")
@RequestMapping(value = PluginInfoDef.Demo.INTERFACE_PREFIX + "/v1/importobject")
@RestController
@Validated
public class ImportObjectsScheduleController extends BaseImportObjectsScheduleController {
}