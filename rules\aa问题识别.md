你需要根据



## 插件定义位置是否正确

## 实体类问题
### 继承BaseEntity问题

- 如果实体类继承了BaseEntity，那么则是有缺陷的，应该输出

## Controller层
### 插件url路径规范问题
检查

### ApiResult识别问题

## Service层

## dao层

## GlobalInfoUtils.getProjectId()废弃

## 常量变更
请求状态码：
变更前：Result.SUCCESS_CODE
变更后：ErrorCode.SUCCESS_CODE

EemCommonUtils.BLANK_STR(com.cet.eem.bll.common.util.EemCommonUtils;)
StringFormatUtils.BLANK_STR(com.cet.eem.fusion.common.utils.datatype.StringFormatUtils;)

CommonUtils.BLANK_STR(com.cet.eem.common.CommonUtils;)
StringFormatUtils.BLANK_STR(com.cet.eem.fusion.common.utils.datatype.StringFormatUtils;)

CommonUtils.APPLICATION_MSEXCEL
ContentTypeDef.APPLICATION_MSEXCEL

CommonUtils.APPLICATION_MS_EXCEL_07
ContentTypeDef.APPLICATION_MS_EXCEL_07

CommonUtils.DOUBLE_CONVERSION_COEFFICIENT
NumberCalcUtils.DOUBLE_CONVERSION_COEFFICIENT


# 工程不再提供projectid，而是改用tenantId,请注意代码的所有地方的修改，
相关tenantid获取的方式变更如下
变更前：GlobalInfoUtils.getProjectId()
变更后：GlobalInfoUtils.getTenantId()

变更前：CommonUtils.calcDouble()
变更后：NumberCalcUtils.calcDouble()（同步修改所属类路径：import com.cet.eem.fusion.common.utils.datatype.NumberCalcUtils;）

变更前：CommonUtils.parseInteger()
变更后：NumberUtils.parseInteger()（同步修改所属类路径：import com.cet.eem.fusion.common.utils.datatype.NumberCalcUtils;）

CommonUtils.formatDoubleWithOutScientificNotation()
StringFormatUtils.formatDoubleWithOutScientificNotation()

## UnitService废弃

## EemCloudAuthService废弃

## nodeAuthBffService.checkPartAuth

## AuthUtils废弃

## GlobalInfoUtils.getHttpResponse()废弃

## JsonUtil废弃

## CommonUtils.sort废弃

## commonUtilsService.writeAddOperationLogs()废弃