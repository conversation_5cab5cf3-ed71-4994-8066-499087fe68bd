package com.cet.eem.solution.common.entity.vo.cetml;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/15 16:24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductLiquidVO {
    @ApiModelProperty("产液量预测结果")
    @JsonProperty("liquid_pred")
    private List<Double> productData;

    @ApiModelProperty("有效冲程")
    @JsonProperty("stroke_eff_list")
    private List<Double> effectStroke;
}