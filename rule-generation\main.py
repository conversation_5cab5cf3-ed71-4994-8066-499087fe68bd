"""
Java代码检索工具主程序
批量检索指定目录下的所有Java文件并生成MD报告
"""

import sys
import logging
import argparse
import os
from service.search_service import SearchService
from service.report_service_v2 import ReportServiceV2

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class BatchSearchTool:
    """批量检索工具类"""

    def __init__(self):
        """初始化工具"""
        self.search_service = SearchService()
        self.report_service = ReportServiceV2()

    def batch_search_directory(self, directory_path: str, k: int = None, threshold: float = None, output_filename: str = None, exclude_controller: bool = False):
        """
        批量检索目录下的所有Java文件
        Args:
            directory_path (str): 目录路径
            k (int): 每个文件返回的结果数量
            threshold (float): 相似度阈值
            output_filename (str): 输出报告文件名
            exclude_controller (bool): 是否忽略包含Controller的文件
        Returns:
            str: 报告文件路径
        """
        if threshold is not None:
            # 临时更新报告服务的阈值
            self.report_service.similarity_threshold = threshold

        logger.info("=" * 50)
        logger.info("开始批量检索")
        logger.info(f"目录路径: {directory_path}")
        logger.info(f"每文件结果数: {k or 3}")
        logger.info(f"相似度阈值: {threshold or self.report_service.similarity_threshold}")
        logger.info(f"忽略Controller文件: {'是' if exclude_controller else '否'}")
        logger.info("=" * 50)

        try:
            # 执行批量检索
            search_results = self.search_service.search_directory(directory_path, k, exclude_controller)

            if not search_results["success"]:
                logger.error(f"批量检索失败: {search_results.get('error', '未知错误')}")
                return None

            # 生成报告
            logger.info("生成检索报告...")
            report_path = self.report_service.generate_search_report(
                search_results,
                output_filename
            )

            logger.info("=" * 50)
            logger.info("批量检索完成")
            logger.info(f"总文件数: {search_results['total_files']}")
            logger.info(f"成功检索: {search_results['successful_searches']}")
            logger.info(f"失败检索: {search_results['failed_searches']}")
            logger.info(f"报告文件: {report_path}")
            logger.info("=" * 50)
            return report_path

        except Exception as e:
            logger.error(f"批量检索失败: {e}")
            raise

    def cleanup(self):
        """清理资源"""
        try:
            self.search_service.cleanup()
            logger.info("资源清理完成")
        except Exception as e:
            logger.warning(f"资源清理失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Java代码批量检索工具')
    parser.add_argument(
        '--directory', '-d',
        type=str,
        required=True,
        help='要检索的目录路径'
    )
    parser.add_argument(
        '--limit', '-k',
        type=int,
        default=3,
        help='每个文件返回的结果数量 (默认: 3)'
    )
    parser.add_argument(
        '--threshold', '-t',
        type=float,
        default=0.7,
        help='相似度阈值 (默认: 0.7)'
    )
    parser.add_argument(
        '--output', '-o',
        type=str,
        help='输出报告文件名 (可选)'
    )
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出'
    )
    parser.add_argument(
        '--exclude-controller',
        action='store_true',
        help='忽略包含Controller的文件 (默认: 不忽略)'
    )

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 验证输入目录
    if not os.path.exists(args.directory):
        print(f"❌ 目录不存在: {args.directory}")
        sys.exit(1)

    if not os.path.isdir(args.directory):
        print(f"❌ 路径不是目录: {args.directory}")
        sys.exit(1)

    # 显示配置信息
    print("=" * 60)
    print("Java代码批量检索工具")
    print("=" * 60)
    print(f"检索目录: {args.directory}")
    print(f"每文件结果数: {args.limit}")
    print(f"相似度阈值: {args.threshold}")
    print(f"输出文件: {args.output or '自动生成'}")
    print(f"详细输出: {'是' if args.verbose else '否'}")
    print(f"忽略Controller文件: {'是' if args.exclude_controller else '否'}")
    print("=" * 60)

    # 初始化工具
    batch_tool = BatchSearchTool()

    try:
        # 执行批量检索
        report_path = batch_tool.batch_search_directory(
            args.directory,
            args.limit,
            args.threshold,
            args.output,
            args.exclude_controller
        )

        if report_path:
            print(f"\n✅ 批量检索成功！")
            print(f"📄 报告文件: {report_path}")

            # 显示报告文件的绝对路径
            abs_report_path = os.path.abspath(report_path)
            print(f"📁 完整路径: {abs_report_path}")
        else:
            print(f"\n❌ 批量检索失败")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"批量检索过程中发生异常: {e}")
        print(f"\n❌ 批量检索失败: {e}")
        sys.exit(1)
    finally:
        batch_tool.cleanup()


if __name__ == "__main__":
    main()