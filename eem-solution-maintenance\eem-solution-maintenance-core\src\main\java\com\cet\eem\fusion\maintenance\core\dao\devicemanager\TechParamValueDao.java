package com.cet.eem.fusion.maintenance.core.dao.devicemanager;

import com.cet.eem.bll.maintenance.model.devicemanage.EquipmentSearchDto;
import com.cet.eem.bll.maintenance.model.devicemanage.TechParamValue;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;

import java.util.List;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-17
 */
public interface TechParamValueDao extends BaseModelDao<TechParamValue> {

    /**
     * 查看设备对应的技术参数
     * @param searchDto
     * @return
     */
    List<TechParamValue> queryTechParam(EquipmentSearchDto searchDto);

    List<TechParamValue> queryTechParam(List<BaseVo> baseVos);

    List<TechParamValue> queryTechParamWith(List<TechParamValue> techParamValues);
}
