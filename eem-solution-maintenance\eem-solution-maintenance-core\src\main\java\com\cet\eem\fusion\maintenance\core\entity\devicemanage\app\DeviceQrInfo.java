package com.cet.eem.fusion.maintenance.core.entity.devicemanage.app;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/5/30 14:40
 */
@Data
public class DeviceQrInfo {
    /**
     * 节点ID
     */
    @JsonProperty(value = "nodeID")
    private Long nodeId;
    /**
     * 节点名称
     */
    private String nodeName;
    /**
     * 节点类型
     */
    private Long nodeType;
    /**
     * 类型名称
     */
    private String nodeTypeName;
    /**
     * 状态
     */
    private Integer state;
    /**
     * 父节点ID
     */
    private Long parentNodeId;
    /**
     * 图片
     */
    private String picture;
    /**
     * 分区名称
     */
    private String areaName;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 配电室名称
     */
    private String substationName;
}
