package com.cet.eem.solution.common.entity.vo.peccore;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ConfigServer获取核心平台设备下测点信息
 * <AUTHOR>
 * @date 2023/11/6 16:42
 */
@Data
public class MeasureInfo {
    @ApiModelProperty("测点id")
    private Integer measId;

    @ApiModelProperty("测点名称")
    private String measName;

    private Integer measType;

    private Integer measDef;

    private Integer measAmp;

    private Integer measDiv;

    private Double measMin;

    private Double measMax;
}
