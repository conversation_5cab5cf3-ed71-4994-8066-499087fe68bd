package com.cet.eem.fusion.maintenance.core.service.maintenance.impl;

import com.cet.eem.fusion.config.sdk.service.EemNodeService;
import com.cet.eem.bll.common.dao.project.ProjectDao;
import com.cet.electric.baseconfig.common.entity.Project;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.*;
import com.cet.eem.bll.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.PlanSheetWithSubLayer;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.bll.maintenance.dao.PlanSheetDao;
import com.cet.eem.bll.maintenance.dao.WorkOrderDao;
import com.cet.eem.bll.maintenance.model.plan.AddMaintenancePlanRequest;
import com.cet.eem.bll.maintenance.model.plan.EditMaintenancePlanRequest;
import com.cet.eem.bll.maintenance.model.plan.MaintenancePlanSheetVo;
import com.cet.eem.bll.maintenance.model.plan.QueryMaintenancePlanRequest;
import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.bll.maintenance.schedule.domain.PlanSheetDomain;
import com.cet.eem.bll.maintenance.service.inspection.InspectionPlanService;
import com.cet.eem.bll.maintenance.service.maintenance.MaintenancePlanService;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.exception.ValidationException;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.conditions.update.LambdaUpdateWrapper;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.service.EemCloudAuthService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.quartz.SchedulerException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : MaintenancePlanServiceImpl
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-05-14 09:01
 */
@Service
public class MaintenancePlanServiceImpl implements MaintenancePlanService {

    @Autowired
    private PlanSheetDao planSheetDao;

    @Autowired
    private ProjectDao projectDao;

    @Autowired
    private EemCloudAuthService eemCloudAuthService;

    @Autowired
    private PlanSheetDomain planSheetDomain;

    @Autowired
    private InspectionPlanService inspectionPlanService;

    @Autowired
    private ModelServiceUtils modelServiceUtils;

    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    NodeDao nodeDao;

    @Override
    public PlanSheet addMaintenancePlan(AddMaintenancePlanRequest addMaintenancePlanRequest) throws SchedulerException {
        checkPlanNameIsRepeatWhileCreate(addMaintenancePlanRequest.getName());
        PlanSheetWithSubLayer planSheetWithSubLayer = new PlanSheetWithSubLayer();
        BeanUtils.copyProperties(addMaintenancePlanRequest, planSheetWithSubLayer);
        //设置当前用户信息
        setUserInfo(planSheetWithSubLayer);
        //设置当前项目信息
        setProjectInfo(planSheetWithSubLayer);
        //设置类型信息
        planSheetWithSubLayer.setWorkSheetType(WorkSheetTaskType.MAINTENANCE);
        planSheetWithSubLayer.setEnabledDays(EnabledDays.allDaysInstance());
        planSheetWithSubLayer.setCreateTime(System.currentTimeMillis());
        planSheetWithSubLayer.setEnabled(true);
        planSheetWithSubLayer.setDeleted(false);
        planSheetDao.insert(planSheetWithSubLayer);

        List<BaseVo> items = new ArrayList<>();
        updateMaintenanceItems(planSheetWithSubLayer, items);

        try {
            planSheetDomain.startInspectionPlan(planSheetWithSubLayer);
        } catch (Exception e) {
            //模型服务不支持事务功能，只能异常时删除，当作回滚操作，不能完全避免脏数据
            deleteMaintenancePlan(Collections.singletonList(planSheetWithSubLayer.getId()));
            throw e;
        }
        return planSheetWithSubLayer;
    }

    private void updateMaintenanceItems(PlanSheetWithSubLayer planSheetWithSubLayer, List<BaseVo> items) {
        // 查询已经存在的记录
        PlanSheetWithSubLayer oldPlanSheetWithSubLayer = planSheetDao.selectRelatedById(PlanSheetWithSubLayer.class, planSheetWithSubLayer.getId());

        // 如果未关联任何维保项
        MaintenanceExtend maintenanceExtend = planSheetWithSubLayer.getMaintenanceExtend();
        if (maintenanceExtend == null || CollectionUtils.isEmpty(maintenanceExtend.getMaintenanceItems())) {
            List<MaintenanceItem> maintenanceItemList = oldPlanSheetWithSubLayer.getMaintenanceItemList();
            if (CollectionUtils.isEmpty(maintenanceItemList)) {
                return;
            }

            planSheetDao.moveChild(planSheetWithSubLayer.getId(), maintenanceItemList);
            return;
        }

        // 提取出维保项
        List<MaintenanceExtend.GroupItemMapping> maintenanceItems = maintenanceExtend.getMaintenanceItems();
        for (MaintenanceExtend.GroupItemMapping maintenanceItem : maintenanceItems) {
            if (CollectionUtils.isEmpty(maintenanceItem.getItemIds())) {
                continue;
            }
            maintenanceItem.getItemIds().forEach(itemId -> items.add(new BaseVo(itemId, ModelLabelDef.MAINTENANCE_ITEM)));
        }

        // 判断旧关联的数据中哪些需要移除
        List<MaintenanceItem> oldMaintenanceItemList = oldPlanSheetWithSubLayer.getMaintenanceItemList();
        if (CollectionUtils.isNotEmpty(oldMaintenanceItemList)) {
            List<MaintenanceItem> removeItems = new ArrayList<>();
            for (MaintenanceItem item : oldMaintenanceItemList) {
                Optional<BaseVo> any = items.stream().filter(it -> it.getId().equals(item.getId())).findAny();
                if (!any.isPresent()) {
                    removeItems.add(item);
                }
            }

            if (CollectionUtils.isNotEmpty(removeItems)) {
                planSheetDao.moveChild(planSheetWithSubLayer.getId(), removeItems);
            }
        }

        modelServiceUtils.writeRelations(new BaseVo(planSheetWithSubLayer.getId(), planSheetWithSubLayer.getModelLabel()), items);
    }

    @Override
    public PlanSheet editMaintenancePlan(EditMaintenancePlanRequest editMaintenancePlanRequest) throws SchedulerException {
        checkPlanNameIsRepeatWhileEdit(editMaintenancePlanRequest.getId(), editMaintenancePlanRequest.getName());
        PlanSheet planSheet = planSheetDao.selectById(editMaintenancePlanRequest.getId());
        Assert.notNull(planSheet, "维保工单不存在！");
        BeanUtils.copyProperties(editMaintenancePlanRequest, planSheet);
        planSheetDomain.reschedulePlan(planSheet);
        planSheetDao.updateById(planSheet);
        updateMaintenanceTarget(editMaintenancePlanRequest);
        return planSheetDao.selectRelatedById(PlanSheetWithSubLayer.class, editMaintenancePlanRequest.getId());
    }

    @Override
    public void disablePlanSheet(Collection<Long> ids) throws SchedulerException {
        LambdaUpdateWrapper<PlanSheet> updateWrapper = LambdaUpdateWrapper.of(PlanSheet.class);
        updateWrapper.set(PlanSheet::getEnabled, false);
        LambdaQueryWrapper<PlanSheet> queryWrapper = LambdaQueryWrapper.of(PlanSheet.class);
        queryWrapper.in(PlanSheet::getId, ids);
        queryWrapper.eq(PlanSheet::getEnabled, true);
        planSheetDomain.pausePlan(ids);
        planSheetDao.update(updateWrapper, queryWrapper);
    }

    private void checkPlanSheetEndTimeIsValid(List<PlanSheet> planSheetList) {
        LocalDateTime now = LocalDateTime.now();
        for (PlanSheet planSheet : planSheetList) {
            if (Objects.isNull(planSheet.getFinishTime())) {
                continue;
            }
            LocalDateTime finishTime = TimeUtil.timestamp2LocalDateTime(planSheet.getFinishTime());
            if (finishTime.isBefore(now)) {
                throw new ValidationException(String.format("%s由于已达到结束时间无法启用,请修改结束时间后重试", planSheet.getName()));
            }
        }
    }

    @Override
    public void enablePlanSheet(Collection<Long> ids) throws SchedulerException {
        LambdaQueryWrapper<PlanSheet> queryWrapper = LambdaQueryWrapper.of(PlanSheet.class);
        queryWrapper.in(PlanSheet::getId, ids);
        queryWrapper.eq(PlanSheet::getEnabled, false);
        List<PlanSheet> planSheetList = planSheetDao.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(planSheetList)) {
            return;
        }
        checkPlanSheetEndTimeIsValid(planSheetList);
        planSheetList.forEach(s -> s.setEnabled(true));
        planSheetDao.saveOrUpdateBatch(planSheetList);

        List<Long> idsAfterFilter = planSheetList.stream().map(PlanSheet::getId).collect(Collectors.toList());
        planSheetDomain.resumePlan(idsAfterFilter);
    }

    @Override
    public ResultWithTotal<List<MaintenancePlanSheetVo>> queryMaintenancePlanBySchedule(QueryMaintenancePlanRequest queryInspectionPlanRequest) {
        ResultWithTotal<List<PlanSheetWithSubLayer>> listResultWithTotal = planSheetDao.queryMaintenancePlanBySchedule(queryInspectionPlanRequest);
        List<MaintenancePlanSheetVo> planSheetVoList = transferToVo(listResultWithTotal.getData());
        if (CollectionUtils.isNotEmpty(planSheetVoList)) {
            inspectionPlanService.setTeamName(planSheetVoList, queryInspectionPlanRequest.getTenantId());
            inspectionPlanService.setNextExecuteTime(planSheetVoList);
            setLevelName(planSheetVoList);
        }
        List<MaintenancePlanSheetVo> collect = planSheetVoList.stream().sorted(Comparator.comparing(MaintenancePlanSheetVo::getId).reversed()).collect(Collectors.toList());
        return ResultWithTotal.ok(collect, listResultWithTotal.getTotal());

    }

    @Override
    public ResultWithTotal<List<MaintenancePlanSheetVo>> queryMaintenancePlan(QueryMaintenancePlanRequest queryInspectionPlanRequest) {
        ResultWithTotal<List<PlanSheetWithSubLayer>> listResultWithTotal = planSheetDao.queryMaintenancePlanSheetSubLayerWithPage(queryInspectionPlanRequest);
        List<MaintenancePlanSheetVo> planSheetVoList = transferToVo(listResultWithTotal.getData());
        if (CollectionUtils.isNotEmpty(planSheetVoList)) {
            inspectionPlanService.setTeamName(planSheetVoList, queryInspectionPlanRequest.getTenantId());
            inspectionPlanService.setNextExecuteTime(planSheetVoList);
            setLevelName(planSheetVoList);
        }
        List<MaintenancePlanSheetVo> planSheetList = planSheetVoList.stream().sorted(Comparator.comparing(MaintenancePlanSheetVo::getId).reversed()).collect(Collectors.toList());

        assemblyPlanInfo(planSheetList);

        return ResultWithTotal.ok(planSheetList, listResultWithTotal.getTotal());
    }

    /**
     * 组装计划信息
     *
     * @param planSheetList 计划列表
     */
    private void assemblyPlanInfo(List<MaintenancePlanSheetVo> planSheetList) {
        if (CollectionUtils.isEmpty(planSheetList)) {
            return;
        }

        // 提取出所有的计划与巡检对象的关系
        List<DevicePlanRelationship> relationShipList = planSheetList.stream()
                .filter(it -> CollectionUtils.isNotEmpty(it.getDevicePlanRelationshipList()))
                .flatMap(it -> it.getDevicePlanRelationshipList().stream()).collect(Collectors.toList());

        // 提取巡检对象，并查询和组装名称
        List<BaseVo> nodes = relationShipList.stream().map(it -> new BaseVo(it.getDeviceId(), it.getDeviceLabel())).collect(Collectors.toList());
        Map<BaseVo, String> nodeNameMap = nodeDao.queryNodeNameMap(nodes);
        relationShipList.forEach(it-> it.setDeviceName(nodeNameMap.get(new BaseVo(it.getDeviceId(), it.getDeviceLabel()))));
    }

    @Override
    public List<InspectionWorkOrderDto> queryPlanWorkOrder(Long workSheetId) {
        return workOrderDao.queryWorkOrderByPlanSheetId(workSheetId, WorkSheetTaskType.INSPECTION);
    }

    private void setLevelName(List<MaintenancePlanSheetVo> planSheetVoList) {
        Map<Integer, String> enumMap = modelServiceUtils.getEnumByLabel(ModelLabelDef.WORKSHEET_TASK_LEVEL);
        planSheetVoList.forEach(s -> {
            if (Objects.nonNull(s.getWorksheetTaskLevel())) {
                s.setWorksheetTaskLevelName(enumMap.get(s.getWorksheetTaskLevel()));
            }
        });

    }

    private List<MaintenancePlanSheetVo> transferToVo(List<PlanSheetWithSubLayer> data) {
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }
        return data.stream().map(MaintenancePlanSheetVo::new).collect(Collectors.toList());
    }

    private void checkNameRepeat(List<PlanSheet> planSheetList) {
        if (CollectionUtils.isEmpty(planSheetList)) {
            return;
        }
        planSheetList = planSheetList.stream().filter(it -> !BooleanUtils.isTrue(it.getDeleted())).collect(Collectors.toList());
        Assert.isTrue(CollectionUtils.isEmpty(planSheetList), "维保计划名称重复");
    }

    private void checkPlanNameIsRepeatWhileEdit(Long id, String planName) {
        LambdaQueryWrapper<PlanSheet> queryWrapper = LambdaQueryWrapper.of(PlanSheet.class);
        queryWrapper.eq(PlanSheet::getName, planName);
        queryWrapper.eq(PlanSheet::getProjectId, GlobalInfoUtils.getTenantId());
        queryWrapper.eq(PlanSheet::getWorkSheetType, WorkSheetTaskType.MAINTENANCE);
        queryWrapper.ne(PlanSheet::getId, id);
        List<PlanSheet> planSheetList = planSheetDao.selectList(queryWrapper);
        checkNameRepeat(planSheetList);
    }

    private void checkPlanNameIsRepeatWhileCreate(String planName) {
        LambdaQueryWrapper<PlanSheet> queryWrapper = LambdaQueryWrapper.of(PlanSheet.class);
        queryWrapper.eq(PlanSheet::getName, planName)
                .eq(PlanSheet::getWorkSheetType, WorkSheetTaskType.MAINTENANCE)
                .eq(PlanSheet::getProjectId, GlobalInfoUtils.getTenantId());
        List<PlanSheet> planSheetList = planSheetDao.selectList(queryWrapper);
        checkNameRepeat(planSheetList);
    }

    private void setProjectInfo(PlanSheetWithSubLayer planSheetWithSubLayer) {
        Long projectId = GlobalInfoUtils.getTenantId();
        if (Objects.isNull(projectId)) {
            return;
        }
        Project project = projectDao.selectById(projectId);
        planSheetWithSubLayer.setProjectId(project.getId());
        planSheetWithSubLayer.setProjectName(project.getName());
    }

    private void setUserInfo(PlanSheetWithSubLayer planSheetWithSubLayer) {
        Long userId = GlobalInfoUtils.getUserId();
        if (Objects.isNull(userId)) {
            return;
        }
        Result<UserVo> userVoResult = eemCloudAuthService.queryByUserId(userId);
        userVoResult.throwExceptionIfFailed();
        UserVo userVo = userVoResult.getData();
        planSheetWithSubLayer.setCreator(userVo.getId());
        planSheetWithSubLayer.setCreatorName(userVo.getName());
    }

    private void updateMaintenanceTarget(EditMaintenancePlanRequest editInspectionPlanRequest) {
        PlanSheetWithSubLayer planSheetWithSubLayer = planSheetDao.selectRelatedById(PlanSheetWithSubLayer.class, editInspectionPlanRequest.getId());
        doSaveInspectionTarget(editInspectionPlanRequest, planSheetWithSubLayer);
        doDeleteInspectionTarget(editInspectionPlanRequest, planSheetWithSubLayer);
    }


    private void doSaveInspectionTarget(EditMaintenancePlanRequest editInspectionPlanRequest, PlanSheetWithSubLayer planSheetWithSubLayer) {
        List<DevicePlanRelationship> newDeviceList = editInspectionPlanRequest.getDeviceList();
        List<DevicePlanRelationship> oldDeviceList = planSheetWithSubLayer.getDevicePlanRelationshipList();
        if (CollectionUtils.isEmpty(newDeviceList)) {
            return;
        }
        List<DevicePlanRelationship> devicePlanRelationshipList = newDeviceList.stream().filter(s -> {
            if (CollectionUtils.isEmpty(oldDeviceList)) {
                return true;
            }
            for (DevicePlanRelationship devicePlanRelationship : oldDeviceList) {
                if (s.contentEquals(devicePlanRelationship)) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(devicePlanRelationshipList)) {
            planSheetDao.insertChild(editInspectionPlanRequest.getId(), devicePlanRelationshipList);
        }
    }

    private void doDeleteInspectionTarget(EditMaintenancePlanRequest editInspectionPlanRequest, PlanSheetWithSubLayer planSheetWithSubLayer) {
        List<DevicePlanRelationship> newDeviceList = editInspectionPlanRequest.getDeviceList();
        List<DevicePlanRelationship> oldDeviceList = planSheetWithSubLayer.getDevicePlanRelationshipList();
        if (CollectionUtils.isEmpty(oldDeviceList)) {
            return;
        }
        List<DevicePlanRelationship> devicePlanRelationshipList = oldDeviceList.stream().filter(s -> {
            if (CollectionUtils.isEmpty(newDeviceList)) {
                return true;
            }
            for (DevicePlanRelationship devicePlanRelationship : newDeviceList) {
                if (s.contentEquals(devicePlanRelationship)) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(devicePlanRelationshipList)) {
            planSheetDao.deleteChild(editInspectionPlanRequest.getId(), devicePlanRelationshipList);
        }
    }

    public void deleteMaintenancePlan(List<Long> inspectionPlanIds) throws SchedulerException {
        planSheetDomain.deletePlan(inspectionPlanIds);
        planSheetDao.deletePlanSheets(inspectionPlanIds);
    }

}
