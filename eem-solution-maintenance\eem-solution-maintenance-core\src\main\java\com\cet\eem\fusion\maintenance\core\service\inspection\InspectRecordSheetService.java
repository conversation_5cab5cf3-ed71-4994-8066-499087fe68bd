package com.cet.eem.fusion.maintenance.core.service.inspection;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.InspectionParameter;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.InspectionScheme;
import com.cet.eem.bll.maintenance.model.devicemanage.TechParamValue;
import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.bll.maintenance.model.workorder.inspection.recordsheet.*;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.electric.commons.ApiResult;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * @ClassName : InspectRecordSheetService
 * @Description : 巡检记录表
 * <AUTHOR> jiangzixuan
 * @Date: 2022-10-10 17:23
 */
public interface InspectRecordSheetService {
    /**
     * 查询巡检记录表
     * @param param
     * @return
     */
    ResultWithTotal<List<InspectRecordSheetVo>> queryRecordSheet(RecordSheetQueryParam param,Long projectId);

    /**
     * 查询快速模板
     * @param projectId
     * @return
     */
    List<QueryTemplateWithNodeName> queryTemplate(Long projectId);

    /**
     * 新建文件夹或者模板
     * @param addQuickQueryTemplate
     * @param projectId
     * @return
     */
    QueryTemplate addQueryTemplate(AddQuickQueryTemplate addQuickQueryTemplate,Long projectId);

    /**
     * 更新模板
     * @param queryTemplate
     * @return
     */
    QueryTemplate updateQueryTemplate(UpdateQuickQueryTemplate queryTemplate);

    /**
     * 批量删除
     * @param id
     * @param projectId
     */
    void deleteTemplateAndFolder(List<Long> id,Long projectId);

    /**
     * 导出巡检记录表数据
     * @param param
     * @param response
     */
    void exportRecordSheet(RecordSheetQueryParam param, HttpServletResponse response,Long projectId);

    /**
     * 巡检对象节点树
     * @param nodes
     * @return
     */
    List<BaseVo> queryInspectionNodeTree(List<BaseVo> nodes);

    /**
     *
     * @param data
     * @param groupMap
     * @return
     */
    List<InspectRecordSheetVo> filterByNodes(List<InspectionWorkOrderDto> data, Map<Long, String> groupMap,List<InspectionParameter> parameters);

    List<String> queryTitle(List<Long> schemeId);

    /**
     * 根据节点--巡检计划过滤巡检方案，多节点取并集
     * @param nodes
     * @return
     */
    List<InspectionScheme> queryInspectionSchemeByNodes(List<BaseVo> nodes);

    /**
     * 查询设备信息
     * @param baseVo
     * @return
     */
    List<TechParamValue> queryNodeParamInfo(List<BaseVo> baseVo);
}
