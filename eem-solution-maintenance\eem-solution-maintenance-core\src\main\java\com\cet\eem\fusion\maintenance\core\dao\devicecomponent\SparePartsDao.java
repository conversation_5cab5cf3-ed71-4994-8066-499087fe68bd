package com.cet.eem.fusion.maintenance.core.dao.devicecomponent;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SpareParts;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.DeviceWithSubLayer;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface SparePartsDao extends BaseModelDao<SpareParts> {

    DeviceWithSubLayer queryByNameAndId(String name, Long id, Long deviceId);

    List<SpareParts> queryByDeviceAndKeyWord(Long id, String keyWord);

    List<SpareParts> queryByDevice(String model, String objectLabel);

    DeviceWithSubLayer queryByModel(String model, Long id, Long deviceId);

    /**
     * 根据备件id查询备件以及所属的备件系统
     *
     * @param ids 备件id集合
     * @return 备件信息
     */
    List<Map<String, Object>> querySparePartsStorageWithSystem(Collection<Long> ids);
}
