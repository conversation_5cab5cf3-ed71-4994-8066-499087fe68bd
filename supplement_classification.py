#!/usr/bin/env python3
"""
补充分类遗漏问题的脚本
"""

import json
from pathlib import Path

def load_json_file(file_path):
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def save_json_file(data, file_path):
    """保存JSON文件"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"Error saving {file_path}: {e}")
        return False

def supplement_classification():
    """补充分类遗漏的问题"""
    
    # 加载遗漏问题分析结果
    missing_analysis = load_json_file('output/missing_issues_analysis.json')
    if not missing_analysis:
        print("无法加载遗漏问题分析结果")
        return
    
    # 加载现有分类文件
    miss_method_data = load_json_file('output/miss_method.json') or []
    wrong_params_data = load_json_file('output/wrong_params.json') or []
    
    print(f"当前分类状态:")
    print(f"  miss_method: {len(miss_method_data)} 个问题")
    print(f"  wrong_params: {len(wrong_params_data)} 个问题")
    print(f"  需要补充: {len(missing_analysis['missing_issues'])} 个问题")
    
    # 分类遗漏的问题
    new_miss_method = []
    new_wrong_params = []
    
    for issue in missing_analysis['missing_issues']:
        # 创建分类条目
        classified_issue = {
            "class": issue['class'],
            "issue_id": issue['issue_id'],
            "error_code": issue['error_code'],
            "description": issue['description'],
            "line": issue['line']
        }
        
        # 根据错误类型分类
        if issue['error_code'] == 'miss_method':
            new_miss_method.append(classified_issue)
        elif issue['error_code'] == 'wrong_params':
            new_wrong_params.append(classified_issue)
    
    # 合并到现有分类
    updated_miss_method = miss_method_data + new_miss_method
    updated_wrong_params = wrong_params_data + new_wrong_params
    
    print(f"\n补充后的分类状态:")
    print(f"  miss_method: {len(updated_miss_method)} 个问题 (+{len(new_miss_method)})")
    print(f"  wrong_params: {len(updated_wrong_params)} 个问题 (+{len(new_wrong_params)})")
    print(f"  总计: {len(updated_miss_method) + len(updated_wrong_params)} 个问题")
    
    # 保存更新后的分类文件
    if save_json_file(updated_miss_method, 'output/miss_method.json'):
        print("✅ miss_method.json 更新成功")
    else:
        print("❌ miss_method.json 更新失败")
    
    if save_json_file(updated_wrong_params, 'output/wrong_params.json'):
        print("✅ wrong_params.json 更新成功")
    else:
        print("❌ wrong_params.json 更新失败")
    
    # 验证完整性
    total_classified = len(updated_miss_method) + len(updated_wrong_params)
    original_total = missing_analysis['summary']['total_original']
    
    print(f"\n=== 完整性验证 ===")
    print(f"原始问题总数: {original_total}")
    print(f"分类后总数: {total_classified}")
    print(f"完整性比例: {total_classified/original_total*100:.1f}%")
    
    if total_classified == original_total:
        print("🎉 完整性检查通过！所有问题都已分类")
    else:
        print(f"⚠️  仍有 {original_total - total_classified} 个问题未分类")
    
    return total_classified == original_total

if __name__ == "__main__":
    success = supplement_classification()
    if success:
        print("\n✅ 分类补充完成")
    else:
        print("\n❌ 分类补充存在问题")
