package com.cet.eem.fusion.maintenance.core.service.inspection.app.impl;

import com.cet.eem.auth.service.AuthUtils;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.DevicePlanRelationship;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInEquipment;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInPointSequence;
import com.cet.eem.bll.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.SignInGroupWithAllSubLayer;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.SignInPointWithSubLayer;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.bll.maintenance.dao.SignInGroupDao;
import com.cet.eem.bll.maintenance.dao.SignInPointDao;
import com.cet.eem.bll.maintenance.dao.WorkOrderDao;
import com.cet.eem.bll.maintenance.def.WorkOrderDef;
import com.cet.eem.bll.maintenance.def.WorkSheetStatusDef;
import com.cet.eem.bll.maintenance.model.sign.SignGroupWithSignIn;
import com.cet.eem.bll.maintenance.model.wo.MobileWorkOrderQueryVO;
import com.cet.eem.bll.maintenance.model.wo.WorkOrderSimpleQueryDTO;
import com.cet.eem.bll.maintenance.model.workorder.WoStatusCountDTO;
import com.cet.eem.bll.maintenance.model.workorder.WoStatusCountQueryVO;
import com.cet.eem.bll.maintenance.model.workorder.app.InspectionWorkOrderDetailDto;
import com.cet.eem.bll.maintenance.model.workorder.app.SignPointWithWorkOrder;
import com.cet.eem.bll.maintenance.model.workorder.app.WorkOrderCountDto;
import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.bll.maintenance.service.inspection.InspectionWorkOrderService;
import com.cet.eem.bll.maintenance.service.inspection.app.InspectionWorkOrderMobileService;
import com.cet.eem.bll.maintenance.utils.WorkSheetStatusUtils;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import com.cet.eem.fusion.common.modelutils.model.base.ConditionBlock;
import com.cet.eem.fusion.common.modelutils.model.base.Order;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/4/20
 */
@Service
public class InspectionWorkOrderMobileServiceImpl implements InspectionWorkOrderMobileService {
    @Autowired
    AuthUtils authUtils;

    @Autowired
    WorkOrderDao workOrderDao;

    @Autowired
    InspectionWorkOrderService inspectionWorkOrderService;

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    SignInGroupDao signInGroupDao;

    @Autowired
    WorkSheetStatusUtils workSheetStatusUtils;

    @Value("${cet.eem.work-order.inspect.app.summary-query-pre-day}")
    private Integer preDay;

    @Autowired
    SignInPointDao signInPointDao;

    @Override
    public InspectionWorkOrderDetailDto queryWorkOrderDetail(Page page) {
        // 查询用户信息，获取用户所在的班组信息
        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        Long teamId = null;
        List<Long> groupIds = user.getRelativeUserGroup();
        if (CollectionUtils.isNotEmpty(groupIds)) {
            teamId = groupIds.get(0);
        }

        // 根据班组查询工单信息
        List<InspectionWorkOrderDto> workOrderList = queryWorkOrder(user, teamId);

        // 根据待处理的工单关联的对象去统计签到点的数量和巡检设备进度数量
        List<WorkOrderCountDto> workOrderDetails = countWorkOrderByStatus(workOrderList);

        InspectionWorkOrderDetailDto result = new InspectionWorkOrderDetailDto();
        result.setWorkOrderDetails(workOrderDetails);
        sortWorkOrderCount(workOrderDetails, page);

        List<InspectionWorkOrderDto> list = new ArrayList<>();
        workOrderDetails.forEach(it -> {
            if (CollectionUtils.isNotEmpty(it.getWorkOrders())) {
                list.addAll(it.getWorkOrders());
            }
        });
        inspectionWorkOrderService.assemblyCommonData(list, user.getTenantId());

        // 根据签到点对工单进行分组，此处只考虑待处理的工单
        List<InspectionWorkOrderDto> toBeSentList = list.stream().filter(it -> WorkSheetStatusDef.TO_BE_SENT.equals(it.getWorkSheetStatus())).collect(Collectors.toList());
        List<SignPointWithWorkOrder> signPointWithWorkOrders = getSignPointWithWorkOrders(toBeSentList);
        result.setSignPointWithWorkOrders(signPointWithWorkOrders);
        sortSignPointWithWorkOrder(signPointWithWorkOrders);
        return result;
    }

    private void sortSignPointWithWorkOrder(List<SignPointWithWorkOrder> signPointWithWorkOrders) {
        for (SignPointWithWorkOrder signPointWithWorkOrder : signPointWithWorkOrders) {
            if (CollectionUtils.isEmpty(signPointWithWorkOrder.getWorkOrders())) {
                continue;
            }

            List<InspectionWorkOrderDto> workOrders = signPointWithWorkOrder.getWorkOrders();
            sort(workOrders, true);
            signPointWithWorkOrder.setWorkOrders(workOrders);
        }
    }

    private void sortWorkOrderCount(List<WorkOrderCountDto> workOrderDetails, Page page) {
        if (CollectionUtils.isEmpty(workOrderDetails)) {
            return;
        }

        if (page == null) {
            page = new Page(0, Integer.MAX_VALUE);
        }
        for (WorkOrderCountDto workOrderDetail : workOrderDetails) {
            List<InspectionWorkOrderDto> workOrders = workOrderDetail.getWorkOrders();
            if (CollectionUtils.isEmpty(workOrders)) {
                continue;
            }

            if (WorkSheetStatusDef.TO_BE_SENT.equals(workOrderDetail.getWorkOrderStatus())) {
                sort(workOrders, true);
                workOrderDetail.setWorkOrders(workOrders);
            } else {
                sort(workOrders, false);
                workOrderDetail.setWorkOrders(workOrders.stream()
                        .skip(page.getIndex()).limit(page.getLimit()).collect(Collectors.toList()));
            }
        }
    }

    private void sort(List<InspectionWorkOrderDto> workOrders, boolean b) {
        workOrders.sort((v1, v2) -> {
            int sort = CommonUtils.sort(v1.getExecuteTimePlan(), v2.getExecuteTimePlan(), b);
            if (sort != 0) {
                return sort;
            }

            return CommonUtils.sort(v1.getId(), v2.getId(), b);
        });
    }

    /**
     * 查询工单
     *
     * @param user
     * @param teamId
     * @return
     */
    private List<InspectionWorkOrderDto> queryWorkOrder(UserVo user, Long teamId) {
        // 待处理、异常和退回的一直显示。超时和已完成的，默认显示一天或者一周，此处取24（或者一周，此处做成配置项）内

        // 查询超时和已完成
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime st = now.plusDays(-1L * preDay);
        // 查询提前指定时间的工单信息
        List<Integer> statuses = Arrays.asList(WorkSheetStatusDef.ACCOMPLISHED, WorkSheetStatusDef.OVERTIME);
        ResultWithTotal<List<Map<String, Object>>> workOrderResult = workOrderDao.queryWorkOrderByWorkStatus(st, now, WorkSheetTaskType.INSPECTION, statuses, teamId, user.getId());
        List<InspectionWorkOrderDto> workOrderList = JsonTransferUtils.transferList(workOrderResult.getData(), InspectionWorkOrderDto.class);

        // 查询待处理、异常、退回工单
        statuses = Arrays.asList(WorkSheetStatusDef.AUDITED, WorkSheetStatusDef.TO_BE_SENT, WorkSheetStatusDef.TO_BE_AUDITED);
        LocalDateTime et = TimeUtil.getFirstTimeOfNextDay(now.toLocalDate());
        workOrderList.addAll(workOrderDao.queryRuntimeWorkOrderByWorkStatus(statuses, WorkSheetTaskType.INSPECTION, teamId, et, user.getId(), null, InspectionWorkOrderDto.class));
        return workOrderList;
    }

    private List<InspectionWorkOrderDto> queryWorkOrderPage(MobileWorkOrderQueryVO query, Long teamId) {
        // 待处理、异常和退回的一直显示。超时和已完成的，默认显示一天或者一周，此处取24（或者一周，此处做成配置项）内

        WorkOrderSimpleQueryDTO workOrderSimpleQueryDTO = new WorkOrderSimpleQueryDTO();
        workOrderSimpleQueryDTO.setPage(query.getPage());
        workOrderSimpleQueryDTO.setTeamId(teamId);
        workOrderSimpleQueryDTO.setTaskType(WorkSheetTaskType.INSPECTION);
        workOrderSimpleQueryDTO.setWorkOrderStatus(Collections.singletonList(query.getWorkOrderStatus()));
        workOrderSimpleQueryDTO.setUserId(query.getUserId());
        workOrderSimpleQueryDTO.setSignPointId(query.getSignPointId());
        List<Order> orders = new ArrayList<>();
        orders.add(new Order(WorkOrderDef.EXECUTE_TIME_PLAN, ConditionBlock.ASC, 1));
        orders.add(new Order(ColumnDef.ID, ConditionBlock.ASC, 2));
        workOrderSimpleQueryDTO.setOrders(orders);

        List<Integer> statuses = Arrays.asList(WorkSheetStatusDef.ACCOMPLISHED, WorkSheetStatusDef.OVERTIME);
        if (statuses.contains(query.getWorkOrderStatus())) {
            // 查询超时和已完成
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime st = now.plusDays(-1L * preDay);
            // 查询提前指定时间的工单信息
            workOrderSimpleQueryDTO.setStartTime(st);
            workOrderSimpleQueryDTO.setEndTime(now);
        } else {
            // 查询待处理、异常、退回工单
            LocalDateTime et = TimeUtil.getFirstTimeOfNextDay(LocalDateTime.now().toLocalDate());
            workOrderSimpleQueryDTO.setStartTime(TimeUtil.BEGIN_TIME);
            workOrderSimpleQueryDTO.setEndTime(et);
        }

        ResultWithTotal<List<InspectionWorkOrderDto>> listResultWithTotal = workOrderDao.queryWorkOrderByWorkStatusPage(workOrderSimpleQueryDTO, InspectionWorkOrderDto.class);
        return listResultWithTotal.getData();
    }


    /**
     * 根据工单状态统计工单数量
     *
     * @param workOrderList
     * @return
     */
    private List<WorkOrderCountDto> countWorkOrderByStatus(List<InspectionWorkOrderDto> workOrderList) {
        List<WorkOrderCountDto> workOrderDetails = new ArrayList<>();
        Map<Integer, String> workSheetStatusMap = workSheetStatusUtils.getWorkSheetStatusMapByTaskType(WorkSheetTaskType.INSPECTION);
        Map<Integer, List<InspectionWorkOrderDto>> workStatusMap = workOrderList.stream().collect(Collectors.groupingBy(InspectionWorkOrderDto::getWorkSheetStatus));
        workStatusMap.forEach((key, val) -> {
            WorkOrderCountDto workOrderCountDto = new WorkOrderCountDto();
            workOrderCountDto.setWorkOrderStatus(key);
            workOrderCountDto.setWorkOrderStatusName(workSheetStatusMap.get(key));
            workOrderCountDto.setCount(val.size());
            workOrderCountDto.setWorkOrders(val);
            workOrderDetails.add(workOrderCountDto);
        });
        return workOrderDetails;
    }

    private List<SignPointWithWorkOrder> getSignPointWithWorkOrders(List<InspectionWorkOrderDto> workOrderList) {
        if (CollectionUtils.isEmpty(workOrderList)) {
            return Collections.emptyList();
        }

        Set<Long> groupIds = workOrderList.stream().map(InspectionWorkOrderDto::getSignGroupId).collect(Collectors.toSet());
        // 查询签到点信息
        List<SignInGroupWithAllSubLayer> signGroups = signInGroupDao.querySignInGroupWithAllSubLayer(groupIds);

        List<SignPointWithWorkOrder> signPointWithWorkOrders = new ArrayList<>();
        for (SignInGroupWithAllSubLayer signGroup : signGroups) {
            Long groupId = signGroup.getId();
            List<SignInPointWithSubLayer> signPointList = signGroup.getSignInPointWithSubLayerList();
            List<SignInPointSequence> sortList = signGroup.getSignInPointSequenceList();
            if (sortList == null) {
                sortList = Collections.emptyList();
            }
            Map<Long, Integer> sortMap = sortList.stream().collect(Collectors.toMap(SignInPointSequence::getRegistrationPointId, SignInPointSequence::getSort, (v1, v2) -> v1));

            List<InspectionWorkOrderDto> tmpWorkOrder = workOrderList.stream().filter(it -> groupId.equals(it.getSignGroupId())).collect(Collectors.toList());
            if (tmpWorkOrder.isEmpty()) {
                continue;
            }

            // 获取巡检对象与签到点的映射关系
            Map<BaseVo, SignInPointWithSubLayer> nodeSignPointMap = getBaseVoSignInPointWithSubLayerMap(signPointList);

            Map<Long, SignPointWithWorkOrder> signPointIdAndWorkOrderMap = new HashMap<>();
            // 遍历每个工单，根据工单所关联的巡检对象所属的签到点进行分组
            tmpWorkOrder.forEach(workorder -> {
                // 判断关联的巡检对象不为空
                List<DevicePlanRelationship> ships = workorder.getDevicePlanRelationshipList();
                if (CollectionUtils.isEmpty(ships)) {
                    return;
                }

                // 根据巡检对象获取所属的签到点
                DevicePlanRelationship obj = ships.get(0);
                BaseVo node = new BaseVo(obj.getDeviceId(), obj.getDeviceLabel(), obj.getDeviceName());
                SignInPointWithSubLayer signInPointWithSubLayer = nodeSignPointMap.get(node);
                if (signInPointWithSubLayer == null) {
                    return;
                }

                // 根据签到点id与工单的映射关系，查询工单信息，查询不到则新建
                getSingPointWithWorkOrder(groupId, signPointIdAndWorkOrderMap, workorder, signInPointWithSubLayer);
            });

            signPointIdAndWorkOrderMap.forEach((key1, val) -> {
                val.setSort(sortMap.get(val.getRegisterPointId()));
                signPointWithWorkOrders.add(val);
            });
        }

        signPointWithWorkOrders.sort((v1, v2) -> {
            long r1 = v1.getSignGroupId() - v2.getSignGroupId();
            if (r1 != 0) {
                return (int) r1;
            }

            return v1.getSort() - v2.getSort();
        });
        return signPointWithWorkOrders;
    }

    /**
     * 根据签到点id与工单的映射关系，查询工单信息，查询不到则新建
     *
     * @param groupId
     * @param signPointIdAndWorkOrderMap
     * @param workorder
     * @param signInPointWithSubLayer
     * @return
     */
    private void getSingPointWithWorkOrder(Long groupId, Map<Long, SignPointWithWorkOrder> signPointIdAndWorkOrderMap,
                                           InspectionWorkOrderDto workorder, SignInPointWithSubLayer signInPointWithSubLayer) {
        SignPointWithWorkOrder signPointWithWorkOrder = signPointIdAndWorkOrderMap.computeIfAbsent(signInPointWithSubLayer.getId(), k -> new SignPointWithWorkOrder());

        List<InspectionWorkOrderDto> workOrders = signPointWithWorkOrder.getWorkOrders();
        if (workOrders == null) {
            workOrders = new ArrayList<>();
            signPointWithWorkOrder.setWorkOrders(workOrders);
        }

        signPointWithWorkOrder.setRegisterPointId(signInPointWithSubLayer.getId());
        signPointWithWorkOrder.setRegisterPointName(signInPointWithSubLayer.getName());
        signPointWithWorkOrder.setInterval(signInPointWithSubLayer.getInterval());
        signPointWithWorkOrder.setNfc(signInPointWithSubLayer.getNfc());
        signPointWithWorkOrder.setSignGroupId(groupId);
        signPointWithWorkOrder.setAddress(signInPointWithSubLayer.getAddress());
        workOrders.add(workorder);
    }

    private Map<BaseVo, SignInPointWithSubLayer> getBaseVoSignInPointWithSubLayerMap(List<SignInPointWithSubLayer> signInPointWithSubLayerList) {
        Map<BaseVo, SignInPointWithSubLayer> map = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        if (CollectionUtils.isEmpty(signInPointWithSubLayerList)) {
            return map;
        }

        for (SignInPointWithSubLayer signInPointWithSubLayer : signInPointWithSubLayerList) {
            List<SignInEquipment> equipmentList = signInPointWithSubLayer.getEquipmentList();
            if (CollectionUtils.isEmpty(equipmentList)) {
                continue;
            }

            for (SignInEquipment signInEquipment : equipmentList) {
                map.put(new BaseVo(signInEquipment.getObjectId(), signInEquipment.getObjectLabel()), signInPointWithSubLayer);
            }
        }
        return map;
    }

    @Override
    public List<WoStatusCountDTO> queryWoStatusCount(Long userId) {
        // 查询用户信息，获取用户所在的班组信息
        UserVo user = authUtils.queryAndCheckUser(userId);
        Long teamId = null;
        List<Long> groupIds = user.getRelativeUserGroup();
        if (CollectionUtils.isNotEmpty(groupIds)) {
            teamId = groupIds.get(0);
        }

        // 待处理、异常和退回的一直显示。超时和已完成的，默认显示一天或者一周，此处取24（或者一周，此处做成配置项）内

        // 查询超时和已完成
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime st = now.plusDays(-1L * preDay);
        // 查询提前指定时间的工单信息
        List<Integer> statuses = Arrays.asList(WorkSheetStatusDef.ACCOMPLISHED, WorkSheetStatusDef.OVERTIME);
        WoStatusCountQueryVO workOrderCountQueryVO = new WoStatusCountQueryVO();
        workOrderCountQueryVO.setStartTime(st);
        workOrderCountQueryVO.setEndTime(now);
        workOrderCountQueryVO.setTaskType(WorkSheetTaskType.INSPECTION);
        workOrderCountQueryVO.setStatuses(statuses);
        workOrderCountQueryVO.setTeamId(teamId);
        workOrderCountQueryVO.setUserId(user.getId());
        List<WoStatusCountDTO> woCountByTaskTypeVos = workOrderDao.queryWorkOrderCount(workOrderCountQueryVO);
        List<WoStatusCountDTO> res = new ArrayList<>(woCountByTaskTypeVos);

        // 查询待处理、异常、退回工单
        statuses = Arrays.asList(WorkSheetStatusDef.AUDITED, WorkSheetStatusDef.TO_BE_SENT, WorkSheetStatusDef.TO_BE_AUDITED);
        LocalDateTime et = TimeUtil.getFirstTimeOfNextDay(now.toLocalDate());
        workOrderCountQueryVO.setStartTime(TimeUtil.BEGIN_TIME);
        workOrderCountQueryVO.setEndTime(et);
        workOrderCountQueryVO.setStatuses(statuses);
        res.addAll(workOrderDao.queryWorkOrderCount(workOrderCountQueryVO));
        return res;
    }

    @Override
    public List<InspectionWorkOrderDto> queryWorkOrderList(MobileWorkOrderQueryVO query) {
        // 查询用户信息，获取用户所在的班组信息
        UserVo user = authUtils.queryAndCheckUser(query.getUserId());
        Long teamId = null;
        List<Long> groupIds = user.getRelativeUserGroup();
        if (CollectionUtils.isNotEmpty(groupIds)) {
            teamId = groupIds.get(0);
        }

        List<InspectionWorkOrderDto> workOrderList = queryWorkOrderPage(query, teamId);
        inspectionWorkOrderService.assemblyCommonData(workOrderList, user.getTenantId());
        return workOrderList;
    }

    @Override
    public List<SignPointWithWorkOrder> querySignPoint(Long userId) {
        // 查询用户信息，获取用户所在的班组信息
        UserVo user = authUtils.queryAndCheckUser(userId);
        Long teamId = null;
        List<Long> groupIds = user.getRelativeUserGroup();
        if (CollectionUtils.isNotEmpty(groupIds)) {
            teamId = groupIds.get(0);
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime st = now.plusDays(-1L * preDay);
        WorkOrderSimpleQueryDTO workOrderQueryVo = new WorkOrderSimpleQueryDTO();
        workOrderQueryVo.setUserId(user.getId());
        workOrderQueryVo.setTeamId(teamId);
        workOrderQueryVo.setStartTime(st);
        workOrderQueryVo.setEndTime(now);
        workOrderQueryVo.setTaskType(WorkSheetTaskType.INSPECTION);
        workOrderQueryVo.setWorkOrderStatus(Collections.singletonList(WorkSheetStatusDef.TO_BE_SENT));

        List<SignGroupWithSignIn> signGroupWithSignIns = workOrderDao.querySignPoint(workOrderQueryVo);
        List<Long> signPointIds = signGroupWithSignIns.stream().map(SignGroupWithSignIn::getSignPointId).collect(Collectors.toList());
        List<Long> signGroupIds = signGroupWithSignIns.stream().map(SignGroupWithSignIn::getSignGroupId).collect(Collectors.toList());
        List<SignInGroupWithAllSubLayer> signInGroupWithSubLayers = signInPointDao.querySignInPoint(signPointIds, signGroupIds);
        return getSignPointWithWorkOrders(signInGroupWithSubLayers, signGroupWithSignIns);
    }

    private List<SignPointWithWorkOrder> getSignPointWithWorkOrders(List<SignInGroupWithAllSubLayer> signInGroupWithSubLayers, List<SignGroupWithSignIn> signGroupWithSignIns) {
        if (CollectionUtils.isEmpty(signInGroupWithSubLayers)) {
            return Collections.emptyList();
        }

        Map<Long, List<SignGroupWithSignIn>> signGroupMap = signGroupWithSignIns.stream().collect(Collectors.groupingBy(SignGroupWithSignIn::getSignGroupId));

        List<SignPointWithWorkOrder> signPointWithWorkOrders = new ArrayList<>();
        for (SignInGroupWithAllSubLayer signGroup : signInGroupWithSubLayers) {
            List<SignGroupWithSignIn> signGroupWithSignIns1 = signGroupMap.get(signGroup.getId());
            if (CollectionUtils.isEmpty(signGroupWithSignIns1)) {
                continue;
            }

            Long groupId = signGroup.getId();
            List<SignInPointWithSubLayer> signPointList = signGroup.getSignInPointWithSubLayerList();
            Map<Long, SignInPointWithSubLayer> signInPointWithSubLayerMap = signPointList.stream().collect(Collectors.toMap(BaseEntity::getId, it -> it, (v1, v2) -> v1));
            List<SignInPointSequence> sortList = signGroup.getSignInPointSequenceList();
            if (sortList == null) {
                sortList = Collections.emptyList();
            }
            Map<Long, Integer> sortMap = sortList.stream().collect(Collectors.toMap(SignInPointSequence::getRegistrationPointId, SignInPointSequence::getSort, (v1, v2) -> v1));

            Map<Long, SignPointWithWorkOrder> signPointIdAndWorkOrderMap = new HashMap<>();
            for (SignGroupWithSignIn signGroupWithSignIn : signGroupWithSignIns1) {
                SignInPointWithSubLayer signInPointWithSubLayer = signInPointWithSubLayerMap.get(signGroupWithSignIn.getSignPointId());
                if (Objects.nonNull(signInPointWithSubLayer)) {
                    getSingPointWithWorkOrder(groupId, signPointIdAndWorkOrderMap, signInPointWithSubLayer);
                }
            }

            signPointIdAndWorkOrderMap.forEach((key1, val) -> {
                val.setSort(sortMap.get(val.getRegisterPointId()));
                signPointWithWorkOrders.add(val);
            });
        }

        signPointWithWorkOrders.sort((v1, v2) -> {
            long r1 = v1.getSignGroupId() - v2.getSignGroupId();
            if (r1 != 0) {
                return (int) r1;
            }

            return v1.getSort() - v2.getSort();
        });
        return signPointWithWorkOrders;
    }

    private void getSingPointWithWorkOrder(Long groupId, Map<Long, SignPointWithWorkOrder> signPointIdAndWorkOrderMap,
                                           SignInPointWithSubLayer signInPointWithSubLayer) {
        SignPointWithWorkOrder signPointWithWorkOrder = signPointIdAndWorkOrderMap.computeIfAbsent(signInPointWithSubLayer.getId(), k -> new SignPointWithWorkOrder());

        signPointWithWorkOrder.setRegisterPointId(signInPointWithSubLayer.getId());
        signPointWithWorkOrder.setRegisterPointName(signInPointWithSubLayer.getName());
        signPointWithWorkOrder.setInterval(signInPointWithSubLayer.getInterval());
        signPointWithWorkOrder.setNfc(signInPointWithSubLayer.getNfc());
        signPointWithWorkOrder.setSignGroupId(groupId);
        signPointWithWorkOrder.setAddress(signInPointWithSubLayer.getAddress());
    }
}
