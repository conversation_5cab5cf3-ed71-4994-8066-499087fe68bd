package com.cet.eem.fusion.maintenance.core.config;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/4/25
 */
@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "cet.eem.work-order")
@Slf4j
@ToString
public class MaintenanceConfig {
    private static final String LOG_NAME = "[运维配置]";

    /**
     * 根租户id
     */
    private Long rootTenantId = 1L;

    /**
     * 单次处理工单数量
     */
    private Integer batchHandleCount = 100;

    /**
     * 是否加载巡检计划定时任务
     */
    private boolean loadPlanSheet;

    private InspectWorkOrderConfig inspect;
}
