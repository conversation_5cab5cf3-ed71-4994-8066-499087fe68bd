﻿package com.cet.eem.fusion.refrigeration.core.dao;

import com.cet.eem.bll.common.model.domain.subject.energysaving.WeatherPredict;
import com.cet.eem.dao.BaseModelDao;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName : WeatherPredictDao
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-14 15:42
 */
public interface WeatherPredictDao extends BaseModelDao<WeatherPredict> {
    List<WeatherPredict> queryWeather(LocalDateTime st, LocalDateTime et);

    List<WeatherPredict> writeOrUpdateData(List<WeatherPredict> weatherPredicts);

    List<WeatherPredict> queryWeatherData(LocalDateTime st, LocalDateTime et, Integer cycle,Long projectId);
}
