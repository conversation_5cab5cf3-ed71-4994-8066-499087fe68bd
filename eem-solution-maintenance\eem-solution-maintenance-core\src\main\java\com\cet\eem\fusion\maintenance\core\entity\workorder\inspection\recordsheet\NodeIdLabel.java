package com.cet.eem.fusion.maintenance.core.entity.workorder.inspection.recordsheet;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * @ClassName : NodeIdLabel
 * @Description : 节点信息
 * <AUTHOR> jiangzixuan
 * @Date: 2022-10-17 16:31
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class NodeIdLabel {
    @ApiModelProperty("主键标识")
    @NotNull
    protected Long id;
    @NotNull
    @ApiModelProperty("模型标识")
    protected String modelLabel;
}