package com.cet.eem.fusion.maintenance.core.dao;

import com.cet.eem.bll.common.model.domain.subject.activiti.WorksheetAbnormalReason;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.ProcessFlowUnit;
import com.cet.eem.bll.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.bll.maintenance.def.WorkOrderDef;
import com.cet.eem.bll.maintenance.def.WorkSheetStatusDef;
import com.cet.eem.bll.maintenance.model.WorkOrderQueryVo;
import com.cet.eem.bll.maintenance.model.WorksheetAbnormalReasonDto;
import com.cet.eem.bll.maintenance.model.sign.SignGroupWithSignIn;
import com.cet.eem.bll.maintenance.model.wo.WorkOrderSimpleQueryDTO;
import com.cet.eem.bll.maintenance.model.workorder.WoStatusCountDTO;
import com.cet.eem.bll.maintenance.model.workorder.WoStatusCountQueryVO;
import com.cet.eem.bll.maintenance.model.workorder.WorkOrderPo;
import com.cet.eem.bll.maintenance.model.workorder.WorkOrderSearchVo;
import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionCountSearchDto;
import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionSearchDto;
import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.bll.maintenance.model.workorder.inspection.recordsheet.RecordSheetModelLabel;
import com.cet.eem.bll.maintenance.model.workorder.maintenance.QueryMaintenanceWorkOrderCountRequest;
import com.cet.eem.bll.maintenance.utils.WorkSheetStatusUtils;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.common.utils.ParamUtils;
import com.cet.eem.fusion.common.def.pec.PecsNodeType;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.common.def.auth.LoginDef;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.exception.ValidationException;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.common.util.JsonUtil;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import com.cet.eem.fusion.common.utils.performance.annotation.ExecuteIndexAnnotation;
import com.cet.eem.fusion.common.modelutils.model.base.ConditionBlock;
import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;
import com.cet.eem.fusion.common.modelutils.model.tool.SubConditionBuilder;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.modelservice.common.query.ModelQuery;
import com.cet.electric.workflow.api.TriggerRestApi;
import com.cet.electric.workflow.api.UserTaskRestApi;
import com.cet.electric.workflow.common.model.ProcessInstanceResponse;
import com.cet.electric.workflow.common.model.node.config.UserTaskConfig;
import com.cet.electric.workflow.common.model.params.MultiTableTriggerParams;
import com.cet.electric.workflow.common.model.params.TableTriggerParams;
import com.cet.electric.workflow.common.model.params.UserTaskParams;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 4/12/2021
 */
@Service
@ExecuteIndexAnnotation
public class WorkOrderDaoImpl implements WorkOrderDao {
    @Autowired
    UserTaskRestApi userTaskRestApi;

    @Autowired
    TriggerRestApi triggerRestApi;

    @Autowired
    WorkSheetStatusUtils workSheetStatusUtils;

    @Override
    public ResultWithTotal<List<Map<String, Object>>> getModelEntityList(QueryCondition condition, Long userId) {
        ModelQuery modelQuery = JsonTransferUtils.parseObject(JsonTransferUtils.toJSONString(condition), ModelQuery.class);
        // 根据陈凯的描述，该方法未经过权限过滤， 2023-01-05
        ApiResult<List<Map<String, Object>>> result = userTaskRestApi.getModelEntityList(modelQuery);
        ParamUtils.checkResultGeneric(result);
        return ResultWithTotal.ok(result.getData(), result.getTotal());
    }

    @Override
    public ResultWithTotal<List<Map<String, Object>>> getRuntimeModelEntityList(QueryCondition condition, Long userId, Long tenantId) {
        ModelQuery modelQuery = JsonTransferUtils.parseObject(JsonTransferUtils.toJSONString(condition), ModelQuery.class);
        ApiResult<List<Map<String, Object>>> result = userTaskRestApi.getRuntimeModelEntityList(userId, tenantId, modelQuery);
        ParamUtils.checkResultGeneric(result);
        return ResultWithTotal.ok(result.getData(), result.getTotal());
    }

    @Override
    public <T extends InspectionWorkOrderDto> List<T> queryWorkOrderByWorkStatus(List<Integer> workOrderStatus, Integer taskType, Long teamId, LocalDateTime et, Long userId, Class<T> clazz) {
        QueryConditionBuilder<BaseEntity> builder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET)
                .where(ColumnDef.TASK_TYPE, ConditionBlock.OPERATOR_EQ, taskType)
                .where(WorkOrderDef.WORKSHEET_STATUS, ConditionBlock.OPERATOR_IN, workOrderStatus)
                .leftJoin(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP));

        if (et != null) {
            builder.where(ColumnDef.EXECUTE_TIME_PLAN, ConditionBlock.OPERATOR_LT, TimeUtil.localDateTime2timestamp(et));
        }

        if (ParamUtils.checkPrimaryKeyValid(teamId)) {
            builder.where(WorkOrderDef.TEAM_ID, ConditionBlock.OPERATOR_EQ, teamId);
        }

        ResultWithTotal<List<Map<String, Object>>> modelEntityList = getModelEntityList(builder.build(), userId);
        return JsonTransferUtils.transferList(modelEntityList.getData(), clazz);
    }

    @Override
    public <T extends InspectionWorkOrderDto> List<T> queryRuntimeWorkOrderByWorkStatus(List<Integer> workOrderStatus, Integer taskType, Long teamId, LocalDateTime et, Long userId, Long tenantId, Class<T> clazz) {
        QueryConditionBuilder<BaseEntity> builder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET)
                .where(ColumnDef.TASK_TYPE, ConditionBlock.OPERATOR_EQ, taskType)
                .where(WorkOrderDef.WORKSHEET_STATUS, ConditionBlock.OPERATOR_IN, workOrderStatus)
                .leftJoin(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP));

        if (et != null) {
            builder.where(ColumnDef.EXECUTE_TIME_PLAN, ConditionBlock.OPERATOR_LT, TimeUtil.localDateTime2timestamp(et));
        }

        if (ParamUtils.checkPrimaryKeyValid(teamId)) {
            builder.where(WorkOrderDef.TEAM_ID, ConditionBlock.OPERATOR_EQ, teamId);
        }

        ResultWithTotal<List<Map<String, Object>>> modelEntityList = getRuntimeModelEntityList(builder.build(), userId, tenantId);
        return JsonTransferUtils.transferList(modelEntityList.getData(), clazz);
    }

    @Override
    public <T extends InspectionWorkOrderDto> List<T> queryWorkOrderByWorkStatus(List<Integer> workOrderStatus, Integer taskType, LocalDateTime st, LocalDateTime et, Class<T> clazz) {
        QueryConditionBuilder<BaseEntity> builder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET)
                .where(ColumnDef.TASK_TYPE, ConditionBlock.OPERATOR_EQ, taskType)
                .where(WorkOrderDef.WORKSHEET_STATUS, ConditionBlock.OPERATOR_IN, workOrderStatus)
                .leftJoin(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP));

        if (st != null) {
            builder.where(ColumnDef.EXECUTE_TIME_PLAN, ConditionBlock.OPERATOR_GE, TimeUtil.localDateTime2timestamp(st));
        }

        if (et != null) {
            builder.where(ColumnDef.EXECUTE_TIME_PLAN, ConditionBlock.OPERATOR_LT, TimeUtil.localDateTime2timestamp(et));
        }

        ResultWithTotal<List<Map<String, Object>>> modelEntityList = getModelEntityList(builder.build(), PecsNodeType.ROOT_USER_ID);
        return JsonTransferUtils.transferList(modelEntityList.getData(), clazz);
    }

    @Override
    public <T extends InspectionWorkOrderDto> ResultWithTotal<List<T>> queryWorkOrderByWorkStatusPage(WorkOrderSimpleQueryDTO workOrderQueryVo, Class<T> clazz) {
        QueryConditionBuilder<BaseEntity> builder = createWoByWorkStatusQueryBuilder(workOrderQueryVo);

        if (CollectionUtils.isNotEmpty(workOrderQueryVo.getSelectFields())) {
            builder.select(workOrderQueryVo.getSelectFields());
        }

        return queryWorkOrder(builder, workOrderQueryVo.getUserId(), clazz);
    }

    @NotNull
    private <T extends InspectionWorkOrderDto> ResultWithTotal<List<T>> queryWorkOrder(QueryConditionBuilder<BaseEntity> builder, Long userId, Class<T> clazz) {
        ResultWithTotal<List<Map<String, Object>>> modelEntityList = getModelEntityList(builder.build(), userId);
        List<T> dataList = JsonTransferUtils.transferList(modelEntityList.getData(), clazz);
        return ResultWithTotal.ok(dataList);
    }

    private static QueryConditionBuilder<BaseEntity> createWoByWorkStatusQueryBuilder(WorkOrderQueryVo workOrderQueryVo) {
        if (CollectionUtils.isEmpty(workOrderQueryVo.getWorkOrderStatus())) {
            throw new ValidationException("入参错误，缺少工单状态数据！");
        }

        QueryConditionBuilder<BaseEntity> builder = QueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET)
                .eq(ColumnDef.TASK_TYPE, workOrderQueryVo.getTaskType())
                .ge(ColumnDef.EXECUTE_TIME_PLAN, workOrderQueryVo.getStartTime())
                .lt(ColumnDef.EXECUTE_TIME_PLAN, workOrderQueryVo.getEndTime())
                .leftJoin(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP))
                .limit(workOrderQueryVo.getPage())
                .orderBy(workOrderQueryVo.getOrders());

        if (workOrderQueryVo.getWorkOrderStatus().size() == 1) {
            builder.eq(WorkOrderDef.WORKSHEET_STATUS, workOrderQueryVo.getWorkOrderStatus().get(0));
        } else {
            builder.in(WorkOrderDef.WORKSHEET_STATUS, workOrderQueryVo.getWorkOrderStatus());
        }

        if (Objects.nonNull(workOrderQueryVo.getTeamId())) {
            builder.eq(WorkOrderDef.TEAM_ID, workOrderQueryVo.getTeamId());
        }

        if (Objects.nonNull(workOrderQueryVo.getSignPointId())) {
            builder.eq(WorkOrderDef.SIGN_POINT_ID, workOrderQueryVo.getSignPointId());
        }

        return builder;
    }

    @Override
    public <T extends InspectionWorkOrderDto> ResultWithTotal<List<T>> queryWorkOrderByWorkStatusByPage(WorkOrderQueryVo workOrderQueryVo, Class<T> clazz) {
        QueryConditionBuilder<BaseEntity> builder = createWoByWorkStatusQueryBuilder(workOrderQueryVo);

        return queryWorkOrder(builder, workOrderQueryVo.getUserId(), clazz);
    }

    @Override
    public ResultWithTotal<List<Map<String, Object>>> queryWorkOrderByWorkStatus(LocalDateTime st, LocalDateTime et, Integer taskType, List<Integer> workOrderStatus, Long teamId, Long userId) {
        QueryConditionBuilder<BaseEntity> builder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET)
                .leftJoin(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP));

        builder.where(ColumnDef.TASK_TYPE, ConditionBlock.OPERATOR_EQ, taskType);
        builder.where(ColumnDef.EXECUTE_TIME_PLAN, ConditionBlock.OPERATOR_GE, TimeUtil.localDateTime2timestamp(st));
        builder.where(ColumnDef.EXECUTE_TIME_PLAN, ConditionBlock.OPERATOR_LT, TimeUtil.localDateTime2timestamp(et));

        if (ParamUtils.checkPrimaryKeyValid(teamId)) {
            builder.where(WorkOrderDef.TEAM_ID, ConditionBlock.OPERATOR_EQ, teamId);
        }
        builder.where(WorkOrderDef.WORKSHEET_STATUS, ConditionBlock.OPERATOR_IN, workOrderStatus);

        return getModelEntityList(builder.build(), userId);
    }

    @Override
    public List<InspectionWorkOrderDto> queryWorkOrderByIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        QueryConditionBuilder<BaseEntity> builder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET)
                .selectChildByLabels(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP))
                .where(ColumnDef.ID, ConditionBlock.OPERATOR_IN, ids);

        ResultWithTotal<List<Map<String, Object>>> tmpResult = getModelEntityList(builder.build(), null);
        return JsonTransferUtils.transferList(tmpResult.getData(), InspectionWorkOrderDto.class);
    }

    @Override
    public List<WorkOrderPo> selectBatchIds(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        QueryConditionBuilder<BaseEntity> builder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET)
                .selectChildByLabels(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP))
                .where(ColumnDef.ID, ConditionBlock.OPERATOR_IN, ids);

        ResultWithTotal<List<Map<String, Object>>> tmpResult = getModelEntityList(builder.build(), null);
        return JsonTransferUtils.transferList(tmpResult.getData(), WorkOrderPo.class);
    }

    @Override
    public ResultWithTotal<List<InspectionWorkOrderDto>> queryWorkOrder(InspectionSearchDto dto, Long userId) {
        QueryConditionBuilder<BaseEntity> builder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET)
                .leftJoin(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP))
                .orderByDescending(ColumnDef.EXECUTE_TIME_PLAN)
                .limit(dto.getPage());

        assemblyCondition(dto, builder);

        ResultWithTotal<List<Map<String, Object>>> modelEntityList = getModelEntityList(builder.build(), userId);
        List<InspectionWorkOrderDto> workOrderList = JsonUtil.mapList2BeanList(modelEntityList.getData(), InspectionWorkOrderDto.class);
        List<WorksheetAbnormalReasonDto> worksheetAbnormalReasonDtos = queryWorksheetAbnormalReason(workOrderList.stream().map(BaseEntity::getId).collect(Collectors.toSet()));
        Map<Long, List<WorksheetAbnormalReasonDto>> map = worksheetAbnormalReasonDtos.stream().collect(Collectors.groupingBy(WorksheetAbnormalReason::getPmWorkSheetId));
        for (InspectionWorkOrderDto workOrderDto : workOrderList) {
            workOrderDto.setAbnormalReasonList(map.get(workOrderDto.getId()));
        }

        return ResultWithTotal.ok(workOrderList, modelEntityList.getTotal());
    }

    private List<WorksheetAbnormalReasonDto> queryWorksheetAbnormalReason(Collection<Long> workSheetIds) {
        if (CollectionUtils.isEmpty(workSheetIds)) {
            return Collections.emptyList();
        }

        QueryConditionBuilder<BaseEntity> builder = ParentQueryConditionBuilder.of(ModelLabelDef.WORKSHEET_ABNORMAL_REASON)
                .where("pmworksheet_id", ConditionBlock.OPERATOR_IN, workSheetIds);

        ResultWithTotal<List<Map<String, Object>>> modelEntityList = getModelEntityList(builder.build(), GlobalInfoUtils.getUserId());
        return JsonUtil.mapList2BeanList(modelEntityList.getData(), WorksheetAbnormalReasonDto.class);
    }

    private void assemblyCondition(InspectionSearchDto dto, QueryConditionBuilder<BaseEntity> builder) {
        builder.where(ColumnDef.TASK_TYPE, ConditionBlock.OPERATOR_EQ, dto.getTaskType());
        builder.where(ColumnDef.EXECUTE_TIME_PLAN, ConditionBlock.OPERATOR_GE, TimeUtil.localDateTime2timestamp(dto.getStartTime()));
        builder.where(ColumnDef.EXECUTE_TIME_PLAN, ConditionBlock.OPERATOR_LT, TimeUtil.localDateTime2timestamp(dto.getEndTime()));
        builder.where(ColumnDef.PROJECT_ID, ConditionBlock.OPERATOR_EQ, GlobalInfoUtils.getTenantId());

        if (StringUtils.isNotBlank(dto.getCode())) {
            builder.where(ColumnDef.CODE, ConditionBlock.OPERATOR_LIKE, dto.getCode());
        }

        if (ParamUtils.checkPrimaryKeyValid(dto.getTeamId())) {
            builder.where(WorkOrderDef.TEAM_ID, ConditionBlock.OPERATOR_EQ, dto.getTeamId());
        }

        if (ParamUtils.checkPrimaryKeyValid(dto.getWorkSheetStatus())) {
            builder.where(WorkOrderDef.WORKSHEET_STATUS, ConditionBlock.OPERATOR_EQ, dto.getWorkSheetStatus());
        }

        if (ParamUtils.checkPrimaryKeyValid(dto.getSignPointId())) {
            builder.where(WorkOrderDef.SIGN_POINT_ID, ConditionBlock.OPERATOR_EQ, dto.getSignPointId());
        }

        if (ParamUtils.checkPrimaryKeyValid(dto.getSignGroupId())) {
            builder.where(WorkOrderDef.SIGN_GROUP_ID, ConditionBlock.OPERATOR_EQ, dto.getSignGroupId());
        }

        if (CollectionUtils.isNotEmpty(dto.getSelectFields())) {
            builder.select(dto.getSelectFields());
        }
    }

    @Override
    public ResultWithTotal<List<InspectionWorkOrderDto>> queryWorkOrder(WorkOrderSearchVo dto, Long userId) {
        QueryConditionBuilder<BaseEntity> builder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET)
                .distinct()
                .limit(dto.getPage())
                .composeMethod(true)
                .select(Arrays.asList(ColumnDef.ID, ColumnDef.EXECUTE_TIME_PLAN))
                .orderByDescending(ColumnDef.EXECUTE_TIME_PLAN);

        if (CollectionUtils.isEmpty(dto.getTaskTypes())) {
            dto.setTaskTypes(WorkSheetTaskType.ALL_TASK_TYPES);
        }

        int group = 1;
        for (Integer taskType : dto.getTaskTypes()) {
            assemblyCondition(taskType, dto, builder, group++);
        }

        if (dto.getNode() != null) {
            SubConditionBuilder subConditionBuilder = new SubConditionBuilder(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP)
                    .where(WorkOrderDef.DEVICE_ID, ConditionBlock.OPERATOR_EQ, dto.getNode().getId())
                    .where(WorkOrderDef.DEVICE_LABEL, ConditionBlock.OPERATOR_EQ, dto.getNode().getModelLabel());
            builder.leftJoin(subConditionBuilder.build());
        } else {
            builder.leftJoin(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP));
        }

        ResultWithTotal<List<Map<String, Object>>> modelEntityList = getModelEntityList(builder.build(), userId);
        List<InspectionWorkOrderDto> workOrderList = JsonUtil.mapList2BeanList(modelEntityList.getData(), InspectionWorkOrderDto.class);
        Set<Long> workOrderIds = workOrderList.stream().map(BaseEntity::getId).collect(Collectors.toSet());
        List<InspectionWorkOrderDto> workOrderWithObject = queryWorkOrderByIds(workOrderIds);
        return ResultWithTotal.ok(workOrderWithObject, modelEntityList.getTotal());
    }

    @Override
    public ResultWithTotal<List<InspectionWorkOrderDto>> queryFinishWorkOrder(InspectionSearchDto dto, Long userId) {

        QueryConditionBuilder<BaseEntity> builder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET)
                .selectChildByLabels(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP))
                .orderByDescending(ColumnDef.FINISH_TIME)
                .limit(dto.getPage());
        builder.where(ColumnDef.TASK_TYPE, ConditionBlock.OPERATOR_EQ, dto.getTaskType());
        builder.where(ColumnDef.FINISH_TIME, ConditionBlock.OPERATOR_GE, TimeUtil.localDateTime2timestamp(dto.getStartTime()));
        builder.where(ColumnDef.FINISH_TIME, ConditionBlock.OPERATOR_LT, TimeUtil.localDateTime2timestamp(dto.getEndTime()));
        builder.where(ColumnDef.PROJECT_ID, ConditionBlock.OPERATOR_EQ, GlobalInfoUtils.getTenantId());
        if (ParamUtils.checkPrimaryKeyValid(dto.getTeamId())) {
            builder.where(WorkOrderDef.TEAM_ID, ConditionBlock.OPERATOR_EQ, dto.getTeamId());
        }

        if (ParamUtils.checkPrimaryKeyValid(dto.getWorkSheetStatus())) {
            builder.where(WorkOrderDef.WORKSHEET_STATUS, ConditionBlock.OPERATOR_EQ, dto.getWorkSheetStatus());
        }

        ResultWithTotal<List<Map<String, Object>>> modelEntityList = getModelEntityList(builder.build(), userId);
        List<InspectionWorkOrderDto> workOrderList = JsonUtil.mapList2BeanList(modelEntityList.getData(), InspectionWorkOrderDto.class);
        return ResultWithTotal.ok(workOrderList, modelEntityList.getTotal());
    }

    private void assemblyCondition(Integer taskType, WorkOrderSearchVo dto, QueryConditionBuilder<BaseEntity> builder, Integer group) {
        builder.where(ColumnDef.EXECUTE_TIME_PLAN, ConditionBlock.OPERATOR_GE, TimeUtil.localDateTime2timestamp(dto.getStartTime()), group);
        builder.where(ColumnDef.EXECUTE_TIME_PLAN, ConditionBlock.OPERATOR_LT, TimeUtil.localDateTime2timestamp(dto.getEndTime()), group);
        builder.where(ColumnDef.PROJECT_ID, ConditionBlock.OPERATOR_EQ, dto.getProjectId(), group);

        if (CollectionUtils.isNotEmpty(dto.getTaskTypes())) {
            builder.where(ColumnDef.TASK_TYPE, ConditionBlock.OPERATOR_EQ, taskType, group);
        }

        if (ParamUtils.checkPrimaryKeyValid(dto.getTeamId())) {
            if (!Objects.equals(taskType, WorkSheetTaskType.REPAIR)) {
                builder.where(WorkOrderDef.TEAM_ID, ConditionBlock.OPERATOR_EQ, dto.getTeamId(), group);
                return;
            }

            if (dto.isInspectUser()) {
                builder.where(WorkOrderDef.INSPECT_TEAM_ID, ConditionBlock.OPERATOR_EQ, dto.getTeamId(), group);
            } else {
                builder.where(WorkOrderDef.TEAM_ID, ConditionBlock.OPERATOR_EQ, dto.getTeamId(), group);
            }
        }
    }

    @Override
    public Map<String, Object> queryRuntimeWorkOrder(String code, Long userId, Long tenantId) {
        QueryConditionBuilder<BaseEntity> builder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET);
        builder.where(ColumnDef.CODE, ConditionBlock.OPERATOR_EQ, code);

        ResultWithTotal<List<Map<String, Object>>> result = getRuntimeModelEntityList(builder.build(), userId, tenantId);
        List<Map<String, Object>> workOrderList = result.getData();
        if (CollectionUtils.isNotEmpty(workOrderList)) {
            return workOrderList.get(0);
        }

        return null;
    }

    @Override
    public List<InspectionWorkOrderDto> queryRuntimeWorkOrders(List<String> codes, Long userId, Long tenantId) {
        QueryConditionBuilder<BaseEntity> builder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET);
        builder.where(ColumnDef.CODE, ConditionBlock.OPERATOR_IN, codes);

        ResultWithTotal<List<Map<String, Object>>> result = getRuntimeModelEntityList(builder.build(), userId, tenantId);
        List<Map<String, Object>> workOrderList = result.getData();
        return JsonTransferUtils.transferList(workOrderList, InspectionWorkOrderDto.class);
    }

    @Override
    public <T extends InspectionWorkOrderDto> T queryWorkOrder(String code, Class<T> clazz) {
        QueryCondition condition = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET)
                .where(WorkOrderDef.CODE, ConditionBlock.OPERATOR_EQ, code)
                .leftJoin(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP))
                .build();

        return queryWorkOrder(condition, clazz);
    }

    @Override
    public <T extends InspectionWorkOrderDto> T queryWorkOrder(Long workOrderId, Class<T> clazz) {
        QueryCondition condition = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET, workOrderId)
                .selectChildByLabels(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP))
                .build();

        return queryWorkOrder(condition, clazz);
    }

    private <T extends InspectionWorkOrderDto> T queryWorkOrder(QueryCondition condition, Class<T> clazz) {
        ResultWithTotal<List<Map<String, Object>>> result = getModelEntityList(condition, GlobalInfoUtils.getUserId());
        List<T> workOrderList = JsonTransferUtils.transferList(result.getData(), clazz);
        if (CollectionUtils.isEmpty(workOrderList)) {
            return null;
        }

        return workOrderList.get(0);
    }

    @Override
    public List<InspectionWorkOrderDto> queryWorkOrderByPlanSheetId(Long planSheetId, Integer taskType) {
        QueryConditionBuilder<BaseEntity> builder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET)
                .where(ColumnDef.TASK_TYPE, ConditionBlock.OPERATOR_EQ, taskType)
                .where(ColumnDef.PLAN_SHEET_ID, ConditionBlock.OPERATOR_EQ, planSheetId);
        ResultWithTotal<List<Map<String, Object>>> modelEntityList = getModelEntityList(builder.build(), GlobalInfoUtils.getUserId());
        return JsonTransferUtils.transferList(modelEntityList.getData(), InspectionWorkOrderDto.class);
    }

    @Override
    public List<Map<String, Object>> queryWorkOrderCountByStatus(InspectionCountSearchDto dto) {
        QueryConditionBuilder<BaseEntity> builder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET)
                .where(ColumnDef.TASK_TYPE, ConditionBlock.OPERATOR_EQ, dto.getTaskType())
                .where(ColumnDef.EXECUTE_TIME_PLAN, ConditionBlock.OPERATOR_GE, TimeUtil.localDateTime2timestamp(dto.getStartTime()))
                .where(ColumnDef.EXECUTE_TIME_PLAN, ConditionBlock.OPERATOR_LT, TimeUtil.localDateTime2timestamp(dto.getEndTime()))
                .where(ColumnDef.PROJECT_ID, ConditionBlock.OPERATOR_EQ, GlobalInfoUtils.getTenantId())
                .count(WorkOrderDef.WORKSHEET_STATUS);

        if (ParamUtils.checkPrimaryKeyValid(dto.getTeamId())) {
            builder.where(WorkOrderDef.TEAM_ID, ConditionBlock.OPERATOR_EQ, dto.getTeamId());
        }

        if (ParamUtils.checkPrimaryKeyValid(dto.getSignPointId())) {
            builder.where(WorkOrderDef.SIGN_POINT_ID, ConditionBlock.OPERATOR_EQ, dto.getSignPointId());
        }

        ResultWithTotal<List<Map<String, Object>>> modelEntityList = getModelEntityList(builder.build(), GlobalInfoUtils.getUserId());
        return modelEntityList.getData();
    }

    @Override
    public List<Map<String, Object>> queryWorkOrderCountByTaskType(WorkOrderSearchVo dto) {
        QueryConditionBuilder<BaseEntity> builder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET).count(WorkOrderDef.TASK_TYPE);
        createBuilder(dto, builder);
        if (dto.getNode() != null) {
            SubConditionBuilder subConditionBuilder = new SubConditionBuilder(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP)
                    .where(WorkOrderDef.DEVICE_ID, ConditionBlock.OPERATOR_EQ, dto.getNode().getId())
                    .where(WorkOrderDef.DEVICE_LABEL, ConditionBlock.OPERATOR_EQ, dto.getNode().getModelLabel());
            builder.leftJoin(subConditionBuilder.build());
        }

        ResultWithTotal<List<Map<String, Object>>> modelEntityList = getModelEntityList(builder.build(), GlobalInfoUtils.getUserId());
        return modelEntityList.getData();
    }

    @Override
    public List<WoStatusCountDTO> queryWorkOrderCount(WoStatusCountQueryVO dto) {
        QueryConditionBuilder<BaseEntity> builder = QueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET)
                .count(WorkOrderDef.WORKSHEET_STATUS)
                .eq(ColumnDef.TASK_TYPE, dto.getTaskType())
                .ge(ColumnDef.EXECUTE_TIME_PLAN, dto.getStartTime())
                .lt(ColumnDef.EXECUTE_TIME_PLAN, dto.getEndTime())
                .eq(WorkOrderDef.TEAM_ID, dto.getTeamId());

        if (CollectionUtils.isNotEmpty(dto.getStatuses())) {
            builder.in(WorkOrderDef.WORKSHEET_STATUS, dto.getStatuses());
        }

        ResultWithTotal<List<Map<String, Object>>> modelEntityList = getModelEntityList(builder.build(), GlobalInfoUtils.getUserId());
        List<Map<String, Object>> data = modelEntityList.getData();
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }

        List<WoStatusCountDTO> res = new ArrayList<>();
        Map<Integer, String> workSheetStatusMap = workSheetStatusUtils.getWorkSheetStatusMapByTaskType(WorkSheetTaskType.INSPECTION);
        for (Map<String, Object> map : data) {
            WoStatusCountDTO workOrderCountDto = new WoStatusCountDTO();
            Integer status = NumberUtils.parseInteger(map.get(WorkOrderDef.WORKSHEET_STATUS));
            Integer count = NumberUtils.parseInteger(map.get(ColumnDef.COUNT_ID));

            workOrderCountDto.setCount(count);
            workOrderCountDto.setWorkOrderStatus(status);
            workOrderCountDto.setWorkOrderStatusName(workSheetStatusMap.get(status));
            res.add(workOrderCountDto);
        }

        return res;
    }

    @Override
    public List<Map<String, Object>> queryRuntimeWorkOrderCountByTaskType(WorkOrderSearchVo dto, Long tenantId) {
        List<Integer> workSheetStatuses = Arrays.asList(WorkSheetStatusDef.TO_BE_SENT, WorkSheetStatusDef.ALREADY_SENT,
                WorkSheetStatusDef.AUDITED, WorkSheetStatusDef.TO_BE_AUDITED, WorkSheetStatusDef.ABNORMAL);

        QueryConditionBuilder<BaseEntity> builder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET)
                .composeMethod(true)
                .count(WorkOrderDef.TASK_TYPE);

        if (CollectionUtils.isEmpty(dto.getTaskTypes())) {
            dto.setTaskTypes(WorkSheetTaskType.ALL_TASK_TYPES);
        }

        int group = 1;
        for (Integer taskType : dto.getTaskTypes()) {
            assemblyCondition(taskType, dto, workSheetStatuses, builder, group++);
        }

        if (dto.getNode() != null) {
            SubConditionBuilder subConditionBuilder = new SubConditionBuilder(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP)
                    .where(WorkOrderDef.DEVICE_ID, ConditionBlock.OPERATOR_EQ, dto.getNode().getId())
                    .where(WorkOrderDef.DEVICE_LABEL, ConditionBlock.OPERATOR_EQ, dto.getNode().getModelLabel());
            builder.leftJoin(subConditionBuilder.build());
        }

        ResultWithTotal<List<Map<String, Object>>> modelEntityList = getRuntimeModelEntityList(builder.build(), GlobalInfoUtils.getUserId(), tenantId);
        return modelEntityList.getData();
    }

    private void assemblyCondition(Integer taskType, WorkOrderSearchVo dto, List<Integer> workSheetStatuses, QueryConditionBuilder<BaseEntity> builder, Integer group) {
        builder.in(WorkOrderDef.WORKSHEET_STATUS, workSheetStatuses, group);
        if (dto.getStartTime() != null) {
            builder.ge(ColumnDef.EXECUTE_TIME_PLAN, TimeUtil.localDateTime2timestamp(dto.getStartTime()), group);
        }

        if (dto.getEndTime() != null) {
            builder.lt(ColumnDef.EXECUTE_TIME_PLAN, TimeUtil.localDateTime2timestamp(dto.getEndTime()), group);
        }

        if (taskType != null) {
            builder.eq(WorkOrderDef.TASK_TYPE, taskType, group);
        }

        if (dto.getTeamId() != null) {
            if (!Objects.equals(taskType, WorkSheetTaskType.REPAIR)) {
                builder.eq(WorkOrderDef.TEAM_ID, dto.getTeamId(), group);
                return;
            }

            if (dto.isInspectUser()) {
                builder.eq(WorkOrderDef.INSPECT_TEAM_ID, dto.getTeamId(), group);
            } else {
                builder.eq(WorkOrderDef.TEAM_ID, dto.getTeamId(), group);
            }
        }
    }

    private void createBuilder(WorkOrderSearchVo dto, QueryConditionBuilder<BaseEntity> builder) {
        if (CollectionUtils.isEmpty(dto.getTaskTypes())) {
            createNoTaskTypeBuilder(dto, builder);
            return;
        }

        if (!dto.getTaskTypes().contains(WorkSheetTaskType.REPAIR)) {
            builder.in(ColumnDef.TASK_TYPE, dto.getTaskTypes())
                    .ge(ColumnDef.EXECUTE_TIME_PLAN, TimeUtil.localDateTime2timestamp(dto.getStartTime()))
                    .lt(ColumnDef.EXECUTE_TIME_PLAN, TimeUtil.localDateTime2timestamp(dto.getEndTime()));
            assemblyWorkOrderCountByTypeCondition(dto, builder, null, null);
        } else {
            builder.composeMethod(true);
            List<Integer> taskTypes = dto.getTaskTypes().stream()
                    .filter(it -> !Objects.equals(it, WorkSheetTaskType.REPAIR))
                    .collect(Collectors.toList());

            int group = 1;
            // 拼接非维修工单查询条件
            builder.in(ColumnDef.TASK_TYPE, taskTypes, group)
                    .ge(ColumnDef.EXECUTE_TIME_PLAN, TimeUtil.localDateTime2timestamp(dto.getStartTime()), group)
                    .lt(ColumnDef.EXECUTE_TIME_PLAN, TimeUtil.localDateTime2timestamp(dto.getEndTime()), group);
            assemblyWorkOrderCountByTypeCondition(dto, builder, group, null);

            // 拼接维修工单查询条件
            group++;
            builder.eq(ColumnDef.TASK_TYPE, WorkSheetTaskType.REPAIR, group)
                    .ge(ColumnDef.CREATE_TIME, TimeUtil.localDateTime2timestamp(dto.getStartTime()), group)
                    .lt(ColumnDef.CREATE_TIME, TimeUtil.localDateTime2timestamp(dto.getEndTime()), group);
            assemblyWorkOrderCountByTypeCondition(dto, builder, group, WorkSheetTaskType.REPAIR);
        }
    }

    private void createNoTaskTypeBuilder(WorkOrderSearchVo dto, QueryConditionBuilder<BaseEntity> builder) {
        builder.composeMethod(true);

        int group = 1;
        // 拼接非维修工单查询条件
        builder.in(ColumnDef.TASK_TYPE, Arrays.asList(WorkSheetTaskType.MAINTENANCE, WorkSheetTaskType.INSPECTION), group)
                .ge(ColumnDef.EXECUTE_TIME_PLAN, TimeUtil.localDateTime2timestamp(dto.getStartTime()), group)
                .lt(ColumnDef.EXECUTE_TIME_PLAN, TimeUtil.localDateTime2timestamp(dto.getEndTime()), group);
        assemblyWorkOrderCountByTypeCondition(dto, builder, group, null);

        // 拼接维修工单查询条件
        group++;
        builder.eq(ColumnDef.TASK_TYPE, WorkSheetTaskType.REPAIR, group)
                .ge(ColumnDef.CREATE_TIME, TimeUtil.localDateTime2timestamp(dto.getStartTime()), group)
                .lt(ColumnDef.CREATE_TIME, TimeUtil.localDateTime2timestamp(dto.getEndTime()), group);
        assemblyWorkOrderCountByTypeCondition(dto, builder, group, WorkSheetTaskType.REPAIR);
    }

    /**
     * 组装工单数据统计条件
     *
     * @param dto     查询入参
     * @param builder 查询条件构造对象
     */
    private void assemblyWorkOrderCountByTypeCondition(WorkOrderSearchVo dto, QueryConditionBuilder<BaseEntity> builder, Integer group, Integer taskType) {
        builder.where(ColumnDef.PROJECT_ID, ConditionBlock.OPERATOR_EQ, GlobalInfoUtils.getTenantId(), group);

        if (ParamUtils.checkPrimaryKeyValid(dto.getTeamId())) {
            if (!Objects.equals(taskType, WorkSheetTaskType.REPAIR)) {
                builder.where(WorkOrderDef.TEAM_ID, ConditionBlock.OPERATOR_EQ, dto.getTeamId(), group);
                return;
            }

            if (dto.isInspectUser()) {
                builder.where(WorkOrderDef.INSPECT_TEAM_ID, ConditionBlock.OPERATOR_EQ, dto.getTeamId(), group);
            } else {
                builder.where(WorkOrderDef.TEAM_ID, ConditionBlock.OPERATOR_EQ, dto.getTeamId(), group);
            }
        }
    }

    @Override
    public List<Map<String, Object>> queryWorkOrderCountByStatus(QueryMaintenanceWorkOrderCountRequest queryMaintenanceWorkOrderCountRequest) {
        QueryConditionBuilder<BaseEntity> builder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET)
                .where(ColumnDef.TASK_TYPE, ConditionBlock.OPERATOR_EQ, WorkSheetTaskType.MAINTENANCE)
                .where(ColumnDef.EXECUTE_TIME_PLAN, ConditionBlock.OPERATOR_GE, queryMaintenanceWorkOrderCountRequest.getStartTime())
                .where(ColumnDef.EXECUTE_TIME_PLAN, ConditionBlock.OPERATOR_LT, queryMaintenanceWorkOrderCountRequest.getEndTime())
                .where(ColumnDef.PROJECT_ID, ConditionBlock.OPERATOR_EQ, GlobalInfoUtils.getTenantId())
                .count(WorkOrderDef.WORKSHEET_STATUS);

        if (ParamUtils.checkPrimaryKeyValid(queryMaintenanceWorkOrderCountRequest.getTeamId())) {
            builder.where(WorkOrderDef.TEAM_ID, ConditionBlock.OPERATOR_EQ, queryMaintenanceWorkOrderCountRequest.getTeamId());
        }
        if (ParamUtils.checkPrimaryKeyValid(queryMaintenanceWorkOrderCountRequest.getTaskLevel())) {
            builder.where(WorkOrderDef.TASK_LEVEL, ConditionBlock.OPERATOR_EQ, queryMaintenanceWorkOrderCountRequest.getTaskLevel());
        }
        ResultWithTotal<List<Map<String, Object>>> modelEntityList = getModelEntityList(builder.build(), GlobalInfoUtils.getUserId());
        return modelEntityList.getData();
    }

    @Override
    public List<ProcessFlowUnit> queryProcessFlowUnit(Collection<Long> workOrderIds) {
        QueryConditionBuilder<BaseEntity> builder = ParentQueryConditionBuilder.of(ModelLabelDef.PROCESS_FLOW_UNIT)
                .where(ColumnDef.EVENT_ID, ConditionBlock.OPERATOR_IN, workOrderIds)
                .where(ColumnDef.EVENT_LABEL, ConditionBlock.OPERATOR_EQ, ModelLabelDef.PM_WORK_SHEET)
                .orderByDescending(ColumnDef.LOG_TIME);

        ResultWithTotal<List<Map<String, Object>>> modelEntityList = getModelEntityList(builder.build(), GlobalInfoUtils.getUserId());
        return JsonTransferUtils.transferList(modelEntityList.getData(), ProcessFlowUnit.class);
    }

    @Override
    public List<WorksheetAbnormalReason> queryWorksheetAbnormalReason(Long workOrderId) {
        QueryConditionBuilder<BaseEntity> builder = ParentQueryConditionBuilder.of(ModelLabelDef.WORKSHEET_ABNORMAL_REASON)
                .where(ModelLabelDef.PM_WORK_SHEET + ModelLabelDef.PREFIX_ID, ConditionBlock.OPERATOR_EQ, workOrderId);

        ResultWithTotal<List<Map<String, Object>>> modelEntityList = getModelEntityList(builder.build(), GlobalInfoUtils.getUserId());
        return JsonTransferUtils.transferList(modelEntityList.getData(), WorksheetAbnormalReason.class);
    }

    @Override
    public Map<String, UserTaskConfig> queryTaskConfigList(Collection<Long> workOrderIds, Long tenantId) {
        if (CollectionUtils.isEmpty(workOrderIds)) {
            return Collections.emptyMap();
        }

        // 根据工单号查询taskId
        QueryConditionBuilder<BaseEntity> builder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET, workOrderIds);
        ResultWithTotal<List<Map<String, Object>>> modelEntityList = getRuntimeModelEntityList(builder.build(), LoginDef.USER_ROOT, tenantId);
        List<InspectionWorkOrderDto> workOrders = JsonTransferUtils.transferList(modelEntityList.getData(), InspectionWorkOrderDto.class);
        if (CollectionUtils.isEmpty(workOrders)) {
            return Collections.emptyMap();
        }

        // 根据taskId查询配置，并跟工单号进行组装
        List<String> taskIds = workOrders.stream().map(InspectionWorkOrderDto::getTaskId).distinct().collect(Collectors.toList());
        ApiResult<List<UserTaskConfig>> taskConfigListResult = userTaskRestApi.getTaskConfigList(GlobalInfoUtils.getUserId(), taskIds);
        ParamUtils.checkResultGeneric(taskConfigListResult);
        List<UserTaskConfig> taskConfigList = taskConfigListResult.getData();
        Map<String, UserTaskConfig> taskConfigMap = taskConfigList.stream().collect(Collectors.toMap(UserTaskConfig::getTaskId, it -> it));
        Map<String, UserTaskConfig> result = new HashMap<>(taskConfigMap.size());
        for (InspectionWorkOrderDto workOrder : workOrders) {
            result.put(workOrder.getCode(), taskConfigMap.get(workOrder.getTaskId()));
        }

        return result;
    }

    @Override
    public ResultWithTotal<List<InspectionWorkOrderDto>> queryFinishWorkOrderByNode(InspectionSearchDto dto, Long userId, Long projectId,
                                                                                    Collection<BaseVo> baseVo, List<Long> schemeIds) {

        Map<String, List<BaseVo>> nodeMap = baseVo.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));

        //2.0.63工单服务有bug对于多节点的情况，按类型去分次查询
        List<Map<String, Object>> result = new ArrayList<>();
        for (Map.Entry<String, List<BaseVo>> entry : nodeMap.entrySet()) {
            SubConditionBuilder subConditionBuilder = new SubConditionBuilder(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP);
            QueryConditionBuilder<BaseEntity> builder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET)
                    .distinct()
                    .orderBy(ColumnDef.FINISH_TIME)
                    .leftJoin(subConditionBuilder.build())
                    .limit(dto.getPage());
            String label = entry.getKey();
            List<BaseVo> baseVos = nodeMap.get(label);
            List<Long> ids = baseVos.stream().map(BaseVo::getId).distinct().collect(Collectors.toList());
            subConditionBuilder.where(ColumnDef.DEVICE_ID_DEVICE_PLAN, ConditionBlock.OPERATOR_IN, ids);
            subConditionBuilder.where(ColumnDef.DEVICE_Label, ConditionBlock.OPERATOR_EQ, label);
            assembleBuild(builder, dto, projectId, schemeIds);
            ResultWithTotal<List<Map<String, Object>>> modelEntityList = getModelEntityList(builder.build(), userId);
            result.addAll(modelEntityList.getData());
        }

        List<InspectionWorkOrderDto> workOrderList = JsonUtil.mapList2BeanList(result, InspectionWorkOrderDto.class);
        List<WorksheetAbnormalReasonDto> worksheetAbnormalReasonDtos = queryWorksheetAbnormalReason(workOrderList.stream().map(BaseEntity::getId).collect(Collectors.toSet()));
        Map<Long, List<WorksheetAbnormalReasonDto>> map = worksheetAbnormalReasonDtos.stream().collect(Collectors.groupingBy(WorksheetAbnormalReason::getPmWorkSheetId));
        for (InspectionWorkOrderDto workOrderDto : workOrderList) {
            workOrderDto.setAbnormalReasonList(map.get(workOrderDto.getId()));
        }

        return ResultWithTotal.ok(workOrderList, result.size());
    }

    private void assembleBuild(QueryConditionBuilder<BaseEntity> builder, InspectionSearchDto dto, Long projectId,
                               List<Long> schemeIds) {
        builder.where(ColumnDef.TASK_TYPE, ConditionBlock.OPERATOR_EQ, dto.getTaskType());
        builder.where(ColumnDef.FINISH_TIME, ConditionBlock.OPERATOR_GE, TimeUtil.localDateTime2timestamp(dto.getStartTime()));
        builder.where(ColumnDef.FINISH_TIME, ConditionBlock.OPERATOR_LT, TimeUtil.localDateTime2timestamp(dto.getEndTime()));
        builder.where(ColumnDef.PROJECT_ID, ConditionBlock.OPERATOR_EQ, projectId);
        builder.where(RecordSheetModelLabel.SCHEME_ID, ConditionBlock.OPERATOR_IN, schemeIds);
        if (ParamUtils.checkPrimaryKeyValid(dto.getWorkSheetStatus())) {
            builder.where(WorkOrderDef.WORKSHEET_STATUS, ConditionBlock.OPERATOR_EQ, dto.getWorkSheetStatus());
        }
    }

    @Override
    public ApiResult<List<Map<String, Object>>> saveModelEntityList(List<Map<String, Object>> dataList) {
        ApiResult<List<Map<String, Object>>> result = userTaskRestApi.saveModelEntityList(JsonTransferUtils.parseMap(dataList));
        ParamUtils.checkResultGeneric(result);
        return result;
    }

    @Override
    public ApiResult<ProcessInstanceResponse> startProcessByTable(Long userId, TableTriggerParams triggerParams) {
        triggerParams.setFormData(JsonTransferUtils.parseMap(triggerParams.getFormData()));
        ApiResult<ProcessInstanceResponse> result = triggerRestApi.startProcessByTable(userId, triggerParams);
        ParamUtils.checkResultGeneric(result);
        return result;
    }

    @Override
    public ApiResult<Map<String, Object>> submitForm(Long userId, String taskId, UserTaskParams taskParams) {
        Map<String, Object> formData = taskParams.getFormData();
        taskParams.setFormData(JsonTransferUtils.parseMap(formData));
        ApiResult<Map<String, Object>> result = userTaskRestApi.submitForm(userId, taskId, taskParams);
        ParamUtils.checkResultGeneric(result);
        return result;
    }

    @Override
    public ApiResult<List<ProcessInstanceResponse>> startProcessesByManyTables(Long userId, MultiTableTriggerParams multiTableTriggerParams) {
        multiTableTriggerParams.setFormDataList(JsonTransferUtils.parseMap(multiTableTriggerParams.getFormDataList()));
        ApiResult<List<ProcessInstanceResponse>> result = triggerRestApi.startProcessesByManyTables(userId, multiTableTriggerParams);
        ParamUtils.checkResultGeneric(result);
        return result;
    }


    @Override
    public List<SignGroupWithSignIn> querySignPoint(WorkOrderSimpleQueryDTO workOrderQueryVo) {
        QueryConditionBuilder<BaseEntity> builder = QueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET)
                .eq(ColumnDef.TASK_TYPE, workOrderQueryVo.getTaskType())
                .ge(ColumnDef.EXECUTE_TIME_PLAN, workOrderQueryVo.getStartTime())
                .lt(ColumnDef.EXECUTE_TIME_PLAN, workOrderQueryVo.getEndTime())
                .select(Arrays.asList(WorkOrderDef.SIGN_POINT_ID, WorkOrderDef.SIGN_GROUP_ID))
                .distinct();

        if (workOrderQueryVo.getWorkOrderStatus().size() == 1) {
            builder.eq(WorkOrderDef.WORKSHEET_STATUS, workOrderQueryVo.getWorkOrderStatus().get(0));
        } else {
            builder.in(WorkOrderDef.WORKSHEET_STATUS, workOrderQueryVo.getWorkOrderStatus());
        }

        if (Objects.nonNull(workOrderQueryVo.getTeamId())) {
            builder.eq(WorkOrderDef.TEAM_ID, workOrderQueryVo.getTeamId());
        }

        ResultWithTotal<List<Map<String, Object>>> modelEntityList = getModelEntityList(builder.build(), workOrderQueryVo.getUserId());
        return JsonTransferUtils.transferList(modelEntityList.getData(), SignGroupWithSignIn.class);
    }
}
