package com.cet.eem.fusion.maintenance.core.service.bff;

import com.cet.eem.auth.service.AuthUtils;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.bll.maintenance.model.workorder.MaintenanceContent;
import com.cet.eem.bll.maintenance.model.workorder.OperationUser;
import com.cet.eem.bll.maintenance.model.workorder.WorkOrderSearchVo;
import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.bll.maintenance.service.WorkOrderService;
import com.cet.eem.bll.maintenance.service.inspection.InspectorService;
import com.cet.eem.bll.maintenance.utils.InspectorUserCheckUtils;
import com.cet.eem.bll.maintenance.utils.WorkSheetStatusUtils;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/6/20
 */
@Service
public class WorkOrderBffServiceImpl implements WorkOrderBffService {
    @Autowired
    WorkOrderService workOrderService;

    @Autowired
    AuthUtils authUtils;

    @Autowired
    InspectorService inspectorService;

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    WorkSheetStatusUtils workSheetStatusUtils;

    @Autowired
    InspectorUserCheckUtils inspectorUserCheckUtils;

    @Override
    public ResultWithTotal<List<InspectionWorkOrderDto>> queryWorkOrderList(WorkOrderSearchVo searchVo) {
        // 查询用户信息和班组信息
        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());

        // 判断当前用户是否为巡检用户，对于巡检用户只能看自己班组的工单
        searchVo.setTeamId(inspectorUserCheckUtils.getAndCheckTeamId(searchVo.getTeamId(), user));
        searchVo.setInspectUser(inspectorService.isInspectUser(user));

        ResultWithTotal<List<InspectionWorkOrderDto>> result = workOrderService.queryWorkOrderList(searchVo);
        List<InspectionWorkOrderDto> workOrderList = result.getData();
        assemblyCommonData(workOrderList, user.getTenantId());
        return ResultWithTotal.ok(workOrderList.stream().sorted(Comparator.comparing(InspectionWorkOrderDto::getExecuteTimePlan).reversed()).collect(Collectors.toList()), result.getTotal());
    }

    private void assemblyCommonData(List<InspectionWorkOrderDto> workOrderList, Long tenantId) {
        if (CollectionUtils.isEmpty(workOrderList)) {
            return;
        }

        Map<Integer, Map<Integer, String>> workSheetStatusMap = workSheetStatusUtils.getWorkSheetStatusMap();
        Set<Long> userIds = new HashSet<>();

        Map<Integer, String> workStatusMap = null;
        Map<Integer, String> taskType = modelServiceUtils.getEnumByLabel(ModelLabelDef.WORK_SHEET_TASK_TYPE);
        Map<String, Set<Long>> codeAndUserMap = new HashMap<>();
        for (InspectionWorkOrderDto workOrderDto : workOrderList) {
            workStatusMap = workSheetStatusUtils.getWorkSheetStatusMapByTaskType(workOrderDto.getTaskType(), workSheetStatusMap);
            workOrderDto.setWorkSheetStatusName(workStatusMap.get(workOrderDto.getWorkSheetStatus()));
            workOrderDto.setTaskTypeName(taskType.get(workOrderDto.getTaskType()));
            MaintenanceContent maintenanceContent = JsonTransferUtils.parseObject(workOrderDto.getMaintenanceContent(), MaintenanceContent.class);
            if (maintenanceContent == null) {
                continue;
            }
            Set<Long> tmpUsers = getStaffIds(maintenanceContent);
            userIds.addAll(tmpUsers);
            codeAndUserMap.put(workOrderDto.getCode(), tmpUsers);
        }

        queryStaffName(workOrderList, tenantId, userIds, codeAndUserMap);
    }

    private Set<Long> getStaffIds(MaintenanceContent maintenanceContent) {
        Set<Long> tmpUsers = new HashSet<>();
        if (CollectionUtils.isNotEmpty(maintenanceContent.getUsers())) {
            tmpUsers.addAll(maintenanceContent.getUsers().stream().map(OperationUser::getUserId).filter(Objects::nonNull).collect(Collectors.toSet()));
        }

        if(CollectionUtils.isNotEmpty(maintenanceContent.getItemExtends())) {
            tmpUsers.addAll(maintenanceContent.getItemExtends().stream()
                    .filter(it->CollectionUtils.isNotEmpty(it.getExecutor()))
                    .flatMap(it->it.getExecutor().stream())
                    .collect(Collectors.toSet()));
        }
        return tmpUsers;
    }

    private void queryStaffName(List<InspectionWorkOrderDto> workOrderList, Long tenantId, Set<Long> userIds, Map<String, Set<Long>> codeAndUserMap) {
        List<UserVo> users = authUtils.queryBatchUser(userIds, tenantId);
        Map<Long, String> userMap = users.stream().collect(Collectors.toMap(UserVo::getId, UserVo::getName));

        for (InspectionWorkOrderDto workOrderDto : workOrderList) {
            Set<Long> tmpUserIds = codeAndUserMap.get(workOrderDto.getCode());
            if(CollectionUtils.isEmpty(tmpUserIds)) {
                continue;
            }

            List<String> userNames = new ArrayList<>();
            for (Long userId : tmpUserIds) {
                String name = userMap.get(userId);
                if (name != null) {
                    userNames.add(name);
                }
            }
            workOrderDto.setStaffName(StringUtils.join(userNames, "、"));
        }
    }
}
