﻿package com.cet.eem.fusion.refrigeration.core.handler;

import com.cet.eem.bll.common.model.domain.subject.energysaving.DeviceChain;
import com.cet.eem.bll.demand.model.entity.Project;
import com.cet.eem.fusion.refrigeration.core.handle.optimizationstrategy.FaultDiagnosis;
import com.cet.eem.fusion.refrigeration.core.model.config.DeviceChainParam;
import com.cet.eem.fusion.refrigeration.core.model.def.ColdControlTypeDef;
import com.cet.eem.fusion.refrigeration.core.model.def.QuantityDef;
import com.cet.eem.fusion.refrigeration.core.model.weather.QueryParam;
import com.cet.eem.fusion.refrigeration.core.service.trend.ModelConfigurationService;
import com.cet.eem.fusion.refrigeration.core.service.trend.OperationTrendService;
import com.cet.eem.common.constant.EnumDataTypeId;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.model.realtime.RealTimeValue;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
import com.cet.eem.quantity.service.QuantityManageService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class FaultDiagnosisImpl implements FaultDiagnosis {

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    ModelConfigurationService modelConfigurationService;

    @Autowired
    OperationTrendService operationTrendService;

    @Autowired
    QuantityManageService quantityManageService;

    public static final Double UP = 1.0;
    public static final Double DOWN = 0.0;

    @Override
    public Map<Long, Boolean> deviceFaultDiagnosis(Long roomID, Long startTime, Long endTime) {
        List<BaseVo> coldWaterMainEngines = operationTrendService.queryColdWaterMainEngine(
                new QueryParam(roomID, NodeLabelDef.ROOM), NodeLabelDef.COLD_WATER_MAINENGINE);
        if (coldWaterMainEngines.isEmpty()){
            return new HashMap<>();
        }
        List<Long> coldWaterMainEnginesIDs = coldWaterMainEngines.stream().map(BaseVo::getId).collect(Collectors.toList());

        Map<Long, Boolean> res = new HashMap<>();
        for (Long coldWaterMainEnginesID : coldWaterMainEnginesIDs){
            //查询出冷机对应连锁下的所有设备
            List<BaseVo> deviceChains = getDeviceChain(NodeLabelDef.COLD_WATER_MAINENGINE, coldWaterMainEnginesID, roomID);
            //查询连锁设备的故障信号
            Map<Integer, List<RealTimeValue>> signalData = quantityManageService.queryRealTimeBath(
                    new QuantityDataBatchSearchVo(Collections.singletonList(QuantityDef.getFaultSignal()),
                            deviceChains, startTime, endTime,
                            AggregationCycle.FIVE_MINUTES, 5,EnumDataTypeId.REALTIME.getId())
            );
            List<RealTimeValue> faultSignalData = signalData.get(QuantityDef.getFaultSignal().getId());
            //过滤所有设备的故障、停止信号，只要有一个为1，说明存在异常，需要减机，并将对应冷机id，添加到“待操作的冷机id”list中
            List<RealTimeValue> collectForFaultSignal = faultSignalData.stream().filter(x -> Objects.equals(x.getValue(), UP)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collectForFaultSignal)){
                res.put(coldWaterMainEnginesID, false);
            }else {
                res.put(coldWaterMainEnginesID, true);
            }
        }


        return res;
    }

    /**
     * 根据指定设备查询对应的连锁和设备
     * */
    private List<BaseVo> getDeviceChain(String objectLabel, Long objectID, Long roomid){
        QueryCondition condition  = new QueryConditionBuilder<>(ModelLabelDef.DEVICE_CHAIN)
                .eq(ColumnDef.ROOM_ID, roomid).build();
        List<DeviceChain> query = modelServiceUtils.query(condition, DeviceChain.class);
        if (CollectionUtils.isEmpty(query)){
            return null;
        }
        List<Long> deviceChainIDs = query.stream().map(DeviceChain::getId).collect(Collectors.toList());

        List<BaseVo> res = new ArrayList<>();
        for (Long deviceChainID : deviceChainIDs){
            List<DeviceChainParam> deviceChainParams = modelConfigurationService.queryDeviceChainWithDetail(deviceChainID,getProjectID());
            if (CollectionUtils.isEmpty(deviceChainParams)){continue;}
            List<BaseVo> chainList = deviceChainParams.get(0).getNodes().stream().filter(x -> (x.getId().equals(objectID) && x.getModelLabel().equals(objectLabel))).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(chainList)){
                return deviceChainParams.get(0).getNodes().stream().map(x -> new BaseVo(x.getId(), x.getModelLabel())).collect(Collectors.toList());
            }
        }
        return res;
    }

    private Long getProjectID(){
        QueryCondition queryCondition = new QueryConditionBuilder<>(NodeLabelDef.PROJECT).build();
        return modelServiceUtils.query(queryCondition, Project.class).get(0).getId();
    }
}

