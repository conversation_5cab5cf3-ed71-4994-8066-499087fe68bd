package com.cet.eem.fusion.maintenance.core.entity.inspector;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName : AddInspectorTeamRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-09 14:35
 */
@Getter
@Setter
@ApiModel(value = "AddInspectorTeamRequest", description = "新增巡检班组")
public class AddInspectorTeamRequest {
    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空")
    private Long tenantId;


    /**
     * 班组名
     */
    @NotEmpty(message = "班组名不能为空")
    private String name;

    /**
     * 此班组下的用户
     */
    private List<Long> userIds;
}
