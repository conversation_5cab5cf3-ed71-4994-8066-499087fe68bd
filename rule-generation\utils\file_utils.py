"""
文件操作工具类
用于扫描Java文件、读取文件内容等
"""

import os
import logging
from typing import List, Tuple
from pathlib import Path
from config.settings import FILE_CONFIG
from service.java_parser_service import JavaParserService

logger = logging.getLogger(__name__)


class FileUtils:
    """文件操作工具类"""

    def __init__(self):
        """初始化文件工具"""
        self.java_parser = JavaParserService()

    def scan_java_files(self, project_path: str = None, exclude_controller: bool = False) -> List[str]:
        """
        扫描指定路径下的所有Java文件

        Args:
            project_path (str): 项目路径，支持相对路径和绝对路径
            exclude_controller (bool): 是否忽略包含Controller的文件

        Returns:
            List[str]: Java文件路径列表
        """
        if project_path is None:
            raise ValueError("项目路径不能为空，请指定有效的项目路径")
        
        # 处理相对路径
        if not os.path.isabs(project_path):
            project_path = os.path.abspath(project_path)
        
        if not os.path.exists(project_path):
            logger.error(f"项目路径不存在: {project_path}")
            return []
        
        java_files = []
        exclude_dirs = set(FILE_CONFIG["exclude_dirs"])

        logger.info(f"开始扫描Java文件，路径: {project_path}")
        if exclude_controller:
            logger.info("已启用Controller文件过滤")

        for root, dirs, files in os.walk(project_path):
            # 过滤排除的目录
            dirs[:] = [d for d in dirs if d not in exclude_dirs]

            for file in files:
                if any(file.endswith(ext) for ext in FILE_CONFIG["java_extensions"]):
                    # 如果启用了Controller过滤，检查文件名是否包含Controller
                    if exclude_controller and "Controller" in file:
                        logger.debug(f"跳过Controller文件: {file}")
                        continue

                    file_path = os.path.join(root, file)
                    java_files.append(file_path)

        if exclude_controller:
            logger.info(f"扫描完成，找到 {len(java_files)} 个Java文件（已过滤Controller文件）")
        else:
            logger.info(f"扫描完成，找到 {len(java_files)} 个Java文件")
        return java_files
    
    def read_file_content(self, file_path: str) -> str:
        """
        读取文件内容
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            str: 文件内容
        """
        try:
            with open(file_path, 'r', encoding=FILE_CONFIG["encoding"]) as f:
                content = f.read()
            return content
        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    content = f.read()
                return content
            except Exception as e:
                logger.warning(f"读取文件失败 {file_path}: {e}")
                return ""
        except Exception as e:
            logger.warning(f"读取文件失败 {file_path}: {e}")
            return ""
    
    def get_relative_path(self, file_path: str, base_path: str) -> str:
        """
        获取相对于基础路径的相对路径
        
        Args:
            file_path (str): 文件绝对路径
            base_path (str): 基础路径
            
        Returns:
            str: 相对路径
        """
        try:
            return os.path.relpath(file_path, base_path)
        except Exception:
            return file_path
    
    def extract_class_name(self, file_path: str, content: str) -> str:
        """
        从Java文件中提取类名

        Args:
            file_path (str): 文件路径
            content (str): 文件内容

        Returns:
            str: 类名，如果提取失败则返回文件名
        """
        try:
            # 使用Java解析服务提取类名
            return self.java_parser.extract_main_class_name(file_path, content)

        except Exception as e:
            logger.warning(f"提取类名失败 {file_path}: {e}")
            # 返回文件名作为备选
            return os.path.basename(file_path).replace('.java', '')

    @staticmethod
    def smart_truncate_content(content: str, max_length: int) -> str:
        """
        智能截断代码内容，尽量保持代码结构完整

        Args:
            content (str): 原始代码内容
            max_length (int): 最大长度

        Returns:
            str: 截断后的内容
        """
        if len(content) <= max_length:
            return content

        # 保守截断，留出足够空间给截断标记
        safe_length = max_length - 100  # 预留100字符给截断标记
        truncated = content[:safe_length]

        # 寻找最后一个完整的方法或类结束位置
        last_brace = truncated.rfind('}')
        if last_brace > safe_length * 0.7:  # 如果找到的位置不会损失太多内容
            truncated = content[:last_brace + 1]

        # 添加截断标记
        truncated += "\n\n// ... [内容已截断，原文件长度: " + str(len(content)) + " 字符] ..."

        return truncated

    def batch_read_files(self, file_paths: List[str]) -> List[Tuple[str, str, str]]:
        """
        批量读取文件内容并提取类名

        Args:
            file_paths (List[str]): 文件路径列表

        Returns:
            List[Tuple[str, str, str]]: (类名, 文件内容, 文件路径) 元组列表
        """
        results = []
        large_files = []
        very_large_files = []

        for i, file_path in enumerate(file_paths):
            try:
                # 检查文件大小
                file_size = os.path.getsize(file_path)

                # 跳过过大的文件
                max_size = FILE_CONFIG["max_file_size"]
                large_threshold = FILE_CONFIG["large_file_threshold"]

                if file_size > max_size:
                    very_large_files.append((file_path, file_size))
                    logger.warning(f"跳过超大文件: {file_path} ({file_size/1024/1024:.1f}MB)")
                    continue

                if file_size > large_threshold:
                    large_files.append((file_path, file_size))

                content = self.read_file_content(file_path)
                if content:
                    class_name = self.extract_class_name(file_path, content)
                    results.append((class_name, content, file_path))

                if (i + 1) % 50 == 0:
                    logger.info(f"已读取 {i + 1}/{len(file_paths)} 个文件")

            except Exception as e:
                logger.warning(f"处理文件失败 {file_path}: {e}")
                continue

        # 统计信息
        if very_large_files:
            logger.warning(f"跳过了 {len(very_large_files)} 个超大文件(>1MB):")
            for file_path, size in very_large_files[:3]:
                logger.warning(f"  {file_path}: {size/1024/1024:.1f}MB")
            if len(very_large_files) > 3:
                logger.warning(f"  ... 还有 {len(very_large_files)-3} 个超大文件")

        if large_files:
            logger.info(f"发现 {len(large_files)} 个大文件(>200KB):")
            for file_path, size in large_files[:5]:
                logger.info(f"  {file_path}: {size/1024:.1f}KB")
            if len(large_files) > 5:
                logger.info(f"  ... 还有 {len(large_files)-5} 个大文件")

        logger.info(f"批量读取完成，成功读取 {len(results)} 个文件")
        return results
