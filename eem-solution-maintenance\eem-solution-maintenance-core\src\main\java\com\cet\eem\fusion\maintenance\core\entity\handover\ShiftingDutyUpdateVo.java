package com.cet.eem.fusion.maintenance.core.entity.handover;

import com.cet.eem.fusion.maintenance.core.def.HandoverDef;
import com.cet.eem.fusion.maintenance.core.entity.workorder.OperationUser;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 交接班
 *
 * <AUTHOR>
 * @date 2020年6月22日 14:22:10
 */
@Getter
@Setter
@ApiModel(description = "交接班")
public class ShiftingDutyUpdateVo {
    @ApiModelProperty("值班员")
    private List<OperationUser> dutyStaffList;

    @ApiModelProperty("值班日志")
    @JsonProperty(value = HandoverDef.DUTYLOG)
    private String dutyLog;

    @ApiModelProperty("值班事项")
    @JsonProperty(value = HandoverDef.HANDOVERMATTER)
    private String handoverMatter;
}
