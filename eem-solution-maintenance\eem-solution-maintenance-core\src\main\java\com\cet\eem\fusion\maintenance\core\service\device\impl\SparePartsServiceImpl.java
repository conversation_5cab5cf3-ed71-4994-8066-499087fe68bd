package com.cet.eem.fusion.maintenance.core.service.device.impl;


import com.cet.eem.auth.service.TreeLevelService;
import com.cet.eem.auth.service.impl.CommonAuthService;
import com.cet.eem.fusion.config.sdk.service.EemNodeService;
import com.cet.eem.fusion.config.sdk.model.node.RoomVo;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.*;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.DeviceSystemWithSubLayer;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.DeviceWithSubLayer;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.bll.maintenance.dao.WorkOrderDao;
import com.cet.eem.bll.maintenance.dao.devicecomponent.*;
import com.cet.eem.bll.maintenance.dao.devicemanager.ArrayCabiNetDao;
import com.cet.eem.bll.maintenance.dao.devicemanager.PowerDisCabinetDao;
import com.cet.eem.bll.maintenance.model.devicemanage.component.*;
import com.cet.eem.bll.maintenance.model.devicemanage.component.dto.QueryReplaceRecordDto;
import com.cet.eem.bll.maintenance.model.devicemanage.component.dto.QuerySparePartsDto;
import com.cet.eem.bll.maintenance.model.devicemanage.component.vo.DeviceSystemVoWithSubLayer;
import com.cet.eem.bll.maintenance.model.devicemanage.component.vo.SparePartsCountVo;
import com.cet.eem.bll.maintenance.model.devicemanage.component.vo.SparePartsDeviceVo;
import com.cet.eem.bll.maintenance.model.workorder.WorkOrderPo;
import com.cet.eem.bll.maintenance.service.device.SparePartsService;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.common.utils.ErrorUtils;
import com.cet.eem.fusion.common.def.base.ExcelType;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.def.label.NodeLabelDef;
import com.cet.eem.fusion.common.utils.file.FileUtils;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.fusion.common.utils.excel.PoiExcelUtils;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;
import com.cet.eem.model.base.SingleModelConditionDTO;
import com.cet.eem.model.model.AbstractModelEntity;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;
import com.cet.eem.fusion.common.modelutils.model.tool.SubConditionBuilder;
import com.cet.eem.service.EemModelDataService;
import com.cet.eem.toolkit.StringUtils;
import com.cet.electric.modelservice.common.entity.IdTextPair;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class SparePartsServiceImpl implements SparePartsService {
    private final EemModelDataService modelService;
    @Autowired
    DeviceSystemDao deviceSystemDao;
    @Autowired
    SparePartsDao sparePartsDao;
    @Autowired
    DeviceDao deviceDao;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    SparePartsReplaceRecordDao sparePartsReplaceRecordDao;
    @Autowired
    DeviceComponentDao deviceComponentDao;
    @Autowired
    NodeDao nodeDao;
    @Autowired
    EemModelDataService eemModelDataService;
    @Autowired
    WorkOrderDao workOrderDao;
    @Autowired
    PowerDisCabinetDao powerDisCabinetDao;
    @Autowired
    ArrayCabiNetDao arrayCabiNetDao;
    @Autowired
    CommonAuthService authManageService;
    @Autowired
    TreeLevelService treeLevelService;

    /**
     * 导出记录最大数量
     */
    @Value("${cet.eem.event.inspection.export-max-size: 10000}")
    private int exportMaxCount;

    public SparePartsServiceImpl(EemModelDataService modelService) {
        this.modelService = modelService;
    }

    /**
     * 根据设备查询备件库
     *
     * @param querySparePartsDto 查询条件
     * @return List<SpareParts>
     */
    @Override
    public List<SpareParts> querySparePartsByDevice(QuerySparePartsDto querySparePartsDto) {
        if (null == querySparePartsDto.getKeyWord() || "".equals(querySparePartsDto.getKeyWord())) {
            return deviceDao.querySparepartsByDeviceId(querySparePartsDto.getId());
        } else {
            return sparePartsDao.queryByDeviceAndKeyWord(querySparePartsDto.getId(), querySparePartsDto.getKeyWord());
        }
    }

    /**
     * 传的设备类型实际是对应的id
     *
     * @return List<IdTextPair>
     */
    private List<IdTextPair> getDeviceClassList() {
        String label = "deviceclass";
        return modelService.getEnumrationByModel(label).getData();
    }

    /**
     * 根据项目查询系统和系统下的设备备件信息
     *
     * @return List<DeviceSystemVoWithSubLayer>
     */
    @Override
    public List<DeviceSystemVoWithSubLayer> querySpareDevice() {
        //查询系统及其设备备件信息
        List<DeviceSystemWithSubLayer> list = deviceDao.queryDeviceBySystem();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<DeviceSystemVoWithSubLayer> result = new ArrayList<>();
        //前端需要的设备类型对应的id，下面是为显示的数据添加objectLabelId信息来返回
        for (DeviceSystemWithSubLayer item : list) {
            DeviceSystemVoWithSubLayer deviceSystemVoWithSubLayer = new DeviceSystemVoWithSubLayer();
            deviceSystemVoWithSubLayer.setId(item.getId());
            deviceSystemVoWithSubLayer.setName(item.getName());
            deviceSystemVoWithSubLayer.setProjectId(item.getProjectId());
            if (CollectionUtils.isEmpty(item.getSparePartsDeviceList())) {
                result.add(deviceSystemVoWithSubLayer);
                continue;
            } else {
                List<SparePartsDeviceVo> sparePartsDeviceVoList = new ArrayList<>();
                for (SparePartsDevice itemNow : item.getSparePartsDeviceList()) {
                    SparePartsDeviceVo sparePartsDeviceVo = new SparePartsDeviceVo();
                    Integer objectLabelId = getObjectLabelId(itemNow.getObjectLabel());
                    sparePartsDeviceVo.setObjectLabel(itemNow.getObjectLabel());
                    assert objectLabelId != null;
                    sparePartsDeviceVo.setObjectLabelId(objectLabelId.longValue());
                    sparePartsDeviceVo.setModel(itemNow.getModel());
                    sparePartsDeviceVo.setId(itemNow.getId());
                    sparePartsDeviceVo.setName(itemNow.getName());
                    sparePartsDeviceVo.setModelLabel(itemNow.getModelLabel());
                    sparePartsDeviceVo.setCompany(itemNow.getCompany());
                    sparePartsDeviceVo.setSecondarySparePartsId(itemNow.getSecondarySparePartsId());
                    sparePartsDeviceVo.setCriticalParams(itemNow.getCriticalParams());
                    sparePartsDeviceVoList.add(sparePartsDeviceVo);
                }
                deviceSystemVoWithSubLayer.setSparePartsDeviceVoList(sparePartsDeviceVoList);
                result.add(deviceSystemVoWithSubLayer);
            }
        }
        return result;
    }

    private Integer getObjectLabelId(String objectLabel) {
        List<IdTextPair> deviceClassList = getDeviceClassList();
        for (IdTextPair item : deviceClassList) {
            //根据设备类型返回对应的id
            if (item.getPropertyLabel().equals(objectLabel)) {
                return item.getId();
            }
        }
        return null;
    }

    /**
     * 编辑备件信息
     *
     * @param editSpareParts 编辑条件
     * @return SpareParts
     */
    @Override
    public SpareParts editSpareParts(EditSpareParts editSpareParts) {
        checkIdRepeatWhileCreate(editSpareParts);
        SpareParts spareParts = new SpareParts();
        spareParts.setModel(editSpareParts.getModel());
        spareParts.setUnit(editSpareParts.getUnit());
        spareParts.setName(editSpareParts.getName());
        spareParts.setId(editSpareParts.getId());
        sparePartsDao.updateById(spareParts);
        return spareParts;
    }

    /**
     * 在设备备件下删除备件
     *
     * @param id       备件id
     * @param deviceId 设备id
     */
    @Override
    public void deleteSpareParts(Long id, Long deviceId) {
        SpareParts spareParts = sparePartsDao.selectById(id);
        DeviceWithSubLayer deviceWithSubLayer = deviceDao.selectRelatedById(DeviceWithSubLayer.class, deviceId);
        if (null == spareParts || null == deviceWithSubLayer) {
            return;
        }
        Assert.isTrue(CollectionUtils.isNotEmpty(deviceWithSubLayer.getSparePartsList()), "该设备下找不到该备件");
        deviceDao.deleteChild(deviceId, Collections.singletonList(spareParts));
    }

    /**
     * 在系统下新增设备备件
     *
     * @param addSparePartsDevice 新增备件信息
     * @return SparePartsDeviceVo
     */
    @Override
    public SparePartsDeviceVo createDevice(AddSparePartsDevice addSparePartsDevice) {
        List<IdTextPair> deviceClassList = getDeviceClassList();
        String objectLabel = null;
        for (IdTextPair item : deviceClassList) {
            Long objectLabelId = addSparePartsDevice.getObjectLabelId();
            Integer id = item.getId();
            if (objectLabelId.intValue() == id) {
                objectLabel = item.getPropertyLabel();
                break;
            }
        }
        //根据备件系统id查询出备件系统，拿到该id的备件系统下的备件list，遍历判断新增备件的型号名和设备类型是否有相同的数据存在list中
        List<DeviceSystemWithSubLayer> deviceSystemWithSubLayers = deviceSystemDao.queryBySystemId(addSparePartsDevice.getDeviceSystemId());
        if (CollectionUtils.isNotEmpty(deviceSystemWithSubLayers)) {
            List<SparePartsDevice> sparePartsDeviceList1 = deviceSystemWithSubLayers.get(0).getSparePartsDeviceList();
            if (CollectionUtils.isNotEmpty(sparePartsDeviceList1)) {
                for (SparePartsDevice sp : sparePartsDeviceList1) {
                    boolean flag = Objects.equals(sp.getName(), addSparePartsDevice.getName());
                    boolean flag1 = Objects.equals(sp.getModel(), addSparePartsDevice.getModel()) && Objects.equals(sp.getObjectLabel(), objectLabel);
                    Assert.isTrue(!flag, "设备名称重复");
                    Assert.isTrue(!flag1, "该设备类型的型号重复");
                }
            }
        }
        SparePartsDevice sparePartsDevice = new SparePartsDevice(addSparePartsDevice.getModel(), objectLabel, addSparePartsDevice.getName());
        sparePartsDevice.setCompany(addSparePartsDevice.getCompany());
        sparePartsDevice.setCriticalParams(addSparePartsDevice.getCriticalParams());
        sparePartsDevice.setSecondarySparePartsId(addSparePartsDevice.getSecondarySparePartsId());
        deviceSystemDao.insertChild(addSparePartsDevice.getDeviceSystemId(), Collections.singletonList(sparePartsDevice));
        LambdaQueryWrapper<SparePartsDevice> wrapper = LambdaQueryWrapper.of(SparePartsDevice.class).eq(SparePartsDevice::getName, addSparePartsDevice.getName());
        DeviceSystemWithSubLayer deviceSystemWithSubLayer1 = deviceSystemDao.selectRelatedById(DeviceSystemWithSubLayer.class, addSparePartsDevice.getDeviceSystemId(), Collections.singletonList(wrapper));
        List<SparePartsDevice> sparePartsDeviceList = deviceSystemWithSubLayer1.getSparePartsDeviceList();
        SparePartsDevice sparePartsDevice1 = sparePartsDeviceList.get(0);
        SparePartsDeviceVo deviceVo = new SparePartsDeviceVo();
        deviceVo.setObjectLabelId(addSparePartsDevice.getObjectLabelId());
        deviceVo.setId(sparePartsDevice1.getId());
        deviceVo.setModel(sparePartsDevice1.getModel());
        deviceVo.setName(sparePartsDevice1.getName());
        deviceVo.setObjectLabel(sparePartsDevice1.getObjectLabel());
        deviceVo.setModelLabel(sparePartsDevice1.getModelLabel());
        deviceVo.setCompany(sparePartsDevice1.getCompany());
        deviceVo.setCriticalParams(sparePartsDevice1.getCriticalParams());
        deviceVo.setSecondarySparePartsId(sparePartsDevice1.getSecondarySparePartsId());
        return deviceVo;
    }

    /**
     * 新增系统
     *
     * @param addDeviceSystem 新增系统信息
     * @return DeviceSystem
     */
    @Override
    public DeviceSystem addDeviceSystem(AddDeviceSystem addDeviceSystem) {
        checkSystemRepeat(addDeviceSystem);
        DeviceSystem deviceSystem = new DeviceSystem();
        deviceSystem.setProjectId(GlobalInfoUtils.getTenantId());
        deviceSystem.setName(addDeviceSystem.getName());
        deviceSystemDao.insert(deviceSystem);
        return deviceSystem;
    }

    /**
     * 新增设备备件
     *
     * @param addSpareParts 新增设备备件信息
     * @return Map<String, Object>
     */
    @Override
    public Map<String, Object> createSpareparts(AddSpareParts addSpareParts) {
        LambdaQueryWrapper<SpareParts> wrapper = LambdaQueryWrapper.of(SpareParts.class).eq(SpareParts::getModel, addSpareParts.getModel());
        DeviceWithSubLayer deviceWithSubLayer = deviceDao.selectRelatedById(DeviceWithSubLayer.class, addSpareParts.getSparePartsDeviceId(), Collections.singletonList(wrapper));
        Assert.isNull(deviceWithSubLayer, "备件型号重复");
        SpareParts spareParts = new SpareParts();
        spareParts.setModel(addSpareParts.getModel());
        spareParts.setUnit(addSpareParts.getUnit());
        spareParts.setName(addSpareParts.getName());
        deviceDao.insertChild(addSpareParts.getSparePartsDeviceId(), Collections.singletonList(spareParts));
        return deviceDao.selectRelatedTreeById(DeviceWithSubLayer.class, addSpareParts.getSparePartsDeviceId());
    }

    /**
     * 编辑系统
     *
     * @param editDeviceSystem 编辑系统的参数
     * @return DeviceSystem
     */
    @Override
    public DeviceSystem editDeviceSystem(EditDeviceSystem editDeviceSystem) {
        checkSysNameRepeat(editDeviceSystem);
        DeviceSystem deviceSystem = new DeviceSystem();
        deviceSystem.setProjectId(GlobalInfoUtils.getTenantId());
        deviceSystem.setName(editDeviceSystem.getName());
        deviceSystem.setId(editDeviceSystem.getId());
        deviceSystemDao.updateById(deviceSystem);
        return deviceSystem;
    }

    /**
     * 删除系统
     *
     * @param id 单个系统删除
     */
    @Override
    public void deleteDeviceSystem(Long id) {
        DeviceSystemWithSubLayer deviceSystemWithSubLayer = deviceSystemDao.selectRelatedById(DeviceSystemWithSubLayer.class, id);
        if (null == deviceSystemWithSubLayer) {
            return;
        }
        Assert.isTrue(CollectionUtils.isEmpty(deviceSystemWithSubLayer.getSparePartsDeviceList()), "系统下存在设备，无法删除");
        deviceSystemDao.deleteById(id);
    }


    @Override
    public SparePartsDeviceVo editDevice(EditDevice editDevice) {
        List<IdTextPair> deviceClassList = getDeviceClassList();
        String objectLabel = null;
        for (IdTextPair item : deviceClassList) {
            Long objectLabelId = editDevice.getObjectLabelId();
            Integer id = item.getId();
            if (objectLabelId.intValue() == id) {
                objectLabel = item.getPropertyLabel();
                break;
            }
        }
        checkDeviceRepeat(editDevice, objectLabel);
        SparePartsDevice sparePartsDevice = new SparePartsDevice();
        sparePartsDevice.setModel(editDevice.getModel());
        sparePartsDevice.setObjectLabel(objectLabel);
        sparePartsDevice.setName(editDevice.getName());
        sparePartsDevice.setCompany(editDevice.getCompany());
        sparePartsDevice.setCriticalParams(editDevice.getCriticalParams());
        sparePartsDevice.setSecondarySparePartsId(editDevice.getSecondarySparePartsId());
        sparePartsDevice.setId(editDevice.getId());
        //更新设备信息
        deviceDao.updateById(sparePartsDevice);
        //需要显示的信息，因为需要设备类型对应的id进行回显
        SparePartsDeviceVo sparePartsDeviceVo = new SparePartsDeviceVo();
        sparePartsDeviceVo.setModelLabel(sparePartsDevice.getModelLabel());
        sparePartsDeviceVo.setName(sparePartsDevice.getName());
        sparePartsDeviceVo.setModel(sparePartsDevice.getModel());
        sparePartsDeviceVo.setId(sparePartsDevice.getId());
        sparePartsDeviceVo.setObjectLabel(sparePartsDevice.getObjectLabel());
        sparePartsDeviceVo.setObjectLabelId(editDevice.getObjectLabelId());
        sparePartsDeviceVo.setCompany(sparePartsDevice.getCompany());
        sparePartsDeviceVo.setCriticalParams(sparePartsDevice.getCriticalParams());
        sparePartsDeviceVo.setSecondarySparePartsId(sparePartsDevice.getSecondarySparePartsId());
        return sparePartsDeviceVo;
    }

    /**
     * 删除设备备件
     *
     * @param id       设备备件的id
     * @param systemId 系统id
     */
    @Override
    public void deleteDevice(Long id, Long systemId) {
        DeviceWithSubLayer deviceWithSubLayer = deviceDao.selectRelatedById(DeviceWithSubLayer.class, id);
        SparePartsDevice sparePartsDevice = deviceDao.selectById(id);
        if (null == deviceWithSubLayer || null == sparePartsDevice) {
            return;
        }
        Assert.isTrue(CollectionUtils.isEmpty(deviceWithSubLayer.getSparePartsList()), "设备底下存在备件，无法删除");
        deviceSystemDao.deleteChild(systemId, Collections.singletonList(sparePartsDevice));
    }

    private List<String> getObjectLabels(QueryReplaceRecordDto queryReplaceRecordDto) {
        List<String> objectLabels = new ArrayList<>();
        if (null != queryReplaceRecordDto.getObjectLabelId()) {
            objectLabels.add(getObjectLabelByEnum(queryReplaceRecordDto.getObjectLabelId()));
        } else {
            List<IdTextPair> deviceClassList = getDeviceClassList();
            objectLabels.addAll(deviceClassList.stream().map(IdTextPair::getPropertyLabel).collect(Collectors.toList()));
        }
        return objectLabels;
    }

    private List<DeviceWithSubLayer> getSparePartsList(QueryReplaceRecordDto queryReplaceRecordDto, Set<Long> filterDeviceIds) {
        LambdaQueryWrapper<SparePartsDevice> wrapper1 = LambdaQueryWrapper.of(SparePartsDevice.class).in(SparePartsDevice::getId, filterDeviceIds);
        List<DeviceWithSubLayer> deviceWithSubLayers;
        //根据设备id以及备件名称筛选出符合的备件
        if (StringUtils.checkValNull(queryReplaceRecordDto.getKeyWord())) {
            deviceWithSubLayers = deviceDao.selectRelatedList(DeviceWithSubLayer.class, wrapper1);
        } else {
            LambdaQueryWrapper<SpareParts> wrapper = LambdaQueryWrapper.of(SpareParts.class).like(SpareParts::getName, queryReplaceRecordDto.getKeyWord());
            deviceWithSubLayers = deviceDao.selectRelatedList(DeviceWithSubLayer.class, wrapper1, Collections.singletonList(wrapper));
        }

        return deviceWithSubLayers;
    }

    /**
     * 按备件统计
     *
     * @param queryReplaceRecordDto
     * @return
     */
    @Override
    public ResultWithTotal<List<SparePartsReplaceRecordVo>> queryReplaceRecordBySparePart(QueryReplaceRecordDto queryReplaceRecordDto) {
        //根据系统查询其下的设备备件。
        List<DeviceSystemWithSubLayer> deviceSystemWithSubLayers = getdeviceSystemWithSubLayer(queryReplaceRecordDto);
        if (CollectionUtils.isEmpty(deviceSystemWithSubLayers)) {
            return ResultWithTotal.ok();
        }
        List<BaseVo> deviceSystem = deviceSystemWithSubLayers.stream().map(it -> new BaseVo(it.getId(), it.getModelLabel(), it.getName())).collect(Collectors.toList());
        List<String> objectLabels = getObjectLabels(queryReplaceRecordDto);
        Set<Long> filterDeviceIds;
        //根据选择的设备类型筛选出符合的设备id
        if (CollectionUtils.isNotEmpty(objectLabels)) {
            filterDeviceIds = deviceSystemWithSubLayers.stream().filter(s -> CollectionUtils.isNotEmpty(s.getSparePartsDeviceList())).flatMap(s -> s.getSparePartsDeviceList().stream()).filter(s -> objectLabels.contains(s.getObjectLabel())).map(SparePartsDevice::getId).collect(Collectors.toSet());
        } else {
            filterDeviceIds = deviceSystemWithSubLayers.stream().filter(s -> CollectionUtils.isNotEmpty(s.getSparePartsDeviceList())).flatMap(s -> s.getSparePartsDeviceList().stream()).map(SparePartsDevice::getId).collect(Collectors.toSet());
        }
        if (CollectionUtils.isEmpty(filterDeviceIds)) {
            return ResultWithTotal.ok();
        }
        List<DeviceWithSubLayer> deviceWithSubLayers = getSparePartsList(queryReplaceRecordDto, filterDeviceIds);
        if (CollectionUtils.isEmpty(deviceWithSubLayers)) {
            return ResultWithTotal.ok();
        }
        List<SpareParts> list = new ArrayList<>();
        for (DeviceWithSubLayer item : deviceWithSubLayers) {
            if (CollectionUtils.isEmpty(item.getSparePartsList())) {
                continue;
            }
            list.addAll(item.getSparePartsList());
        }
        //根据筛选的备件id来查询备件消耗记录,先查出所有的结果，再进行手动分页
        Set<Long> collect = deviceWithSubLayers.stream().filter(s -> CollectionUtils.isNotEmpty(s.getSparePartsList())).flatMap(s -> s.getSparePartsList().stream()).map(SpareParts::getId).collect(Collectors.toSet());
        ResultWithTotal<List<SparePartsReplaceRecord>> listResultWithTotal = sparePartsReplaceRecordDao.querySparePartsReplace(queryReplaceRecordDto.getStartTime(),
                queryReplaceRecordDto.getEndTime(), collect, new Page(0, exportMaxCount));
        if (CollectionUtils.isEmpty(listResultWithTotal.getData())) {
            return ResultWithTotal.ok();
        }
        Map<Long, String> map = handleSparePartsIdWIthObiectType(deviceWithSubLayers, listResultWithTotal.getData());
        ResultWithTotal<List<SparePartsReplaceRecordVo>> listResultWithTotalVo = new ResultWithTotal<>();
        List<SparePartsReplaceRecord> data = listResultWithTotal.getData();
        //拼接数据
        List<SparePartsReplaceRecordVo> recordVo = setRecordVo(list, data, deviceSystem, map);
        //一个备件的查出来的信息需要合并到一起，按备件进行分组
        List<SparePartsReplaceRecordVo> sparePartsReplaceRecordVoList = groupBySpareParts(recordVo);
        //手动分页
        List<SparePartsReplaceRecordVo> collect1 = sparePartsReplaceRecordVoList.stream().sorted(Comparator.comparing(SparePartsReplaceRecordVo::getNumber).reversed()).skip(queryReplaceRecordDto.getPage().getIndex()).limit(queryReplaceRecordDto.getPage().getLimit()).collect(Collectors.toList());
        listResultWithTotalVo.setData(collect1);
        listResultWithTotalVo.setTotal(sparePartsReplaceRecordVoList.size());
        return listResultWithTotalVo;

    }

    private List<SparePartsReplaceRecordVo> groupBySpareParts(List<SparePartsReplaceRecordVo> sparePartsReplaceRecordVos) {
        Map<BaseVo, List<SparePartsReplaceRecordVo>> collect = sparePartsReplaceRecordVos.stream().collect(Collectors.groupingBy(it -> new BaseVo(it.getSparePartsStorageId(), ModelLabelDef.SPARE_PARTS_STORAGE)));
        List<SparePartsReplaceRecordVo> sparePartsReplaceRecordVoList = new ArrayList<>();
        collect.forEach((key, val) -> {
            double sum = val.stream().mapToDouble(SparePartsReplaceRecordVo::getNumber).sum();
            SparePartsReplaceRecordVo sparePartsReplaceRecordVo = new SparePartsReplaceRecordVo();
            BeanUtils.copyProperties(val.get(0), sparePartsReplaceRecordVo);
            sparePartsReplaceRecordVo.setNumber(BigDecimal.valueOf(sum).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());

            sparePartsReplaceRecordVoList.add(sparePartsReplaceRecordVo);
        });
        return sparePartsReplaceRecordVoList;
    }

    /**
     * long是备件消耗记录的id，string是备件分类的类型
     *
     * @param deviceWithSubLayers
     * @param sparePartsReplaceRecords
     * @return
     */
    private Map<Long, String> handleSparePartsIdWIthObiectType(List<DeviceWithSubLayer> deviceWithSubLayers, List<SparePartsReplaceRecord> sparePartsReplaceRecords) {
        if (CollectionUtils.isEmpty(deviceWithSubLayers) || CollectionUtils.isEmpty(sparePartsReplaceRecords)) {
            return null;
        }
        Map<Long, String> map = new HashMap<>();
        for (SparePartsReplaceRecord sparePartsReplaceRecord : sparePartsReplaceRecords) {
            for (DeviceWithSubLayer deviceWithSubLayer : deviceWithSubLayers) {
                if (CollectionUtils.isEmpty(deviceWithSubLayer.getSparePartsList())) {
                    continue;
                }
                List<SpareParts> collect = deviceWithSubLayer.getSparePartsList().stream().filter(spareParts -> spareParts.getId().equals(sparePartsReplaceRecord.getSparePartsStorageId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    map.put(sparePartsReplaceRecord.getId(), deviceWithSubLayer.getObjectLabel());
                    break;
                }
            }
        }
        return map;
    }

    /**
     * 根据传递的设备类型的id获得设备类型
     *
     * @param objectId 设备类型对应的id(枚举中对应的id）
     * @return String
     */
    private String getObjectLabelByEnum(Long objectId) {
        if (objectId == null) {
            return null;
        }
        List<IdTextPair> deviceClassList = getDeviceClassList();

        for (IdTextPair item : deviceClassList) {
            Integer id = item.getId();
            if (objectId.intValue() == id) {
                return item.getPropertyLabel();
            }
        }
        return null;
    }

    private List<SparePartsReplaceRecordVo> setRecordVo(List<SpareParts> sparePartsList, List<SparePartsReplaceRecord> sparePartsReplaceRecords, List<BaseVo> deviceSystem, Map<Long, String> map) {
        List<SparePartsReplaceRecordVo> result = new ArrayList<>();
        //视图显示的内容与该对象的内容有出入需要转换
        for (SparePartsReplaceRecord sparePartsReplaceRecord : sparePartsReplaceRecords) {
            for (SpareParts spareParts : sparePartsList) {
                if (sparePartsReplaceRecord.getSparePartsStorageId().equals(spareParts.getId())) {
                    SparePartsReplaceRecordVo vo = new SparePartsReplaceRecordVo();
                    vo.setModel(spareParts.getModel());
                    vo.setSparePartsName(spareParts.getName());
                    vo.setBrand(spareParts.getBrand());
                    vo.setUnit(spareParts.getUnit());
                    vo.setDeviceSystemId(sparePartsReplaceRecord.getDeviceSystemId());
                    for (BaseVo baseVo : deviceSystem) {
                        if (baseVo.getId().equals(sparePartsReplaceRecord.getDeviceSystemId())) {
                            vo.setDeviceSystemName(baseVo.getName());
                            break;
                        }
                    }
                    vo.setObjectLabelText(getObjectLabelText(map.get(sparePartsReplaceRecord.getId())));
                    Integer objectLabelId = getObjectLabelId(map.get(sparePartsReplaceRecord.getId()));
                    if (null != objectLabelId) {
                        vo.setObjectLabelId(objectLabelId.longValue());
                    }
                    vo.setObjectLabel(map.get(sparePartsReplaceRecord.getId()));
                    vo.setNumber(sparePartsReplaceRecord.getNumber());
                    vo.setSparePartsStorageId(sparePartsReplaceRecord.getSparePartsStorageId());
                    result.add(vo);
                }
            }
        }
        return result;
    }

    private String getObjectLabelText(String objectLabel) {
        List<IdTextPair> deviceClassList = getDeviceClassList();
        for (IdTextPair item : deviceClassList) {
            //根据设备类型返回对应的描述
            if (item.getPropertyLabel().equals(objectLabel)) {
                return item.getText();
            }
        }
        return null;

    }


    @Override
    public void importDeviceComponentToSpareParts(List<ModelList> modelLists) {
        if (CollectionUtils.isEmpty(modelLists)) {
            return;
        }

        // 入参备份
        List<DeviceImportList> deviceImportLists = new ArrayList<>();
        for (ModelList item : modelLists) {
            DeviceImportList deviceImportList = new DeviceImportList();
            BeanUtils.copyProperties(item, deviceImportList);
            deviceImportLists.add(deviceImportList);
        }

        //筛选掉房间的信息
        deviceImportLists = deviceImportLists.stream().filter(deviceImportList -> !deviceImportList.getModelLabel().equals(NodeLabelDef.ROOM)).collect(Collectors.toList());
        List<BaseVo> baseNode = deviceImportLists.stream().map(deviceImportList -> new BaseVo(deviceImportList.getId(), deviceImportList.getModelLabel())).collect(Collectors.toList());

        // 根据管网设备查询其零件信息
        List<DeviceComponent> deviceComponents = deviceComponentDao.queryComponentByDevice(baseNode);
        if (CollectionUtils.isEmpty(deviceComponents)) {
            return;
        }

        // 查询备件模型信息
        Set<DeviceImportList> modelNodes = deviceImportLists.stream().map(it -> new DeviceImportList(it.getModelLabel(), it.getModel())).collect(Collectors.toSet());
        List<SparePartsDevice> sparePartsDevices = deviceDao.queryByModelInfo(modelNodes);
        // 根据零件信息中的设备model和label查找有无符合的设备信息，如果没有，没有可以导入的数据。
        if (CollectionUtils.isEmpty(sparePartsDevices)) {
            return;
        }

        // 筛选可能重复的备件信息，根据型号进行筛选
        Set<String> filterModel = deviceComponents.stream().map(DeviceComponent::getModel).collect(Collectors.toSet());
        List<Long> sparePartsDeviceIds = sparePartsDevices.stream().map(AbstractModelEntity::getId).collect(Collectors.toList());
        List<DeviceWithSubLayer> deviceWithSubLayers = deviceDao.queryByModelInfo(sparePartsDeviceIds, filterModel);

        List<ComponentWithModel> componentWithModels = addDeviceModel(deviceComponents, deviceImportLists);
        List<ComponentWithModel> deviceWithSpareParts = createDeviceWithSpareParts(componentWithModels, sparePartsDevices);
        List<ComponentWithModel> result = getResult(deviceWithSpareParts, deviceWithSubLayers);
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        List<ComponentWithModel> handleResult = handleResult(result);
        writeImport(handleResult);


    }

    private List<ComponentWithModel> handleResult(List<ComponentWithModel> result) {
        if (CollectionUtils.isEmpty(result) || result.size() == 1) {
            return result;
        }
        List<ComponentWithModel> handleResult = new ArrayList<>();
        Map<BaseVo, List<ComponentWithModel>> collect = result.stream().collect(Collectors.groupingBy(componentWithModel -> new BaseVo(componentWithModel.getObjectId(), componentWithModel.getObjectLabel())));
        collect.forEach((key, val) -> {
            Map<String, List<ComponentWithModel>> collect1 = val.stream().collect(Collectors.groupingBy(ComponentWithModel::getModel));
            collect1.forEach((model, value) -> {
                handleResult.add(value.get(0));
            });
        });

        return handleResult;
    }

    private void writeImport(List<ComponentWithModel> result) {
        for (DeviceComponent d : result) {
            SpareParts s = new SpareParts();
            s.setModel(d.getModel());
            s.setUnit(d.getUnit());
            s.setName(d.getName());
            s.setBrand(d.getBrand());
            deviceDao.insertChild(d.getObjectId(), Collections.singletonList(s));
        }
    }

    private List<ComponentWithModel> getResult(List<ComponentWithModel> deviceWithSpareParts, List<DeviceWithSubLayer> deviceWithSubLayers) {
        if (CollectionUtils.isEmpty(deviceWithSubLayers)) {
            return deviceWithSpareParts;
        }

        List<ComponentWithModel> result = new ArrayList<>();
        //为了减少循环的层数，将设备底下的备件信息与对应的设备信息整合到一起。
        List<DeviceWithSpareParts> deviceWithSpareParts1 = createDeviceWithSpareParts(deviceWithSubLayers);
        Map<Long, List<ComponentWithModel>> sparePartsDeviceMap = deviceWithSpareParts.stream().collect(Collectors.groupingBy(ComponentWithModel::getSparePartsDeviceId));
        sparePartsDeviceMap.forEach((sparePartId, val) -> {
            for (ComponentWithModel component : val) {
                // 判断备件所属设备label以及型号是否一致，以及备件本身型号是否一致
                // 如果一致说明已经存在，则不需要重复创建
                boolean flag = deviceWithSpareParts1.stream()
                        .anyMatch(oldSparePart -> Objects.equals(sparePartId, oldSparePart.getId()) &&
                                component.getDeviceModel().equals(oldSparePart.getModel()) &&
                                component.getObjectLabel().equals(oldSparePart.getObjectLabel()) &&
                                (component.getModel().equals(oldSparePart.getSparePartsModel())));
                if (!flag) {
                    result.add(component);
                }
            }
        });

        return result;
    }

    private List<ComponentWithModel> addDeviceModel(List<DeviceComponent> deviceComponents, List<DeviceImportList> collect) {
        //为零件信息添加对应设备的model信息
        List<ComponentWithModel> componentWithModels = new ArrayList<>();
        for (DeviceComponent item : deviceComponents) {
            for (DeviceImportList itemModel : collect) {
                if (item.getObjectLabel().equals(itemModel.getModelLabel()) && item.getObjectId().equals(itemModel.getId()) && null != itemModel.getModel()) {
                    ComponentWithModel c = new ComponentWithModel();
                    BeanUtils.copyProperties(item, c);
                    c.setDeviceModel(itemModel.getModel());
                    componentWithModels.add(c);
                    break;
                }
            }
        }
        return componentWithModels;
    }


    private List<ComponentWithModel> createDeviceWithSpareParts(List<ComponentWithModel> componentWithModels, List<SparePartsDevice> sparePartsDevices) {
        List<ComponentWithModel> result = new ArrayList<>();
        for (ComponentWithModel item : componentWithModels) {
            for (SparePartsDevice itemNow : sparePartsDevices) {
                if (itemNow.getModel().equals(item.getDeviceModel()) && item.getObjectLabel().equals(itemNow.getObjectLabel())) {
                    ComponentWithModel componentWithModel = new ComponentWithModel();
                    componentWithModel.setModel(item.getModel());
                    componentWithModel.setDeviceModel(item.getDeviceModel());
                    componentWithModel.setName(item.getName());
                    //零件信息中的objectid是具体设备的id，备件库是根据设备型号进行划分的，此时的objectid需要对应查询到的设备备件信息的id。
                    componentWithModel.setObjectId(itemNow.getId());
                    componentWithModel.setSparePartsDeviceId(itemNow.getId());
                    componentWithModel.setObjectLabel(itemNow.getObjectLabel());
                    componentWithModel.setUnit(item.getUnit());
                    componentWithModel.setBrand(item.getBrand());
                    result.add(componentWithModel);
                }
            }
        }
        return result;
    }


    private List<DeviceWithSpareParts> createDeviceWithSpareParts(List<DeviceWithSubLayer> deviceWithSubLayer) {
        List<DeviceWithSpareParts> list = new ArrayList<>();
        for (DeviceWithSubLayer item : deviceWithSubLayer) {
            if (CollectionUtils.isEmpty(item.getSparePartsList())) {
                continue;
            }

            for (SpareParts sparePart : item.getSparePartsList()) {
                DeviceWithSpareParts deviceWithSpareParts = new DeviceWithSpareParts();
                deviceWithSpareParts.setModel(item.getModel());
                deviceWithSpareParts.setObjectLabel(item.getObjectLabel());
                deviceWithSpareParts.setSparePartsName(sparePart.getName());
                deviceWithSpareParts.setSparePartsModel(sparePart.getModel());
                deviceWithSpareParts.setId(item.getId());
                list.add(deviceWithSpareParts);
            }
        }
        return list;
    }

    @Override
    public void deleteSystemList(List<Long> ids) {

        LambdaQueryWrapper<DeviceSystem> wrapper = LambdaQueryWrapper.of(DeviceSystem.class)
                .in(DeviceSystem::getId, ids);
        List<DeviceSystemWithSubLayer> deviceSystemWithSubLayers = deviceSystemDao.selectRelatedList(DeviceSystemWithSubLayer.class, wrapper);
        if (CollectionUtils.isEmpty(deviceSystemWithSubLayers)) {
            return;
        }
        for (DeviceSystemWithSubLayer deviceSystemWithSubLayer : deviceSystemWithSubLayers) {
            Assert.isTrue(CollectionUtils.isEmpty(deviceSystemWithSubLayer.getSparePartsDeviceList()), "系统下存在设备，无法删除");
        }
        List<Long> collect = deviceSystemWithSubLayers.stream().map(DeviceSystemWithSubLayer::getId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            deviceSystemDao.deleteBatchIds(collect);
        }

    }

    @Override
    public void deleteDeviceList(List<Long> ids, Long systemId) {
        LambdaQueryWrapper<SparePartsDevice> wrapper = LambdaQueryWrapper.of(SparePartsDevice.class)
                .in(SparePartsDevice::getId, ids);
        List<DeviceWithSubLayer> deviceWithSubLayers = deviceDao.selectRelatedList(DeviceWithSubLayer.class, wrapper);
        if (CollectionUtils.isEmpty(deviceWithSubLayers)) {
            return;
        }
        for (DeviceWithSubLayer deviceWithSubLayer : deviceWithSubLayers) {
            Assert.isTrue(CollectionUtils.isEmpty(deviceWithSubLayer.getSparePartsList()), "设备底下存在备件，无法删除");
        }
        Set<Long> collect = deviceWithSubLayers.stream().map(DeviceWithSubLayer::getId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(collect)) {
            deviceSystemDao.deleteChild(systemId, deviceWithSubLayers);
        }
    }

    @Override
    public String deleteSparePartsList(List<Long> ids, Long deviceId) {
        List<SpareParts> list = sparePartsDao.selectBatchIds(ids);
        LambdaQueryWrapper<SparePartsReplaceRecord> queryWrapper = LambdaQueryWrapper.of(SparePartsReplaceRecord.class)
                .in(SparePartsReplaceRecord::getSparePartsStorageId, ids);
        List<SparePartsReplaceRecord> sparePartsReplaceRecords = sparePartsReplaceRecordDao.selectList(queryWrapper);
        Set<Long> collect = sparePartsReplaceRecords.stream().map(SparePartsReplaceRecord::getSparePartsStorageId).collect(Collectors.toSet());
        String join = null;
        //全部都不能删除
        Assert.isTrue(collect.size() != ids.size(), "名称为" + String.join("、", list.stream().map(SpareParts::getName).collect(Collectors.toList())) + "的备件已经被使用，无法删除");

        if (CollectionUtils.isNotEmpty(collect)) {
            List<SpareParts> spareParts = sparePartsDao.selectBatchIds(collect);
            list.removeAll(spareParts);
            join = String.join("、", spareParts.stream().map(SpareParts::getName).collect(Collectors.toList()));
        }
        deviceDao.deleteChild(deviceId, list);
        return join;
    }

    private List<DeviceSystemWithSubLayer> getdeviceSystemWithSubLayer(QueryReplaceRecordDto queryReplaceRecordDto) {
        LambdaQueryWrapper<DeviceSystem> querywrapper = LambdaQueryWrapper.of(DeviceSystem.class);
        if (null != queryReplaceRecordDto.getSystemId()) {
            querywrapper.eq(DeviceSystem::getId, queryReplaceRecordDto.getSystemId());
        }

        List<DeviceSystemWithSubLayer> deviceSystemWithSubLayers = deviceSystemDao.selectRelatedList(DeviceSystemWithSubLayer.class, querywrapper);
        return deviceSystemWithSubLayers;
    }

    @Override
    public ResultWithTotal<List<SparePartsCountVo>> queryReplaceByDevice(QueryReplaceRecordDto queryReplaceRecordDto) {
        //获得设备类型，null就是查全部的
        String objectLabelByEnum = getObjectLabelByEnum(queryReplaceRecordDto.getObjectLabelId());
        //查询备件更换记录,查出来的结果，后续需要按设备进行分组处理，所以先是查出全部结果，后续手动进行分组。
        ResultWithTotal<List<SparePartsReplaceRecord>> listResultWithTotal = sparePartsReplaceRecordDao.querySparePartsReplaceByDevice(
                queryReplaceRecordDto.getStartTime(), queryReplaceRecordDto.getEndTime(), objectLabelByEnum, new Page(0, exportMaxCount));
        if (CollectionUtils.isEmpty(listResultWithTotal.getData())) {
            return ResultWithTotal.ok();
        }
        //查询备件信息
        Set<Long> collect = listResultWithTotal.getData().stream().map(SparePartsReplaceRecord::getSparePartsStorageId).collect(Collectors.toSet());
        List<SpareParts> sparePartsList = sparePartsDao.selectBatchIds(new ArrayList<>(collect));
        List<BaseVo> baseVos = new ArrayList<>();
        for (SparePartsReplaceRecord item : listResultWithTotal.getData()) {
            BaseVo b = new BaseVo();
            b.setModelLabel(item.getObjectLabel());
            b.setId(item.getObjectId());
            baseVos.add(b);
        }
        //获得具体的设备名称
        List<DeviceWithName> deviceWithNames = nodeDao.queryNodes(baseVos, DeviceWithName.class);
        List<DeviceWithName> deviceWithKeyword;
        if (null != queryReplaceRecordDto.getKeyWord()) {
            deviceWithKeyword = deviceWithNames.stream().filter(deviceWithName -> deviceWithName.getName().contains(queryReplaceRecordDto.getKeyWord())).collect(Collectors.toList());
        } else {
            deviceWithKeyword = deviceWithNames;
        }
        //拼接设备名称，备件信息等
        List<SparePartsReplaceRecordVo> recordVo = getRecordVo(listResultWithTotal.getData(), deviceWithKeyword, sparePartsList);
        //结果按设备进行分组
        List<SparePartsCountVo> sparePartsCountVos = groupByDevice(recordVo);
        //手动分页
        List<SparePartsCountVo> collect1 = sparePartsCountVos.stream().sorted(Comparator.comparing(SparePartsCountVo::getNumber).reversed()).skip(queryReplaceRecordDto.getPage().getIndex()).limit(queryReplaceRecordDto.getPage().getLimit()).collect(Collectors.toList());
        ResultWithTotal<List<SparePartsCountVo>> listResultWithTotalVo = new ResultWithTotal<>();
        listResultWithTotalVo.setData(collect1);
        listResultWithTotalVo.setTotal(sparePartsCountVos.size());
        return listResultWithTotalVo;

    }

    private List<SparePartsReplaceRecordVo> getRecordVo(List<SparePartsReplaceRecord> sparePartsReplaceRecords, List<DeviceWithName> deviceWithNames, List<SpareParts> sparePartsList) {
        List<SparePartsReplaceRecordVo> replaceRecordVos = new ArrayList<>();
        for (SparePartsReplaceRecord item : sparePartsReplaceRecords) {  //转换成视图需要的数据
            for (DeviceWithName itemNow : deviceWithNames) {
                if (itemNow.getId().equals(item.getObjectId()) && itemNow.getModelLabel().equals(item.getObjectLabel())) {
                    SparePartsReplaceRecordVo vo = new SparePartsReplaceRecordVo();
                    BeanUtils.copyProperties(item, vo);
                    vo.setObjectName(itemNow.getName());
                    vo.setObjectLabelText(getObjectLabelText(item.getObjectLabel()));
                    Integer objectLabelId = getObjectLabelId(item.getObjectLabel());
                    if (null != objectLabelId) {
                        vo.setObjectLabelId(objectLabelId.longValue());
                    }
                    replaceRecordVos.add(vo);
                    break;
                }
            }
        }
        for (SparePartsReplaceRecordVo item : replaceRecordVos) {
            for (SpareParts itemNow : sparePartsList) {
                if (item.getSparePartsStorageId().equals(itemNow.getId())) {
                    BeanUtils.copyProperties(itemNow, item);
                    item.setSparePartsName(itemNow.getName());
                }
            }
        }
        return replaceRecordVos;
    }

    private List<SparePartsReplaceRecordVo> SparePartsReplaceRecord(QueryReplaceRecordDto queryReplaceRecordDto) {
        //根据系统查询其下的设备备件。
        LambdaQueryWrapper<DeviceSystem> querywrapper = LambdaQueryWrapper.of(DeviceSystem.class);
        if (null != queryReplaceRecordDto.getSystemId()) {
            querywrapper.eq(DeviceSystem::getId, queryReplaceRecordDto.getSystemId());
        }

        List<DeviceSystemWithSubLayer> deviceSystemWithSubLayers = deviceSystemDao.selectRelatedList(DeviceSystemWithSubLayer.class, querywrapper);
        if (CollectionUtils.isEmpty(deviceSystemWithSubLayers)) {
            return Collections.emptyList();
        }
        List<SparePartsDevice> sparePartsDeviceList = new ArrayList<>();
        for (DeviceSystemWithSubLayer deviceSystemWithSubLayer : deviceSystemWithSubLayers) {
            if (CollectionUtils.isNotEmpty(deviceSystemWithSubLayer.getSparePartsDeviceList())) {
                sparePartsDeviceList.addAll(deviceSystemWithSubLayer.getSparePartsDeviceList());
            }
        }
        Set<Long> deviceSystemIds = deviceSystemWithSubLayers.stream().map(DeviceSystemWithSubLayer::getId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(sparePartsDeviceList)) {
            return Collections.emptyList();
        }
        Set<Long> filterDeviceIds = sparePartsDeviceList.stream().map(SparePartsDevice::getId).collect(Collectors.toSet());
        LambdaQueryWrapper<SparePartsDevice> wrapper = LambdaQueryWrapper.of(SparePartsDevice.class).in(SparePartsDevice::getId, filterDeviceIds);
        //筛选符合的备件id
        List<DeviceWithSubLayer> deviceWithSubLayers = deviceDao.selectRelatedList(DeviceWithSubLayer.class, wrapper);
        if (CollectionUtils.isEmpty(deviceWithSubLayers)) {
            return Collections.emptyList();
        }
        List<SpareParts> sparePartsList = new ArrayList<>();
        for (DeviceWithSubLayer item : deviceWithSubLayers) {
            if (CollectionUtils.isEmpty(item.getSparePartsList())) {
                continue;
            }
            sparePartsList.addAll(item.getSparePartsList());
        }
        Set<Long> collect = deviceWithSubLayers.stream().filter(s -> CollectionUtils.isNotEmpty(s.getSparePartsList())).flatMap(s -> s.getSparePartsList().stream()).map(SpareParts::getId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(collect)) {
            return Collections.emptyList();
        }
        ResultWithTotal<List<SparePartsReplaceRecord>> listResultWithTotal = sparePartsReplaceRecordDao.
                queryByTimeAndId(queryReplaceRecordDto.getStartTime(), queryReplaceRecordDto.getEndTime(),
                        collect, queryReplaceRecordDto.getPage(), new ArrayList<>(deviceSystemIds), null);
        if (CollectionUtils.isEmpty(listResultWithTotal.getData())) {
            return Collections.emptyList();
        }

        ResultWithTotal<List<SparePartsCountVo>> listResultWithTotalVo = new ResultWithTotal<>();
        listResultWithTotalVo.setTotal(listResultWithTotal.getTotal());

        List<DeviceSystem> deviceSystems = deviceSystemDao.selectBatchIds(deviceSystemIds);
        List<BaseVo> baseVos = new ArrayList<>();
        for (SparePartsReplaceRecord item : listResultWithTotal.getData()) {
            BaseVo b = new BaseVo();
            b.setModelLabel(item.getObjectLabel());
            b.setId(item.getObjectId());
            baseVos.add(b);
        }
        //获得具体的设备名称
        List<DeviceWithName> deviceWithNames = nodeDao.queryNodes(baseVos, DeviceWithName.class);
        List<DeviceWithName> deviceWithKeyword;
        if (null != queryReplaceRecordDto.getKeyWord()) {
            deviceWithKeyword = deviceWithNames.stream().filter(deviceWithName -> deviceWithName.getName().contains(queryReplaceRecordDto.getKeyWord())).collect(Collectors.toList());
        } else {
            deviceWithKeyword = deviceWithNames;
        }
        List<SparePartsReplaceRecordVo> recordVo = getRecordVo(listResultWithTotal.getData(), deviceWithKeyword, deviceSystems, sparePartsList);
        return recordVo;
    }


    private List<SparePartsCountVo> groupByDevice(List<SparePartsReplaceRecordVo> sparePartsReplaceRecordVos) {
        Map<BaseVo, List<SparePartsReplaceRecordVo>> collect = sparePartsReplaceRecordVos.stream().collect(Collectors.groupingBy(it -> new BaseVo(it.getObjectId(), it.getObjectLabel())));
        List<SparePartsCountVo> sparePartsCountVos = new ArrayList<>();
        collect.forEach((key, val) -> {
            double sum = val.stream().mapToDouble(SparePartsReplaceRecordVo::getNumber).sum();
            List<SparePartsReplaceRecordVo> sparePartsReplaceRecordVoList = groupBySpareParts(val);
            SparePartsCountVo sparePartsCountVo = new SparePartsCountVo();
            BeanUtils.copyProperties(val.get(0), sparePartsCountVo);
            sparePartsCountVo.setSparePartsReplaceRecordVos(sparePartsReplaceRecordVoList);
            sparePartsCountVo.setNumber(BigDecimal.valueOf(sum).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
            sparePartsCountVos.add(sparePartsCountVo);
        });
        return sparePartsCountVos;

    }

    private List<SparePartsReplaceRecordVo> getRecordVo(List<SparePartsReplaceRecord> sparePartsReplaceRecords, List<DeviceWithName> deviceWithNames, List<DeviceSystem> deviceSystems, List<SpareParts> sparePartsList) {
        List<SparePartsReplaceRecordVo> replaceRecordVos = new ArrayList<>();
        for (SparePartsReplaceRecord item : sparePartsReplaceRecords) {  //转换成视图需要的数据
            for (DeviceWithName itemNow : deviceWithNames) {
                if (itemNow.getId().equals(item.getObjectId()) && itemNow.getModelLabel().equals(item.getObjectLabel())) {
                    SparePartsReplaceRecordVo vo = new SparePartsReplaceRecordVo();
                    BeanUtils.copyProperties(item, vo);
                    vo.setObjectName(itemNow.getName());
                    for (DeviceSystem deviceSystem : deviceSystems) {
                        if (deviceSystem.getId().equals(item.getDeviceSystemId())) {
                            vo.setDeviceSystemName(deviceSystem.getName());
                            break;
                        }
                    }
                    vo.setObjectLabelText(getObjectLabelText(item.getObjectLabel()));
                    Integer objectLabelId = getObjectLabelId(item.getObjectLabel());
                    if (null != objectLabelId) {
                        vo.setObjectLabelId(objectLabelId.longValue());
                    }
                    replaceRecordVos.add(vo);
                    break;
                }
            }
        }
        for (SparePartsReplaceRecordVo item : replaceRecordVos) {
            for (SpareParts itemNow : sparePartsList) {
                if (item.getSparePartsStorageId().equals(itemNow.getId())) {
                    BeanUtils.copyProperties(itemNow, item);
                    item.setSparePartsName(itemNow.getName());
                }
            }
        }
        return replaceRecordVos;
    }

    private List<SparePartsReplaceRecordVo> addSparePartsName(List<SparePartsReplaceRecord> sparePartsReplaceRecords, List<SpareParts> spareParts) {
        if (CollectionUtils.isEmpty(sparePartsReplaceRecords) || CollectionUtils.isEmpty(spareParts)) {
            return Collections.emptyList();
        }
        List<SparePartsReplaceRecordVo> result = new ArrayList<>();
        for (SparePartsReplaceRecord item : sparePartsReplaceRecords) {
            for (SpareParts itemNow : spareParts) {
                if (item.getSparePartsStorageId().equals(itemNow.getId())) {
                    SparePartsReplaceRecordVo sparePartsReplaceRecordVo = new SparePartsReplaceRecordVo();
                    sparePartsReplaceRecordVo.setSparePartsName(itemNow.getName());
                    sparePartsReplaceRecordVo.setModel(itemNow.getModel());
                    sparePartsReplaceRecordVo.setBrand(itemNow.getBrand());
                    sparePartsReplaceRecordVo.setUnit(itemNow.getUnit());
                    sparePartsReplaceRecordVo.setObjectLabel(item.getObjectLabel());
                    sparePartsReplaceRecordVo.setObjectId(item.getObjectId());
                    sparePartsReplaceRecordVo.setNumber(item.getNumber());
                    sparePartsReplaceRecordVo.setLogtime(item.getLogtime());
                    sparePartsReplaceRecordVo.setWorkOrderId(item.getWorkOrderId());
                    sparePartsReplaceRecordVo.setId(item.getId());
                    result.add(sparePartsReplaceRecordVo);
                }
            }
        }
        return result;
    }

    @Override
    public List<SparePartsReplaceRecordVo> querySparePartsByDevice(BaseVo baseVo) {
        //根据设备查备件更换记录
        List<SparePartsReplaceRecord> sparePartsReplaceRecords = sparePartsReplaceRecordDao.queryByDevice(baseVo.getModelLabel(), baseVo.getId());
        if (CollectionUtils.isEmpty(sparePartsReplaceRecords)) {
            return Collections.emptyList();
        }
        //根据备件id查备件名称
        Set<Long> collect = sparePartsReplaceRecords.stream().map(SparePartsReplaceRecord::getSparePartsStorageId).collect(Collectors.toSet());
        List<SpareParts> spareParts = sparePartsDao.selectBatchIds(collect);
        //根据工单id查工单编号
        Set<Long> workOrderId = sparePartsReplaceRecords.stream().map(SparePartsReplaceRecord::getWorkOrderId).collect(Collectors.toSet());
        List<WorkOrderPo> workOrderPos = workOrderDao.selectBatchIds(workOrderId);
        if (CollectionUtils.isEmpty(spareParts)) {
            return Collections.emptyList();
        }
        //根据备件id匹配备件名称信息。
        List<SparePartsReplaceRecordVo> replaceRecordVos = addSparePartsName(sparePartsReplaceRecords, spareParts);
        //根据工单id匹配工单编号信息
        List<SparePartsReplaceRecordVo> replaceRecordVos1 = addWorkOrderCode(replaceRecordVos, workOrderPos);
        return replaceRecordVos1;
    }

    @Override
    public List<SparePartsReplaceRecordVo> querySparePartsReplaceRecord(@NotNull Collection<Long> wordOrderIds) {
        List<SparePartsReplaceRecord> records = sparePartsReplaceRecordDao.queryByWorkOderId(wordOrderIds);

        return JsonTransferUtils.transferList(records, SparePartsReplaceRecordVo.class);
    }

    @Override
    public void exportSparePartsReplaceRecord(HttpServletResponse response, QueryReplaceRecordDto queryReplaceRecordDto) {
        queryReplaceRecordDto.setPage(new Page(0, exportMaxCount));
        ResultWithTotal<List<SparePartsReplaceRecordVo>> listResultWithTotal = queryReplaceRecordBySparePart(queryReplaceRecordDto);

        String fileName = "按备件统计导出" + LocalDateTime.now().format(TimeUtil.SECONDTIMEFORMAT);

        try (Workbook workBook = PoiExcelUtils.createWorkBook(ExcelType.BIG_DATA)) {
            List<Integer> colWidth = Arrays.asList(18, 18, 18, 18, 18, 18);
            PoiExcelUtils.createSheet(workBook, "按备件统计导出", (sheet, baseCellStyle, rowIndex) -> {
                int rowNum = 0;
                writeHeaderByType(sheet, baseCellStyle, rowNum++);
                if (CollectionUtils.isNotEmpty(listResultWithTotal.getData())) {
                    writeRecord(sheet, baseCellStyle, rowNum, listResultWithTotal.getData());
                }

            }, colWidth);

            FileUtils.downloadExcel(response, workBook, fileName, ContentTypeDef.APPLICATION_MSEXCEL);
        } catch (Exception e) {
            ErrorUtils.exportError("备件统计", e);
        }
    }

    private void writeRecord(Sheet sheet, CellStyle baseCellStyle, int rowNum, List<SparePartsReplaceRecordVo> sparePartsReplaceRecordVos) {
        int col;

        for (SparePartsReplaceRecordVo log : sparePartsReplaceRecordVos) {
            col = 0;
            Row row = PoiExcelUtils.createRow(sheet, rowNum);
            PoiExcelUtils.createCell(row, col++, baseCellStyle, log.getSparePartsName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, log.getModel());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, log.getDeviceSystemName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, log.getObjectLabelText());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, log.getNumber());
            PoiExcelUtils.createCell(row, col, baseCellStyle, log.getUnit());
            rowNum++;
        }
    }

    private void writeHeaderByType(Sheet sheet, CellStyle baseCellStyle, int startRow) {
        LinkedHashMap<String, CellStyle> headerMap = new LinkedHashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        headerMap.put("备件名称", baseCellStyle);
        headerMap.put("型号规格", baseCellStyle);
        headerMap.put("所属系统", baseCellStyle);
        headerMap.put("所属设备类型", baseCellStyle);
        headerMap.put("消耗量", baseCellStyle);
        headerMap.put("单位", baseCellStyle);
        PoiExcelUtils.createHeaderName(sheet, startRow, headerMap);
    }

    @Override
    public void exportSparePartsReplaceRecordByDevice(HttpServletResponse response, QueryReplaceRecordDto queryReplaceRecordDto) {
        queryReplaceRecordDto.setPage(new Page(0, exportMaxCount));
        ResultWithTotal<List<SparePartsCountVo>> listResultWithTotal = queryReplaceByDevice(queryReplaceRecordDto);

        String fileName = "按设备统计导出" + LocalDateTime.now().format(TimeUtil.SECONDTIMEFORMAT);
        try (Workbook workBook = PoiExcelUtils.createWorkBook(ExcelType.BIG_DATA)) {
            List<Integer> colWidth = Arrays.asList(18, 18, 18);
            PoiExcelUtils.createSheet(workBook, "按设备统计导出", (sheet, baseCellStyle, rowIndex) -> {
                int rowNum = 0;
                writeHeader(sheet, baseCellStyle, rowNum++);
                writeRecordByDevice(sheet, baseCellStyle, rowNum, listResultWithTotal.getData());
            }, colWidth);

            FileUtils.downloadExcel(response, workBook, fileName, ContentTypeDef.APPLICATION_MSEXCEL);
        } catch (Exception e) {
            ErrorUtils.exportError("设备统计", e);
        }
    }

    private Integer getRoomType(Long id) {
        QueryCondition condition = ParentQueryConditionBuilder.of(NodeLabelDef.ROOM, id).build();
        List<Map<String, Object>> query = modelServiceUtils.query(condition);
        if (CollectionUtils.isEmpty(query)) {
            return null;
        }
        List<RoomVo> roomVos = JsonTransferUtils.transferList(query, RoomVo.class);
        for (RoomVo roomVo : roomVos) {
            if (null != roomVo.getRoomtype()) {
                return roomVo.getRoomtype();
            }
        }
        return null;
    }

    private List<DeviceWithName> getDeviceList(BaseVo baseVo) {
        List<BaseVo> child = new ArrayList<>();
        Integer roomType = getRoomType(baseVo.getId());
        List<String> subLabels = treeLevelService.getSonLabel(NodeLabelDef.ROOM, roomType);
        List<SingleModelConditionDTO> subChildren = new ArrayList<>();
        for (String subLabel : subLabels) {
            subChildren.add(new SubConditionBuilder(subLabel).queryDepth(1).build());
        }
        QueryCondition condition = ParentQueryConditionBuilder.of(baseVo.getModelLabel(), baseVo.getId())
                .selectChildren(subChildren).queryAsTree().build();
        List<Map<String, Object>> baseVos = modelServiceUtils.query(condition);
        if (CollectionUtils.isEmpty(baseVos)) {
            return Collections.emptyList();
        }
        List<BaseVo> baseVos1 = JsonTransferUtils.transferList(baseVos, BaseVo.class);
        for (BaseVo item : baseVos1) {
            if (CollectionUtils.isNotEmpty(item.getChildren())) {
                child.addAll(item.getChildren());
            }
        }
        if (CollectionUtils.isEmpty(child)) {
            return Collections.emptyList();
        }
        List<DeviceWithName> deviceWithNames = JsonTransferUtils.transferList(child, DeviceWithName.class);
        return deviceWithNames;
    }

    @Override
    public List<SpareParts> querySparePartsStorageByDevice(BaseVo baseVo) {
        //维保对象为房间
        if (baseVo.getModelLabel().equals(NodeLabelDef.ROOM)) {
            return Collections.emptyList();
        }
        List<BaseVo> baseVos = new ArrayList<>();
        baseVos.add(baseVo);
        //获得具体的设备名称
        List<DeviceWithName> deviceWithNames = nodeDao.queryNodes(baseVos, DeviceWithName.class);
        if (CollectionUtils.isEmpty(deviceWithNames)) {
            return Collections.emptyList();
        }
        List<SpareParts> sparePartsList = deviceDao.querySparepartsStorageByDevice(deviceWithNames.get(0).getModel(), deviceWithNames.get(0).getModelLabel());
        return sparePartsList;
    }

    private void writeRecordByDevice(Sheet sheet, CellStyle baseCellStyle, int rowNum, List<SparePartsCountVo> sparePartsCountVos) {
        if (CollectionUtils.isEmpty(sparePartsCountVos)) {
            return;
        }

        int col;

        for (SparePartsReplaceRecordVo log : sparePartsCountVos) {
            col = 0;
            Row row = PoiExcelUtils.createRow(sheet, rowNum);
            PoiExcelUtils.createCell(row, col++, baseCellStyle, log.getObjectName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, log.getObjectLabelText());
            PoiExcelUtils.createCell(row, col, baseCellStyle, log.getNumber());
            rowNum++;
        }
    }

    private void writeHeader(Sheet sheet, CellStyle baseCellStyle, int startRow) {
        LinkedHashMap<String, CellStyle> headerMap = new LinkedHashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        headerMap.put("设备名称", baseCellStyle);
        headerMap.put("设备类型", baseCellStyle);
        headerMap.put("备件消耗量", baseCellStyle);

        PoiExcelUtils.createHeaderName(sheet, startRow, headerMap);
    }

    private List<SparePartsReplaceRecordVo> addWorkOrderCode(List<SparePartsReplaceRecordVo> sparePartsReplaceRecordVos, List<WorkOrderPo> workOrderPos) {
        if (CollectionUtils.isEmpty(sparePartsReplaceRecordVos) || CollectionUtils.isEmpty(workOrderPos)) {
            return Collections.emptyList();
        }
        for (SparePartsReplaceRecordVo item : sparePartsReplaceRecordVos) {
            for (WorkOrderPo itemNow : workOrderPos) {
                if (item.getWorkOrderId().equals(itemNow.getId())) {
                    item.setCode(itemNow.getCode());
                }
            }
        }
        return sparePartsReplaceRecordVos;
    }


    private void checkDeviceRepeat(EditDevice editDevice, String objectLabel) {
        DeviceSystemWithSubLayer deviceSystemWithSubLayer = deviceDao.queryByModelAndObjectLabel(editDevice.getModel(), objectLabel, editDevice.getId(), editDevice.getDeviceSystemId());
        Assert.isNull(deviceSystemWithSubLayer, "设备名称重复");
    }


    private void checkIdRepeatWhileCreate(EditSpareParts editSpareParts) {
        DeviceWithSubLayer deviceWithSubLayer = sparePartsDao.queryByModel(editSpareParts.getModel(), editSpareParts.getId(), editSpareParts.getSparePartsDeviceId());
        Assert.isNull(deviceWithSubLayer, "备件型号重复");
    }

    private void checkSystemRepeat(AddDeviceSystem addDeviceSystem) {
        DeviceSystem deviceSystem = deviceSystemDao.queryByName(addDeviceSystem.getName());
        Assert.isNull(deviceSystem, "系统名称重复");
    }

    private void checkSysNameRepeat(EditDeviceSystem editDeviceSystem) {
        DeviceSystem deviceSystem = deviceSystemDao.queryByNameAndId(editDeviceSystem.getName(), editDeviceSystem.getId());
        Assert.isNull(deviceSystem, "系统名称重复");
    }


}
