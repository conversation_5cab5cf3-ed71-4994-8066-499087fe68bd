"""
Milvus向量数据库操作工具类
封装Milvus的连接、创建集合、插入、搜索等操作
"""

import logging
from typing import List, Dict, Any, Tuple
from pymilvus import (
    connections, FieldSchema, CollectionSchema, DataType,
    Collection, utility
)
from config.settings import MILVUS_CONFIG, COLLECTION_CONFIG, INDEX_CONFIG, SEARCH_CONFIG

logger = logging.getLogger(__name__)


class MilvusUtils:
    """Milvus操作工具类"""
    
    def __init__(self):
        """初始化Milvus连接"""
        self.collection = None
        self.connect()
    
    def connect(self):
        """连接到Milvus服务"""
        try:
            connections.connect(
                alias=MILVUS_CONFIG["alias"],
                host=MILVUS_CONFIG["host"],
                port=MILVUS_CONFIG["port"]
            )
            logger.info(f"成功连接到Milvus服务: {MILVUS_CONFIG['host']}:{MILVUS_CONFIG['port']}")
        except Exception as e:
            logger.error(f"连接Milvus失败: {e}")
            raise
    
    def create_collection(self, recreate: bool = False, collection_name: str = None) -> Collection:
        """
        创建或获取集合

        Args:
            recreate (bool): 是否重新创建集合
            collection_name (str): 指定集合名称，默认使用配置中的名称

        Returns:
            Collection: Milvus集合对象
        """
        if collection_name is None:
            collection_name = COLLECTION_CONFIG["name"]

        logger.info(f"使用集合名称: {collection_name}")

        # 如果需要重新创建，先删除现有集合
        if recreate and utility.has_collection(collection_name):
            logger.info(f"重新创建集合，删除现有集合: {collection_name}")
            self.delete_collection(collection_name)
        
        if not utility.has_collection(collection_name):
            # 定义字段
            fields = [
                FieldSchema(name="id", dtype=DataType.INT64, is_primary=True, auto_id=True),
                FieldSchema(name="class_name", dtype=DataType.VARCHAR, max_length=200),
                FieldSchema(name="code_content", dtype=DataType.VARCHAR,
                          max_length=COLLECTION_CONFIG["max_content_length"]),
                FieldSchema(name="code_vector", dtype=DataType.FLOAT_VECTOR,
                          dim=COLLECTION_CONFIG["vector_dim"])
            ]
            
            # 创建集合schema
            schema = CollectionSchema(fields, description=COLLECTION_CONFIG["description"])
            
            # 创建集合
            self.collection = Collection(name=collection_name, schema=schema)
            logger.info(f"集合 {collection_name} 创建成功")
            
            # 创建索引
            self._create_index()
        else:
            self.collection = Collection(name=collection_name)
            logger.info(f"集合 {collection_name} 已存在，直接加载")
        
        return self.collection
    
    def _create_index(self):
        """为向量字段创建索引"""
        try:
            self.collection.create_index(
                field_name="code_vector",
                index_params=INDEX_CONFIG
            )
            logger.info("向量索引创建成功")
        except Exception as e:
            logger.error(f"创建索引失败: {e}")
            raise
    
    def insert_vectors(self, class_names: List[str], contents: List[str],
                      vectors: List[List[float]]) -> List[int]:
        """
        批量插入向量数据

        Args:
            class_names (List[str]): 类名列表
            contents (List[str]): 代码内容列表
            vectors (List[List[float]]): 向量列表

        Returns:
            List[int]: 插入的记录ID列表
        """
        if not self.collection:
            raise ValueError("集合未初始化，请先调用create_collection()")

        if len(class_names) != len(contents) != len(vectors):
            raise ValueError("类名、内容和向量的数量必须一致")
        
        try:
            # 智能截断过长的内容并记录统计信息
            max_length = COLLECTION_CONFIG["max_content_length"]
            truncated_contents = []
            truncated_count = 0

            # 导入智能截断函数
            from utils.file_utils import FileUtils

            for i, content in enumerate(contents):
                if len(content) > max_length:
                    truncated_content = FileUtils.smart_truncate_content(content, max_length)
                    truncated_contents.append(truncated_content)
                    truncated_count += 1
                    logger.warning(f"文件 {class_names[i]} 内容过长({len(content)}字符)，已智能截断到{len(truncated_content)}字符")
                else:
                    truncated_contents.append(content)

            if truncated_count > 0:
                logger.info(f"共有 {truncated_count} 个文件内容被智能截断")
            
            # 准备数据
            data = [
                class_names,
                truncated_contents,
                vectors
            ]
            
            # 插入数据
            insert_result = self.collection.insert(data)
            logger.info(f"成功插入 {len(class_names)} 条记录")
            
            # 刷新数据到磁盘
            self.collection.flush()
            
            return insert_result.primary_keys
            
        except Exception as e:
            logger.error(f"插入向量数据失败: {e}")
            raise
    
    def search_vectors(self, query_vector: List[float], limit: int = None) -> List[Dict[str, Any]]:
        """
        搜索相似向量
        
        Args:
            query_vector (List[float]): 查询向量
            limit (int): 返回结果数量限制
            
        Returns:
            List[Dict[str, Any]]: 搜索结果列表
        """
        if not self.collection:
            raise ValueError("集合未初始化，请先调用create_collection()")
        
        if limit is None:
            limit = SEARCH_CONFIG["default_limit"]
        
        try:
            # 加载集合到内存
            self.collection.load()
            
            # 执行搜索
            search_results = self.collection.search(
                data=[query_vector],
                anns_field="code_vector",
                param=SEARCH_CONFIG,
                limit=limit,
                output_fields=["class_name", "code_content"]
            )
            
            # 格式化结果
            results = []
            for result in search_results[0]:
                results.append({
                    "id": result.id,
                    "distance": round(result.distance, 4),
                    "class_name": result.entity.get("class_name"),
                    "code_content": result.entity.get("code_content")
                })
            
            logger.info(f"搜索完成，返回 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"搜索向量失败: {e}")
            raise
    
    def delete_collection(self, collection_name: str = None):
        """删除集合

        Args:
            collection_name (str): 要删除的集合名称，默认使用配置中的名称
        """
        if collection_name is None:
            collection_name = COLLECTION_CONFIG["name"]

        try:
            if utility.has_collection(collection_name):
                utility.drop_collection(collection_name)
                logger.info(f"集合 {collection_name} 删除成功")
        except Exception as e:
            logger.error(f"删除集合失败: {e}")
            raise
    
    def collection_exists(self) -> bool:
        """检查集合是否存在"""
        return utility.has_collection(COLLECTION_CONFIG["name"])
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """获取集合统计信息"""
        if not self.collection:
            return {}

        try:
            # 使用正确的方法获取统计信息
            self.collection.load()  # 确保集合已加载
            stats = self.collection.num_entities
            return {
                "row_count": stats,
                "collection_name": COLLECTION_CONFIG["name"]
            }
        except Exception as e:
            logger.warning(f"获取集合统计信息失败: {e}")
            return {
                "row_count": "未知",
                "collection_name": COLLECTION_CONFIG["name"]
            }
    
    def release_collection(self):
        """释放集合内存"""
        if self.collection:
            try:
                self.collection.release()
                logger.info("集合内存已释放")
            except Exception as e:
                logger.warning(f"释放集合内存失败: {e}")
