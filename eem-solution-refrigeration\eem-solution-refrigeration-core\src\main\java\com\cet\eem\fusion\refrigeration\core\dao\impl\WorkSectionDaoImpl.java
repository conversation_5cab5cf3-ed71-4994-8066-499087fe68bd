﻿package com.cet.eem.fusion.refrigeration.core.impl;

import com.cet.eem.fusion.refrigeration.core.dao.aioptimization.WorkSectionDao;
import com.cet.eem.fusion.refrigeration.core.model.aioptimization.WorkSection;
import com.cet.eem.fusion.refrigeration.core.model.def.ColdOptimizationLabelDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import com.cet.eem.model.model.BaseEntity;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName : WorkSectionDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-07 19:49
 */
@Repository
public class WorkSectionDaoImpl extends ModelDaoImpl<WorkSection> implements WorkSectionDao {
    public static final String LABEL= ColdOptimizationLabelDef.WORK_SECTION;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Override
    public List<WorkSection> queryWorkSection(BaseVo baseVo) {
        LambdaQueryWrapper<WorkSection> wrapper = LambdaQueryWrapper.of(WorkSection.class);
        wrapper.eq(WorkSection::getObjectId, baseVo.getId())
                .eq(WorkSection::getObjectLabel, baseVo.getModelLabel());
        List<WorkSection> workSections = this.selectList(wrapper);
        if (CollectionUtils.isEmpty(workSections)) {
            return Collections.emptyList();
        }
        return workSections;
    }

    @Override
    public List<WorkSection> queryWorkSections(List<BaseVo> nodes) {
        if (CollectionUtils.isEmpty(nodes)){
            return Collections.emptyList();
        }
        Map<String, List<BaseVo>> nodeMap = nodes.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        int group = 1;

        QueryConditionBuilder<BaseEntity> builder = new QueryConditionBuilder<>(LABEL)
                .composeMethod(true);
        for (Map.Entry<String, List<BaseVo>> entry : nodeMap.entrySet()) {
            String label = entry.getKey();
            List<Long> ids = nodeMap.get(label).stream().map(BaseVo::getId).distinct().collect(Collectors.toList());
            builder.eq(ColumnDef.C_OBJECT_Label, label, group);
            builder.in(ColumnDef.C_OBJECT_ID,ids,group);
            group++;
        }

        return modelServiceUtils.query(builder.build(), WorkSection.class);
    }
}
