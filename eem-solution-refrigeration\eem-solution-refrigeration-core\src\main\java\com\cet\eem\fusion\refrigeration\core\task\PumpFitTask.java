﻿package com.cet.eem.fusion.refrigeration.core.task;

import com.cet.eem.bll.common.task.TaskSchedule;
import com.cet.eem.fusion.refrigeration.core.service.task.ColdLoadPredictCetMlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * @ClassName : PumpFitTask
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-06-30 13:42
 */
@Component
@Slf4j
public class PumpFitTask implements TaskSchedule {
    @Autowired
    ColdLoadPredictCetMlService coldLoadPredictCetMlService;
    @Scheduled(cron = "${cet.eem.task.energy-saving.pumpFit.interval}")
    @Override
    public void execute() throws IOException {
        coldLoadPredictCetMlService.savePumpFitPredictData();
    }
}
