package com.cet.eem.fusion.maintenance.core.dao;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.PlanSheet;
import com.cet.eem.bll.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.PlanSheetWithSubLayer;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.bll.maintenance.def.WorkOrderDef;
import com.cet.eem.bll.maintenance.model.plan.QueryInspectionPlanRequest;
import com.cet.eem.bll.maintenance.model.plan.QueryMaintenancePlanRequest;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.common.util.JsonUtil;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import com.cet.eem.fusion.common.modelutils.model.base.ConditionBlock;
import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;
import com.cet.eem.fusion.common.modelutils.model.tool.SubConditionBuilder;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName : PlanSheetDaoImpl
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-23 13:46
 */
@Repository
public class PlanSheetDaoImpl extends ModelDaoImpl<PlanSheet> implements PlanSheetDao {
    public static final String PROJECT_ID = "project_id";
    public static final String DELETED = "deleted";
    public static final String WORKSHEET_TYPE = "worksheettype";
    public static final String ENABLED = "enabled";
    public static final String TEAM_ID = "teamid";
    @Autowired
    private ModelServiceUtils modelServiceUtils;

    @Override
    public ResultWithTotal<List<PlanSheetWithSubLayer>> queryInspectionPlanSheetSubLayerWithPage(QueryInspectionPlanRequest queryInspectionPlanRequest) {
        QueryConditionBuilder<PlanSheet> queryConditionBuilder = new QueryConditionBuilder<PlanSheet>(ModelLabelDef.PLAN_SHEET);
        queryConditionBuilder.where(PROJECT_ID, ConditionBlock.OPERATOR_EQ, GlobalInfoUtils.getTenantId(), 1);
        queryConditionBuilder.where(DELETED, ConditionBlock.OPERATOR_EQ, false, 2);
        queryConditionBuilder.where(WORKSHEET_TYPE, ConditionBlock.OPERATOR_EQ, WorkSheetTaskType.INSPECTION, 3);
        if (Objects.nonNull(queryInspectionPlanRequest.getEnabled())) {
            queryConditionBuilder.where(ENABLED, ConditionBlock.OPERATOR_EQ, queryInspectionPlanRequest.getEnabled(), 4);
        }
        if (StringUtils.isNotEmpty(queryInspectionPlanRequest.getName())) {
            queryConditionBuilder.where(ColumnDef.NAME, ConditionBlock.OPERATOR_LIKE, queryInspectionPlanRequest.getName(), 5);
        }
        if (Objects.nonNull(queryInspectionPlanRequest.getTeamId())) {
            queryConditionBuilder.where(TEAM_ID, ConditionBlock.OPERATOR_EQ, queryInspectionPlanRequest.getTeamId(), 6);
        }
        if (BooleanUtils.isTrue(queryInspectionPlanRequest.isHide())) {
            queryConditionBuilder.where(WorkOrderDef.FINISH_TIME, ConditionBlock.OPERATOR_EQ, null, 7);
            queryConditionBuilder.where(WorkOrderDef.FINISH_TIME, ConditionBlock.OPERATOR_GT, System.currentTimeMillis(), 7);
        }
        queryConditionBuilder.selectChildByLabels(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP));
        queryConditionBuilder.limit(queryInspectionPlanRequest.getPage());
        ResultWithTotal<List<Map<String, Object>>> query = this.eemModelDataService.query(queryConditionBuilder.build());
        return ResultWithTotal.ok(JsonUtil.mapList2BeanList(query.getData(), PlanSheetWithSubLayer.class), query.getTotal());
    }

    @Override
    public ResultWithTotal<List<PlanSheetWithSubLayer>> queryMaintenancePlanSheetSubLayerWithPage(QueryMaintenancePlanRequest queryMaintenancePlanRequest) {
        QueryConditionBuilder<PlanSheet> queryConditionBuilder = ParentQueryConditionBuilder.of(ModelLabelDef.PLAN_SHEET);
        int group = 1;
        queryConditionBuilder.where(PROJECT_ID, ConditionBlock.OPERATOR_EQ, GlobalInfoUtils.getTenantId(), group++);
        queryConditionBuilder.where(DELETED, ConditionBlock.OPERATOR_EQ, false, group++);
        queryConditionBuilder.where(WORKSHEET_TYPE, ConditionBlock.OPERATOR_EQ, WorkSheetTaskType.MAINTENANCE, group++);
        if (Objects.nonNull(queryMaintenancePlanRequest.getWorksheetTaskLevel())) {
            queryConditionBuilder.where("worksheettasklevel", ConditionBlock.OPERATOR_EQ, queryMaintenancePlanRequest.getWorksheetTaskLevel(), group++);
        }
        if (Objects.nonNull(queryMaintenancePlanRequest.getEnabled())) {
            queryConditionBuilder.where(ENABLED, ConditionBlock.OPERATOR_EQ, queryMaintenancePlanRequest.getEnabled(), group++);
        }
        if (StringUtils.isNotEmpty(queryMaintenancePlanRequest.getName())) {
            queryConditionBuilder.where(ColumnDef.NAME, ConditionBlock.OPERATOR_LIKE, queryMaintenancePlanRequest.getName(), group++);
        }
        if (Objects.nonNull(queryMaintenancePlanRequest.getTeamId())) {
            queryConditionBuilder.where(TEAM_ID, ConditionBlock.OPERATOR_EQ, queryMaintenancePlanRequest.getTeamId(), group++);
        }
        if (BooleanUtils.isTrue(queryMaintenancePlanRequest.isHide())) {
            queryConditionBuilder.where(WorkOrderDef.FINISH_TIME, ConditionBlock.OPERATOR_EQ, null, group);
            queryConditionBuilder.where(WorkOrderDef.FINISH_TIME, ConditionBlock.OPERATOR_GT, System.currentTimeMillis(), group);
        }
        queryConditionBuilder.selectChildByLabels(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP));
        queryConditionBuilder.limit(queryMaintenancePlanRequest.getPage());
        ResultWithTotal<List<Map<String, Object>>> query = this.eemModelDataService.query(queryConditionBuilder.build());
        return ResultWithTotal.ok(JsonUtil.mapList2BeanList(query.getData(), PlanSheetWithSubLayer.class), query.getTotal());
    }

    @Override
    public List<PlanSheet> queryUnFinishedPlan() {
        QueryConditionBuilder<PlanSheet> queryConditionBuilder = ParentQueryConditionBuilder.of(ModelLabelDef.PLAN_SHEET);
        queryConditionBuilder.where(WorkOrderDef.FINISH_TIME, ConditionBlock.OPERATOR_EQ, null, 2);
        queryConditionBuilder.where(WorkOrderDef.FINISH_TIME, ConditionBlock.OPERATOR_GT, System.currentTimeMillis(), 2);
        queryConditionBuilder.where(ColumnDef.DELETED, ConditionBlock.OPERATOR_EQ, false, 3);
        ResultWithTotal<List<Map<String, Object>>> query = this.eemModelDataService.query(queryConditionBuilder.build());
        return JsonUtil.mapList2BeanList(query.getData(), PlanSheet.class);
    }

    @Override
    public void deletePlanSheets(List<Long> ids) {
        modelServiceUtils.delete(ModelLabelDef.PLAN_SHEET, ids);
    }

    @Override
    public List<PlanSheetWithSubLayer> queryMaintenancePlanSheetByStrategyType(Integer strategyType) {
        QueryCondition condition = ParentQueryConditionBuilder.of(ModelLabelDef.PLAN_SHEET)
                .eq(ColumnDef.FINISH_TIME, null, 1)
                .gt(ColumnDef.FINISH_TIME, null, 1)
                .eq(ColumnDef.EXECUTE_STRATEGY, strategyType, 2)
                .eq(ColumnDef.DELETED, false, 3)
                .eq(ColumnDef.WORK_SHEET_TYPE, WorkSheetTaskType.MAINTENANCE)
                .leftJoin(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP)
                .build();

        return modelServiceUtils.query(condition, PlanSheetWithSubLayer.class);
    }

    @Override
    public ResultWithTotal<List<PlanSheetWithSubLayer>> queryMaintenancePlanBySchedule(QueryMaintenancePlanRequest queryMaintenancePlanRequest) {
        QueryConditionBuilder<PlanSheet> queryConditionBuilder = new QueryConditionBuilder<PlanSheet>(ModelLabelDef.PLAN_SHEET);
        queryConditionBuilder.where(PROJECT_ID, ConditionBlock.OPERATOR_EQ, 1L, 1);
        queryConditionBuilder.where(DELETED, ConditionBlock.OPERATOR_EQ, false, 2);
        queryConditionBuilder.where(WORKSHEET_TYPE, ConditionBlock.OPERATOR_EQ, WorkSheetTaskType.MAINTENANCE, 3);
        if (Objects.nonNull(queryMaintenancePlanRequest.getWorksheetTaskLevel())) {
            queryConditionBuilder.where("worksheettasklevel", ConditionBlock.OPERATOR_EQ, queryMaintenancePlanRequest.getWorksheetTaskLevel(), 4);
        }
        if (Objects.nonNull(queryMaintenancePlanRequest.getEnabled())) {
            queryConditionBuilder.where(ENABLED, ConditionBlock.OPERATOR_EQ, queryMaintenancePlanRequest.getEnabled(), 4);
        }
        if (StringUtils.isNotEmpty(queryMaintenancePlanRequest.getName())) {
            queryConditionBuilder.where(ColumnDef.NAME, ConditionBlock.OPERATOR_LIKE, queryMaintenancePlanRequest.getName(), 5);
        }
        if (Objects.nonNull(queryMaintenancePlanRequest.getTeamId())) {
            queryConditionBuilder.where(TEAM_ID, ConditionBlock.OPERATOR_EQ, queryMaintenancePlanRequest.getTeamId(), 6);
        }
        if (BooleanUtils.isTrue(queryMaintenancePlanRequest.isHide())) {
            queryConditionBuilder.where(WorkOrderDef.FINISH_TIME, ConditionBlock.OPERATOR_EQ, null, 7);
            queryConditionBuilder.where(WorkOrderDef.FINISH_TIME, ConditionBlock.OPERATOR_GT, System.currentTimeMillis(), 7);
        }
        queryConditionBuilder.selectChildByLabels(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP));
        queryConditionBuilder.limit(queryMaintenancePlanRequest.getPage());
        ResultWithTotal<List<Map<String, Object>>> query = this.eemModelDataService.query(queryConditionBuilder.build());
        return ResultWithTotal.ok(JsonUtil.mapList2BeanList(query.getData(), PlanSheetWithSubLayer.class), query.getTotal());

    }

    @Override
    public List<PlanSheetWithSubLayer> queryPlanSheet(Long projectId) {
        SubConditionBuilder subConditionBuilder = new SubConditionBuilder(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP);
        QueryConditionBuilder<BaseEntity> builder = ParentQueryConditionBuilder.of(ModelLabelDef.PLAN_SHEET)
                .distinct()
                .leftJoin(subConditionBuilder.build());
        builder.where("project_id", ConditionBlock.OPERATOR_EQ, projectId);
        builder.where("worksheettype", ConditionBlock.OPERATOR_EQ, WorkSheetTaskType.INSPECTION);
        return modelServiceUtils.query(builder, PlanSheetWithSubLayer.class);
    }
}
