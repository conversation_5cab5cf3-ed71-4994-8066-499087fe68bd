﻿package com.cet.eem.fusion.refrigeration.core.entity.vo;

import com.cet.eem.common.model.datalog.DataLogData;
import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : ColdMachineVo
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-07-13 10:22
 */
@Getter
@Setter
public class ColdMachineVO {
    private Long objectId;
    private String objectLabel;
    /**
     * 泵--区分冷却泵还是冷冻泵
     */
    private Integer pumpFunctionType;
    /**
     * 状态
     */
    private List<DatalogValue> status;

    /**
     * 下一时刻状态
     */
    private List<DatalogValue> nextStatus;
    /**
     * 功率
     */
    private List<DatalogValue> power;
    /**
     * 额定冷负荷--冷机
     */
    private Double ratedCoolingLoad;
    /**
     * 额定功率
     */
    private Double ratedPower;
}
