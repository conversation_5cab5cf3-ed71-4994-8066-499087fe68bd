package com.cet.eem.fusion.transformer.core.service.impl;

import com.cet.eem.bll.common.dao.poi.EemPoiRecordDao;
import com.cet.eem.bll.common.model.domain.object.organization.Project;
import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityAggregationData;
import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityObject;
import com.cet.eem.bll.common.model.domain.perception.logicaldevice.EemPoiRecord;
import com.cet.eem.bll.common.model.topology.vo.LinkNode;
import com.cet.eem.bll.common.model.topology.vo.PointNode;
import com.cet.eem.bll.common.service.ProjectService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.constant.AggregationType;
import com.cet.eem.common.constant.EnergyTypeDef;
import com.cet.eem.common.constant.EnumOperationType;
import com.cet.eem.common.constant.PoiTypeEnum;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.DataLogData;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.fusion.transformer.core.service.TransformerindexDataService;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;

import com.cet.eem.node.service.Topology1Service;
import com.cet.eem.quantity.dao.QuantityAggregationDataDao;
import com.cet.eem.quantity.dao.QuantityObjectDao;
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
import com.cet.eem.fusion.transformer.core.service.TransformerindexDataPOService;
import com.cet.eem.toolkit.CollectionUtils;
import com.cet.eem.transformer.dao.PowerTransformerDao;
import com.cet.eem.fusion.transformer.core.dao.TransformerindexDataPODao;
import com.cet.eem.transformer.model.PowerTransformerDto;
import com.cet.eem.fusion.transformer.core.entity.po.TransformerindexDataPOPO;
import com.cet.eem.transformer.model.constant.Constant;
import com.cet.eem.transformer.model.util.DateUtil;
import com.cet.eem.transformer.service.impl.TransformerAnalysisServiceImpl;
import com.cet.eem.transformer.service.impl.TransformerOverviewServiceImpl;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

import static com.cet.eem.task.impl.TransformerTaskServiceImpl.fixedGrouping;

/**
 * @ClassName : AvgloadTaskServiceImpl
 * <AUTHOR> yangy
 * @Date: 2022-03-28 10:18
 */
@Service
public class TransformerindexDataServiceImpl implements TransformerindexDataService {
    protected static final Logger logger = LoggerFactory.getLogger(TransformerindexDataServiceImpl.class);

    @Autowired
    TransformerOverviewServiceImpl transformerOverviewService;
    @Autowired
    PowerTransformerDao powerTransformerDao;
    @Autowired
    TransformerindexDataDao transformerindexDataDao;
    @Autowired
    TransformerAnalysisServiceImpl transformerAnalysisService;
    @Autowired
    QuantityObjectDao quantityObjectDao;
    @Autowired
    QuantityAggregationDataDao quantityAggregationDataDao;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    Topology1Service topology1Service;
    @Value("${cet.eem.task.transformerindex.startTime:2025-04-01}")
    String startTime;
    @Autowired
    EemPoiRecordDao eemPoiRecordDao;
    private static final String LOG_KEY = "[变压器负载率-平均功率转存]";

    @Override
    public void transferData() {
        logger.info("{}开始执行", LOG_KEY);
        int count = 0;
        long timeMillis = System.currentTimeMillis();
        //获取所有项目 逐个项目查询变压器 处理数据 入库
        List<Long> projectIds = getProjectID();
        if (CollectionUtils.isEmpty(projectIds)) {
            return;
        }
        for (int i = 0; i < projectIds.size(); i++) {
            Long projectId = projectIds.get(i);
            List<PowerTransformerDto> transformerDtos = powerTransformerDao.queryByProjectId(projectId);
            List<BaseVo> baseVos = transformerDtos.stream().map(transformerDto -> new BaseVo(transformerDto.getId(), transformerDto.getModelLabel())).collect(Collectors.toList());
            List<EemPoiRecord> pois = eemPoiRecordDao.queryPoiRecord(baseVos, PoiTypeEnum.TRANSFORMER_INDEX_POI);
            //如果没有poi的时间 .
            if (CollectionUtils.isEmpty(pois)) {
                count += added(transformerDtos, projectId);
                logger.info("id为{}的项目，{}执行完成: {} ",projectId, LOG_KEY, LocalDateTime.now());
                logger.info("{}初次转存总共用时：{} ms，共更新{}条数据", LOG_KEY, System.currentTimeMillis() - timeMillis, count);
                return;
            }
            //处理新增的变压器  ---此处返回 新增的poi数据到下面转存,起始时间为基本配置时间.
            List<PowerTransformerDto> powerTransformerAdds = powerTransformerAdds(pois, transformerDtos);
            if (CollectionUtils.isNotEmpty(powerTransformerAdds)) {
                count += added(powerTransformerAdds, projectId);
            }
            long maxTime = pois.stream().map(EemPoiRecord::getValue).filter(Objects::nonNull).mapToLong(value -> value).max().getAsLong();
            //poi里有时间. 以时间分组
            Map<Long, List<EemPoiRecord>> timeMap = pois.stream().collect(Collectors.groupingBy(EemPoiRecord::getValue));
            for (Map.Entry<Long, List<EemPoiRecord>> entry : timeMap.entrySet()) {
                Long key = entry.getKey();
                List<EemPoiRecord> val = entry.getValue();
                List<Long> transIds = val.stream().map(EemPoiRecord::getObjectId).collect(Collectors.toList());
                //部分数据
                List<PowerTransformerDto> parkTransformerDatas = transformerDtos.stream().filter(item -> transIds.contains(item.getId())).collect(Collectors.toList());
                count += saveDatas1(parkTransformerDatas, pois, AggregationCycle.ONE_DAY, key, projectId);
            }
            //更新月度数据.
            count += saveDatas1(transformerDtos, pois, AggregationCycle.ONE_MONTH, maxTime, projectId);
            //更新年度数据
            count += saveDatas1(transformerDtos, pois, AggregationCycle.ONE_YEAR, maxTime, projectId);
            //poi时间入库
            transformerindexDataDao.saveEemPoiRecord(pois);
        }
        logger.info("{}执行完成: {} ", LOG_KEY, LocalDateTime.now());
        logger.info("{}总共用时：{} ms，共更新{}条数据", LOG_KEY, System.currentTimeMillis() - timeMillis, count);
    }

    /**
     * 初次跑数据或者后面新增变压器
     *
     * @param transformerDtos
     */
    public int added(List<PowerTransformerDto> transformerDtos, Long projectId) {
        //创建新的poi时间
        List<EemPoiRecord> poiRecords = getEemPoiRecord(transformerDtos, null);
        int count1 = saveDatas1(transformerDtos, poiRecords, AggregationCycle.ONE_DAY, null, projectId);
        //更新月度数据.
        int count2 = saveDatas1(transformerDtos, poiRecords, AggregationCycle.ONE_MONTH, null, projectId);
        //更新年度数据
        int count3 = saveDatas1(transformerDtos, poiRecords, AggregationCycle.ONE_YEAR, null, projectId);
        //poi时间入库
        transformerindexDataDao.saveEemPoiRecord(poiRecords);
        return count1 + count2 + count3;
    }

    /**
     * 新增的变压器
     *
     * @param pois
     * @param transformerDtos
     * @return
     */
    public List<PowerTransformerDto> powerTransformerAdds(List<EemPoiRecord> pois, List<PowerTransformerDto> transformerDtos) {
        List<Long> poiTransIds = pois.stream().map(EemPoiRecord::getObjectId).collect(Collectors.toList());
        //新增的数据
        return transformerDtos.stream().filter(item -> !poiTransIds.contains(item.getId())).collect(Collectors.toList());
    }

    /**
     * 月度更新数据
     *
     * @param transformerDtos
     * @param pois
     * @param cycle
     * @param st
     */
    public Integer saveDatas1(List<PowerTransformerDto> transformerDtos, List<EemPoiRecord> pois, Integer cycle, Long st, Long projectId) {
        if (Objects.isNull(st)) {
            st = TimeUtil.localDateTime2timestamp(TimeUtil.parse(startTime, TimeUtil.DATE_TIME_FORMAT));
        }
        int count = 0;
        //获取时间数组
        List<Map<String, Long>> timeRanges = getTimeRanges(st, cycle);
        for (Map<String, Long> time : timeRanges) {
            Map<Long, List<TransformerindexData>> loadRates = getTransformerindexData1(transformerDtos, Constant.ONE, time.get("st"), time.get("end"), cycle,projectId);
            //更新poi时间
            if (Objects.equals(AggregationCycle.ONE_DAY, cycle)) {
                pois = updatePoiTime1(loadRates, pois);
            }
            //负载率入库
            List<TransformerindexData> data = loadRates.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
            data = updateTransOldData(data, cycle, Constant.ONE);
            List<TransformerindexData> save = transformerindexDataDao.save(data);
            //平均功率入库
            Map<Long, List<TransformerindexData>> meanPowers = getTransformerindexData1(transformerDtos, Constant.TWO, time.get("st"), time.get("end"), cycle, projectId);
            List<TransformerindexData> data1 = meanPowers.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
            data1 = updateTransOldData(data1, cycle, Constant.TWO);
            List<TransformerindexData> save1 = transformerindexDataDao.save(data1);
            count += save1.size();
            count += save.size();
        }
        return count;
    }

    public List<TransformerindexData> updateTransOldData(List<TransformerindexData> data, Integer cycle, Integer type) {
        //时间
        List<LocalDateTime> logTimes = data.stream().map(item -> TimeUtil.timestamp2LocalDateTime(item.getLogtime())).distinct().collect(Collectors.toList());
        List<TransformerindexData> oldDatas = transformerindexDataDao.queryByTimes(logTimes, type, cycle);
        //根据变压器id分组
        Map<Long, List<TransformerindexData>> oldMap = oldDatas.stream().collect(Collectors.groupingBy(TransformerindexData::getPowertransformerid));
        data.forEach(trans -> {
            //每个变压器一月的数据
            List<TransformerindexData> olddata = oldMap.get(trans.getPowertransformerid());
            if (CollectionUtils.isEmpty(olddata)) {
                return;
            }
            olddata.forEach(old -> {
                if (Objects.equals(old.getLogtime(), trans.getLogtime()) && Objects.equals(old.getType(), trans.getType())) {
                    trans.setId(old.getId());
                }
            });
        });
        return data;
    }

    /**
     * 月度数据 更新poi
     *
     * @param datas
     * @param pois
     * @return
     */
    public List<EemPoiRecord> updatePoiTime1(Map<Long, List<TransformerindexData>> datas, List<EemPoiRecord> pois) {
        Map<Long, List<EemPoiRecord>> poiMap = pois.stream().collect(Collectors.groupingBy(EemPoiRecord::getObjectId));
        datas.forEach((key, data) -> {
            List<EemPoiRecord> poiRecords = poiMap.get(key);
            if (CollectionUtils.isEmpty(poiRecords)) {
                return;
            }
            OptionalLong max = data.stream().map(TransformerindexData::getLogtime).filter(Objects::nonNull).mapToLong(value -> value).max();
            if (max.isPresent()) {
                poiRecords.get(0).setValue(max.getAsLong());
            }
        });
        return pois;
    }

    /**
     * 如果poi时间为空 ,获取poi时间
     *
     * @return
     */
    public List<EemPoiRecord> getEemPoiRecord(List<PowerTransformerDto> transformerDtos, Long logtime) {
        List<EemPoiRecord> list = new ArrayList<>();
        if (Objects.isNull(logtime)) {
            logtime = TimeUtil.localDateTime2timestamp(TimeUtil.parse(startTime, TimeUtil.DATE_TIME_FORMAT));
        }
        for (PowerTransformerDto transformerDto : transformerDtos) {
            EemPoiRecord poiRecord = new EemPoiRecord();
            poiRecord.setObjectId(transformerDto.getId());
            poiRecord.setPoiType(PoiTypeEnum.TRANSFORMER_INDEX_POI.getId());
            poiRecord.setValue(logtime);
            poiRecord.setCycle(AggregationCycle.ONE_DAY);
            poiRecord.setEnergyType(EnergyTypeDef.ELECTRIC);
            poiRecord.setObjectLabel(NodeLabelDef.POWER_TRANS_FORMER);
            list.add(poiRecord);
        }
        return list;
    }

    /**
     * 更新poi时间.
     *
     * @param dataList
     * @param pois
     * @return
     */
    public List<EemPoiRecord> updatePoiTime(List<TransformerindexData> dataList, List<EemPoiRecord> pois) {
        Map<Long, Long> dataMap = dataList.stream().collect(Collectors.toMap(TransformerindexData::getPowertransformerid, TransformerindexData::getLogtime));
        //更新时间
        pois.forEach(eem -> {
            if (Objects.isNull(dataMap.get(eem.getObjectId()))) {
                return;
            }
            eem.setValue(dataMap.get(eem.getObjectId()));
        });
        return pois;
    }

    /**
     * 获取时间
     *
     * @return
     */
    public List<Map<String, Long>> getTimeRanges(Long st, Integer cycle) {
        List<Map<String, Long>> mapList = new ArrayList<>();
        //获取明天开始时间
        LocalDateTime end;
        switch (cycle) {
            case AggregationCycle.ONE_DAY:
                st = TimeUtil.getFirstTimeOfDay(st);
                //下月初1号0点0时
                end = TimeUtil.getFirstDayOfNextMonth(LocalDateTime.now());
                cycle = AggregationCycle.ONE_MONTH;
                break;
            case AggregationCycle.ONE_MONTH:
                st = TimeUtil.getFirstDayOfThisMonth(st);
                end = TimeUtil.getFirstDayOfNextMonth(LocalDateTime.now());
                break;
            case AggregationCycle.ONE_YEAR:
                st = TimeUtil.getFirstDayOfThisYear(st);
                end = TimeUtil.getFirstDayOfNextYear(LocalDate.now()).atStartOfDay();
                break;
            default:
                end = LocalDateTime.now();
                break;
        }
        //获取时间 --TimeUtil 里面 不包含结束时间,遂提取出来修改.
        List<Long> timeRange = DateUtil.getTimeRange(st, TimeUtil.localDateTime2timestamp(end), cycle);
        timeRange.forEach(time -> {
            if (timeRange.indexOf(time) == timeRange.size() - 1) {
                return;
            }
            Map<String, Long> date = new HashMap<>();
            date.put("st", time);
            date.put("end", timeRange.get(timeRange.indexOf(time) + 1));
            mapList.add(date);
        });
        return mapList;
    }

    /**
     * 获取数据
     *
     * @param transformerDtos
     * @param type
     * @param st
     * @param end
     * @return
     */
    //todo 暂时没用到先注释
//    public List<TransformerindexData> getTransformerindexData(List<PowerTransformerDto> transformerDtos, Integer type, Long st, Long end, Integer cycle) {
//        List<TransformerindexData> dataList = transformerDtos.stream().map(data -> new TransformerindexData(data.getId(), type)).collect(Collectors.toList());
//        Map<Long, Double> avgDatas;
//        if (Objects.equals(type, Constant.ONE)) {
//            //负载率
//            avgDatas = getLoadrate(transformerDtos, cycle, st, end);
//        } else {
//            //平均功率
//            avgDatas = getAveragepowerfactor(transformerDtos, cycle, st, end);
//        }
//        long timeMillis = System.currentTimeMillis();
//        for (TransformerindexData data : dataList) {
//            data.setValue(avgDatas.get(data.getPowertransformerid()));
//            data.setAggregationcycle(cycle);
//            data.setType(type);
//            data.setLogtime(st);
//            data.setUpdatetime(timeMillis);
//        }
//        //时间
//        List<LocalDateTime> logTimes = dataList.stream().map(item -> TimeUtil.timestamp2LocalDateTime(item.getLogtime())).collect(Collectors.toList());
//        List<TransformerindexData> oldDatas = transformerindexDataDao.queryByTimes(logTimes, type, cycle);
//        dataList = dataList.stream().filter(item -> Objects.nonNull(item.getValue())).collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(oldDatas)) {
//            return dataList;
//        }
//        Map<Long, Long> map = oldDatas.stream().collect(Collectors.toMap(TransformerindexData::getPowertransformerid, TransformerindexData::getId));
//        dataList.forEach(trans -> trans.setId(map.get(trans.getPowertransformerid())));
//        return dataList;
//    }

    /**
     * 获取数据1 月度查询数据 todo
     *
     * @param transformerDtos
     * @param type
     * @param st
     * @param end
     * @return
     */
    public Map<Long, List<TransformerindexData>> getTransformerindexData1(List<PowerTransformerDto> transformerDtos, Integer type, Long st, Long end, Integer cycle, Long projectId) {
        if (Objects.equals(type, Constant.ONE)) {
            //负载率
            return getLoadrateByMonth(transformerDtos, cycle, st, end, projectId);
        } else {
            //平均功率
            return getAveragepowerfactor1(transformerDtos, cycle, st, end,projectId);
        }
    }


    /**
     * 获取每个变压的实时负载值
     *
     * @param transformers
     * @param cycle
     * @param st
     * @param end
     * @return
     */
    //todo 定时任务暂时未用到
//    public Map<Long, Double> getLoadrate(List<PowerTransformerDto> transformers, Integer cycle, Long st, Long end) {
//        Map<Long, Double> map = new HashMap<>();
//        //变压器分割为100个一转存 .
//        List<List<PowerTransformerDto>> lists = Lists.partition(transformers, Constant.ONEHUNDRED);
//
//        lists.forEach(list -> {
//            QuantityDataBatchSearchVo searchVo = transformerAnalysisService.createQuantityDataBatchSearchVo(null, st,
//                    end, cycle, Arrays.asList(transformerAnalysisService.getPositiveActiveElectric(), transformerAnalysisService.getReactivePowerElectric()));
//            Map<Long, List<DataLogData>> listMap = transformerAnalysisService.queryLoadRateBatch(list, searchVo, getProjectID());
//            //list转为map .
//            Map<Long, PowerTransformerDto> transMap = list.stream().collect(Collectors.toMap(PowerTransformerDto::getId, data -> data));
//            transMap.forEach((key, transformerDto) -> {
//                List<DataLogData> logData = listMap.get(key);
//                if (CollectionUtils.isEmpty(logData)) {
//                    //todo 查询不到值的数据 不入库 poi记录时间到当前天
//                    //  map.put(key, null);
//                    return;
//                }
//                //负载值 ---能耗没有值   目前数据为0的数据 就是能耗为null .
//                List<Double> datas = logData.stream().map(DataLogData::getValue).filter(Objects::nonNull).collect(Collectors.toList());
//                if (CollectionUtils.isEmpty(datas)) {
//                    //todo 查询不到值的数据 不入库 poi记录时间到当前天
//                    //  map.put(key, null);
//                    return;
//                }
//                double sum = logData.stream().map(DataLogData::getValue).filter(Objects::nonNull).mapToDouble(value -> value).sum();
//                //负载率
//                map.put(key, CommonUtils.calcDouble(sum, transformerDto.getRatedcapacity(), EnumOperationType.DIVISION.getId()));
//            });
//        });
//        return map;
//    }

//    /**
//     * 将变压器转换成项目id和变压器集合的map
//     * @param transformers
//     * @param projectIdPowerTransformerMap
//     */
//    private void getProjectIDPowerTransformerMap(List<PowerTransformerDto> transformers, Map<Long, List<PowerTransformerDto>> projectIdPowerTransformerMap) {
//        Map<Long, List<PowerTransformerDto>> collect = transformers.stream()
//                .filter(it -> CollectionUtils.isNotEmpty(it.getProject_model()))
//                .collect(Collectors.groupingBy(
//                        it -> it.getProject_model().get(0).getId()));
//        projectIdPowerTransformerMap.putAll(collect);
    //todo 这个方法取消
//    }

    /**
     * 一次性月度查询数据
     *
     * @param transformers
     * @param cycle
     * @param st
     * @param end
     * @return
     */
    public Map<Long, List<TransformerindexData>> getLoadrateByMonth(List<PowerTransformerDto> transformers, Integer cycle, Long st, Long end, Long projectId) {
        Map<Long, List<TransformerindexData>> datas = new HashMap<>();
        long timeMillis = System.currentTimeMillis();
        List<List<PowerTransformerDto>> tmp = Lists.partition(transformers, Constant.ONEHUNDRED);
        tmp.forEach(it -> {
            if (CollectionUtils.isEmpty(it)) {
                return;
            }
            QuantityDataBatchSearchVo searchVo = transformerAnalysisService.createQuantityDataBatchSearchVo(null, st,
                    end, cycle, Arrays.asList(transformerAnalysisService.getPositiveActiveElectric(), transformerAnalysisService.getReactivePowerElectric()));
            Map<Long, List<DataLogData>> listMap = transformerAnalysisService.queryLoadRateBatch(it, searchVo, projectId);
            Map<Long, Double> rated = it.stream().filter(item -> Objects.nonNull(item.getRatedcapacity())).collect(Collectors.toMap(PowerTransformerDto::getId, PowerTransformerDto::getRatedcapacity));
            listMap.forEach((key, val) -> {
                List<TransformerindexData> transDatas = val.stream().filter(item -> Objects.nonNull(item.getValue()))
                        .map(item -> new TransformerindexData(cycle, CommonUtils.calcDouble(item.getValue(), rated.get(key),
                                EnumOperationType.DIVISION.getId()), item.getTime(), key, Constant.ONE, timeMillis)).collect(Collectors.toList());
                datas.put(key, transDatas);
            });
        });

        return datas;
    }


    private List<Long> getProjectID() {
        QueryCondition queryCondition = new QueryConditionBuilder<>(NodeLabelDef.PROJECT).build();
        return modelServiceUtils.query(queryCondition, Project.class).stream().map(Project::getId).collect(Collectors.toList());
    }

    /**
     * 转存平均功率
     */
    //todo 定时任务暂时未用到
//    public Map<Long, Double> getAveragepowerfactor(List<PowerTransformerDto> transformers, Integer cycle, Long st, Long end) {
//        Map<Long, Double> map = new HashMap<>();
//        Map<String, Object> nodeRelation = getNodeRelation();
//        List<PointNode> nodes = JsonTransferUtils.transferList(transformerOverviewService.getList(TransformerAnalysisServiceImpl.DATAINFO, nodeRelation), PointNode.class);
//        List<LinkNode> links = JsonTransferUtils.transferList(transformerOverviewService.getList(TransformerAnalysisServiceImpl.DATA_LINK, nodeRelation), LinkNode.class);
//        //设备的id 已变压器id分组
//        Map<Long, List<Long>> lineMap = new HashMap<>();
//        List<BaseVo> allNodes = new ArrayList<>();
//        Map<Long, List<BaseVo>> tops = new HashMap<>();
//        Map<Long, List<BaseVo>> downs = new HashMap<>();
//        //变压器分割为100个一转存 .
//        List<List<PowerTransformerDto>> lists = Lists.partition(transformers, Constant.ONEHUNDRED);
//        lists.forEach(list -> {
//            list.forEach(transformer -> {
//                List<BaseVo> top = transformerAnalysisService.getTopOrDownNodes(transformer.getId(), nodes, links, true);
//                List<BaseVo> down = transformerAnalysisService.getTopOrDownNodes(transformer.getId(), nodes, links, false);
//                allNodes.addAll(top);
//                allNodes.addAll(down);
//                List<Long> linIds = allNodes.stream().map(BaseVo::getId).collect(Collectors.toList());
//                lineMap.put(transformer.getId(), linIds);
//                tops.put(transformer.getId(), top);
//                downs.put(transformer.getId(), down);
//
//            });
//            //查询什么的总数据
//            List<QuantityObject> quantityObjects = quantityObjectDao.queryQuantityObject(allNodes);
//            //对总数据分组
//            Map<Long, List<QuantityObject>> quantityObjectsMap = transformerOverviewService.quantityObjects(quantityObjects, lineMap);
//            Map<Long, List<QuantityAggregationData>> quantityDatas = getQuantityDatas(quantityObjects, quantityObjectsMap, cycle, st, end);
//            list.forEach(transformer -> {
//                Long id = transformer.getId();
//                Double powerFactor = transformerOverviewService.getPowerFactor(quantityObjectsMap.get(id), quantityDatas.get(id), tops.get(id), downs.get(id));
//                if (Objects.isNull(powerFactor)) {
//                    return;
//                }
//                map.put(transformer.getId(), powerFactor);
//            });
//        });
//        return map;
//    }

    /**
     * 平均功率 月度转存
     *
     * @param transformers
     * @param cycle
     * @param st
     * @param end
     * @return
     */
    public Map<Long, List<TransformerindexData>> getAveragepowerfactor1(List<PowerTransformerDto> transformers, Integer cycle, Long st, Long end,Long projectId) {
        Map<String, Object> nodeRelation = getNodeRelation(projectId);
        List<PointNode> nodes = JsonTransferUtils.transferList(transformerOverviewService.getList(TransformerAnalysisServiceImpl.DATAINFO, nodeRelation), PointNode.class);
        List<LinkNode> links = JsonTransferUtils.transferList(transformerOverviewService.getList(TransformerAnalysisServiceImpl.DATA_LINK, nodeRelation), LinkNode.class);
        //设备的id 已变压器id分组
        Map<Long, List<Long>> lineMap = new HashMap<>();
        List<BaseVo> allNodes = new ArrayList<>();
        Map<Long, List<BaseVo>> tops = new HashMap<>();
        Map<Long, List<BaseVo>> downs = new HashMap<>();
        transformers.forEach(transformer -> {
            List<BaseVo> top = transformerAnalysisService.getTopOrDownNodes(transformer.getId(), nodes, links, true);
            List<BaseVo> down = transformerAnalysisService.getTopOrDownNodes(transformer.getId(), nodes, links, false);
            List<BaseVo> transBaseVo = new ArrayList<>();
            transBaseVo.addAll(top);
            transBaseVo.addAll(down);
            allNodes.addAll(transBaseVo);
            List<Long> linIds = transBaseVo.stream().map(BaseVo::getId).collect(Collectors.toList());
            lineMap.put(transformer.getId(), linIds);
            tops.put(transformer.getId(), top);
            downs.put(transformer.getId(), down);
        });
        //查询什么的总数据
        List<QuantityObject> quantityObjects = quantityObjectDao.queryQuantityObject(allNodes);
        //对总数据分组
        Map<Long, List<QuantityObject>> quantityObjectsMap = transformerOverviewService.quantityObjects(quantityObjects, lineMap);
        //包含一个月的数据
        Map<Long, List<QuantityAggregationData>> quantityDatas = getQuantityDatas(quantityObjects, quantityObjectsMap, cycle, st, end);
        Map<Long, List<TransformerindexData>> listMap = new HashMap<>();
        long timeMillis = System.currentTimeMillis();
        transformers.forEach(transformer -> {
            Long id = transformer.getId();
            List<QuantityAggregationData> list = quantityDatas.get(id);
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            List<TransformerindexData> meanDatas = new ArrayList<>();
            //每月的数据
            Map<Long, List<QuantityAggregationData>> timeDatas = list.stream().collect(Collectors.groupingBy(QuantityAggregationData::getLogtime));
            timeDatas.forEach((time, data) -> {
                Double powerFactor = transformerOverviewService.getPowerFactor(quantityObjectsMap.get(id), data, tops.get(id), downs.get(id));
                if (Objects.isNull(powerFactor)) {
                    return;
                }
                TransformerindexData transformerindexData = new TransformerindexData(cycle, powerFactor, time, id, Constant.TWO, timeMillis);
                meanDatas.add(transformerindexData);
                // map.put(transformer.getId(), powerFactor);
            });
            listMap.put(id, meanDatas);
        });
        return listMap;
    }
    public Map<String, Object> getNodeRelation(Long projectId) {
        try {
            return topology1Service.queryTopology(projectId, EnergyTypeDef.ELECTRIC);
        } catch (InstantiationException e) {
            logger.error("获取节点失败 {}", e.getMessage());
        } catch (IllegalAccessException e) {
            logger.error("获取节点失败 {}", e.getMessage());
        }
        return new HashMap<>();
    }

    /**
     * 平均功率分组
     *
     * @param quantityObjects
     * @param quantityObjectsMap
     * @param cycle
     * @param st
     * @param end
     * @return
     */
    public Map<Long, List<QuantityAggregationData>> getQuantityDatas(List<QuantityObject> quantityObjects, Map<Long, List<QuantityObject>> quantityObjectsMap, Integer cycle, Long st, Long end) {

        List<Long> quantityObjectId = quantityObjects.stream().map(QuantityObject::getId).collect(Collectors.toList());
        List<QuantityAggregationData> quantityAggregationData = quantityAggregationDataDao.queryQuantityData(
                TimeUtil.timestamp2LocalDateTime(st), TimeUtil.timestamp2LocalDateTime(end),
                quantityObjectId, cycle, AggregationType.STEP_ACCUMULATION);
        //根据quantityobject_id分组
        Map<Long, List<QuantityAggregationData>> maps = new HashMap<>();
        quantityObjectsMap.forEach((key, quantitys) -> {
            List<Long> quantityIds = quantitys.stream().map(QuantityObject::getId).collect(Collectors.toList());
            List<QuantityAggregationData> list = new ArrayList<>();
            quantityAggregationData.forEach(data -> {
                if (quantityIds.contains(data.getQuantityobject_id())) {
                    list.add(data);
                }
            });
            maps.put(key, list);
        });
        return maps;
    }

    public List<LocalDateTime> getTime() {
        List<LocalDateTime> times = new ArrayList<>();
        //获取当天的开始时间
        LocalDateTime theStartOfDay = TimeUtil.getTheStartOfDay(LocalDate.now());
        //获取昨天的开始时间
        LocalDateTime firstTimeOfLastDay = DateUtil.getFirstTimeOfLastDay(LocalDate.now());
        //获取当前月的开始时间
        LocalDateTime firstDayOfNextMonth = TimeUtil.getFirstDayOfNextMonth(LocalDateTime.now());
        //获取当年的第一天
        LocalDateTime firstDayOfYear = LocalDateTime.of(LocalDate.from(LocalDateTime.now().with(TemporalAdjusters.firstDayOfYear())), LocalTime.MIN);
        times.add(theStartOfDay);
        times.add(firstTimeOfLastDay);
        times.add(firstDayOfYear);
        times.add(firstDayOfNextMonth);
        return times;
    }

    /**
     * 根据时间获取数据
     *
     * @param dateTime
     * @return
     */
    public List<TransformerindexData> getOldTransformerindexData(List<TransformerindexData> dataList, LocalDateTime dateTime, Integer type) {
        return dataList.stream().filter(item -> Objects.equals(item.getLogtime(), TimeUtil.localDateTime2timestamp(dateTime))
                && Objects.equals(item.getType(), type)).collect(Collectors.toList());
    }
}