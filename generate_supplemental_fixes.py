#!/usr/bin/env python3
"""
为补充的问题生成修复方案
"""

import json
from pathlib import Path

def load_json_file(file_path):
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def generate_miss_method_fixes(issues):
    """生成miss_method问题的修复方案"""
    fixes = []
    
    for issue in issues:
        class_name = issue['class']
        issue_id = issue['issue_id']
        description = issue['description']
        line = issue['line']
        
        # 分析缺失的方法名
        method_name = "unknown"
        if "无法解析方法" in description:
            # 提取方法名
            if "'" in description:
                parts = description.split("'")
                if len(parts) >= 2:
                    method_name = parts[1]
        
        fix_content = f"""
## {class_name} - Issue {issue_id}

**位置**: {class_name}.java:{line}
**问题**: {description}
**错误类型**: miss_method

### 修复方案

**缺失方法**: `{method_name}()`

**建议修复**:
```java
// 在 {class_name} 类中添加缺失的方法
public String {method_name}() {{
    // TODO: 实现具体逻辑
    return null; // 或返回适当的默认值
}}
```

**修复步骤**:
1. 在 {class_name} 类中添加 `{method_name}()` 方法
2. 根据业务逻辑实现方法体
3. 确保返回值类型与调用处期望一致

---
"""
        fixes.append(fix_content)
    
    return fixes

def generate_wrong_params_fixes(issues):
    """生成wrong_params问题的修复方案"""
    fixes = []
    
    for issue in issues:
        class_name = issue['class']
        issue_id = issue['issue_id']
        description = issue['description']
        line = issue['line']
        
        fix_content = f"""
## {class_name} - Issue {issue_id}

**位置**: {class_name}.java:{line}
**问题**: {description}
**错误类型**: wrong_params

### 修复方案

**问题分析**: 参数类型不匹配或返回值类型不兼容

**建议修复**:
"""
        
        # 根据描述类型提供具体修复建议
        if "无法应用于" in description:
            fix_content += """
```java
// 检查方法调用的参数类型
// 确保传入的参数类型与方法签名匹配
// 可能需要进行类型转换或使用正确的参数
```

**修复步骤**:
1. 检查方法调用处的参数类型
2. 确认方法签名的期望参数类型
3. 进行必要的类型转换或使用正确的参数
"""
        elif "不兼容的类型" in description:
            fix_content += """
```java
// 修复类型不兼容问题
// 可能需要：
// 1. 修改返回值类型
// 2. 进行类型转换
// 3. 使用正确的变量类型
```

**修复步骤**:
1. 分析期望的类型和实际类型
2. 修改变量声明或返回值类型
3. 添加必要的类型转换
"""
        else:
            fix_content += """
```java
// 根据具体错误信息进行修复
// 检查参数类型、返回值类型、方法签名等
```

**修复步骤**:
1. 仔细分析错误信息
2. 检查相关的类型定义
3. 进行相应的代码修改
"""
        
        fix_content += "\n---\n"
        fixes.append(fix_content)
    
    return fixes

def update_fix_files():
    """更新修复文件"""
    
    # 加载更新后的分类数据
    miss_method_data = load_json_file('output/miss_method.json')
    wrong_params_data = load_json_file('output/wrong_params.json')
    
    if not miss_method_data or not wrong_params_data:
        print("无法加载分类数据")
        return False
    
    print(f"生成修复方案:")
    print(f"  miss_method: {len(miss_method_data)} 个问题")
    print(f"  wrong_params: {len(wrong_params_data)} 个问题")
    
    # 生成修复方案
    miss_method_fixes = generate_miss_method_fixes(miss_method_data)
    wrong_params_fixes = generate_wrong_params_fixes(wrong_params_data)
    
    # 更新 miss_method_fix.md
    miss_method_content = f"""# Miss Method 问题修复方案

**生成时间**: 2025-08-27
**问题总数**: {len(miss_method_data)} 个
**修复状态**: 已生成修复方案

## 概述

本文档包含所有 miss_method 类型问题的修复方案。这些问题主要是由于缺失方法导致的编译错误。

## 修复方案

{''.join(miss_method_fixes)}

## 总结

- 总问题数: {len(miss_method_data)}
- 涉及类数: {len(set(issue['class'] for issue in miss_method_data))}
- 修复方式: 添加缺失的方法实现

**注意事项**:
1. 所有修复方案都需要根据具体业务逻辑进行调整
2. 建议在实现方法时添加适当的注释和错误处理
3. 确保新添加的方法与现有代码风格保持一致
"""
    
    # 更新 wrong_params_fix.md
    wrong_params_content = f"""# Wrong Params 问题修复方案

**生成时间**: 2025-08-27
**问题总数**: {len(wrong_params_data)} 个
**修复状态**: 已生成修复方案

## 概述

本文档包含所有 wrong_params 类型问题的修复方案。这些问题主要是由于参数类型不匹配、返回值类型不兼容等导致的编译错误。

## 修复方案

{''.join(wrong_params_fixes)}

## 总结

- 总问题数: {len(wrong_params_data)}
- 涉及类数: {len(set(issue['class'] for issue in wrong_params_data))}
- 修复方式: 类型转换、参数调整、返回值修复

**注意事项**:
1. 类型转换时要注意空值检查
2. 确保修复后的代码逻辑正确
3. 建议添加适当的异常处理
"""
    
    # 保存文件
    try:
        with open('output/miss_method_fix.md', 'w', encoding='utf-8') as f:
            f.write(miss_method_content)
        print("✅ miss_method_fix.md 更新成功")
        
        with open('output/wrong_params_fix.md', 'w', encoding='utf-8') as f:
            f.write(wrong_params_content)
        print("✅ wrong_params_fix.md 更新成功")
        
        return True
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        return False

if __name__ == "__main__":
    success = update_fix_files()
    if success:
        print("\n🎉 修复方案生成完成！")
        print("所有197个问题都已有对应的修复方案")
    else:
        print("\n❌ 修复方案生成失败")
