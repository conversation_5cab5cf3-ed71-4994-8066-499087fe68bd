package com.cet.eem.fusion.maintenance.core.controller.bff.maintenance;

import com.cet.electric.matterhorn.cloud.authservice.sdk.common.annotation.OperationPermission;
import com.cet.eem.fusion.common.def.OperationAuthDef;
import com.cet.eem.fusion.config.sdk.service.log.OperationLog;
import com.cet.eem.fusion.config.sdk.def.OperationLogType;
import com.cet.eem.fusion.common.utils.EnumOperationSubType;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.MaintenanceGroup;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.MaintenanceItem;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.MaintenanceTypeDefine;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.bll.maintenance.model.maintance.item.*;
import com.cet.eem.bll.maintenance.service.maintenance.MaintenanceServcie;
import com.cet.eem.bll.maintenance.service.maintenance.MaintenanceTypeService;
import com.cet.electric.commons.ApiResult;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @ClassName : MaintenanceItemBffController
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-21 14:19
 */
public class MaintenanceItemBffController {

    @Autowired
    private MaintenanceServcie maintenanceServcie;

    @Autowired
    private MaintenanceTypeService maintenanceTypeService;

    @ApiOperation(value = "查询维保项目组")
    @OperationPermission(authNames = {OperationAuthDef.Maintenance.GROUP_BROWSER})
    @GetMapping
    public ApiResult<List<MaintenanceGroup>> queryMaintenanceGroupInThisProject() {
        return Result.ok(maintenanceServcie.queryAllGroupInThisProject());
    }

    @ApiOperation(value = "新增维保项目组")
    @OperationPermission(authNames = {OperationAuthDef.Maintenance.GROUP_CREATE})
    @OperationLog(operationType = EEMOperationLogType.MAINTENANCE_GROUP, subType = EnumOperationSubType.ADD, description = "【编辑维保项目组】")
    @PostMapping("/group")
    public ApiResult<MaintenanceGroup> addMaintenanceGroup(@RequestBody AddMaintenanceGroupRequest addMaintenanceGroupRequest) {
        return Result.ok(maintenanceServcie.addMaintenanceGroup(addMaintenanceGroupRequest));
    }

    @ApiOperation(value = "编辑维保项目组")
    @OperationPermission(authNames = {OperationAuthDef.Maintenance.GROUP_UPDATE})
    @OperationLog(operationType = EEMOperationLogType.MAINTENANCE_GROUP, subType = EnumOperationSubType.UPDATE, description = "【编辑维保项目组】")
    @PatchMapping("/group")
    public ApiResult<MaintenanceGroup> editMaintenanceGroup(@RequestBody EditMaintenanceGroupRequest editMaintenanceGroupRequest) {
        return Result.ok(maintenanceServcie.editMaintenanceGroup(editMaintenanceGroupRequest));
    }

    @ApiOperation(value = "删除维保项目组")
    @OperationPermission(authNames = {OperationAuthDef.Maintenance.GROUP_DELETE})
    @OperationLog(operationType = EEMOperationLogType.MAINTENANCE_GROUP, subType = EnumOperationSubType.DELETE, description = "【删除维保项目组】")
    @DeleteMapping("/group")
    public ApiResult<Void> deleteMaintenanceGroup(@RequestBody List<Long> ids) {
        maintenanceServcie.deleteMaintenanceGroup(ids);
        return Result.ok();
    }

    @ApiOperation(value = "查询维保项目")
    @OperationPermission(authNames = {OperationAuthDef.Maintenance.ITEM_BROWSER})
    @GetMapping("/item")
    public ApiResult<List<MaintenanceItemVo>> queryAllMaintenanceItem(@RequestParam Long maintenanceGroupId) {
        return Result.ok(maintenanceServcie.queryAllMaintenanceItem(maintenanceGroupId));
    }

    @ApiOperation(value = "查询维保计划中的维保项目")
    @OperationPermission(authNames = {OperationAuthDef.Maintenance.ITEM_BROWSER})
    @GetMapping("/plan/item")
    public ApiResult<List<MaintenanceItemVo>> queryMaintenanceItemByPlanSheetId(@RequestParam Long planSheetId) {
        return Result.ok(maintenanceServcie.queryMaintenanceItemByPlanSheetId(planSheetId));
    }

    @ApiOperation(value = "查询维保计划中的维保项目")
    @OperationPermission(authNames = {OperationAuthDef.Maintenance.ITEM_BROWSER})
    @PostMapping("/itemByNodeInfo")
    public ApiResult<List<MaintenanceItemVo>> queryMaintenanceItemByPlanSheetId(@RequestBody MaintenanceItemSearchVo searchVo) {
        return Result.ok(maintenanceServcie.queryMaintenanceItemByPlanSheetId(searchVo));
    }

    @ApiOperation(value = "新增维保项目")
    @OperationPermission(authNames = {OperationAuthDef.Maintenance.ITEM_CREATE})
    @OperationLog(operationType = EEMOperationLogType.MAINTENANCE_ITEM, subType = EnumOperationSubType.ADD, description = "【新增维保项目】")
    @PostMapping("/item")
    public ApiResult<MaintenanceItem> addMaintenanceItem(@RequestBody AddMaintenanceItemRequest addMaintenanceItemRequest) {
        return Result.ok(maintenanceServcie.addMaintenanceItem(addMaintenanceItemRequest));
    }

    @ApiOperation(value = "编辑维保项目")
    @OperationPermission(authNames = {OperationAuthDef.Maintenance.ITEM_UPDATE})
    @OperationLog(operationType = EEMOperationLogType.MAINTENANCE_ITEM, subType = EnumOperationSubType.UPDATE, description = "【编辑维保项目】")
    @PatchMapping("/item")
    public ApiResult<MaintenanceItem> editMaintenanceItem(@RequestBody EditMaintenanceItemRequest editMaintenanceItemRequest) {
        return Result.ok(maintenanceServcie.editMaintenanceItem(editMaintenanceItemRequest));
    }

    @ApiOperation(value = "维保项目排序")
    @OperationPermission(authNames = {OperationAuthDef.Maintenance.ITEM_UPDATE})
    @OperationLog(operationType = EEMOperationLogType.MAINTENANCE_ITEM, subType = EnumOperationSubType.UPDATE, description = "【编辑维保项目排序】")
    @PatchMapping("/item/sort")
    public ApiResult<Void> editMaintenanceItem(@RequestBody List<MaintenanceItemSortVo> maintenanceItemSortVos) {
        maintenanceServcie.editMaintenanceItemSort(maintenanceItemSortVos);
        return Result.ok();
    }

    @ApiOperation(value = "删除维保项目")
    @OperationPermission(authNames = {OperationAuthDef.Maintenance.ITEM_DELETE})
    @OperationLog(operationType = EEMOperationLogType.MAINTENANCE_ITEM, subType = EnumOperationSubType.DELETE, description = "【删除维保项目】")
    @DeleteMapping("/item")
    public ApiResult<Object> deleteMaintenanceItem(@RequestBody List<Long> ids) {
        maintenanceServcie.deleteMaintenanceItem(ids);
        return Result.ok();
    }
    @ApiOperation(value = "导入维保项目")
    @OperationPermission(authNames = {OperationAuthDef.Maintenance.ITEM_BROWSER})
    @PostMapping("/importItem")
    public ApiResult<Object> importMaintenanceItem(@RequestParam("file") MultipartFile file) throws IOException {
        Long projectId = GlobalInfoUtils.getTenantId();
        maintenanceServcie.importItem(file,projectId);
        return Result.ok();
    }

    @ApiOperation(value = "导出维保项目")
    @OperationPermission(authNames = {OperationAuthDef.Maintenance.ITEM_BROWSER})
    @PostMapping("/exportItem")
    public ApiResult<Object> exportMaintenanceItem(HttpServletResponse response,@RequestBody List<Long> ids) throws Exception {
        maintenanceServcie.exportItem(response,ids);
        return null;
    }

    @ApiOperation(value = "查询维保类型")
    @OperationPermission(authNames = {OperationAuthDef.Maintenance.TYPE_BROWSER})
    @GetMapping("/type")
    public ApiResult<List<MaintenanceTypeDefine>> queryMaintenanceItemByPlanSheetId(
            @RequestParam(required = false) @ApiParam(name = "name", value = "名称关键字") String name) {
        List<MaintenanceTypeDefine> typeDefines = maintenanceTypeService.queryMaintenanceType(name);
        return Result.ok(typeDefines);
    }

    @ApiOperation(value = "新增维保类型")
    @OperationPermission(authNames = {OperationAuthDef.Maintenance.TYPE_CREATE})
    @OperationLog(operationType = EEMOperationLogType.MAINTENANCE_TYPE, subType = EnumOperationSubType.ADD, description = "【新增维保类型】")
    @PostMapping("/type")
    public ApiResult<List<MaintenanceTypeDefine>> addMaintenanceItem(@RequestBody List<AddMaintenanceTypeVo> maintenanceTypeVos) {
        List<MaintenanceTypeDefine> typeDefines = maintenanceTypeService.createMaintenanceType(maintenanceTypeVos);
        return Result.ok(typeDefines);
    }

    @ApiOperation(value = "更新维保类型")
    @OperationPermission(authNames = {OperationAuthDef.Maintenance.TYPE_UPDATE})
    @OperationLog(operationType = EEMOperationLogType.MAINTENANCE_TYPE, subType = EnumOperationSubType.UPDATE, description = "【编辑维保类型】")
    @PatchMapping("/type")
    public ApiResult<List<MaintenanceTypeDefine>> updateMaintenanceType(@RequestBody List<UpdateMaintenanceTypeVo> maintenanceItemSortVos) {
        maintenanceTypeService.updateMaintenanceType(maintenanceItemSortVos);
        return Result.ok();
    }

    @ApiOperation(value = "删除维保类型")
    @OperationPermission(authNames = {OperationAuthDef.Maintenance.TYPE_DELETE})
    @OperationLog(operationType = EEMOperationLogType.MAINTENANCE_TYPE, subType = EnumOperationSubType.DELETE, description = "【删除维保类型】")
    @DeleteMapping("/type")
    public ApiResult<Void> deleteMaintenanceType(@RequestBody List<Long> ids) {
        maintenanceTypeService.deleteMaintenanceType(ids);
        return Result.ok();
    }
}