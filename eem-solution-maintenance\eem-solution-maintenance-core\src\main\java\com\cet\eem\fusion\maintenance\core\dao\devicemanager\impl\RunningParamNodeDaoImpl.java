package com.cet.eem.fusion.maintenance.core.dao.devicemanager.impl;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.MeasureNode;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.RunningParamNode;
import com.cet.eem.bll.maintenance.dao.devicemanager.RunningParamNodeDao;
import com.cet.eem.bll.maintenance.model.devicemanage.template.NodeWithMeasureNode;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.conditions.query.QueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-14
 */
@Service
public class RunningParamNodeDaoImpl extends ModelDaoImpl<RunningParamNode> implements RunningParamNodeDao {
    @Override
    public NodeWithMeasureNode nodeWithMeasureNode(Long id) {
        LambdaQueryWrapper<NodeWithMeasureNode> queryWrapper = LambdaQueryWrapper.of(NodeWithMeasureNode.class);
        queryWrapper.eq(NodeWithMeasureNode::getId, id);
        return this.selectRelatedById(NodeWithMeasureNode.class, id,
                Collections.singletonList(QueryWrapper.of(MeasureNode.class)));
    }
}
