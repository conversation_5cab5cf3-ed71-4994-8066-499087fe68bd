﻿package com.cet.eem.fusion.refrigeration.core.dao;

import com.cet.eem.fusion.refrigeration.core.model.config.system.NumericalCondition;
import com.cet.eem.dao.BaseModelDao;

import java.util.List;

/**
 * @ClassName : NumericalConditionDao
 * @Description :
 * <AUTHOR> jiangzi<PERSON>uan
 * @Date: 2022-06-28 19:05
 */
public interface NumericalConditionDao extends BaseModelDao<NumericalCondition> {
    /**
     * 根据系统id查询内容
     * @param controlSchemeId
     * @return
     */
    List<NumericalCondition> queryCondition(List<Long> controlSchemeId);
}

