package com.cet.eem.fusion.maintenance.core.entity.workorder.inspection.recordsheet;

import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.maintenance.core.entity.devicemanage.TechParamValue;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : TemplateDetailVo
 * @Description : 模型详细信息
 * <AUTHOR> jiangzixuan
 * @Date: 2022-10-19 09:12
 */
@Getter
@Setter
public class TemplateDetailVo {
    private List<BaseVo> node;
    private Integer cycle;
    @JsonProperty(RecordSheetModelLabel.SCHEME_ID)
    private List<Long> inspectionSchemeId;
    private List<DeviceFieldData> dashboard;
    private List<Long> techParamDataId;
    private List<TechParamValue> techParamValues;
}