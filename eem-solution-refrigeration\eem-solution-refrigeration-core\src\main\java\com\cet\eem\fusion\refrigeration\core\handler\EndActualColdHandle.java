﻿package com.cet.eem.fusion.refrigeration.core.handler;


import com.cet.eem.bll.common.dao.node.EnergySupplyDao;
import com.cet.eem.bll.common.dao.node.NodeDao;
import com.cet.eem.bll.common.dao.poi.EemPoiRecordDao;
import com.cet.eem.bll.common.def.quantity.FrequencyDef;
import com.cet.eem.bll.common.def.quantity.PhasorDef;
import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;
import com.cet.eem.bll.common.model.domain.perception.logicaldevice.EemPoiRecord;
import com.cet.eem.bll.common.model.domain.subject.energysaving.ColdActual;

import com.cet.eem.bll.common.model.node.relations.EnergySupplyToPo;
import com.cet.eem.bll.common.util.LockService;
import com.cet.eem.fusion.refrigeration.core.config.colddata.ColdEndCapacityConfig;
import com.cet.eem.fusion.refrigeration.core.dao.colddata.ColdActualDao;
import com.cet.eem.fusion.refrigeration.core.model.config.RefrigeratingSystemVo;
import com.cet.eem.fusion.refrigeration.core.model.config.system.RefrigeratingSystem;
import com.cet.eem.fusion.refrigeration.core.model.weather.ColdLoadType;
import com.cet.eem.fusion.refrigeration.core.model.weather.PredictDataType;
import com.cet.eem.fusion.refrigeration.core.model.weather.PumpVo;
import com.cet.eem.fusion.refrigeration.core.service.trend.ModelConfigurationService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.constant.EnergyTypeDef;
import com.cet.eem.common.constant.EnumDataTypeId;
import com.cet.eem.common.constant.PoiTypeEnum;
import com.cet.eem.common.definition.LoginDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.exception.ValidationException;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.DataLogDataWithGroup;
import com.cet.eem.common.model.datalog.DataLogDataWithGroup;
import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryResultContentTaker;
import com.cet.eem.node.service.Topology1Service;
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
import com.cet.eem.quantity.model.quantity.QuantitySearchVo;
import com.cet.eem.quantity.service.QuantityManageService;
import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 末端实际冷量处理
 *
 * <AUTHOR>
 * @date 2021/12/22
 */
@Service
@Slf4j
public class EndActualColdHandle {
    @Autowired
    NodeDao nodeDao;

    @Autowired
    QuantityManageService quantityManageService;

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    Topology1Service topology1Service;

    @Autowired
    ModelConfigurationService modelConfigurationService;

    @Autowired
    EnergySupplyDao energySupplyDao;

    @Autowired
    LockService lockService;

    @Autowired
    EemPoiRecordDao eemPoiRecordDao;

    @Autowired
    ColdEndCapacityConfig coldEndCapacityConfig;

    @Autowired
    ColdActualDao coldActualDao;

    private List<String> COLD_ENE_NODE_LABELS = Arrays.asList(NodeLabelDef.BUILDING, NodeLabelDef.FLOOR, NodeLabelDef.ROOM);

    public void calcColdActualData() throws InstantiationException, IllegalAccessException {
        log.info("开始转存末端冷量需求计算实际值");
        int quantityBeginIndex = 1;
        // 需要处理数据的总组数
        int totalGroup = 3;

        List<BaseVo> projects = modelServiceUtils.query((Long) null, NodeLabelDef.PROJECT, BaseVo.class);
        long now = System.currentTimeMillis();

        for (BaseVo project : projects) {
            // 分系统查询末端管道以及节点
            List<RefrigeratingSystemVo> refrigeratingSystems = modelConfigurationService.queryAiUseSystem(project.getId(), LoginDef.USER_ROOT);
            if (CollectionUtils.isEmpty(refrigeratingSystems)) {
                continue;
            }

            for (RefrigeratingSystem refrigeratingSystem : refrigeratingSystems) {
                // 查询当前房间下所有制冷相关的设备
                List<Map<String, Object>> roomWithChildren = modelServiceUtils.queryWithChildren(NodeLabelDef.ROOM, Collections.singletonList(refrigeratingSystem.getRoomId()), NodeLabelDef.REFRIGRATION_DEVICE);
                List<BaseVo> coldDevices = resolveChildren(roomWithChildren);
                if (CollectionUtils.isEmpty(coldDevices)) {
                    continue;
                }

                // 查询制冷房间关联的管道
                List<BaseVo> pipeLineNodes = topology1Service.queryFlowNode(project.getId(), EnergyTypeDef.COLD, coldDevices, NodeLabelDef.PIPELINE);
                List<EnergySupplyToPo> energySupplyToPos = energySupplyDao.queryEnergySupplyByObjectNodes(pipeLineNodes, EnergySupplyToPo.class);
                if (CollectionUtils.isEmpty(energySupplyToPos)) {
                    continue;
                }

                // 查询末端管道供能到的对象
                Set<BaseVo> nodes = energySupplyToPos.stream()
                        .filter(it -> COLD_ENE_NODE_LABELS.contains(it.getSupplytolabel()))
                        .map(it -> new BaseVo(it.getSupplytoid(), it.getSupplytolabel()))
                        .collect(Collectors.toSet());

                // 根据末端节点查询所有的空调
                List<BaseVo> airConditioners = queryAirConditioners(nodes);
                List<PumpVo> pumpVos = nodeDao.queryNodes(airConditioners, PumpVo.class);
                if (CollectionUtils.isEmpty(airConditioners)) {
                    continue;
                }

                BaseVo roomNode = new BaseVo(refrigeratingSystem.getRoomId(), NodeLabelDef.ROOM);
                EemPoiRecord eemPoiRecord = eemPoiRecordDao.queryPoiRecord(roomNode, PoiTypeEnum.COLD_END_CAPACITY_POI.getId());
                if (Objects.isNull(eemPoiRecord)) {
                    eemPoiRecord = new EemPoiRecord();
                    eemPoiRecord.setPoiType(PoiTypeEnum.COLD_END_CAPACITY_POI.getId());
                    eemPoiRecord.setObjectId(refrigeratingSystem.getRoomId());
                    eemPoiRecord.setObjectLabel(NodeLabelDef.ROOM);
                    eemPoiRecord.setValue(TimeUtil.localDateTime2timestamp(coldEndCapacityConfig.parseOverallStartTime()));
                    eemPoiRecord = modelServiceUtils.writeData(eemPoiRecord, EemPoiRecord.class).get(0);
                }
                Long st = eemPoiRecord.getValue();
                Long et = now;

                List<Long> times = TimeUtil.getTimeRange(st, et, AggregationCycle.ONE_DAY, coldEndCapacityConfig.getMaxQueryDay());
                for (Long time : times) {
                    // 查询POI
                    if (!BooleanUtils.isTrue(lockService.getLock(eemPoiRecord.getId(), ModelLabelDef.EEM_POI_RECORD))) {
                        throw new ValidationException("加锁失败！");
                    }

                    st = time;
                    et = TimeUtil.addDateTimeByCycle(st, AggregationCycle.ONE_DAY, coldEndCapacityConfig.getMaxQueryDay());
                    if (now < et) {
                        et = now;
                    }

                    // 根据节点读取所关联的定时记录数据
                    Map<Integer, List<TrendDataVo>> dataLogResult = queryQuantityDataList(st, et, quantityBeginIndex, airConditioners);

                    // 计算制冷数据
                    List<DatalogValue> result = calcEndColdCapacity(quantityBeginIndex, totalGroup, dataLogResult, pumpVos);
                    if (CollectionUtils.isNotEmpty(result)) {
                        // 入库
                        writeColdActualData(result, project.getId(), refrigeratingSystem.getRoomId());

                        OptionalLong max = result.stream().mapToLong(DatalogValue::getTime).max();
                        if (max.isPresent()) {
                            eemPoiRecord.setValue(max.getAsLong());
                            modelServiceUtils.writeData(Collections.singletonList(eemPoiRecord));
                        }
                    }

                    lockService.unlock(eemPoiRecord.getId(), ModelLabelDef.EEM_POI_RECORD);
                }
            }

        }
        log.info("结束转存末端冷量需求计算实际值，耗时{}ms", (System.currentTimeMillis() - now));
    }

    private void writeColdActualData(List<DatalogValue> result, Long id, Long roomId) {
        List<ColdActual> actuals = new ArrayList<>();
        for (DatalogValue DatalogValue : result) {
            ColdActual actual = new ColdActual();
            actual.setProjectId(id);
            actual.setRoomId(roomId);
            actual.setLogTime(TimeUtil.timestamp2LocalDateTime(DatalogValue.getTime()));
            actual.setValue(DatalogValue.getValue());
            actual.setColdLoadType(ColdLoadType.END);
            actual.setPredictDataType(PredictDataType.COLD_LOAD);
            actuals.add(actual);
        }
        coldActualDao.writeColdActualDataList(actuals);
    }

    /**
     * 计算末端冷量
     *
     * @param quantityBeginIndex
     * @param totalGroup
     * @param dataLogResult
     */
    private List<DatalogValue> calcEndColdCapacity(int quantityBeginIndex, int totalGroup, Map<Integer, List<TrendDataVo>> dataLogResult, Collection<PumpVo> nodes) {

        List<TrendDataVo> newTotalAirDataList = dataLogResult.get(quantityBeginIndex++);
        List<TrendDataVo> dewTotalPointDataList = dataLogResult.get(quantityBeginIndex);
        if (CollectionUtils.isEmpty(newTotalAirDataList) || CollectionUtils.isEmpty(dewTotalPointDataList)) {
            return Collections.emptyList();
        }

        List<DatalogValue> result = new ArrayList<>();
        for (PumpVo node : nodes) {
            if (Objects.isNull(node.getRatedAirVolume())) {
                continue;
            }
            // 只考虑第一组数据
            List<DatalogValue> newAirDataList = newTotalAirDataList.stream()
                    .filter(it -> Objects.equals(node.getModelLabel(), it.getMonitoredlabel()) && Objects.equals(node.getId(), it.getMonitoredid()))
                    .flatMap(it -> it.getDataList().stream())
                    .collect(Collectors.toList());
            List<DatalogValue> dewPointDataList = dewTotalPointDataList.stream()
                    .filter(it -> Objects.equals(node.getModelLabel(), it.getMonitoredlabel()) && Objects.equals(node.getId(), it.getMonitoredid()))
                    .flatMap(it -> it.getDataList().stream())
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(newAirDataList) || CollectionUtils.isEmpty(dewPointDataList)) {
                continue;
            }
            // 对数据进行编组，然后根据时间分组进行数据处理
            List<DataLogDataWithGroup> dataLogs = new ArrayList<>();
            addDatalogValue(dataLogs, newAirDataList, quantityBeginIndex++);
            addDatalogValue(dataLogs, dewPointDataList, quantityBeginIndex);
            result.addAll(calcEndColdCapacity(totalGroup, dataLogs, node.getRatedAirVolume()));
        }

        return calcEndColdCapacity(result);
    }

    private List<DatalogValue> calcEndColdCapacity(List<DatalogValue> dataLogs) {
        List<DatalogValue> result = new ArrayList<>();
        Map<Long, List<DatalogValue>> dataLogMap = dataLogs.stream().collect(Collectors.groupingBy(DatalogValue::getTime));
        dataLogMap.forEach((time, val) -> {
            Double tmp = val.stream().filter(it -> Objects.nonNull(it.getValue())).mapToDouble(DatalogValue::getValue).sum();
            DatalogValue e = new DatalogValue();
            e.setTime(time);
            e.setValue(tmp);
            result.add(e);
        });

        return result;
    }

    private List<DatalogValue> calcEndColdCapacity(int totalGroup, List<DataLogDataWithGroup> dataLogs, Double ratedAirVolume) {
        List<DatalogValue> result = new ArrayList<>();
        Map<Long, List<DataLogDataWithGroup>> dataLogMap = dataLogs.stream().collect(Collectors.groupingBy(DatalogValue::getTime));
        dataLogMap.forEach((time, val) -> {
            Double tmp = calcSingleTimeEndColdCapacity(totalGroup, val, ratedAirVolume);
            if (Objects.nonNull(tmp)) {
                DatalogValue e = new DatalogValue();
                e.setTime(time);
                e.setValue(tmp);
                result.add(e);
            }
        });

        return result;
    }

    private Double calcSingleTimeEndColdCapacity(int totalGroup, List<DataLogDataWithGroup> val, Double ratedAirVolume) {
        if (val.size() != totalGroup) {
            return null;
        }

        // 判空
        long count = val.stream().filter(it -> Objects.isNull(it.getValue())).count();
        if (count > 0) {
            return null;
        }

        // 计算冷量：Q*(h1-h3)*1.2/3600
        val.sort((v1, v2) -> CommonUtils.sort(v1.getGroup(), v2.getGroup(), true));
        return ratedAirVolume * (val.get(0).getValue() - val.get(1).getValue()) * coldEndCapacityConfig.getSpecificVolume() / coldEndCapacityConfig.getCoef();
    }

    private Map<Integer, List<TrendDataVo>> queryQuantityDataList(Long st, Long et, int quantityBeginIndex, List<BaseVo> airConditioners) {
        QuantityDataBatchSearchVo aggregationDataBatch = new QuantityDataBatchSearchVo();
        aggregationDataBatch.setDataTypeId(EnumDataTypeId.REALTIME.getId());
        aggregationDataBatch.setAggregationCycle(AggregationCycle.ONE_DAY);
        aggregationDataBatch.setStartTime(st);
        aggregationDataBatch.setEndTime(et);
        aggregationDataBatch.setQuantitySettings(getQuantitySetting(quantityBeginIndex));
        aggregationDataBatch.setNodes(airConditioners);
        Map<Integer, List<TrendDataVo>> dataLogResult;
        try {
            dataLogResult = quantityManageService.queryDataLogBatch(aggregationDataBatch);
        } catch (Exception e) {
            log.error("查询物理量数据异常，入参：{}", JsonTransferUtils.toJSONString(aggregationDataBatch));
            throw new ValidationException("查询物理量数据异常");
        }
        return dataLogResult;
    }

    /**
     * 查询所有的空调节点
     *
     * @param nodes 父节点
     * @return
     */
    private List<BaseVo> queryAirConditioners(Collection<BaseVo> nodes) {
        List<Map<String, Object>> maps = nodeDao.queryChildNode(nodes, NodeLabelDef.AIR_CONDITIONER);
        if (CollectionUtils.isEmpty(maps)) {
            return Collections.emptyList();
        }

        return resolveChildren(maps);
    }

    private List<BaseVo> resolveChildren(List<Map<String, Object>> maps) {
        List<Map<String, Object>> airConditionerMapList = new ArrayList<>();
        for (Map<String, Object> map : maps) {
            List<Map<String, Object>> children = QueryResultContentTaker.getChildren(map);
            if (CollectionUtils.isNotEmpty(children)) {
                airConditionerMapList.addAll(children);
            }
        }
        return JsonTransferUtils.transferList(airConditionerMapList, BaseVo.class);
    }

    private void addDatalogValue(List<DataLogDataWithGroup> result, List<DatalogValue> dataList, Integer group) {
        for (DatalogValue DatalogValue : dataList) {
            result.add(new DataLogDataWithGroup(DatalogValue, group));
        }
    }

    private List<QuantitySearchVo> getQuantitySetting(int index) {
        List<QuantitySearchVo> list = new ArrayList<>();
        // 机组风量


        // 新风工况焓值
        list.add(new QuantitySearchVo(index++,
                20,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.SUPPLY,
                EnergyTypeDef.HEAT));

        // 表冷露点焓值
        list.add(new QuantitySearchVo(index,
                20,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.RETURN,
                EnergyTypeDef.HEAT));

        return list;
    }
}

