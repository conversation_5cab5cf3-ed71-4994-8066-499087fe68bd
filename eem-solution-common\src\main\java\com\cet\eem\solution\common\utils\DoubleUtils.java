package com.cet.eem.solution.common.utils;

import lombok.experimental.UtilityClass;

import java.util.Objects;

/**
 * <AUTHOR> (2024/6/24 20:18)
 */
@UtilityClass
public class DoubleUtils {
    private static final double EPSILON = 0.00001;

    public static boolean compareTwoDouble(Double a, Double b) {
        if (Objects.isNull(a) || Objects.isNull(b)) {
            return false;
        }
        return (Math.abs(a - b) < EPSILON);
    }

    /**
     * 保留指定小数位的 double 值。
     *
     * @param value  要处理的 double 值
     * @param places 保留的小数位数
     * @return 保留指定小数位的 double 值
     */
    public static Double round(Double value, int places) {
        if (Objects.isNull(value)) {
            return null;
        }

        double scale = Math.pow(10, places);
        return Math.round(value * scale) / scale;
    }

    public static Double roundTwo(Double value) {
        return round(value, 2);
    }
}