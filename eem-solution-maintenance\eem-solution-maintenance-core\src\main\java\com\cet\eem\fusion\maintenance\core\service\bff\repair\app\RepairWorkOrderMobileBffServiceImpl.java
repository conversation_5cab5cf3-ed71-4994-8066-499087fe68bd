package com.cet.eem.fusion.maintenance.core.service.bff.repair.app;

import com.cet.eem.auth.service.AuthUtils;
import com.cet.eem.bll.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.bll.maintenance.model.maintance.WorkOrderVo;
import com.cet.eem.bll.maintenance.model.workorder.app.WorkOrderCountDto;
import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.bll.maintenance.service.bff.repair.RepairWorkOrderBffService;
import com.cet.eem.bll.maintenance.service.inspection.InspectorService;
import com.cet.eem.bll.maintenance.service.repair.app.RepairWorkOrderMobileService;
import com.cet.eem.bll.maintenance.utils.InspectorUserCheckUtils;
import com.cet.eem.bll.maintenance.utils.WorkSheetStatusUtils;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/6/3
 */
@Service
public class RepairWorkOrderMobileBffServiceImpl implements RepairWorkOrderMobileBffService {
    @Autowired
    AuthUtils authUtils;

    @Autowired
    RepairWorkOrderBffService repairWorkOrderBffService;

    @Autowired
    RepairWorkOrderMobileService repairWorkOrderMobileService;

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    WorkSheetStatusUtils workSheetStatusUtils;

    @Autowired
    InspectorUserCheckUtils inspectorUserCheckUtils;

    @Autowired
    InspectorService inspectorService;

    @Override
    public List<WorkOrderCountDto> queryWorkOrder(Page page) {
        // 查询用户信息，获取用户所在的班组信息
        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        Long teamId = inspectorUserCheckUtils.getAndCheckTeamId(null, user);
        boolean inspectUser = inspectorService.isInspectUser(user);

        List<WorkOrderVo> workOrderDtoList = repairWorkOrderMobileService.queryWorkOrder(user, teamId,inspectUser, WorkOrderVo.class);
        List<WorkOrderCountDto> workOrderCount = countWorkOrderByStatus(workOrderDtoList, page);

        List<WorkOrderVo> list = new ArrayList<>();
        workOrderCount.forEach(it -> {
            if (CollectionUtils.isNotEmpty(it.getWorkOrders())) {
                for (InspectionWorkOrderDto workOrder : it.getWorkOrders()) {
                    list.add((WorkOrderVo) workOrder);
                }
            }
        });
        repairWorkOrderBffService.assemblyWholeWorkOrderList(list, user.getTenantId(), user);
        return workOrderCount;
    }

    private List<WorkOrderCountDto> countWorkOrderByStatus(List<WorkOrderVo> workOrderList, Page page) {
        if (page == null) {
            page = new Page(0, Integer.MAX_VALUE);
        }
        List<WorkOrderCountDto> workOrderDetails = new ArrayList<>();
        Map<Integer, String> workSheetStatusMap = workSheetStatusUtils.getWorkSheetStatusMapByTaskType(WorkSheetTaskType.REPAIR);
        Map<Integer, List<InspectionWorkOrderDto>> workStatusMap = workOrderList.stream().collect(Collectors.groupingBy(InspectionWorkOrderDto::getWorkSheetStatus));
        Page finalPage = page;
        workStatusMap.forEach((key, val) -> {
            WorkOrderCountDto workOrderCountDto = new WorkOrderCountDto();
            workOrderCountDto.setWorkOrderStatus(key);
            workOrderCountDto.setWorkOrderStatusName(workSheetStatusMap.get(key));
            workOrderCountDto.setCount(val.size());
            List<InspectionWorkOrderDto> tmpList = val.stream().sorted(((v1, v2) -> {
                if (v1.getTaskLevel() == null) {
                    return 1;
                }

                if (v2.getTaskLevel() == null) {
                    return -1;
                }
                int sort = CommonUtils.sort(v1.getTaskLevel(), v2.getTaskLevel(), true);
                if (sort != 0) {
                    return sort;
                }

                return CommonUtils.sort(v1.getCreateTime(), v2.getCreateTime(), true);
            })).skip(finalPage.getIndex()).limit(finalPage.getLimit()).collect(Collectors.toList());
            workOrderCountDto.setWorkOrders(tmpList);

            workOrderDetails.add(workOrderCountDto);
        });
        return workOrderDetails;
    }
}
