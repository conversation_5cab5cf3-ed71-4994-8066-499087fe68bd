package com.cet.eem.fusion.maintenance.core.controller.inspect;

import com.cet.eem.maintenanceservice.controller.bff.inspect.InspectionPlanBffController;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName : InspectionPlanController
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-22 09:19
 */
@Api(value = "/eem/v1/inspect/plan", tags = "巡检计划管理")
@RequestMapping(value = "/eem/v1/inspect/plan")
@RestController
@Validated
public class InspectionPlanController extends InspectionPlanBffController {

}
