package com.cet.eem.fusion.maintenance.core.dao.handover;

import com.cet.eem.bll.maintenance.model.handover.ShiftingDutyPo;
import com.cet.eem.bll.maintenance.model.handover.ShiftingDutySearchDto;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/3/23
 */
public interface HandOverDao extends BaseModelDao<ShiftingDutyPo> {
    /**
     * 查询交接班记录
     *
     * @param dto
     * @return
     */
    ResultWithTotal<List<Map<String, Object>>> queryShiftingDuty(ShiftingDutySearchDto dto);

    /**
     * 获取最新一条正在值班的记录
     *
     * @param teamId
     * @param projectId
     * @return
     */
    ShiftingDutyPo getLastOnDutyRecord(Long teamId, Long projectId, Long userId);

    /**
     * 查询最近一次的在值班记录
     *
     * @param teamId
     * @return
     */
    ShiftingDutyPo getHandoverPo(Long teamId, Long projectId);

    /**
     * 查询最近一次的交接班记录
     *
     * @param teamId
     * @param dutyStatus
     * @return
     */
    <T extends ShiftingDutyPo> T queryLastShiftDuty(Long teamId, Integer dutyStatus, Long projectId, Long userId, Class<T> clazz);

    ShiftingDutyPo queryLastShiftDuty(Long teamId, Long projectId);
}
