package com.cet.eem.fusion.maintenance.core.entity.devicemanage.template;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-12
 */
@Data
@AllArgsConstructor
@ApiModel("模板表")
@ModelLabel(ModelLabelDef.NODE_TEMPLATE)
public class AttributeTemplateDto extends EntityWithName {
    private Boolean openprotect;

    public AttributeTemplateDto() {
        this.modelLabel = ModelLabelDef.NODE_TEMPLATE;
    }

    public AttributeTemplateDto(String name, Boolean isopenprotect) {
        this();
        this.name = name;
        this.openprotect = isopenprotect;
    }
}
