package com.cet.eem.fusion.maintenance.core.entity.maintance;

import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.cet.eem.fusion.maintenance.core.entity.po.EventPlan;
import com.cet.eem.fusion.maintenance.core.entity.po.SparePartsReplaceRecord;
import com.cet.eem.fusion.maintenance.core.entity.workorder.MaintenanceContent;
import com.cet.eem.fusion.maintenance.core.entity.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.fusion.maintenance.core.entity.workorder.repair.RepairSourceIndexVo;
import com.cet.electric.workflow.common.model.node.config.UserTaskConfig;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/2
 */
@Getter
@Setter
public class WorkOrderVo extends InspectionWorkOrderDto {
    @ApiModelProperty("工单内容")
    private MaintenanceContent maintenanceContentObj;

    public MaintenanceContent getMaintenanceContentObj() {
        return JsonTransferUtils.parseObject(this.maintenanceContent, MaintenanceContent.class);
    }

    @ApiModelProperty("备件更换记录")
    private List<SparePartsReplaceRecord> sparePartsReplaceRecords;

    @ApiModelProperty("预案信息")
    private EventPlan eventPlan;

    @ApiModelProperty("设备归类名称")
    private String deviceClassificationName;

    @ApiModelProperty("事件归类名称")
    private String eventClassificationName;

    @ApiModelProperty("故障场景名称")
    private String faultScenariosName;

    @ApiModelProperty("故障预案")
    private List<EventPlan> eventPlans;

    @ApiModelProperty("工单当前所处的任务节点信息")
    private UserTaskConfig userTaskConfig;

    @ApiModelProperty("填报方式名称")
    private String fillFormTypeName;

    @ApiModelProperty("运值班组名称")
    private String inspectTeamName;
    @JsonProperty(WorkOrderDef.SOURCE_INDEX)
    private String sourceIndex;
    private RepairSourceIndexVo repairSourceIndexVo;
}
