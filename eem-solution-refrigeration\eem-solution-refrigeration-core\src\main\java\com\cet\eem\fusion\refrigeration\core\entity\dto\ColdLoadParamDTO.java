﻿package com.cet.eem.fusion.refrigeration.core.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : ColdLoadParam
 * @Description : 冷负荷预测算法查询
 * <AUTHOR> jiangzixuan
 * @Date: 2023-06-07 11:27
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ColdLoadParamDTO {
    @JsonProperty("logtime")
    private Long logTime;
    @JsonProperty("actual_humidity")
    private Double actualHumidity;
    @JsonProperty("actual_temp")
    private Double actualTemp;
    @JsonProperty("actual_power")
    private Double actualPower;
    @JsonProperty("actual_cooling_load")
    private Double actualCoolingLoad;
    @JsonProperty("fit_data")
    private List<ColdMachineData> coldMachineData;
}
