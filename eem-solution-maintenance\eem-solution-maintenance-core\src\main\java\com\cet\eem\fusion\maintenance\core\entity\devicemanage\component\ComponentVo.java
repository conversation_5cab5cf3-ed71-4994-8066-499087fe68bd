package com.cet.eem.fusion.maintenance.core.entity.devicemanage.component;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-04-25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ComponentVo extends Component {

    private String equipmentname;
    private Integer amount;


    public ComponentVo(String name, String code, String brand, String unit, String specification, Integer amount) {
        super(name, code, brand, unit, specification);
        this.amount = amount;
    }


    public ComponentVo(Long equipmentId, String equipmentname, Integer amount) {
        super(equipmentId);
        this.equipmentname = equipmentname;
        this.amount = amount;
    }
}
