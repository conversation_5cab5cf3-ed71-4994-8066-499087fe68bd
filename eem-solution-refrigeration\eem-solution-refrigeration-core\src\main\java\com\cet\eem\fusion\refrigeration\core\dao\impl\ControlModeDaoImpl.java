﻿package com.cet.eem.fusion.refrigeration.core.impl;

import com.cet.eem.fusion.refrigeration.core.dao.aioptimization.ControlModeDao;
import com.cet.eem.fusion.refrigeration.core.model.aioptimization.plc.ControlMode;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @ClassName : ControlModeDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-09-01 16:48
 */
@Repository
public class ControlModeDaoImpl extends ModelDaoImpl<ControlMode> implements ControlModeDao {

    @Override
    public ControlMode queryControlModeBySystemId(Long systemId) {
        LambdaQueryWrapper<ControlMode> wrapper = LambdaQueryWrapper.of(ControlMode.class);
        wrapper.eq(ControlMode::getSystemId, systemId);
        List<ControlMode> controlModes = this.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(controlModes)) {
            return controlModes.get(0);
        }
        return null;
    }
}
