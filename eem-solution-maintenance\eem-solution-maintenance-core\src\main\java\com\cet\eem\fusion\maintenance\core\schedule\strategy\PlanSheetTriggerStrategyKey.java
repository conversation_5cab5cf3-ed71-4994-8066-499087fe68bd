package com.cet.eem.fusion.maintenance.core.schedule.strategy;

/**
 * @ClassName : PlanSheetTriggerStrategyKey
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-23 14:52
 */
public class PlanSheetTriggerStrategyKey {
    private PlanSheetTriggerStrategyKey(){};
    public static final String ONLY_ONCE = "aggregationCycle_18";
    public static final String ONE_DAY = "aggregationCycle_12";
    public static final String ONE_WEEK = "aggregationCycle_13";
    public static final String ONE_MONTH = "aggregationCycle_14";
    public static final String HALF_YEAR = "aggregationCycle_16";
    public static final String ONE_YEAR = "aggregationCycle_17";
    public static final String CUSTOM = "aggregationCycle_0";

    public static String generatorStrategyKey(Integer aggregationCycle) {
        return "aggregationCycle_" + aggregationCycle;
    }
}
