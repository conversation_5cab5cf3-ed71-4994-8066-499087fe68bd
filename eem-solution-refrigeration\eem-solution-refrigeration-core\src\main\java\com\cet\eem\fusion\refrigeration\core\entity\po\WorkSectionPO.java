﻿package com.cet.eem.fusion.refrigeration.core.entity.po;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.fusion.refrigeration.core.model.def.ColdOptimizationLabelDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @ClassName : WorkSection
 * @Description : 工作区间
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-07 19:38
 */
@Data
@ModelLabel(ColdOptimizationLabelDef.WORK_SECTION)
public class WorkSectionPO extends BaseEntity {
    @JsonProperty(ColumnDef.C_OBJECT_ID)
    private Long objectId;
    @JsonProperty(ColumnDef.C_OBJECT_Label)
    private String objectLabel;
    private Double max;
    private Double min;
    /**
     * 区间数据类型
     */
    @JsonProperty(ColdOptimizationLabelDef.SECTION_DATA_TYPE)
    private Integer sectionDataType;
    /**
     * 区间类型
     */
    @JsonProperty(ColdOptimizationLabelDef.SECTION_TYPE)
    private Integer sectionType;

    public WorkSection() {
        this.modelLabel = ColdOptimizationLabelDef.WORK_SECTION;
    }
}
