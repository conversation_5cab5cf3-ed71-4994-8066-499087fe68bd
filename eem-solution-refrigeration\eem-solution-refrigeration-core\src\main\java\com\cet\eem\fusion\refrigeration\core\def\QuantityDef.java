﻿package com.cet.eem.fusion.refrigeration.core.def;

import com.cet.eem.bll.common.def.quantity.FrequencyDef;
import com.cet.eem.bll.common.def.quantity.PhasorDef;
import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;
import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;
import com.cet.eem.common.constant.EnergyTypeDef;
import com.cet.eem.quantity.model.quantity.QuantitySearchVo;

/**
 * @ClassName : QuantityDef
 * @Description : 物理量通用
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-07 16:39
 */

public class QuantityDef {
    /**
     * 冷冻水供水物理量
     *
     * @return
     */
    public static QuantitySearchVo getFreezingWaterForSupplyQuantitySetting() {
        return new QuantitySearchVo(6007206,
                QuantityCategoryDef.TEMP,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.SUPPLY,
                47);
    }

    /**
     * 冷冻水回水物理量
     *
     * @return
     */
    public static QuantitySearchVo getFreezingWaterForReturnQuantitySetting() {
        return new QuantitySearchVo(6007207,
                QuantityCategoryDef.TEMP,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.RETURN,
                47);
    }


    /**
     * 冷冻水回水物理量
     *
     * @return
     */
    public QuantitySearchVo getFreezingWaterForReturnStandardQuantitySetting() {

        return new QuantitySearchVo(6000439,
                QuantityCategoryDef.TEMP,
                192,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                47);
    }

    /**
     * 冷却水供水物理量
     *
     * @return
     */
    public static QuantitySearchVo getCoolingWaterForSupplyQuantitySetting() {
        return new QuantitySearchVo(6007203,
                QuantityCategoryDef.TEMP,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.SUPPLY,
                48);
    }

    /**
     * 冷却水回水物理量
     *
     * @return
     */
    public static QuantitySearchVo getCoolingWaterForReturnQuantitySetting() {
        return new QuantitySearchVo(6007204,
                QuantityCategoryDef.TEMP,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.RETURN,
                48);
    }

    /**
     * 冷却水供水标准物理量
     *
     * @return
     */
    public QuantitySearchVo getCoolingWaterForSupplyStandaQuantitySetting() {
        return new QuantitySearchVo(6000440,
                QuantityCategoryDef.TEMP,
                191,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                48);
    }

    /**
     * 冷却水回水标准物理量
     *
     * @return
     */
    public QuantitySearchVo getCoolingWaterForReturnStandaQuantitySetting() {
        return new QuantitySearchVo(6000441,
                QuantityCategoryDef.TEMP,
                192,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                48);
    }


    /**
     * 冷水主机功率
     *
     * @return
     */
    public static QuantitySearchVo getMainPowerQuantitySetting() {
        return new QuantitySearchVo(2000004,
                QuantityCategoryDef.POWER,
                QuantityTypeDef.POWER,
                FrequencyDef.NONE,
                PhasorDef.TOTAL,
                EnergyTypeDef.ELECTRIC);
    }

    /**
     * 冷却塔功率
     *
     * @return
     */
    public QuantitySearchVo getTowerPowerQuantitySetting() {

        return new QuantitySearchVo(2000004,
                QuantityCategoryDef.POWER,
                QuantityTypeDef.POWER,
                FrequencyDef.NONE,
                PhasorDef.TOTAL,
                EnergyTypeDef.ELECTRIC);
    }

    /**
     * 冷冻水泵功率
     *
     * @return
     */
    public QuantitySearchVo getFreezingPumpPowerQuantitySetting() {
        return new QuantitySearchVo(2000004,
                QuantityCategoryDef.POWER,
                QuantityTypeDef.POWER,
                FrequencyDef.NONE,
                PhasorDef.TOTAL,
                EnergyTypeDef.ELECTRIC);
    }

    /**
     * 冷却水泵功率
     *
     * @return
     */
    public QuantitySearchVo getCoolingPumpPowerQuantitySetting() {
        return new QuantitySearchVo(2000004,
                QuantityCategoryDef.POWER,
                QuantityTypeDef.POWER,
                FrequencyDef.NONE,
                PhasorDef.TOTAL,
                EnergyTypeDef.ELECTRIC);
    }


    /**
     * 冷水主机冷机负荷物理量
     *
     * @return
     */
    public static QuantitySearchVo getMainCoolingLoadQuantitySetting() {
        return new QuantitySearchVo(6008010,
                QuantityCategoryDef.POWER,
                QuantityTypeDef.POWER,
                FrequencyDef.NONE,
                PhasorDef.TOTAL,
                EnergyTypeDef.COLD);
    }

    /**
     * 获得温度实际值
     *
     * @return
     */
    public static QuantitySearchVo getAiPredictForTempQuantitySetting() {
        return new QuantitySearchVo(6002006,
                QuantityCategoryDef.TEMP,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ENERGY);
    }

    /**
     * 获得湿度实际值
     *
     * @return
     */
    public static QuantitySearchVo getAiPredictForHumQuantitySetting() {
        return new QuantitySearchVo(6005000,
                QuantityCategoryDef.HUMIDITY,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ENERGY);
    }

    /**
     * 冷冻水供回水压差设定值
     *
     * @return
     */
    public static QuantitySearchVo getDifferentialPressureSetting() {

        return new QuantitySearchVo(6000456,
                QuantityCategoryDef.THRESHOLD,
                QuantityTypeDef.INSTANTANEOUS,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.COLD);
    }

    /**
     * 冷冻水出水温度设定值
     *
     * @return
     */
    public static QuantitySearchVo getFreezingWaterForSupplyStandardQuantitySetting() {
        return new QuantitySearchVo(6000438,
                QuantityCategoryDef.TEMP,
                191,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                47);
    }

    /**
     * 流入冷却水泵的水温
     *
     * @return
     */
    public static QuantitySearchVo getInPumpTemp() {

        return new QuantitySearchVo(6007204,
                QuantityCategoryDef.TEMP,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.RETURN,
                EnergyTypeDef.ENERGY);
    }

    /**
     * 末端回水温度
     *
     * @return
     */
    public static QuantitySearchVo getEndReturnTemp() {

        return new QuantitySearchVo(6000451,
                QuantityCategoryDef.TEMP,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                30,
                EnergyTypeDef.COLD);
    }

    /**
     * 冷冻水供水标准物理量
     *
     * @return
     */
    public static QuantitySearchVo getFreezingWaterPipelineForSupplyStandardQuantitySetting() {
        return new QuantitySearchVo(6000438,
                QuantityCategoryDef.TEMP,
                191,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.COLD);
    }

    /**
     * 冷冻水管道瞬时热流量
     *
     * @return
     */
    public static QuantitySearchVo getFreezingWaterPipelineForStream() {
        return new QuantitySearchVo(6008008,
                21,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                11,
                EnergyTypeDef.COLD);
    }

    /**
     * 冷冻水回水物理量
     *
     * @return
     */
    public static QuantitySearchVo getFreezingWaterPipelineForReturnStandardQuantitySetting() {

        return new QuantitySearchVo(6000439,
                QuantityCategoryDef.TEMP,
                192,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.COLD);
    }

    /**
     * 末端供水温度
     *
     * @return
     */
    public static QuantitySearchVo getEndSupplyTemp() {

        return new QuantitySearchVo(6000450,
                QuantityCategoryDef.TEMP,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                29,
                EnergyTypeDef.COLD);
    }

    /**
     * 末端空调开机时间
     *
     * @return
     */
    public QuantitySearchVo getEndAirStartTime() {

        return new QuantitySearchVo(6004008,
                QuantityCategoryDef.DEVICE_WORK_TIME,
                189,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ENERGY);
    }

    /**
     * 设备状态
     *
     * @return
     */
    public static QuantitySearchVo getDeviceStatus() {

        QuantitySearchVo quantitySearchVo = new QuantitySearchVo(9014154,
                QuantityCategoryDef.STATUS,
                QuantityTypeDef.INSTANTANEOUS,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ENERGY);
        quantitySearchVo.setControlMeanType(1);
        quantitySearchVo.setStateMeanType(6);
        return quantitySearchVo;
    }

    /**
     * 设备功率
     *
     * @return
     */
    public QuantitySearchVo getDevicePower() {

        return new QuantitySearchVo(2000004,
                QuantityCategoryDef.POWER,
                QuantityTypeDef.POWER,
                FrequencyDef.NONE,
                PhasorDef.TOTAL,
                EnergyTypeDef.ELECTRIC);
    }

    /**
     * 冷冻泵设备频率
     *
     * @return
     */
    public static QuantitySearchVo getDeviceFrequency() {

        return new QuantitySearchVo(6000444,
                QuantityCategoryDef.VOLTAGE,
                188,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ENERGY);
    }

    /**
     * 冷却泵设备频率
     *
     * @return
     */
    public static QuantitySearchVo getFreezingDeviceFrequency() {

        return new QuantitySearchVo(6000448,
                QuantityCategoryDef.VOLTAGE,
                13,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                48);
    }

    /**
     * 冷冻水供水总管流量
     *
     * @return
     */
    public static QuantitySearchVo getColdPipelineStream() {

        return new QuantitySearchVo(6008004,
                21,
                2,
                FrequencyDef.NONE,
                29,
                EnergyTypeDef.COLD);
    }

    /**
     * 冷冻水供水总管流量
     *
     * @return
     */
    public static QuantitySearchVo getDevicePowerEnergy() {

        return new QuantitySearchVo(4000004,
                QuantityCategoryDef.ELECTRIC,
                QuantityTypeDef.P_INTG_POS,
                FrequencyDef.NONE,
                PhasorDef.TOTAL,
                EnergyTypeDef.ELECTRIC);
    }

    /**
     * 冷却塔频率
     *
     * @return
     */
    public static QuantitySearchVo getDeviceTowerEnergy() {

        return new QuantitySearchVo(6000449,
                QuantityCategoryDef.VOLTAGE,
                QuantityTypeDef.FREQUENCY,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ENERGY);
    }

    public static QuantitySearchVo getMachineI() {
        return new QuantitySearchVo(1000212,
                QuantityCategoryDef.CURRENT,
                185,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ELECTRIC);
    }

    public static QuantitySearchVo getNumberOfStart() {
        return new QuantitySearchVo(6004002,
                29,
                179,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ENERGY);
    }

    public static QuantitySearchVo getRunningTime() {
        return new QuantitySearchVo(6004004,
                25,
                181,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ENERGY);
    }

    public static QuantitySearchVo getFaultSignal() {
        QuantitySearchVo quantitySearchVo = new QuantitySearchVo(9014139,
                17,
                21,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ENERGY);
        quantitySearchVo.setControlMeanType(1);
        quantitySearchVo.setStateMeanType(6);
        return quantitySearchVo;

    }

    public static QuantitySearchVo getPlcAi() {
        QuantitySearchVo quantitySearchVo = new QuantitySearchVo(9014192,
                17,
                1,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ENERGY);
        quantitySearchVo.setControlMeanType(1);
        quantitySearchVo.setStateMeanType(22);
        return quantitySearchVo;

    }

    public static QuantitySearchVo getPlcAuto() {
        QuantitySearchVo quantitySearchVo = new QuantitySearchVo(9014188,
                17,
                1,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ENERGY);
        quantitySearchVo.setControlMeanType(1);
        quantitySearchVo.setStateMeanType(20);
        return quantitySearchVo;

    }

    public static QuantitySearchVo getPlcManual() {
        QuantitySearchVo quantitySearchVo = new QuantitySearchVo(9014190,
                17,
                1,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ENERGY);
        quantitySearchVo.setControlMeanType(1);
        quantitySearchVo.setStateMeanType(21);
        return quantitySearchVo;

    }

    public static QuantitySearchVo getMachineDelayStart() {
        QuantitySearchVo quantitySearchVo = new QuantitySearchVo(6000696,
                25,
                194,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.REFRIGERANT_WATER);
        return quantitySearchVo;

    }

    public static QuantitySearchVo getFreezingPumpDelayStart() {
        QuantitySearchVo quantitySearchVo = new QuantitySearchVo(6000698,
                25,
                194,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                47);
        return quantitySearchVo;

    }

    public static QuantitySearchVo getCoolingPumpDelayStart() {
        QuantitySearchVo quantitySearchVo = new QuantitySearchVo(6000700,
                25,
                194,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                48);
        return quantitySearchVo;

    }

    public static QuantitySearchVo getMachineDelayStop() {
        QuantitySearchVo quantitySearchVo = new QuantitySearchVo(6000697,
                25,
                195,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.REFRIGERANT_WATER);
        return quantitySearchVo;

    }

    public static QuantitySearchVo getFreezingPumpDelayStop() {
        QuantitySearchVo quantitySearchVo = new QuantitySearchVo(6000699,
                25,
                195,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                47);
        return quantitySearchVo;

    }

    public static QuantitySearchVo getCoolingPumpDelayStop() {
        QuantitySearchVo quantitySearchVo = new QuantitySearchVo(6000701,
                25,
                195,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                48);
        return quantitySearchVo;

    }
    public static QuantitySearchVo getMachineStatus() {
        QuantitySearchVo quantitySearchVo = new QuantitySearchVo(9014137,
                17,
                21,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                EnergyTypeDef.ENERGY);
        return quantitySearchVo;

    }

}
