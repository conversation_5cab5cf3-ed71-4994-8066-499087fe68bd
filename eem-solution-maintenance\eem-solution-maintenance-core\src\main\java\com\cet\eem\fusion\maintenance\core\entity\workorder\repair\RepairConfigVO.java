package com.cet.eem.fusion.maintenance.core.entity.workorder.repair;

import com.cet.electric.modelservice.common.entity.IdTextPair;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> jiangzixuan
 * @classname : RepairConfigVO
 * @description : 增加的2个字段的配置文件读取
 * @date: 2025-01-07 14:21
 */
@Getter
@Setter
public class RepairConfigVO {
    /**
     * 是否启用新增的2个字段
     */
    private Boolean addEnable;
    /**
     * 维修类型
     */
    private List<IdTextPair> repairCategory;
    /**
     * 维修专业类别
     */
    private List<IdTextPair> professionalCategory;
}