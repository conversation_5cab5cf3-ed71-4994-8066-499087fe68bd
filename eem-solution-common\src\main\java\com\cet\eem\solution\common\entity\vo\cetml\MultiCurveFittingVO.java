package com.cet.eem.solution.common.entity.vo.cetml;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>  (2025/6/27 12:15)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MultiCurveFittingVO {
    @ApiModelProperty("拟合函数类型 1:线性 2:幂拟合 3:指数拟合 4:对数拟合")
    private Double type;

    @ApiModelProperty("拟合类型中文")
    private String name;

    @ApiModelProperty("拟合结果公式")
    private String equation;

    @ApiModelProperty("参数a")
    private Double a;

    @ApiModelProperty("参数b")
    private Double b;

    @ApiModelProperty("决定系数R")
    @JsonProperty("r2_score")
    private Double r2Score;

    @ApiModelProperty("调整后决定系数")
    @JsonProperty("adj_r2_score")
    private Double adjScore;

    @ApiModelProperty("综合评分70%权重给调整R²，30%权重给MAPE评分")
    @JsonProperty("composite_score")
    private Double compositeScore;

    @ApiModelProperty("均方根误差")
    private Double rmse;

    @ApiModelProperty("平均绝对误差")
    private Double mae;

    @ApiModelProperty("平均绝对百分比误差")
    private Double mape;
}
