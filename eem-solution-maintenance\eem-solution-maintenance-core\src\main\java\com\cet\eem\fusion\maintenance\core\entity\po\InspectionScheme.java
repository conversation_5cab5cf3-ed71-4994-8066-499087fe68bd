package com.cet.eem.fusion.maintenance.core.entity.po;

import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : InspectionScheme
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-13 19:58
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.INSPECTION_SCHEME)
public class InspectionScheme extends EntityWithName {

    /**
     * 创建时间
     */
    @JsonProperty("createtime")
    private Long createTime;

    /**
     * 创建用户
     */
    @JsonProperty("createuser")
    private Long createUser;

    /**
     * 项目id
     */
    @JsonProperty("projectid")
    private Long projectId;

    public InspectionScheme() {
        this.modelLabel = ModelLabelDef.INSPECTION_SCHEME;
    }

    public InspectionScheme(Long id, String name) {
        this.modelLabel = ModelLabelDef.INSPECTION_SCHEME;
        this.id = id;
        this.name = name;
    }
}
