package com.cet.eem.fusion.maintenance.core.entity.devicemanage;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import lombok.Data;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-19
 */
@Data
@ModelLabel(ModelLabelDef.POWER_DIS_CABINET)
public class PowerDisCabinet extends EntityWithName {
    private String model;

    public PowerDisCabinet() {
        this.modelLabel = ModelLabelDef.POWER_DIS_CABINET;
    }
}
