package com.cet.eem.fusion.maintenance.core.entity.workorder.maintenance;

import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.cet.eem.fusion.maintenance.core.entity.po.MaintenanceExtend;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName : AddMaintenanceWorkOrderRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-05-21 09:55
 */
@Getter
@Setter
@ApiModel(description = "新增维保工单")
public class AddMaintenanceWorkOrderRequest {
    @ApiModelProperty("维保项目")
    @JsonProperty("maintenanceextend")
    private MaintenanceExtend maintenanceExtend;

    @ApiModelProperty("人员数量")
    private Integer personNumber;

    @ApiModelProperty("计划执行时间")
    @JsonProperty(WorkOrderDef.EXECUTE_TIME_PLAN)
    private Long executeTimePlan;

    @ApiModelProperty("预计耗时")
    @JsonProperty(WorkOrderDef.TIME_CONSUME_PLAN)
    private Long timeConsumePlan;

    @ApiModelProperty("责任班组")
    @JsonProperty(WorkOrderDef.TEAM_ID)
    private Long teamId;

    @ApiModelProperty("维保目标")
    @NotNull(message = "维保目标不允许为空！")
    private List<BaseVo> objects;

    /**
     * @deprecated 接口调整，不在需要
     */
    @ApiModelProperty("维保目标id")
    @JsonProperty(ColumnDef.C_OBJECT_ID)
    @Deprecated
    private Long objectId;

    /**
     * @deprecated 接口调整，不在需要
     */
    @ApiModelProperty("维保目标label")
    @JsonProperty(ColumnDef.C_OBJECT_Label)
    @Deprecated
    private String objectLabel;

    @ApiModelProperty("等级")
    @JsonProperty(WorkOrderDef.TASK_LEVEL)
    private Integer taskLevel;

    @ApiModelProperty("系统安全措施")
    @JsonProperty(ColumnDef.SAFETY_MEASURE)
    private String safetyMeasure;
}
