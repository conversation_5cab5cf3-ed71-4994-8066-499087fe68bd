package com.cet.eem.fusion.maintenance.core.entity.workorder;

import com.cet.electric.workflow.common.model.params.ManyUserTaskParams;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/6
 */
@Getter
@Setter
@ApiModel(description = "工单表单提交参数")
public class WorkOrderFormBatchSubmitParam {
    @ApiModelProperty("工单编号")
    private List<String> codes;

    @ApiModelProperty("表单通用参数")
    private ManyUserTaskParams params;
}
