﻿package com.cet.eem.fusion.refrigeration.core.controller;

import com.cet.eem.auth.aspect.OperationPermission;
import com.cet.eem.bll.common.def.OperationAuthDef;
import com.cet.eem.bll.common.log.annotation.OperationLog;
import com.cet.eem.bll.common.log.constant.EEMOperationLogType;
import com.cet.eem.bll.common.log.constant.EnumOperationSubType;
import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.fusion.refrigeration.core.model.aioptimization.WorkSection;
import com.cet.eem.fusion.refrigeration.core.model.aioptimization.plc.ControlMode;
import com.cet.eem.fusion.refrigeration.core.model.config.MesShift;
import com.cet.eem.fusion.refrigeration.core.model.config.ParameterConfig;
import com.cet.eem.fusion.refrigeration.core.model.config.system.ConditionParam;
import com.cet.eem.fusion.refrigeration.core.model.config.system.ParamConditionConfigVo;
import com.cet.eem.fusion.refrigeration.core.model.config.system.ParameterConfigVo;
import com.cet.eem.fusion.refrigeration.core.model.def.ColdOperationAuthDef;
import com.cet.eem.fusion.refrigeration.core.model.weather.PumpVo;
import com.cet.eem.fusion.refrigeration.core.service.aioptimization.AiPlcControlService;
import com.cet.eem.fusion.refrigeration.core.service.aioptimization.ParameterConfigService;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.xml.bind.ValidationException;
import java.io.IOException;
import java.util.List;

/**
 * @ClassName : ParameterConfigController
 * @Description : 制冷参数配置
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-16 09:55
 */
@Api(value = "ParameterConfigController", tags = {"制冷参数配置接口"})
@RestController
@RequestMapping(value = "/eem/v1/parameterConfig")
public class ParameterConfigController {
    @Autowired
    ParameterConfigService parameterConfigService;
    @Autowired
    AiPlcControlService aiPlcControlService;

    @ApiOperation(value = "写入工作区间配置")
    @PutMapping(value = "/workSection", produces = "application/json")
    public Result<Object> writeWorkSection(@RequestParam String objectLabel, @RequestParam Long objectId, @RequestBody List<WorkSection> workSections) {
        parameterConfigService.writeWorkSection(objectId, objectLabel, workSections);
        return Result.ok();
    }

    @ApiOperation(value = "查询工作区间配置")
    @PostMapping(value = "/workSection", produces = "application/json")
    public Result<List<WorkSection>> queryWorkSection(@RequestBody BaseVo baseVo) {
        return Result.ok(parameterConfigService.queryWorkSection(baseVo));
    }

    @ApiOperation(value = "删除工作区间配置")
    @DeleteMapping(value = "/workSection", produces = "application/json")
    public Result<Object> deleteWorkSection(@RequestBody List<Long> ids) {
        parameterConfigService.deleteWorkSection(ids);
        return Result.ok();
    }

    @ApiOperation(value = "批量删除工作区间配置")
    @DeleteMapping(value = "/workSections", produces = "application/json")
    public Result<Object> deleteWorkSections(@RequestBody List<BaseVo> nodes) {
        parameterConfigService.deleteWorkSections(nodes);
        return Result.ok();
    }

    @ApiOperation(value = "下载运行效率曲线")
    @PostMapping(value = "/exportOperatingEfficiencyCurve", produces = "application/json")
    public Result<Object> exportOperatingEfficiencyCurve(@RequestBody PumpVo baseVo, HttpServletResponse response) {
        parameterConfigService.exportOperatingEfficiencyCurve(baseVo, response);
        return Result.ok();
    }

    @ApiOperation(value = "导入运行效率曲线")
    @PostMapping(value = "/importOperatingEfficiencyCurve", produces = "application/json")
    public Result<Object> importOperatingEfficiencyCurve(@RequestParam Long id, @RequestParam String label, MultipartFile file) throws IOException, ValidationException {
        parameterConfigService.importOperatingEfficiencyCurve(id, label, file);
        return Result.ok();
    }

    @ApiOperation(value = "删除运行效率曲线")
    @DeleteMapping(value = "/operatingEfficiencyCurve", produces = "application/json")
    public Result<Object> deleteOperatingEfficiencyCurve(@RequestBody List<BaseVo> baseVo) {
        parameterConfigService.deleteOperatingEfficiencyCurve(baseVo);
        return Result.ok();
    }

    @ApiOperation(value = "写入冷机运行约束条件")
    @OperationPermission(authNames = {ColdOperationAuthDef.MODEL_CONFIG_MACHINE_OPERATION_UPDATE})
    @OperationLog(operationType = EEMOperationLogType.AI_PREDICT_USE, subType = EnumOperationSubType.UPDATE, description = "【新增/修改冷机运行约束条件】")
    @PutMapping(value = "/machineOperationConstraints", produces = "application/json")
    public Result<ParameterConfigVo> writeMachineOperationConstraints(@RequestBody ParameterConfigVo config) {

        return Result.ok(parameterConfigService.writeMachineOperationConstraints(config));
    }

    @ApiOperation(value = "写入系统供水配置")
    @OperationPermission(authNames = {ColdOperationAuthDef.MODEL_CONFIG_SYSTEM_SUPPLY_WATER_UPDATE})
    @OperationLog(operationType = EEMOperationLogType.AI_PREDICT_USE, subType = EnumOperationSubType.UPDATE, description = "【新增/修改系统供水设置】")
    @PutMapping(value = "/systemSupplyWaterConfig", produces = "application/json")
    public Result<ParameterConfig> writeSystemSupplyWaterConfig(@RequestBody ParameterConfig config) {
        return Result.ok(parameterConfigService.writeSystemSupplyWaterConfig(config));
    }

    @ApiOperation(value = "查询参数配置")
    @PostMapping(value = "/paramConfig", produces = "application/json")
    public Result<ParamConditionConfigVo> queryParamConfig(@RequestParam Long systemId) {
        return Result.ok(parameterConfigService.queryParamConditionConfig(systemId));
    }

    @ApiOperation(value = "写入参数配置")
    @OperationPermission(authNames = {ColdOperationAuthDef.MODEL_CONFIG_ADD_SUB_CONDITION_UPDATE})
    @OperationLog(operationType = EEMOperationLogType.AI_PREDICT_USE, subType = EnumOperationSubType.UPDATE, description = "【新增/修改制冷设备加减机条件设置】")
    @PostMapping(value = "/conditionConfig", produces = "application/json")
    public Result<Object> queryParamConfig(@RequestBody List<ConditionParam> params) {
        parameterConfigService.writeAddAndSubConditionConfig(params);
        return Result.ok();
    }

    @ApiOperation(value = "查询mes班次配置")
    @PostMapping(value = "/shiftConfig", produces = "application/json")
    public Result<List<MesShift>> queryShiftConfig(@RequestBody BaseVo baseVo) {
        return Result.ok(parameterConfigService.queryMesShift(baseVo));
    }

    @ApiOperation(value = "写入mes班次配置")
    @OperationPermission(authNames = {ColdOperationAuthDef.MODEL_CONFIG_MES_SHIFT_UPDATE})
    @OperationLog(operationType = EEMOperationLogType.AI_PREDICT_USE, subType = EnumOperationSubType.UPDATE, description = "【新增/修改mes班次设置】")
    @PostMapping(value = "/shiftConfigWrite", produces = "application/json")
    public Result<List<MesShift>> queryShiftConfig(@RequestBody List<MesShift> mesShifts) {
        return Result.ok(parameterConfigService.writeMesShift(mesShifts, GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "查询ai模式配置")
    @PostMapping(value = "/controlMode", produces = "application/json")
    public Result<ControlMode> queryControlMode(@RequestParam Long systemId) {
        return Result.ok(parameterConfigService.queryControlMode(systemId, GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "写入ai模式配置")
    @OperationPermission(authNames = {ColdOperationAuthDef.MODEL_CONFIG_CONTRL_MODE_UPDATE})
    @OperationLog(operationType = EEMOperationLogType.AI_PREDICT_USE, subType = EnumOperationSubType.UPDATE, description = "【修改制冷控制设置】")
    @PostMapping(value = "/controlModeWrite", produces = "application/json")
    public Result<ControlMode> writeControlMode(@RequestBody ControlMode controlMode) {
        return Result.ok(parameterConfigService.writeControlMode(controlMode));
    }

    @ApiOperation(value = "校验控制方案")
    @PostMapping(value = "/checkAiStrategyControl", produces = "application/json")
    public Result<Object> checkAiStrategyControl(@RequestParam Long systemId, @RequestParam Long time) throws InterruptedException {
        aiPlcControlService.checkAiStrategyControl(systemId, time);
        return Result.ok();
    }

    @ApiOperation(value = "校验心跳方案")
    @PostMapping(value = "/checkHeartBeatl", produces = "application/json")
    public Result<Object> checkHeartBeatl() {
        aiPlcControlService.sendHeartBeat();
        return Result.ok();
    }
}
