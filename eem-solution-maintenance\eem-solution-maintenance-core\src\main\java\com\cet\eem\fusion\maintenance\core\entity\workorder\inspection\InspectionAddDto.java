package com.cet.eem.fusion.maintenance.core.entity.workorder.inspection;

import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.maintenance.core.def.WorkOrderDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 4/13/2021
 */
@Getter
@Setter
@ApiModel(description = "巡检工单新增/修改模型")
public class InspectionAddDto {
    @ApiModelProperty("巡检方案id")
    @JsonProperty(WorkOrderDef.INSPECTION_SCHEME_ID)
    @NotNull(message = "巡检方案id不允许为空！")
    private Long inspectionSchemeId;

    @ApiModelProperty("计划执行时间")
    @JsonProperty(WorkOrderDef.EXECUTE_TIME_PLAN)
    @NotNull(message = "计划执行时间不允许为空！")
    private LocalDateTime executeTimePlan;

    @ApiModelProperty("班组")
    @JsonProperty(WorkOrderDef.TEAM_ID)
    @NotNull(message = "班组信息不允许为空！")
    private Long teamId;

    @ApiModelProperty("预计耗时")
    @JsonProperty(WorkOrderDef.TIME_CONSUME_PLAN)
    @NotNull(message = "预计耗时不允许为空！")
    private Long timeConsumePlan;

    @ApiModelProperty("巡检目标id")
    @JsonProperty(ColumnDef.C_OBJECT_ID)
    @NotNull(message = "巡检目标id不允许为空！")
    private Long objectId;

    @ApiModelProperty("巡检目标label")
    @JsonProperty(ColumnDef.C_OBJECT_Label)
    @NotNull(message = "巡检目标label不允许为空！")
    private String objectLabel;

    @ApiModelProperty("签到点分组id")
    @JsonProperty(WorkOrderDef.SIGN_GROUP_ID)
    @NotNull(message = "签到点分组id不允许为空！")
    private Long signGroupId;

    @ApiModelProperty("项目id")
    @JsonProperty(ColumnDef.PROJECT_ID)
    private Long projectId;

    @ApiModelProperty("工单状态")
    @JsonProperty(WorkOrderDef.WORKSHEET_STATUS)
    @NotNull(message = "工单状态不允许为空！")
    private Integer workSheetStatus;
}
