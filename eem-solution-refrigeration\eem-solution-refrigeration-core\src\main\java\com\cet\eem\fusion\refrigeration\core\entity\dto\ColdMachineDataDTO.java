﻿package com.cet.eem.fusion.refrigeration.core.entity.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @ClassName : ColdMachineData
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-06-08 09:24
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ColdMachineDataDTO {
    @JsonProperty("object_id")
    private Long objectId;
    @JsonProperty("rated_refrigerating")
    private Double ratedRefrigerating;
    @JsonProperty("water_return_temp")
    private Double waterReturnTemp;
    @JsonProperty("pump_power")
    private Double pumpPower;

}
