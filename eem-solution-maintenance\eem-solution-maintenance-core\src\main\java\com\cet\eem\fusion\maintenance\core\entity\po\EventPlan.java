package com.cet.eem.fusion.maintenance.core.entity.po;

import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 故障预案模型
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ApiModel("故障预案模型")
public class EventPlan extends EntityWithName {
    @ApiModelProperty("方案步骤")
    private String solution;

    @ApiModelProperty("采用次数")
    @JsonProperty("adoptnumber")
    private int adoptNumber;

    @ApiModelProperty("采用率")
    private double adoptRate;

    @JsonProperty("faultscenarios_model")
    private List<FaultScenarios> faultScenariosList;

    private Integer number;

    public EventPlan() {
        this.modelLabel = ModelLabelDef.EVENT_PLAN;
    }
}
