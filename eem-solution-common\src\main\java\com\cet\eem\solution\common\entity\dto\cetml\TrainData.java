package com.cet.eem.solution.common.entity.dto.cetml;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/12
 */
@Data
public class TrainData {
    @ApiModelProperty("位移数据")
    private List<String> displacement;

    @ApiModelProperty("负荷数据")
    private List<String> loading;

    @ApiModelProperty("功图标识")
    private List<String> code;

    @ApiModelProperty("功图时间")
    private List<Long> logtime;

    @ApiModelProperty("功图类别")
    private List<Integer> classes;

    @ApiModelProperty("功图图像x轴最小值")
    private List<Double> xmin;

    @ApiModelProperty("功图图像x轴最大值")
    private List<Double> xmax;

    @ApiModelProperty("功图图像y轴最小值")
    private List<Double> ymin;

    @ApiModelProperty("功图图像y轴最大值")
    private List<Double> ymax;

}
