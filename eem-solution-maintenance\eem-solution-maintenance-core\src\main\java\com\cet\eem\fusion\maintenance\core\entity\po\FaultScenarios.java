package com.cet.eem.fusion.maintenance.core.entity.po;

import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 故障场景模型
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@ApiModel("故障场景模型")
public class FaultScenarios extends EntityWithName {

    @ApiModelProperty("模型标识")
    private final String modelLabel = ModelLabelDef.FAULT_SCENARIOS;

    @ApiModelProperty("事件归类")
    @JsonProperty("eventclassification")
    private Long eventClassification;

    @ApiModelProperty("事件归类名称")
    private String eventClassificationName;

    @ApiModelProperty("设备归类id")
    @JsonProperty("deviceclassification")
    private Long deviceClassification;

    @ApiModelProperty("设备归类名称")
    private String deviceClassificationName;

    @ApiModelProperty("预警点关键词")
    @JsonProperty("alarmkeyword")
    private Long alarmKeyword;

    @ApiModelProperty("预警点关键词名称")
    private String alarmKeywordName;

    @ApiModelProperty("故障预案")
    @JsonProperty("eventplan_model")
    private List<EventPlan> eventPlans;

    @ApiModelProperty("预案数量")
    private int eventPlanCount;
}
