"""
报告导出器
负责导出各种扫描结果和生成汇总报告
"""
import json
from typing import List, Dict, Any
from datetime import datetime
from pathlib import Path

def json_serializer(obj):
    """自定义JSON序列化器，处理set等不可序列化的类型"""
    if isinstance(obj, set):
        return list(obj)
    raise TypeError(f'Object of type {obj.__class__.__name__} is not JSON serializable')


class ReportExporter:
    """报告导出器类"""
    
    def __init__(self):
        """初始化报告导出器"""
        pass
    
    def export_summary_report(self, all_issues: List[Dict[str, Any]],
                             global_issues: List[Dict[str, Any]],
                             structure_issues: List[Dict[str, Any]],
                             import_issues: List[Dict[str, Any]],
                             output_file: str) -> None:
        """
        导出汇总报告

        Args:
            all_issues: 所有问题列表
            global_issues: 全局匹配问题列表
            structure_issues: 结构问题列表
            import_issues: 导入问题列表
            output_file: 输出文件路径
        """
        print(f"导出汇总报告到文件: {output_file}")
        
        # 统计信息
        summary_stats = {
            "total_issues": len(all_issues),
            "global_issues": len(global_issues),
            "structure_issues": len(structure_issues),
            "import_issues": len(import_issues),
            "by_severity": {},
            "by_type": {},
            "by_file": {},
            "by_category": {}
        }
        
        # 按严重程度统计
        for issue in all_issues:
            severity = issue.get("severity", "unknown")
            summary_stats["by_severity"][severity] = summary_stats["by_severity"].get(severity, 0) + 1
        
        # 按类型统计
        for issue in all_issues:
            issue_type = issue.get("type", "unknown")
            summary_stats["by_type"][issue_type] = summary_stats["by_type"].get(issue_type, 0) + 1
        
        # 按文件统计
        for issue in all_issues:
            file_path = issue.get("file", "unknown")
            file_name = Path(file_path).name if file_path != "unknown" else "unknown"
            summary_stats["by_file"][file_name] = summary_stats["by_file"].get(file_name, 0) + 1
        
        # 按类别统计
        for issue in all_issues:
            category = issue.get("category", issue.get("element", "unknown"))
            summary_stats["by_category"][category] = summary_stats["by_category"].get(category, 0) + 1

        # 特殊处理target_detection问题，生成增强的汇总信息
        target_detection_summary = self._generate_target_detection_summary(structure_issues)

        # 构建输出数据
        output_data = {
            "scan_info": {
                "timestamp": datetime.now().isoformat(),
                "scan_type": "comprehensive_scan",
                "statistics": summary_stats
            },
            "summary": {
                "global_issues": global_issues,
                "structure_issues": structure_issues,
                "import_issues": import_issues,
                "target_detection_summary": target_detection_summary  # 新增：target_detection汇总
            },
            "all_issues": all_issues
        }
        
        # 写入JSON文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2, default=json_serializer)
        
        print(f"汇总报告已保存到 {output_file}")
        self._print_summary_stats(summary_stats)

    def _generate_target_detection_summary(self, structure_issues: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        生成target_detection问题的增强汇总信息

        Args:
            structure_issues: 结构问题列表

        Returns:
            target_detection问题的汇总信息
        """
        target_detection_issues = [
            issue for issue in structure_issues
            if issue.get("category") == "target_detection"
        ]

        if not target_detection_issues:
            return {
                "total_count": 0,
                "services": {},
                "statistics": {
                    "total_services": 0,
                    "total_declarations": 0,
                    "total_usage_lines": 0,
                    "files_affected": 0
                }
            }

        # 按服务类型分组汇总
        services_summary = {}
        files_affected = set()
        total_usage_lines = 0

        for issue in target_detection_issues:
            service_type = issue.get("target_type", "unknown")
            file_path = issue.get("file", "unknown")
            element_name = issue.get("element_name", "unknown")
            declaration_line = issue.get("declaration_line", issue.get("line", 0))
            usage_lines = issue.get("usage_lines", [])
            lines = issue.get("lines", [])

            files_affected.add(file_path)
            total_usage_lines += len(usage_lines)

            if service_type not in services_summary:
                services_summary[service_type] = {
                    "service_type": service_type,
                    "instances": [],
                    "total_declarations": 0,
                    "total_usage_lines": 0,
                    "files": set(),
                    "suggest": issue.get("suggest", ""),
                    "problemtype": issue.get("problemtype", "")
                }

            services_summary[service_type]["instances"].append({
                "file": file_path,
                "element_name": element_name,
                "declaration_line": declaration_line,
                "usage_lines": usage_lines,
                "usage_count": len(usage_lines),
                "all_lines": lines
            })

            services_summary[service_type]["total_declarations"] += 1
            services_summary[service_type]["total_usage_lines"] += len(usage_lines)
            services_summary[service_type]["files"].add(file_path)

        # 转换set为list以便JSON序列化
        for service_type in services_summary:
            services_summary[service_type]["files"] = list(services_summary[service_type]["files"])
            services_summary[service_type]["files_count"] = len(services_summary[service_type]["files"])

        return {
            "total_count": len(target_detection_issues),
            "services": services_summary,
            "statistics": {
                "total_services": len(services_summary),
                "total_declarations": len(target_detection_issues),
                "total_usage_lines": total_usage_lines,
                "files_affected": len(files_affected),
                "affected_files": list(files_affected)
            }
        }

    def _print_summary_stats(self, stats: Dict[str, Any]) -> None:
        """打印汇总统计信息"""
        print(f"问题汇总:")
        print(f"  - 总问题数: {stats['total_issues']}")
        print(f"  - 全局匹配问题: {stats['global_issues']}")
        print(f"  - 其他问题: {stats['structure_issues']}")
        
        if stats['by_severity']:
            print(f"按严重程度:")
            for severity, count in sorted(stats['by_severity'].items()):
                print(f"    - {severity}: {count}")
    


