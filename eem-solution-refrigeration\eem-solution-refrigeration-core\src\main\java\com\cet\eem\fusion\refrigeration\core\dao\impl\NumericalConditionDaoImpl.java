﻿package com.cet.eem.fusion.refrigeration.core.impl;

import com.cet.eem.fusion.refrigeration.core.dao.aioptimization.NumericalConditionDao;
import com.cet.eem.fusion.refrigeration.core.model.config.system.NumericalCondition;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * @ClassName : NumericalConditionDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-06-28 19:05
 */
@Repository
public class NumericalConditionDaoImpl extends ModelDaoImpl<NumericalCondition> implements NumericalConditionDao {

    @Override
    public List<NumericalCondition> queryCondition(List<Long> controlSchemeId) {
        LambdaQueryWrapper<NumericalCondition> wrapper=LambdaQueryWrapper.of(NumericalCondition.class);
        wrapper.in(NumericalCondition::getControlSchemeId,controlSchemeId);
        List<NumericalCondition> numericalConditions = this.selectList(wrapper);
        if (CollectionUtils.isEmpty(numericalConditions)){
            return Collections.emptyList();
        }
        return numericalConditions;
    }
}
