package com.cet.eem.fusion.maintenance.core.service;

import com.cet.electric.workflow.common.constants.ProcessVariableDefinition;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/8/10
 */
@Getter
@Setter
public class WorkOrderServiceCallBackResult {
    @JsonProperty(ProcessVariableDefinition.MODEL_ENTTTY_LIST)
    private List<Map<String, Object>> modelEntityList;

    public WorkOrderServiceCallBackResult(List<Map<String, Object>> modelEntityList) {
        this.modelEntityList = modelEntityList;
    }
}
