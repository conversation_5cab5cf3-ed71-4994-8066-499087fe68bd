package com.cet.eem.fusion.maintenance.core.entity.sign;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName : CreateSignInRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-03-12 14:07
 */
@Getter
@Setter
@ApiModel(value = "CreateSignInGroupRequest", description = "创建签到组")
public class CreateSignInGroupRequest {
    /**
     * 分组名
     */
    @NotEmpty(message = "签到组名不能为空")
    private String name;
}
