"""
向量搜索服务
用于在Milvus向量数据库中搜索相似的Java代码
"""
import logging
import warnings
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

try:
    from pymilvus import connections, Collection, utility
    from transformers import AutoTokenizer, AutoModel
    import torch
    DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    DEPENDENCIES_AVAILABLE = False
    logging.warning(f"向量搜索依赖包未安装: {e}")


@dataclass
class VectorSearchResult:
    """向量搜索结果"""
    class_name: str
    code_content: str
    similarity_score: float
    collection_name: str


class VectorSearchConfig:
    """向量搜索配置"""
    
    # Milvus连接配置
    MILVUS_CONFIG = {
        "host": "************",
        "port": "19530"
    }
    
    # 集合配置
    COLLECTION_CONFIG1 = {
        "name": "java_code_vectors",
        "description": "存储Java代码的向量数据",
        "vector_dim": 768,  # CodeBERT向量维度
        "max_content_length": 500000,  # 代码内容最大长度，大幅增加到500K字符
        "use_text_field": True  # 使用TEXT字段类型而不是VARCHAR
    }

    COLLECTION_CONFIG2 = {
        "name": "old_java_code_vectors",
        "description": "存储Java代码的向量数据",
        "vector_dim": 768,  # CodeBERT向量维度
        "max_content_length": 500000,  # 代码内容最大长度，大幅增加到500K字符
        "use_text_field": True  # 使用TEXT字段类型而不是VARCHAR
    }
    
    # 索引配置
    INDEX_CONFIG = {
        "index_type": "IVF_FLAT",
        "metric_type": "L2",
        "params": {"nlist": 128}
    }

    # 搜索配置
    SEARCH_CONFIG = {
        "metric_type": "L2",
        "params": {"nprobe": 10},
        "default_limit": 5  # 默认返回前5名结果
    }
    
    # CodeBERT模型配置
    MODEL_CONFIG = {
        "model_name": "microsoft/codebert-base",
        "max_length": 512
    }


class VectorSearchService:
    """向量搜索服务"""
    
    def __init__(self):
        self.config = VectorSearchConfig()
        self.connected = False
        self.tokenizer = None
        self.model = None
        self.collections = {}
        
        if not DEPENDENCIES_AVAILABLE:
            logging.warning("向量搜索依赖包未安装，向量搜索功能将被禁用")
            return
            
        self._initialize()
    
    def _initialize(self):
        """初始化向量搜索服务"""
        try:
            # 连接到Milvus
            self._connect_to_milvus()
            
            # 初始化CodeBERT模型
            self._initialize_model()
            
            # 初始化集合
            self._initialize_collections()
            logging.info("向量搜索服务初始化成功")
        except Exception as e:
            logging.error(f"向量搜索服务初始化失败: {e}")
            self.connected = False
    
    def _connect_to_milvus(self):
        """连接到Milvus数据库"""
        try:
            connections.connect(
                alias="default",
                host=self.config.MILVUS_CONFIG["host"],
                port=self.config.MILVUS_CONFIG["port"]
            )
            self.connected = True
            logging.info(f"成功连接到Milvus: {self.config.MILVUS_CONFIG['host']}:{self.config.MILVUS_CONFIG['port']}")
        except Exception as e:
            logging.error(f"连接Milvus失败: {e}")
            raise
    
    def _initialize_model(self):
        """初始化CodeBERT模型"""
        try:
            model_name = self.config.MODEL_CONFIG["model_name"]
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModel.from_pretrained(model_name)
            self.model.eval()
            logging.info(f"成功加载CodeBERT模型: {model_name}")
        except Exception as e:
            logging.error(f"加载CodeBERT模型失败: {e}")
            raise
    
    def _initialize_collections(self):
        """初始化集合"""
        try:
            # 初始化两个集合
            for config in [self.config.COLLECTION_CONFIG1, self.config.COLLECTION_CONFIG2]:
                collection_name = config["name"]
                if utility.has_collection(collection_name):
                    collection = Collection(collection_name)

                    # 检查集合是否已加载，如果没有则加载
                    try:
                        load_state = utility.load_state(collection_name)
                        # 兼容不同版本的 pymilvus API
                        is_loaded = False
                        if hasattr(load_state, 'state'):
                            if hasattr(load_state.state, 'name'):
                                is_loaded = load_state.state.name == 'Loaded'
                            else:
                                is_loaded = str(load_state.state) == 'Loaded'
                        else:
                            # 如果没有 state 属性，尝试直接检查
                            is_loaded = str(load_state) == 'Loaded'

                        if not is_loaded:
                            logging.info(f"正在加载集合: {collection_name}")
                            collection.load()
                            logging.info(f"集合加载成功: {collection_name}")
                        else:
                            logging.info(f"集合已加载: {collection_name}")
                    except Exception as load_error:
                        logging.warning(f"检查/加载集合 {collection_name} 失败: {load_error}")
                        # 尝试强制加载
                        try:
                            collection.load()
                            logging.info(f"强制加载集合成功: {collection_name}")
                        except Exception as force_load_error:
                            logging.error(f"强制加载集合 {collection_name} 也失败: {force_load_error}")

                    self.collections[collection_name] = collection
                    logging.info(f"连接到集合: {collection_name}")
                else:
                    logging.warning(f"集合不存在: {collection_name}")
        except Exception as e:
            logging.error(f"初始化集合失败: {e}")
            raise
    
    def _encode_text(self, text: str) -> List[float]:
        """将文本编码为向量"""
        try:
            # 截断文本以适应模型最大长度
            max_length = self.config.MODEL_CONFIG["max_length"]
            
            # 对文本进行编码
            inputs = self.tokenizer(
                text,
                return_tensors="pt",
                max_length=max_length,
                truncation=True,
                padding=True
            )
            
            # 获取模型输出
            with torch.no_grad():
                outputs = self.model(**inputs)
                # 使用[CLS]标记的向量作为文本表示
                vector = outputs.last_hidden_state[:, 0, :].squeeze().tolist()
            
            return vector
        except Exception as e:
            logging.error(f"文本编码失败: {e}")
            return []
    
    def search_similar_classes(self, class_name: str, limit: int = None) -> List[VectorSearchResult]:
        """
        搜索相似的类 - 两步搜索逻辑

        1. 在 old_java_code_vectors 中精确匹配 class_name
        2. 使用匹配到的向量在 java_code_vectors 中搜索相似度
        Args:
            class_name: 要搜索的类名
            limit: 返回结果数量限制
        Returns:
            相似类的搜索结果列表
        """
        if not self.is_available():
            logging.warning("向量搜索服务不可用，跳过向量搜索")
            return []

        if limit is None:
            limit = self.config.SEARCH_CONFIG["default_limit"]

        try:
            # 第一步：在 old_java_code_vectors 中精确匹配 class_name
            reference_vectors = self._get_reference_vectors(class_name)

            if not reference_vectors:
                # 如果没有找到匹配的类名，回退到原有逻辑
                logging.info(f"在 old_java_code_vectors 中未找到类 '{class_name}'，使用编码向量搜索")
                return self._search_by_encoded_vector(class_name, limit)

            # 第二步：使用参考向量在 java_code_vectors 中搜索相似度
            results = self._search_by_reference_vectors(reference_vectors, limit)

            logging.info(f"为类 '{class_name}' 找到 {len(results)} 个相似结果")
            return results

        except Exception as e:
            logging.error(f"向量搜索失败: {e}")
            return []

    def _get_reference_vectors(self, class_name: str) -> List[List[float]]:
        """
        在 old_java_code_vectors 中获取匹配类名的向量
        Args:
            class_name: 要匹配的类名
        Returns:
            匹配到的向量列表
        """
        reference_vectors = []

        try:
            old_collection_name = self.config.COLLECTION_CONFIG2["name"]  # old_java_code_vectors
            if old_collection_name not in self.collections:
                logging.warning(f"集合 {old_collection_name} 不可用")
                return reference_vectors

            old_collection = self.collections[old_collection_name]

            # 使用 query 方法精确匹配 class_name 字段
            query_results = old_collection.query(
                expr=f'class_name == "{class_name}"',
                output_fields=["class_name", "code_vector", "code_content"]
            )

            logging.info(f"在 {old_collection_name} 中找到 {len(query_results)} 个匹配 '{class_name}' 的记录")

            for result in query_results:
                vector = result.get("code_vector")
                if vector:
                    reference_vectors.append(vector)
                    logging.debug(f"获取到向量，维度: {len(vector)}")

        except Exception as e:
            logging.error(f"从 old_java_code_vectors 获取参考向量失败: {e}")

        return reference_vectors

    def _search_by_reference_vectors(self, reference_vectors: List[List[float]], limit: int) -> List[VectorSearchResult]:
        """
        使用参考向量在 java_code_vectors 中搜索相似度
        Args:
            reference_vectors: 参考向量列表
            limit: 返回结果数量限制
        Returns:
            搜索结果列表
        """
        results = []

        try:
            new_collection_name = self.config.COLLECTION_CONFIG1["name"]  # java_code_vectors
            if new_collection_name not in self.collections:
                logging.warning(f"集合 {new_collection_name} 不可用")
                return results

            new_collection = self.collections[new_collection_name]

            # 对每个参考向量进行搜索
            for i, reference_vector in enumerate(reference_vectors):
                try:
                    # 执行向量搜索
                    search_results = new_collection.search(
                        data=[reference_vector],
                        anns_field="code_vector",
                        param=self.config.SEARCH_CONFIG["params"],
                        limit=limit,
                        output_fields=["class_name", "code_content"]
                    )

                    # 处理搜索结果
                    for hits in search_results:
                        for hit in hits:
                            # 计算相似度分数（L2距离转换为相似度）
                            similarity_score = 1.0 / (1.0 + hit.distance)

                            # 兼容不同版本的 pymilvus API
                            try:
                                class_name = hit.entity.get("class_name") if hit.entity.get("class_name") else ""
                                code_content = hit.entity.get("code_content") if hit.entity.get("code_content") else ""
                            except:
                                # 如果 get 方法不支持默认值，使用索引访问
                                try:
                                    class_name = hit.entity["class_name"] if "class_name" in hit.entity else ""
                                    code_content = hit.entity["code_content"] if "code_content" in hit.entity else ""
                                except:
                                    class_name = ""
                                    code_content = ""

                            result = VectorSearchResult(
                                class_name=class_name,
                                code_content=code_content,
                                similarity_score=similarity_score,
                                collection_name=new_collection_name
                            )
                            results.append(result)

                except Exception as e:
                    logging.error(f"使用第 {i+1} 个参考向量搜索失败: {e}")
                    continue

            # 按相似度分数排序并去重
            results.sort(key=lambda x: x.similarity_score, reverse=True)

            # 去重（基于类名）
            seen_classes = set()
            unique_results = []
            for result in results:
                if result.class_name not in seen_classes:
                    seen_classes.add(result.class_name)
                    unique_results.append(result)
                    if len(unique_results) >= limit:
                        break

            return unique_results

        except Exception as e:
            logging.error(f"使用参考向量搜索失败: {e}")
            return results

    def _search_by_encoded_vector(self, class_name: str, limit: int) -> List[VectorSearchResult]:
        """
        使用编码向量搜索（回退逻辑）

        Args:
            class_name: 要搜索的类名
            limit: 返回结果数量限制

        Returns:
            搜索结果列表
        """
        results = []

        try:
            # 将类名编码为向量
            query_vector = self._encode_text(class_name)
            if not query_vector:
                logging.error("类名编码失败")
                return results

            # 在 java_code_vectors 集合中搜索
            new_collection_name = self.config.COLLECTION_CONFIG1["name"]
            if new_collection_name in self.collections:
                collection = self.collections[new_collection_name]

                try:
                    # 执行向量搜索
                    search_results = collection.search(
                        data=[query_vector],
                        anns_field="code_vector",
                        param=self.config.SEARCH_CONFIG["params"],
                        limit=limit,
                        output_fields=["class_name", "code_content"]
                    )

                    # 处理搜索结果
                    for hits in search_results:
                        for hit in hits:
                            # 计算相似度分数（L2距离转换为相似度）
                            similarity_score = 1.0 / (1.0 + hit.distance)

                            # 兼容不同版本的 pymilvus API
                            try:
                                class_name = hit.entity.get("class_name") if hit.entity.get("class_name") else ""
                                code_content = hit.entity.get("code_content") if hit.entity.get("code_content") else ""
                            except:
                                # 如果 get 方法不支持默认值，使用索引访问
                                try:
                                    class_name = hit.entity["class_name"] if "class_name" in hit.entity else ""
                                    code_content = hit.entity["code_content"] if "code_content" in hit.entity else ""
                                except:
                                    class_name = ""
                                    code_content = ""

                            result = VectorSearchResult(
                                class_name=class_name,
                                code_content=code_content,
                                similarity_score=similarity_score,
                                collection_name=new_collection_name
                            )
                            results.append(result)

                except Exception as e:
                    logging.error(f"在集合 {new_collection_name} 中搜索失败: {e}")

            # 按相似度分数排序
            results.sort(key=lambda x: x.similarity_score, reverse=True)

            return results[:limit]

        except Exception as e:
            logging.error(f"编码向量搜索失败: {e}")
            return results

    def batch_search_classes(self, class_names: List[str], limit: int = None) -> Dict[str, List[Dict[str, Any]]]:
        """
        批量搜索相似的类

        Args:
            class_names: 要搜索的类名列表
            limit: 每个类返回的结果数量限制

        Returns:
            批量搜索结果，格式: {class_name: [search_results]}
        """
        print("\n开始向量搜索相似类...")
        vector_results = {}

        if not self.is_available():
            print("向量搜索服务不可用，跳过向量搜索")
            return vector_results

        print(f"向量搜索服务状态: {self.get_status()}")

        # 对每个缺失的类进行向量搜索
        for class_name in class_names:
            print(f"向量搜索类: {class_name}")

            try:
                # 搜索相似的类
                search_results = self.search_similar_classes(class_name, limit or 5)

                if search_results:
                    # 转换为标准格式
                    class_results = []
                    for result in search_results:
                        class_info = {
                            'full_class_name': result.class_name,
                            'simple_class_name': result.class_name.split('.')[-1] if '.' in result.class_name else result.class_name,
                            'package': '.'.join(result.class_name.split('.')[:-1]) if '.' in result.class_name else '',
                            'source': f'vector_search_{result.collection_name}',
                            'similarity_score': result.similarity_score,
                            'code_content': result.code_content,
                            'search_method': 'vector_similarity'
                        }
                        class_results.append(class_info)

                    vector_results[class_name] = class_results
                    print(f"  找到 {len(class_results)} 个相似类")

                    # 显示前几个结果
                    for i, result in enumerate(class_results[:2]):
                        print(f"    {i+1}. {result['full_class_name']} (相似度: {result['similarity_score']:.3f})")
                else:
                    print(f"  未找到相似类")

            except Exception as e:
                print(f"  向量搜索类 {class_name} 失败: {e}")
                continue

        print(f"向量搜索完成，共找到 {len(vector_results)} 个类的相似结果")
        return vector_results

    def is_available(self) -> bool:
        """检查向量搜索服务是否可用"""
        return (DEPENDENCIES_AVAILABLE and 
                self.connected and 
                self.tokenizer is not None and 
                self.model is not None and 
                len(self.collections) > 0)
    
    def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            "dependencies_available": DEPENDENCIES_AVAILABLE,
            "connected": self.connected,
            "model_loaded": self.tokenizer is not None and self.model is not None,
            "collections_count": len(self.collections),
            "collections": list(self.collections.keys()) if self.collections else []
        }
    
    def load_collections(self):
        """手动加载集合"""
        if not self.connected:
            logging.warning("Milvus未连接，无法加载集合")
            return False

        success_count = 0
        for collection_name, collection in self.collections.items():
            try:
                logging.info(f"尝试加载集合: {collection_name}")
                collection.load()
                logging.info(f"集合加载成功: {collection_name}")
                success_count += 1
            except Exception as e:
                logging.error(f"加载集合 {collection_name} 失败: {e}")

        logging.info(f"成功加载 {success_count}/{len(self.collections)} 个集合")
        return success_count == len(self.collections)

    def check_collections_status(self):
        """检查集合状态"""
        status = {}
        for collection_name in self.collections.keys():
            try:
                exists = utility.has_collection(collection_name)
                load_state = utility.load_state(collection_name)

                # 兼容不同版本的 pymilvus API
                is_loaded = False
                state_name = "Unknown"

                if load_state:
                    if hasattr(load_state, 'state'):
                        if hasattr(load_state.state, 'name'):
                            state_name = load_state.state.name
                            is_loaded = state_name == 'Loaded'
                        else:
                            state_name = str(load_state.state)
                            is_loaded = state_name == 'Loaded'
                    else:
                        state_name = str(load_state)
                        is_loaded = state_name == 'Loaded'

                status[collection_name] = {
                    "exists": exists,
                    "loaded": is_loaded,
                    "state": state_name
                }
            except Exception as e:
                status[collection_name] = {
                    "exists": False,
                    "loaded": False,
                    "error": str(e)
                }
        return status

    def close(self):
        """关闭连接"""
        try:
            if self.connected:
                connections.disconnect("default")
                self.connected = False
                logging.info("已断开Milvus连接")
        except Exception as e:
            logging.error(f"断开Milvus连接失败: {e}")


# 全局向量搜索服务实例
_vector_search_service = None

def get_vector_search_service() -> VectorSearchService:
    """获取全局向量搜索服务实例"""
    global _vector_search_service
    if _vector_search_service is None:
        _vector_search_service = VectorSearchService()
    return _vector_search_service


def process_vector_search_results(vector_results):
    """
    处理向量搜索结果，使用 javalang 解析完整类名
    根据相似度智能筛选输出结果
    Args:
        vector_results: 向量搜索结果列表
    Returns:
        处理后的向量建议列表，按相似度从高到低排序
    """
    if not vector_results:
        return []

    # 先处理所有结果，保留相似度信息用于判断
    processed_results = []

    for result in vector_results[:5]:  # 只取前5个结果
        try:
            # 获取基本信息
            similarity_score = result.get('similarity_score', 0)
            code_content = result.get('code_content', '')

            # 使用 javalang 解析完整类名
            full_class_name = extract_full_class_name_from_code(code_content)

            # 如果解析失败，使用原始的 full_class_name
            if not full_class_name:
                full_class_name = result.get('full_class_name', '')

            # 保留完整类名和相似度用于后续判断
            if full_class_name:
                processed_results.append({
                    'class_name': full_class_name,
                    'similarity_score': similarity_score
                })

        except Exception as e:
            print(f"处理向量搜索结果时出错: {e}")
            continue

    # 根据相似度进行智能筛选
    return smart_filter_results(processed_results)


def smart_filter_results(processed_results):
    """
    根据相似度智能筛选结果
    规则:
    1. 第一名匹配度高于0.7，第二名低于0.5，只输出第一名
    2. 有多个匹配度高于0.7的，返回多个高匹配度的
    3. 没有一个高于0.7，返回5个低匹配度的

    Args:
        processed_results: 包含类名和相似度的结果列表
    Returns:
        筛选后的类名列表
    """
    if not processed_results:
        return []

    # 按相似度排序（确保从高到低）
    processed_results.sort(key=lambda x: x['similarity_score'], reverse=True)

    # 统计高匹配度（>0.7）的结果
    high_score_results = [r for r in processed_results if r['similarity_score'] > 0.7]

    # 规则1: 第一名匹配度高于0.7，第二名低于0.5，只输出第一名
    if len(processed_results) >= 2:
        first_score = processed_results[0]['similarity_score']
        second_score = processed_results[1]['similarity_score']

        if first_score > 0.7 and second_score < 0.5:
            return [processed_results[0]['class_name']]

    # 规则2: 有多个匹配度高于0.7的，返回多个高匹配度的
    if len(high_score_results) > 1:
        return [result['class_name'] for result in high_score_results]

    # 规则3: 没有一个高于0.7，返回5个低匹配度的
    if len(high_score_results) == 0:
        return [result['class_name'] for result in processed_results[:5]]

    # 其他情况（只有一个高匹配度的），返回所有结果
    return [result['class_name'] for result in processed_results]


def extract_full_class_name_from_code(code_content):
    """
    使用 javalang 从代码内容中提取完整的类名（包名+类名）
    Args:
        code_content: Java 代码内容
    Returns:
        完整的类名，格式为 package.ClassName
    """
    if not code_content:
        return ""

    try:
        import javalang

        # 尝试解析 Java 代码
        tree = javalang.parse.parse(code_content)

        # 获取包名
        package_name = ""
        if tree.package:
            package_name = tree.package.name

        # 获取类名或接口名（优先查找公共的类或接口）
        class_name = ""

        # 首先查找公共类
        for path, node in tree.filter(javalang.tree.ClassDeclaration):
            if 'public' in node.modifiers:
                class_name = node.name
                break

        # 如果没有找到公共类，查找公共接口
        if not class_name:
            for path, node in tree.filter(javalang.tree.InterfaceDeclaration):
                if 'public' in node.modifiers:
                    class_name = node.name
                    break

        # 如果还没有找到，取第一个类
        if not class_name:
            for path, node in tree.filter(javalang.tree.ClassDeclaration):
                class_name = node.name
                break

        # 如果还没有找到，取第一个接口
        if not class_name:
            for path, node in tree.filter(javalang.tree.InterfaceDeclaration):
                class_name = node.name
                break

        # 组合完整类名
        if package_name and class_name:
            return f"{package_name}.{class_name}"
        elif class_name:
            return class_name
        else:
            return ""

    except Exception as e:
        # 如果 javalang 解析失败，返回空字符串
        print(f"javalang 解析失败: {e}")
        return ""
