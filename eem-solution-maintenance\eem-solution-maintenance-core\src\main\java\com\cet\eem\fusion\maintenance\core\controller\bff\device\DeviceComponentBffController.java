package com.cet.eem.fusion.maintenance.core.controller.bff.device;

import com.cet.electric.matterhorn.cloud.authservice.sdk.common.annotation.OperationPermission;
import com.cet.eem.fusion.common.def.OperationAuthDef;
import com.cet.eem.fusion.config.sdk.service.log.OperationLog;
import com.cet.eem.fusion.config.sdk.def.OperationLogType;
import com.cet.eem.fusion.common.utils.EnumOperationSubType;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.DeviceComponent;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.bll.maintenance.model.devicemanage.component.AddDeviceComponent;
import com.cet.eem.bll.maintenance.model.devicemanage.component.EditDeviceComponent;
import com.cet.eem.bll.maintenance.model.devicemanage.component.QueryNodeComponentDto;
import com.cet.eem.bll.maintenance.service.device.ComponentService;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.commons.ApiResult;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * @Author: jiangzixuan
 * @Description:设备模块零件库部分相关接口
 * @Data: Created in 2021-05-12
 */

public class DeviceComponentBffController {

    @Autowired
    private ComponentService componentService;


    @ApiOperation(value = "根据设备查看零件库")
    @PostMapping(value = "/componentByEquipment", produces = "application/json")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_BROWSER})
    public ApiResult<List<DeviceComponent>> queryComponentByEquipment(
            @NotNull @ApiParam(name = "dto", value = "查询条件", type = "QueryNodeComponentDto", required = true) @RequestBody QueryNodeComponentDto dto
    ) {
        List<DeviceComponent> deviceComponents = componentService.queryComponent(dto);
        return new Result<>(deviceComponents);
    }

    @ApiOperation(value = "删除零件")
    @RequestMapping(value = "/", method = RequestMethod.DELETE, produces = "application/json")
    @OperationLog(operationType = EEMOperationLogType.DEVICE_COMPONENT_MANAGE, subType = EnumOperationSubType.DELETE, description = "删除零件")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_DELETE})
    public ResultWithTotal<Object> deleteComponent(
            @RequestBody @ApiParam(value = "ids", name = "ids", required = true) List<Long> ids
    ) {
        componentService.delecteComponent(ids);

        return ResultWithTotal.ok();
    }

    @ApiOperation(value = "根据keyword查询规格型号")
    @RequestMapping(value = "/model", method = RequestMethod.POST, produces = "application/json")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_BROWSER})
    public ApiResult<List<String>> queryModelList(@RequestParam @ApiParam(value = "modelkeyword", name = "modelkeyword", required = true) String modelkeyword) {
        Set<String> strings = componentService.queryModelList(modelkeyword);
        return new Result<>(new ArrayList<>(strings));
    }

    @ApiOperation(value = "编辑零件")
    @RequestMapping(value = "/", method = RequestMethod.PUT, produces = "application/json")
    @OperationLog(operationType = EEMOperationLogType.DEVICE_COMPONENT_MANAGE, subType = EnumOperationSubType.UPDATE, description = "更新零件")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_UPDATE})
    public ApiResult<DeviceComponent> editComponent(@Valid @RequestBody EditDeviceComponent editDeviceComponent) {
        DeviceComponent deviceComponent = componentService.editComponent(editDeviceComponent);
        return Result.ok(deviceComponent);
    }

    @ApiOperation(value = "新增零件")
    @PostMapping(value = "/")
    @OperationLog(operationType = EEMOperationLogType.DEVICE_COMPONENT_MANAGE, subType = EnumOperationSubType.ADD, description = "新增零件")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_CREATE})
    public ApiResult<DeviceComponent> addComponent(@Valid @RequestBody AddDeviceComponent addDeviceComponent) {
        DeviceComponent deviceComponent = componentService.addComponent(addDeviceComponent);
        return Result.ok(deviceComponent);
    }

    @ApiOperation(value = "查询零部件节点树")
    @GetMapping("/tree")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_BROWSER})
    public ApiResult<List<Map<String, Object>>> queryDeviceTree() {
        Long projectId= GlobalInfoUtils.getTenantId();
        return Result.ok(componentService.queryDeviceTree(projectId));
    }

    @ApiOperation(value = "批量导出零件数据")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_EXPORT})
    @RequestMapping(value = "/ouputComponents", method = RequestMethod.GET, produces = "application/json")
    public ResultWithTotal<Object> outPutComponents(HttpServletResponse response, @NotNull(message = "节点id不能为空！") @Min(value = 1, message = "节点id必须大于0！")
    @RequestParam @ApiParam(name = "id", value = "节点id", required = true) Long id, @NotNull(message = "节点模型不能为空！")
                                                @RequestParam @ApiParam(name = "modelLabel", value = "节点模型", required = true) String modelLabel, @RequestParam(required = false) @ApiParam(name = "roomtype", required = false) Integer roomtype) throws Exception {
        componentService.exportDeviceComponentByDeviceType(response, id, modelLabel, roomtype);
        return new ResultWithTotal<Object>();
    }

    @ApiOperation(value = "批量导入零件数据")
    @OperationPermission(authNames = {OperationAuthDef.DEVICE_COMPONENT_AND_SPAREPARTS_IMPORT})
    @RequestMapping(value = "/importComponents", method = RequestMethod.POST, produces = "application/json")
    public ResultWithTotal<Object> importComponents1111(
            @RequestParam("file") MultipartFile file) throws Exception {
        componentService.importComponent(file);
        return new ResultWithTotal<Object>();
    }
}
