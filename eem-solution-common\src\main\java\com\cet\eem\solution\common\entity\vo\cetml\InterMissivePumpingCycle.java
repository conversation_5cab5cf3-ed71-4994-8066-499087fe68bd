package com.cet.eem.solution.common.entity.vo.cetml;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 间抽周期数据
 *
 * <AUTHOR>
 */
@Data
public class InterMissivePumpingCycle {

    @JsonProperty("empty_pump_index")
    private List<Long> emptyPumpIndex;

    @JsonProperty("max_index")
    private List<Long> maxIndex;

    @JsonProperty("min_index")
    private List<Long> minIndex;
}
