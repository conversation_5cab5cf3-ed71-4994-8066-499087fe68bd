#!/usr/bin/env python3
"""
QodanaJavaSanity.xml生成器
基于IntelliJ IDEA的inspect功能生成静态代码分析结果
"""
import subprocess
import os
import time
import argparse
from pathlib import Path


class QodanaGenerator:
    def __init__(self, project_path, inspection_profile=None, output_dir=".", verbose=False):
        """
        初始化QodanaJavaSanity.xml生成器
        
        Args:
            project_path: 项目根目录路径
            inspection_profile: 检查配置文件路径 (默认使用ai_class.xml)
            output_dir: 输出目录路径
            verbose: 是否显示详细输出
        """
        self.project_path = os.path.abspath(project_path)
        self.output_dir = os.path.abspath(output_dir)
        self.verbose = verbose
        
        # 设置默认的inspection_profile路径
        if inspection_profile is None:
            # 获取当前脚本所在目录，然后构造inspect目录路径
            script_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(os.path.dirname(script_dir))  # 回到项目根目录
            self.inspection_profile = os.path.join(project_root, "inspect", "ai_class.xml")
        else:
            self.inspection_profile = os.path.abspath(inspection_profile)
        
        # 输出XML文件路径
        self.xml_file = os.path.join(self.output_dir, "QodanaJavaSanity.xml")
        
    def pre_check(self):
        """执行预检查，验证路径和环境"""
        if self.verbose:
            print("开始预检查...")
        
        # 检查项目路径
        if not os.path.exists(self.project_path):
            print(f"错误: 项目路径不存在: {self.project_path}")
            return False
        if self.verbose:
            print(f"✓ 项目路径存在: {self.project_path}")
        
        # 检查检查配置文件
        if not os.path.exists(self.inspection_profile):
            print(f"错误: 检查配置文件不存在: {self.inspection_profile}")
            return False
        if self.verbose:
            print(f"✓ 检查配置文件存在: {self.inspection_profile}")
        
        # 创建输出目录
        try:
            os.makedirs(self.output_dir, exist_ok=True)
            if self.verbose:
                print(f"✓ 输出目录已准备: {self.output_dir}")
        except Exception as e:
            print(f"错误: 无法创建输出目录 {self.output_dir}: {e}")
            return False
        
        # 检查 idea64.exe 是否可用
        try:
            result = subprocess.run(
                ["where", "idea64.exe"], 
                capture_output=True, 
                text=True, 
                shell=True
            )
            if result.returncode == 0:
                idea_path = result.stdout.strip()
                if self.verbose:
                    print(f"✓ 找到 idea64.exe: {idea_path}")
            else:
                if self.verbose:
                    print("警告: 在 PATH 中未找到 idea64.exe，尝试使用常见安装路径")
                # 检查常见安装路径
                if not self._check_idea_common_paths():
                    print("错误: 未找到可用的 IntelliJ IDEA 安装")
                    return False
        except Exception as e:
            print(f"检查 idea64.exe 时出错: {e}")
            return False
        
        return True
    
    def _check_idea_common_paths(self):
        """检查IntelliJ IDEA的常见安装路径"""
        idea_paths = [
            r"C:\Program Files\JetBrains\IntelliJ IDEA 2024.2\bin\idea64.exe",
            r"C:\Program Files\JetBrains\IntelliJ IDEA 2024.1\bin\idea64.exe", 
            r"C:\Program Files\JetBrains\IntelliJ IDEA 2023.3\bin\idea64.exe",
            r"C:\Program Files\JetBrains\IntelliJ IDEA 2023.2\bin\idea64.exe",
            r"C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.2\bin\idea64.exe",
            r"C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.1\bin\idea64.exe",
            r"C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2023.3\bin\idea64.exe"
        ]
        
        for idea_path in idea_paths:
            if os.path.exists(idea_path):
                if self.verbose:
                    print(f"✓ 发现 IntelliJ IDEA: {idea_path}")
                return True
        
        return False
    
    def get_idea_commands(self):
        """获取可能的 IntelliJ IDEA 命令"""
        commands = []
        
        # 方式1: 直接使用 idea64.exe (如果在PATH中)
        commands.append([
            "idea64.exe", "inspect", 
            self.project_path, 
            self.inspection_profile, 
            self.output_dir, 
            "-v2"
        ])
        
        # 方式2: 使用完整路径
        idea_paths = [
            r"C:\Program Files\JetBrains\IntelliJ IDEA 2024.2\bin\idea64.exe",
            r"C:\Program Files\JetBrains\IntelliJ IDEA 2024.1\bin\idea64.exe",
            r"C:\Program Files\JetBrains\IntelliJ IDEA 2023.3\bin\idea64.exe", 
            r"C:\Program Files\JetBrains\IntelliJ IDEA 2023.2\bin\idea64.exe",
            r"C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.2\bin\idea64.exe",
            r"C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2024.1\bin\idea64.exe",
            r"C:\Program Files\JetBrains\IntelliJ IDEA Community Edition 2023.3\bin\idea64.exe"
        ]
        
        for idea_path in idea_paths:
            if os.path.exists(idea_path):
                commands.append([
                    idea_path, "inspect",
                    self.project_path,
                    self.inspection_profile,
                    self.output_dir,
                    "-v2"
                ])
        
        return commands
    
    def run_inspect(self):
        """执行IDEA inspect命令生成QodanaJavaSanity.xml"""
        if self.verbose:
            print("开始运行 IDEA inspect 命令...")
        
        # 如果输出文件已存在，先删除
        if os.path.exists(self.xml_file):
            try:
                os.remove(self.xml_file)
                if self.verbose:
                    print(f"已删除已存在的文件: {self.xml_file}")
            except Exception as e:
                print(f"警告: 无法删除已存在的文件 {self.xml_file}: {e}")
        
        commands = self.get_idea_commands()
        
        for i, cmd in enumerate(commands, 1):
            if self.verbose:
                print(f"\n尝试方式 {i}: {' '.join(cmd)}")
            
            try:
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    shell=True,
                    encoding='utf-8',
                    errors='ignore',
                    timeout=300  # 5分钟超时
                )
                
                if self.verbose:
                    print(f"返回码: {result.returncode}")
                    if result.stdout:
                        print("标准输出:")
                        print(result.stdout[:500] + "..." if len(result.stdout) > 500 else result.stdout)
                    if result.stderr:
                        print("错误输出:")
                        print(result.stderr[:500] + "..." if len(result.stderr) > 500 else result.stderr)
                
                if result.returncode == 0:
                    if self.verbose:
                        print(f"方式 {i} 执行成功!")
                    
                    # 等待文件生成
                    if self.wait_for_file_generation():
                        return True
                    else:
                        print("文件生成超时，尝试下一种方式...")
                        continue
                else:
                    if self.verbose:
                        print(f"方式 {i} 执行失败，返回码: {result.returncode}")
                    # 检查是否是IDEA实例已运行的问题
                    if result.stdout and "一次只能运行一个" in result.stdout:
                        print("检测到 IDEA 实例已在运行，请关闭所有 IDEA 窗口后重试")
                        return False
                    continue
                    
            except subprocess.TimeoutExpired:
                print(f"方式 {i} 执行超时")
                continue
            except Exception as e:
                if self.verbose:
                    print(f"方式 {i} 执行时出错: {e}")
                continue
        
        print("所有方式都失败了，无法生成 QodanaJavaSanity.xml")
        return False
    
    def wait_for_file_generation(self, timeout=120):
        """等待XML文件生成"""
        if self.verbose:
            print(f"等待 {self.xml_file} 文件生成...")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if os.path.exists(self.xml_file):
                # 等待文件稳定（大小不再变化）
                initial_size = os.path.getsize(self.xml_file)
                time.sleep(3)
                
                if os.path.exists(self.xml_file):
                    current_size = os.path.getsize(self.xml_file)
                    if current_size == initial_size and current_size > 0:
                        if self.verbose:
                            print(f"✓ 文件已生成，大小: {current_size} 字节")
                        return True
                    elif current_size > initial_size:
                        # 文件还在增长，继续等待
                        continue
            time.sleep(2)
        
        print(f"等待文件生成超时 ({timeout}秒)")
        return False
    
    def generate(self):
        """生成QodanaJavaSanity.xml的主方法"""
        print("开始生成 QodanaJavaSanity.xml...")
        
        # 执行预检查
        if not self.pre_check():
            return False
        
        # 运行inspect命令
        if self.run_inspect():
            print(f"✅ 成功生成 QodanaJavaSanity.xml: {self.xml_file}")
            return True
        else:
            print("❌ QodanaJavaSanity.xml 生成失败")
            return False


def main():
    """命令行入口"""
    parser = argparse.ArgumentParser(description='生成QodanaJavaSanity.xml文件')
    parser.add_argument('project_path', help='项目根目录路径')
    parser.add_argument('--inspection-profile', help='检查配置文件路径 (默认: ../inspect/ai_class.xml)')
    parser.add_argument('--output-dir', default='.', help='输出目录路径 (默认: 当前目录)')
    parser.add_argument('-v', '--verbose', action='store_true', help='显示详细输出')
    
    args = parser.parse_args()
    
    generator = QodanaGenerator(
        project_path=args.project_path,
        inspection_profile=args.inspection_profile,
        output_dir=args.output_dir,
        verbose=args.verbose
    )
    
    success = generator.generate()
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())