package com.cet.eem.fusion.maintenance.core.dao;

import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionCountSearchDto;
import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionParameterWorkOrderDTO;
import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionSearchDto;
import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.electric.commons.ApiResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/5/21
 */
public interface InspectionWorkOrderDao {
    /**
     * 查询异常工单数量
     *
     * @param dto
     * @return
     */
    Integer queryAbnormalWorkOrderCount(InspectionCountSearchDto dto);

    /**
     * 仅查询巡检参数
     *
     * @param dto
     * @param userId
     * @return
     */
    ResultWithTotal<List<InspectionParameterWorkOrderDTO>> queryInspectionParameterWorkOrder(InspectionSearchDto dto, Long userId);

    /**
     * 查询工单
     *
     * @param dto
     * @param userId
     * @return
     */
    ResultWithTotal<List<InspectionWorkOrderDto>> queryWorkOrder(InspectionSearchDto dto, Long userId);

}
