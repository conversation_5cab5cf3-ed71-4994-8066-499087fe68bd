package com.cet.eem.fusion.maintenance.core.service.singinpoint.impl;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInGroup;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInPoint;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInStatusDef;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInStatusRecord;
import com.cet.eem.bll.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.SignInGroupWithSubLayer;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.bll.maintenance.dao.InspectionWorkOrderDao;
import com.cet.eem.bll.maintenance.dao.SignInGroupDao;
import com.cet.eem.bll.maintenance.dao.SignInPointDao;
import com.cet.eem.bll.maintenance.dao.SignInStatusRecordDao;
import com.cet.eem.bll.maintenance.model.sign.SignGroupStatusGroup;
import com.cet.eem.bll.maintenance.model.workorder.SignInPointStatusCount;
import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionSearchDto;
import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.bll.maintenance.service.singinpoint.SignInStatusRecordService;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/6/17
 */
@Service
public class SignInStatusRecordServiceImpl implements SignInStatusRecordService {
    @Autowired
    SignInStatusRecordDao signInStatusRecordDao;

    @Autowired
    SignInPointDao signInPointDao;

    @Autowired
    SignInGroupDao signInGroupDao;

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    InspectionWorkOrderDao inspectionWorkOrderDao;

    @Override
    public void resetSignInStatus(int status) {
        // 查找所有的签掉点
        List<SignInGroupWithSubLayer> signInGroupWithSubLayers = signInGroupDao.selectRelatedList(SignInGroupWithSubLayer.class, LambdaQueryWrapper.of(SignInGroup.class));
        if (CollectionUtils.isEmpty(signInGroupWithSubLayers)) {
            return;
        }

        // 查询所有的签到点记录
        List<SignInStatusRecord> signInStatusRecords = signInStatusRecordDao.selectAll();
        if (signInStatusRecords == null) {
            signInStatusRecords = new ArrayList<>();
        }

        // 组装数据
        for (SignInGroupWithSubLayer signInGroupWithSubLayer : signInGroupWithSubLayers) {
            if (CollectionUtils.isEmpty(signInGroupWithSubLayer.getSignInPointList())) {
                continue;
            }

            for (SignInPoint signInPoint : signInGroupWithSubLayer.getSignInPointList()) {
                Optional<SignInStatusRecord> any = signInStatusRecords.stream().filter(it -> signInPoint.getId().equals(it.getSignInPointId()) && signInGroupWithSubLayer.getId().equals(it.getSignInGroupId())).findAny();
                if (any.isPresent()) {
                    SignInStatusRecord signInStatusRecord = any.get();
                    signInStatusRecord.setStatus(status);
                    signInStatusRecord.setUpdateTime(LocalDateTime.now());
                    continue;
                }

                SignInStatusRecord signInStatusRecord = new SignInStatusRecord();
                signInStatusRecord.setStatus(status);
                signInStatusRecord.setSignInGroupId(signInGroupWithSubLayer.getId());
                signInStatusRecord.setSignInPointId(signInPoint.getId());
                signInStatusRecord.setProjectId(signInGroupWithSubLayer.getProjectId());
                signInStatusRecord.setUpdateTime(LocalDateTime.now());
                signInStatusRecords.add(signInStatusRecord);
            }
        }

        // 入库
        modelServiceUtils.writeData(signInStatusRecords);
    }

    @Override
    public void updateSignInStatus(Long signPointId, Long signGroupId, int status) {
        SignInStatusRecord signInStatusRecord = signInStatusRecordDao.queryRecord(signPointId, signGroupId);
        // 如果签到点的状态已经是异常，但是要被更新成正常状态，此时仍然保持为异常状态
        if (signInStatusRecord.getStatus() == SignInStatusDef.ABNORMAL && status == SignInStatusDef.NORMAL) {
            status = signInStatusRecord.getStatus();
        }
        getSignInStatusRecord(status, signPointId, signInStatusRecord, signGroupId);
        signInStatusRecord.setProjectId(GlobalInfoUtils.getTenantId());
        modelServiceUtils.writeData(Collections.singletonList(signInStatusRecord));
    }

    @Override
    public void updateSignInStatus(List<SignGroupStatusGroup> groups) {
        List<SignInStatusRecord> signInStatusRecords = signInStatusRecordDao.queryRecord(groups);

        List<SignInStatusRecord> result = new ArrayList<>();
        for (SignGroupStatusGroup group : groups) {
            for (Long signPointId : group.getSignPointIds()) {
                SignInStatusRecord signInStatusRecord = signInStatusRecords.stream()
                        .filter(it -> Objects.equals(it.getSignInPointId(), signPointId) && Objects.equals(it.getSignInGroupId(), group.getSignGroupId()))
                        .findAny().orElse(null);

                signInStatusRecord = getSignInStatusRecord(group.getStatus(), signPointId, signInStatusRecord, group.getSignGroupId());
                result.add(signInStatusRecord);
            }
        }

        modelServiceUtils.writeData(result);
    }

    @Override
    public void createSignInStatus(Long signPointId, Collection<Long> singGroupIds) {
        List<SignInStatusRecord> signInStatusRecords = signInStatusRecordDao.queryRecord(signPointId, singGroupIds);

        List<SignInStatusRecord> result = new ArrayList<>();
        Long projectId = GlobalInfoUtils.getTenantId();
        for (Long group : singGroupIds) {
            SignInStatusRecord signInStatusRecord = signInStatusRecords.stream()
                    .filter(it -> Objects.equals(it.getSignInPointId(), signPointId) && Objects.equals(it.getSignInGroupId(), group))
                    .findAny().orElse(null);

            signInStatusRecord = getSignInStatusRecord(SignInStatusDef.UNSIGNED, signPointId, signInStatusRecord, group);
            signInStatusRecord.setProjectId(projectId);
            result.add(signInStatusRecord);
        }

        modelServiceUtils.writeData(result);
    }

    @Override
    public void createSignInStatusBatch(Map<Long, List<Long>> signGroupIdMap) {
        List<SignInStatusRecord> result = new ArrayList<>();
        Long projectId = GlobalInfoUtils.getTenantId();
        signGroupIdMap.forEach((pointId,groupIds)->{
            groupIds.forEach(groupId->{
                SignInStatusRecord signInStatusRecord = new SignInStatusRecord();
                signInStatusRecord.setSignInPointId(pointId);
                signInStatusRecord.setSignInGroupId(groupId);
                signInStatusRecord.setStatus(SignInStatusDef.UNSIGNED);
                signInStatusRecord.setProjectId(projectId);
                signInStatusRecord.setUpdateTime(LocalDateTime.now());
                result.add(signInStatusRecord);
            });
        });
        modelServiceUtils.writeData(result);
    }

    @Override
    public void deleteSignInStatusRecord(Long signPointId, Collection<Long> singGroupIds) {
        List<SignInStatusRecord> signInStatusRecords = signInStatusRecordDao.queryRecord(signPointId, singGroupIds);
        if (CollectionUtils.isEmpty(signInStatusRecords)) {
            return;
        }

        modelServiceUtils.delete(ModelLabelDef.SIGN_IN_STATUS_RECORD, signInStatusRecords.stream().map(BaseEntity::getId).distinct().collect(Collectors.toList()));
    }

    private SignInStatusRecord getSignInStatusRecord(int status, Long signPointId, SignInStatusRecord signInStatusRecord, Long signGroupId) {
        if (signInStatusRecord == null) {
            signInStatusRecord = new SignInStatusRecord();
            signInStatusRecord.setSignInPointId(signPointId);
            signInStatusRecord.setSignInGroupId(signGroupId);
        }

        signInStatusRecord.setStatus(status);
        signInStatusRecord.setUpdateTime(LocalDateTime.now());
        return signInStatusRecord;
    }

    @Override
    public SignInPointStatusCount querySignInPointStatusCount(Long signInGroupId) {
        SignInGroupWithSubLayer signInGroupWithSubLayer = signInGroupDao.selectRelatedById(SignInGroupWithSubLayer.class, signInGroupId);
        Assert.notNull(signInGroupWithSubLayer, "签到点分组不存在！");

        SignInPointStatusCount result = new SignInPointStatusCount();
        List<SignInPoint> signInPointList = signInGroupWithSubLayer.getSignInPointList();
        if (signInPointList == null) {
            return result;
        }
        result.setSignPointNumber(signInPointList.size());

        Set<Long> signInPointIds = signInPointList.stream().map(BaseEntity::getId).collect(Collectors.toSet());
        List<SignInStatusRecord> signInStatusRecords = signInStatusRecordDao.queryRecord(Arrays.asList(SignInStatusDef.ABNORMAL, SignInStatusDef.NORMAL), signInPointIds, signInGroupId);
        result.setSignedPointNumber(signInStatusRecords.size());

        InspectionSearchDto dto = new InspectionSearchDto();
        LocalDateTime nowTime = LocalDateTime.now();
        LocalDateTime startTime = TimeUtil.getFirstTimeOfDay(nowTime);
        LocalDateTime endTime = TimeUtil.getFirstTimeOfNextDay(nowTime);
        dto.setStartTime(startTime);
        dto.setEndTime(endTime);
        dto.setSignGroupId(signInGroupId);
        dto.setTaskType(WorkSheetTaskType.INSPECTION);
        ResultWithTotal<List<InspectionWorkOrderDto>> workOrder = inspectionWorkOrderDao.queryWorkOrder(dto, GlobalInfoUtils.getUserId());
        List<InspectionWorkOrderDto> data = workOrder.getData();
        if (CollectionUtils.isEmpty(data)) {
            return result;
        }

        result.setCheckedObjectNumber((int) data.stream().filter(it -> it.getStaff() != null).count());
        result.setObjectNumber(data.size());

        return result;
    }


}
