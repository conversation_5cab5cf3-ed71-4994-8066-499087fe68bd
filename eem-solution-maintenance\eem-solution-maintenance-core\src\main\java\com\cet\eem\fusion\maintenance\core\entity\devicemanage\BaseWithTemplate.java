package com.cet.eem.fusion.maintenance.core.entity.devicemanage;

import com.cet.eem.fusion.maintenance.core.entity.devicemanage.template.AttributeTemplate;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseWithTemplate {
    @ApiModelProperty("主键标识")
    private long id;
    @ApiModelProperty("模型标识")
    private String modelLabel;

    private String name;

    private AttributeTemplate template;

    private String nodeTemplatePath;

    public BaseWithTemplate(long id, String modelLabel, String name) {
        this.id = id;
        this.modelLabel = modelLabel;
        this.name = name;
    }
}
