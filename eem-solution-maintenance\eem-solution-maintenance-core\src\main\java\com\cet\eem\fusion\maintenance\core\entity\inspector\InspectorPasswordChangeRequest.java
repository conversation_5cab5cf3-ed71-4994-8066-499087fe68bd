package com.cet.eem.fusion.maintenance.core.entity.inspector;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @ClassName : InspectorPasswordChangeRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-02 09:33
 */
@Getter
@Setter
@ApiModel(value = "InspectorPasswordChangeRequest", description = "巡检人员密码修改")
public class InspectorPasswordChangeRequest {

    /**
     * 巡检人员ID
     */
    @NotNull(message = "巡检人员ID")
    private Long id;

    /**
     * 密码
     */
    private String password;

    /**
     * 密码
     */
    @NotEmpty(message = "新密码不能为空")
    private String newPassword;

    /**
     * 密码
     */
    @NotEmpty(message = "旧密码不能为空")
    private String oldPassword;
}
