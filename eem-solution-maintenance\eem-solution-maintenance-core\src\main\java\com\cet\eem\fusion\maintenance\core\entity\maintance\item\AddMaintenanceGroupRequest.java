package com.cet.eem.fusion.maintenance.core.entity.maintance.item;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

/**
 * @ClassName : CreateMaintenanceGroupRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-05-10 14:12
 */
@Getter
@Setter
@ApiModel(value = "AddMaintenanceGroupRequest", description = "新增维保项目组")
public class AddMaintenanceGroupRequest {

    /**
     * 组名
     */
    @NotEmpty(message = "维保项目组名不能为空")
    private String name;
}
