package com.cet.eem.fusion.maintenance.core.entity.handover;

import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.fusion.maintenance.core.def.HandoverDef;
import com.cet.eem.fusion.maintenance.core.entity.workorder.OperationUser;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 交接班
 *
 * <AUTHOR>
 * @date 2020年6月22日 14:22:10
 */
@Getter
@Setter
@ApiModel(description = "交接班")
public class ShiftingDutyVo extends ShiftingDutyPo {
    @ApiModelProperty("值班员")
    @JsonProperty(value = HandoverDef.DUTYSTAFF)
    private String dutyStaff;

    @ApiModelProperty("值班员列表")
    private List<OperationUser> dutyStaffList;

    public List<OperationUser> getDutyStaffList() {
        return JsonTransferUtils.transferJsonString(dutyStaff, OperationUser.class);
    }

    @ApiModelProperty("值班日志")
    @JsonProperty(value = HandoverDef.DUTYLOG)
    private String dutyLog;

    @ApiModelProperty("交接班注意事项")
    @JsonProperty(value = HandoverDef.HANDOVERMATTER)
    private String handoverMatter;

    @ApiModelProperty("开始时间")
    @JsonProperty(value = HandoverDef.STARTTIME)
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    @JsonProperty(value = HandoverDef.ENDTIME)
    private LocalDateTime endTime;

    @ApiModelProperty("值班时长，时间戳")
    @JsonProperty(value = HandoverDef.DUTYTIME)
    private Long dutyTime;

    @ApiModelProperty("值班长")
    @JsonProperty(value = HandoverDef.DUTYOFFICER)
    private Long dutyOfficer;

    @ApiModelProperty("值班长名称")
    private String dutyOfficerName;

    @ApiModelProperty("值班状态，取值自模型dutystatus")
    @JsonProperty(value = HandoverDef.DUTYSTATUS)
    private Integer dutyStatus;

    @ApiModelProperty("值班状态")
    private String dutyStatusName;

    @ApiModelProperty("接班员")
    @JsonProperty(value = HandoverDef.HANDOVERSTAFF)
    private Long handoverStaff;

    @ApiModelProperty("接班员名称")
    private String handoverStaffName;

    @ApiModelProperty("交班员")
    @JsonProperty(value = HandoverDef.HANDOVERFROMSTAFF)
    private Long handoverFromStaff;

    @ApiModelProperty("交班员名称")
    private String handoverFromStaffName;

    @ApiModelProperty("交班注意事项")
    @JsonProperty(value = HandoverDef.LASTHANDOVERMATTER)
    private String lastHandoverMatter;

    @ApiModelProperty("班组id")
    @JsonProperty(value = HandoverDef.TEAM_ID)
    private Long teamId;

    @ApiModelProperty("班组名称")
    private String teamName;

    @ApiModelProperty("交接确认")
    @JsonProperty(value = HandoverDef.DELIVERY_CONFIRMATION)
    private Boolean deliveryConfirmation;
}
