package com.cet.eem.fusion.maintenance.core.service.inspection.impl;

import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.RowUtil;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.InspectionSchemeDetail;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.InspectionSchemeDetailExport;
import com.cet.eem.bll.maintenance.def.WorkSheetStatusDef;
import com.cet.eem.bll.maintenance.model.workorder.MaintenanceContent;
import com.cet.eem.bll.maintenance.model.workorder.OperationUser;
import com.cet.eem.bll.maintenance.model.workorder.inspection.DeviceWithRootNameAndId;
import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionParameterWorkOrderDTO;
import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionSearchDto;
import com.cet.eem.bll.maintenance.service.inspection.InspectionWorkOrderService;
import com.cet.eem.bll.maintenance.service.inspection.InspectorParameterExportService;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.def.label.NodeLabelDef;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.fusion.common.utils.excel.PoiExcelUtils;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import com.cet.eem.model.base.SingleModelConditionDTO;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;
import com.cet.eem.fusion.common.modelutils.model.tool.SubConditionBuilder;
import com.cet.eem.service.EemModelDataService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : InspectorParameterExportServiceImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2021-08-25 16:26
 */
@Service
public class InspectorParameterExportServiceImpl implements InspectorParameterExportService {
    @Autowired
    private InspectionWorkOrderService inspectionWorkOrderService;

    @Autowired
    EemModelDataService eemModelDataService;
    /**
     * 导出工单最大数量
     */
    @Value("${cet.eem.event.inspection.export-max-size: 2000}")
    private int exportMaxCount;
    private final static Logger logger = LoggerFactory.getLogger(InspectionParameterServiceImpl.class);
    public static final Integer ANALOG_QUANTITY = 2;

    @Override
    public void exportParameter(InspectionSearchDto dto, HttpServletResponse response) {
        dto.setPage(new Page(0, exportMaxCount));
        dto.setWorkSheetStatus(WorkSheetStatusDef.ACCOMPLISHED);
        //查询工单信息
        ResultWithTotal<List<InspectionParameterWorkOrderDTO>> listResultWithTotal = inspectionWorkOrderService.queryInspectionParameterWorkOrder(dto);
        //查询配电室-配电柜-一段线的节点信息
        List<DeviceWithRootNameAndId> deviceWithRootNameAndIds = handleNodes(getNodes());
        List<InspectionParameterWorkOrderDTO> inspectionWorkOrderDtos = handleSameDayData(listResultWithTotal.getData());
        //处理数据，将工单中巡检参数的信息和节点信息拼接到一起
        List<InspectionSchemeDetailExport> inspectionSchemeDetailExports = handleWorkOrdersAndNodes(inspectionWorkOrderDtos, deviceWithRootNameAndIds);
        //写入数据
        exportData(inspectionSchemeDetailExports, response);
    }

    /**
     * 处理同一天对同一设备巡检的情况，取完成时间最早的数据
     *
     * @param dtos
     * @return
     */
    private List<InspectionParameterWorkOrderDTO> handleSameDayData(List<InspectionParameterWorkOrderDTO> dtos) {
        if (dtos.size() <= 1) {
            return dtos;
        }
        List<InspectionParameterWorkOrderDTO> result = new ArrayList<>();

        Map<BaseVo, List<InspectionParameterWorkOrderDTO>> collect = dtos.stream()
                .collect(Collectors.groupingBy(item ->
                        new BaseVo(item.getDevicePlanRelationshipList().get(0).getDeviceId(),
                                item.getDevicePlanRelationshipList().get(0).getDeviceLabel())));
        for (Map.Entry<BaseVo, List<InspectionParameterWorkOrderDTO>> entry : collect.entrySet()) {
            if (entry.getValue().size() < 2) {
                result.add(entry.getValue().get(0));
                continue;
            }
            Map<LocalDateTime, List<InspectionParameterWorkOrderDTO>> collect1 = entry.getValue().stream()
                    .collect(Collectors.groupingBy(item -> TimeUtil.getFirstTimeOfDay(item.getFinishTime())));
            for (Map.Entry<LocalDateTime, List<InspectionParameterWorkOrderDTO>> entry1 : collect1.entrySet()) {
                List<InspectionParameterWorkOrderDTO> collect2 = entry1.getValue().stream()
                        .sorted(Comparator.comparing(inspectionWorkOrderDto -> TimeUtil.localDateTime2timestamp(inspectionWorkOrderDto.getFinishTime())))
                        .collect(Collectors.toList());
                result.add(collect2.get(0));
            }
        }
        return result;
    }


    /**
     * 获取节点树数据
     *
     * @return
     */
    private List<BaseVo> getNodes() {
        QueryConditionBuilder<BaseEntity> queryConditionBuilder = ParentQueryConditionBuilder.of(NodeLabelDef.ROOM);
        List<SingleModelConditionDTO> subChildren = new ArrayList<>();
        subChildren.add(new SubConditionBuilder(ModelLabelDef.POWER_DIS_CABINET).depth(1).build());
        subChildren.add(new SubConditionBuilder(ModelLabelDef.LINE_SEGMENT).depth(2).build());
        queryConditionBuilder.leftJoinCondition(subChildren).queryAsTree();
        ResultWithTotal<List<Map<String, Object>>> query = eemModelDataService.query(queryConditionBuilder.build());
        return JsonTransferUtils.transferList(query.getData(), BaseVo.class);

    }

    /**
     * 转换成包含配电室-配电柜-一段线id和名称的数据
     *
     * @param baseVos
     * @return
     */
    private List<DeviceWithRootNameAndId> handleNodes(List<BaseVo> baseVos) {
        List<DeviceWithRootNameAndId> result = new ArrayList<>();
        List<BaseVo> collectNode = baseVos.stream().filter(baseVo -> CollectionUtils.isNotEmpty(baseVo.getChildren())).collect(Collectors.toList());
        for (BaseVo room : collectNode) {
            for (BaseVo cabinet : room.getChildren()) {
                if (CollectionUtils.isEmpty(cabinet.getChildren())) {
                    continue;
                }
                for (BaseVo device : cabinet.getChildren()) {
                    DeviceWithRootNameAndId deviceWithRootNameAndId = new DeviceWithRootNameAndId(device.getId(), device.getName(), cabinet.getId(), cabinet.getName(), room.getId(), room.getName());
                    result.add(deviceWithRootNameAndId);
                }
            }
        }
        return result;
    }

    /**
     * 拼接数据，将巡检的数据和设备的父节点信息拼接到一起
     *
     * @param dtos
     * @param nodes
     * @return
     */
    private List<InspectionSchemeDetailExport> handleWorkOrdersAndNodes(List<InspectionParameterWorkOrderDTO> dtos, List<DeviceWithRootNameAndId> nodes) {
        List<InspectionSchemeDetailExport> dataList = new ArrayList<>();
        for (InspectionParameterWorkOrderDTO dto : dtos) {
            for (DeviceWithRootNameAndId node : nodes) {
                if (Objects.equals(dto.getDevicePlanRelationshipList().get(0).getDeviceId(), node.getDeviceId())) {
                    for (InspectionSchemeDetail detail : dto.getInspectionSchemeDetails()) {
                        InspectionSchemeDetailExport export = new InspectionSchemeDetailExport();
                        BeanUtils.copyProperties(detail, export);
                        BeanUtils.copyProperties(node, export);
                        export.setFinishTime(TimeUtil.localDateTime2timestamp(dto.getFinishTime()));
                        export.setStaffName(handleStaffName(dto));
                        dataList.add(export);
                    }
                }
            }
        }
        return dataList;
    }

    private String handleStaffName(InspectionParameterWorkOrderDTO dto) {
        MaintenanceContent maintenanceContent = JsonTransferUtils.parseObject(dto.getMaintenanceContent(), MaintenanceContent.class);
        assert maintenanceContent != null;
        List<OperationUser> users = maintenanceContent.getUsers();
        List<String> collect = users.stream().map(OperationUser::getUserName).collect(Collectors.toList());
        return StringUtils.join(collect, ",");
    }

    private void addSize(List<String> list, int size) {
        for (int i = 0; i < size; i++) {
            list.add(" ");
        }
    }

    private void getHeader(ExcelWriter writer, List<List<String>> rows, List<InspectionSchemeDetailExport> val) {
        List<String> header = new ArrayList<>();
        rows.add(header);
        Sheet sheet = writer.getSheet();
        LocalDateTime localDateTime = TimeUtil.timestamp2LocalDateTime(val.get(0).getFinishTime());
        header.add(val.get(0).getPowerDisCabinetName() + "   " + localDateTime.getYear() + "年" + localDateTime.getMonthValue() + "月" + "   " + "日点检表");
        int size = getSize(val);
        addSize(header, size + 1);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, size + 1));
        List<String> headerSecond = new ArrayList<>();
        rows.add(headerSecond);
        headerSecond.add("区域");
        headerSecond.add(val.get(0).getPowerDisCabinetName());
        headerSecond.add("巡检月份");
        addSize(headerSecond, 3);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 2, 5));
        headerSecond.add(TimeUtil.format(localDateTime, TimeUtil.MONTH_TIME_FORMAT));
        addSize(headerSecond, 3);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 6, 9));
        headerSecond.add("班组");
        addSize(headerSecond, 3);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 10, 13));
        headerSecond.add(val.get(0).getRoomName());
        addSize(headerSecond, 3);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 14, 17));
        headerSecond.add("打印日期");
        addSize(headerSecond, 3);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 18, 21));
        Date date = new Date(System.currentTimeMillis());
        headerSecond.add(TimeUtil.format(date.getTime(), TimeUtil.MONTH_TIME_FORMAT));
        addSize(headerSecond, 3);
        sheet.addMergedRegion(new CellRangeAddress(1, 1, 22, 25));
        assembleCellWithTime(sheet, headerSecond, size);
    }

    private void assembleCellWithTime(Sheet sheet, List<String> headerSecond, int size) {
        if (Objects.equals(28, size)) {
            headerSecond.add("签字确认");
            addSize(headerSecond, 1);
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 26, 27));
            headerSecond.add(" ");
            addSize(headerSecond, 1);
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 28, 29));
        } else if (Objects.equals(29, size)) {
            headerSecond.add("签字确认");
            addSize(headerSecond, 2);
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 26, 28));
            headerSecond.add(" ");
            addSize(headerSecond, 1);
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 29, 30));
        } else if (Objects.equals(30, size)) {
            headerSecond.add("签字确认");
            addSize(headerSecond, 2);
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 26, 28));
            headerSecond.add(" ");
            addSize(headerSecond, 2);
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 29, 31));
        } else {
            headerSecond.add("签字确认");
            addSize(headerSecond, 2);
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 26, 28));
            headerSecond.add(" ");
            addSize(headerSecond, 3);
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 29, 32));
        }
    }

    /**
     * 根据时间获得标题的长度问题
     *
     * @param list
     * @return
     */
    private int getSize(List<InspectionSchemeDetailExport> list) {
        LocalDateTime firstDayOfThisMonth = TimeUtil.getFirstDayOfThisMonth(TimeUtil.timestamp2LocalDateTime(list.get(0).getFinishTime()));
        LocalDateTime lastDayOfThisMonth = TimeUtil.getLastDayOfThisMonth(TimeUtil.timestamp2LocalDateTime(list.get(0).getFinishTime()));
        List<Long> timeRange = TimeUtil.getTimeRange(TimeUtil.localDateTime2timestamp(firstDayOfThisMonth), TimeUtil.localDateTime2timestamp(lastDayOfThisMonth), AggregationCycle.ONE_DAY);
        return timeRange.size();
    }

    /**
     * 判断两个时间是否相等，若时段类型为月则只比较到月份，依次类推
     *
     * @param paramOne   参考数据
     * @param paramTwo   比较数据
     * @param periodType 时段类型
     * @return 判断结果
     */
    Boolean isDateEqual(Date paramOne, Date paramTwo, int periodType) {
        DateTime dOne = new DateTime(paramOne.getTime());
        DateTime dTwo = new DateTime(paramTwo.getTime());
        boolean bYear = dOne.getYear() == dTwo.getYear();
        if (periodType == AggregationCycle.ONE_YEAR) {
            return bYear;
        }
        boolean bMonth = dOne.getMonthOfYear() == dTwo.getMonthOfYear();
        if (periodType == AggregationCycle.ONE_MONTH || periodType == AggregationCycle.THREE_MONTHS) {
            return bYear && bMonth;
        }
        boolean bDay = dOne.getDayOfMonth() == dTwo.getDayOfMonth();
        if (periodType == AggregationCycle.ONE_DAY || periodType == AggregationCycle.SEVEN_DAYS) {
            return bYear && bMonth && bDay;
        }
        boolean bHour = dOne.getHourOfDay() == dTwo.getHourOfDay();
        if (periodType == AggregationCycle.ONE_HOUR) {
            return bYear && bMonth && bDay && bHour;
        }
        boolean bMinute = dOne.getMinuteOfHour() == dTwo.getMinuteOfHour();
        if (periodType == AggregationCycle.THIRTY_MINUTES) {
            return bYear && bMonth && bDay && bHour && bMinute;
        }
        return false;

    }

    private List<InspectionSchemeDetailExport> generateDataList(List<InspectionSchemeDetailExport> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        LocalDateTime firstDayOfThisMonth = TimeUtil.getFirstDayOfThisMonth(TimeUtil.timestamp2LocalDateTime(list.get(0).getFinishTime()));
        LocalDateTime lastDayOfThisMonth = TimeUtil.getLastDayOfThisMonth(TimeUtil.timestamp2LocalDateTime(list.get(0).getFinishTime()));
        List<Long> timeRange = TimeUtil.getTimeRange(TimeUtil.localDateTime2timestamp(firstDayOfThisMonth), TimeUtil.localDateTime2timestamp(lastDayOfThisMonth), AggregationCycle.ONE_DAY);
        List<InspectionSchemeDetailExport> result = new ArrayList<>();
        timeRange.forEach(x -> {
            InspectionSchemeDetailExport item = new InspectionSchemeDetailExport();
            item.setFinishTime(x);
            result.add(item);

            for (InspectionSchemeDetailExport data : list) {
                if (isDateEqual(new Date(x), new Date(data.getFinishTime()), AggregationCycle.ONE_DAY)) {
                    BeanUtils.copyProperties(data, item);
                } else {
                    item.setParaName(data.getParaName());
                }
            }

        });
        return result;
    }

    private void exportData(List<InspectionSchemeDetailExport> inspectionSchemeDetailExports, HttpServletResponse response) {
        Map<BaseVo, List<InspectionSchemeDetailExport>> collect = inspectionSchemeDetailExports.stream().collect(Collectors.groupingBy(it -> new BaseVo(it.getPowerDisCabinetId(), ModelLabelDef.POWER_DIS_CABINET)));
        //按配电柜分类写入不同的sheet

        try {
            ExcelWriter writer = ExcelUtil.getWriter(true);
            // 所有行的集合
            //表头
            int i = 1;
            int totalRow = 0;
            for (Map.Entry<BaseVo, List<InspectionSchemeDetailExport>> entry : collect.entrySet()) {
                List<List<String>> rows = new ArrayList<>();
                List<InspectionSchemeDetailExport> val = entry.getValue();
                //加入表格数据
                ExcelWriter sheet1 = writer.setSheet("sheet" + i);
                getHeader(sheet1, rows, val);
                getConsumpRows(sheet1, rows, val);
                CellStyle baseCellStyle = sheet1.getCellStyle();
                Font font = PoiExcelUtils.createFont(sheet1.getWorkbook(), true, null, 15, HSSFColor.HSSFColorPredefined.BLACK.getIndex());
                baseCellStyle.setFont(font);
                sheet1.setColumnWidth(0, 15);
                sheet1.setColumnWidth(1, 40);
                for (int num = 2; num < rows.get(0).size() + 1; num++) {
                    sheet1.setColumnWidth(num, 7);
                }
                writeRows(rows, sheet1);

                sheet1.getWorkbook().setSheetName(i - 1, val.get(0).getRoomName() + "_" + val.get(0).getPowerDisCabinetName());
                sheet1.setRowHeight(0, 20);
                sheet1.setRowHeight(1, 30);
                handleSpecialCellType(sheet1, rows);
                i++;
                totalRow = totalRow + rows.size();
            }

            exportExcel(writer, response);
        } catch (Exception e) {
            logger.error("导出巡检数据失败！", e);
        }


    }

    private ExcelWriter writeRows(List<List<String>> rows, ExcelWriter excelWriter) {
        for (int i = 0; i < rows.size(); i++) {
            RowUtil.writeRow(excelWriter.getSheet().createRow(i), rows.get(i), excelWriter.getStyleSet(), false);
        }
        return excelWriter;
    }


    private void handleSpecialCellType(ExcelWriter sheet1, List<List<String>> rows) {
        Sheet sheet = sheet1.getSheet();
        for (int i = 0; i < rows.size(); i++) {
            Row row = sheet.getRow(i);
            Cell cell = row.getCell(0);
            if (Objects.nonNull(cell) && Objects.nonNull(cell.getStringCellValue()) && cell.getStringCellValue().contains("位置")) {
                CellStyle cellStyle = cell.getCellStyle();
                CellStyle styleForCell = sheet1.createStyleForCell(0, i);
                styleForCell.cloneStyleFrom(cellStyle);
                styleForCell.setAlignment(HorizontalAlignment.RIGHT);
                styleForCell.setBorderRight(BorderStyle.NONE);
                styleForCell.setBorderTop(BorderStyle.NONE);
                styleForCell.setBorderLeft(BorderStyle.NONE);
                cell.setCellStyle(styleForCell);

                Cell cell1 = row.getCell(1);
                CellStyle styleForCell1 = sheet1.createStyleForCell(1, i);
                styleForCell1.cloneStyleFrom(cellStyle);
                styleForCell1.setAlignment(HorizontalAlignment.LEFT);
                styleForCell1.setBorderRight(BorderStyle.NONE);
                styleForCell1.setBorderTop(BorderStyle.NONE);
                styleForCell1.setBorderLeft(BorderStyle.NONE);
                cell1.setCellStyle(styleForCell1);
            }
            if (Objects.nonNull(cell) && Objects.nonNull(cell.getStringCellValue()) && cell.getStringCellValue().contains("巡检人")) {
                CellStyle cellStyle = cell.getCellStyle();
                CellStyle styleForCell = sheet1.createStyleForCell(0, i);
                styleForCell.cloneStyleFrom(cellStyle);
                sheet1.setRowHeight(i, 50);
                cell.setCellStyle(styleForCell);
                for (int j = 1; j < rows.get(i).size(); j++) {
                    Cell cell1 = row.getCell(j);
                    CellStyle styleForCell1 = sheet1.createStyleForCell(j, i);
                    styleForCell1.cloneStyleFrom(cellStyle);
                    styleForCell1.setVerticalAlignment(VerticalAlignment.CENTER);
                    styleForCell1.setWrapText(true);
                    cell1.setCellStyle(styleForCell1);
                }
            }
        }

    }

    /**
     * 输出表格
     *
     * @param writer
     * @param response
     */
    private void exportExcel(ExcelWriter writer, HttpServletResponse response) throws IOException {
        String description = "巡检数据";

        Date date = new Date(System.currentTimeMillis());
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String current = formatter.format(date);
        String excelName = description + current + ".xlsx";
//        返回文件格式
        try {
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(excelName, "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            return;
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8 ");
        writer.flush(response.getOutputStream());
        writer.close();
    }

    private void getSheetHeader(List<List<String>> rows, InspectionSchemeDetailExport detailExports) {
        List<String> row1 = new ArrayList<>();
        rows.add(row1);
        row1.add("位置：");
        row1.add(detailExports.getDeviceName());
        List<String> row2 = new ArrayList<>();
        rows.add(row2);
        row2.add("序号");
        row2.add("巡检内容");
        LocalDateTime firstDayOfThisMonth = TimeUtil.getFirstDayOfThisMonth(TimeUtil.timestamp2LocalDateTime(detailExports.getFinishTime()));
        LocalDateTime lastDayOfThisMonth = TimeUtil.getLastDayOfThisMonth(TimeUtil.timestamp2LocalDateTime(detailExports.getFinishTime()));
        List<Long> timeRange = TimeUtil.getTimeRange(TimeUtil.localDateTime2timestamp(firstDayOfThisMonth), TimeUtil.localDateTime2timestamp(lastDayOfThisMonth), AggregationCycle.ONE_DAY);
        for (int i = 1; i <= timeRange.size(); i++) {
            row2.add(String.valueOf(i));
        }
    }

    private void getConsumpRows(ExcelWriter writer, List<List<String>> rows, List<InspectionSchemeDetailExport> detailExports) {
        //加个空行
        Map<BaseVo, List<InspectionSchemeDetailExport>> collect = detailExports.stream().collect(Collectors.groupingBy(inspectionSchemeDetailExport -> new BaseVo(inspectionSchemeDetailExport.getDeviceId(), ModelLabelDef.LINE_SEGMENT)));
        Sheet sheet = writer.getSheet();
        collect.forEach((key, val) -> {
            rows.add(new ArrayList<>());
            getSheetHeader(rows, val.get(0));
            Map<BaseVo, List<InspectionSchemeDetailExport>> collect2 = val.stream().filter(inspectionSchemeDetailExport -> Objects.equals(ANALOG_QUANTITY, inspectionSchemeDetailExport.getType())).collect(Collectors.groupingBy(inspectionSchemeDetailExport -> new BaseVo(inspectionSchemeDetailExport.getInspectionParameterId(), ModelLabelDef.INSPECTION_PARAMETER)));
            Map<BaseVo, List<InspectionSchemeDetailExport>> collect3 = val.stream().filter(inspectionSchemeDetailExport -> !Objects.equals(ANALOG_QUANTITY, inspectionSchemeDetailExport.getType())).collect(Collectors.groupingBy(inspectionSchemeDetailExport -> new BaseVo(inspectionSchemeDetailExport.getInspectionParameterId(), ModelLabelDef.INSPECTION_PARAMETER)));
            int num = 0;
            int size = collect2.keySet().size() + collect3.keySet().size();
            writeData(collect2, num, rows, sheet, size, val);
            writeData(collect3, collect2.keySet().size(), rows, sheet, size, val);
        });

    }

    private void writeData(Map<BaseVo, List<InspectionSchemeDetailExport>> collect2, int num, List<List<String>> rows, Sheet sheet, int size, List<InspectionSchemeDetailExport> val) {

        for (Map.Entry<BaseVo, List<InspectionSchemeDetailExport>> entry : collect2.entrySet()) {
            List<InspectionSchemeDetailExport> val1 = entry.getValue();
            List<InspectionSchemeDetailExport> collect1 = val1.stream().sorted(Comparator.comparing(InspectionSchemeDetailExport::getFinishTime)).collect(Collectors.toList());
            List<InspectionSchemeDetailExport> inspectionSchemeDetailExports = generateDataList(collect1);
            List<InspectionSchemeDetailExport> inspectionSchemeDetailExports1 = generateDataList(val.stream().sorted(Comparator.comparing(InspectionSchemeDetailExport::getFinishTime)).collect(Collectors.toList()));
            List<String> rowList = new ArrayList<>();
            rows.add(rowList);
            rowList.add(String.valueOf(num + 1));
            rowList.add(collect1.get(0).getParaName());
            addInspectionSchemeDetail(inspectionSchemeDetailExports, rowList);
            if (num == size - 1) {
                List<String> rowList2 = new ArrayList<>();
                rows.add(rowList2);
                rowList2.add("巡检人");
                addSize(rowList2, 1);
                sheet.addMergedRegion(new CellRangeAddress(rows.size() - 1, rows.size() - 1, 0, 1));
                for (int i = 0; i < inspectionSchemeDetailExports1.size(); i++) {
                    rowList2.add(inspectionSchemeDetailExports1.get(i).getStaffName());
                }
            }
            num++;
        }
    }

    private void addInspectionSchemeDetail(List<InspectionSchemeDetailExport> inspectionSchemeDetailExports, List<String> rowList) {
        DecimalFormat df = new DecimalFormat("0.00");
        for (InspectionSchemeDetailExport detailExport : inspectionSchemeDetailExports) {
            if (Objects.equals(1, detailExport.getType())) {
                if (BooleanUtils.isTrue(detailExport.getStatus())) {
                    rowList.add("√");
                } else if (BooleanUtils.isFalse(detailExport.getStatus())) {
                    rowList.add("×");
                }
            } else if (Objects.equals(2, detailExport.getType()) && Objects.nonNull(detailExport.getValue())) {
                rowList.add((df.format(detailExport.getValue())));
            } else {
                rowList.add(" ");
            }

        }
    }


}