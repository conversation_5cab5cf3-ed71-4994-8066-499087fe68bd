package com.cet.eem.solution.common.utils;

import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.common.def.label.NodeLabelDef;
import com.cet.eem.fusion.common.entity.Result;
import com.cet.eem.fusion.common.feign.config.FlinkConfig;
import com.cet.eem.fusion.common.service.RedisService;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.solution.common.def.common.label.TableColumnNameDef;
import com.cet.eem.solution.common.entity.bo.RecalculateProgress;
import com.cet.eem.solution.common.entity.dto.ProgressUpdateDTO;
import com.cet.eem.solution.common.feign.FlinkRedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 进度更新工具类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ProgressUpdaterUtils {

    private static final Integer TIMEOUT = 20;

    private final RedisService redisService;

    @Autowired
    RestTemplate restTemplate;

    @Autowired
    FlinkConfig flinkConfig;

    @Autowired
    FlinkRedisService flinkRedisService;

    private static final Map<String, String> flinkReCalcUrlMap;

    static {
        flinkReCalcUrlMap = new HashMap<>();
        flinkReCalcUrlMap.put("energy", "/flink-job/energy/re-calc/state");
        flinkReCalcUrlMap.put("groupEnergy", "/flink-job/groupEnergy/re-calc/state");
    }


    public ProgressUpdaterUtils(RedisService redisService) {
        this.redisService = redisService;
    }

    /**
     * 更新进度
     *
     * @param taskName 任务名称
     * @param rate     进度
     */
    public void updateProgress(String taskName, Long projectId, Double rate, Long startTime, Long endTime) {
        //封装成对象，转成json数据保存
        RecalculateProgress recalculateProgress = new RecalculateProgress();
        recalculateProgress.setRate(rate);
        recalculateProgress.setStartTime(startTime);
        recalculateProgress.setEndTime(endTime);

        //写入处理数据的百分比
        HashMap<String, String> map = new HashMap<>();
        map.put(NodeLabelDef.PROJECT + CommonUtils.UNDER_LINE + projectId, JsonTransferUtils.toJSONString(recalculateProgress));
        redisService.addHashSet(taskName, map, TIMEOUT, TimeUnit.MINUTES);
    }

    /**
     * 获取进度
     *
     * @param taskName 任务名称
     * @return 进度
     */
    public ProgressUpdateDTO getProgress(String taskName) {
        ProgressUpdateDTO flinkProgressUpdateDTO = flinkJobHandle(taskName);
        if (Objects.nonNull(flinkProgressUpdateDTO)) {
            return flinkProgressUpdateDTO;
        }
        //业务重算进度查询
        Map<String, String> hashSet = redisService.getHashSet(taskName);
        String value = hashSet.get(NodeLabelDef.PROJECT + CommonUtils.UNDER_LINE + GlobalInfoUtils.getTenantId());
        RecalculateProgress progress = null;
        if (Objects.nonNull(value)) {
            progress = JsonTransferUtils.parseString(value, RecalculateProgress.class);
        }

        ProgressUpdateDTO progressUpdateDTO = new ProgressUpdateDTO();
        progressUpdateDTO.setState(false);
        //如果查询不到job，则直接返回true表示可以运行
        if (Objects.isNull(progress)) {
            progressUpdateDTO.setState(true);
            return progressUpdateDTO;
        }

        progressUpdateDTO.setRatio(progress.getRate());
        progressUpdateDTO.setStartTime(progress.getStartTime());
        progressUpdateDTO.setEndTime(progress.getEndTime());
        //如果进度大于等于1，则表示执行完毕，删除并且返回可执行
        if (progress.getRate() >= 1D) {
            deleteProgress(taskName, GlobalInfoUtils.getTenantId());
            progressUpdateDTO.setState(true);
        }
        return progressUpdateDTO;
    }

    private ProgressUpdateDTO flinkJobHandle(String taskName) {
        //如果job名称中包含_flink则直接走flink-job-service相关接口查询
        if (!taskName.contains("_flink")) {
            return null;
        }
        String jobName = taskName.replace("_flink", "");
        if (!flinkReCalcUrlMap.containsKey(jobName)) {
            return null;
        }
        String url = UriComponentsBuilder.fromHttpUrl(flinkConfig.getHost() + flinkReCalcUrlMap.get(jobName))
                .queryParam(ColumnDef.HEADER_PROJECT_ID, 1L).toUriString();
        ResponseEntity<Result> response = restTemplate.postForEntity(url, null, Result.class);
        if (!Objects.equals(response.getStatusCode(), HttpStatus.OK)) {
            log.error("查询进度异常，url：{}，返回值code：{}，返回值：{}", url, response.getStatusCode(), response.getBody());
            return null;
        } else {
            Map<String, Object> data = (Map<String, Object>) response.getBody().getData();
            ProgressUpdateDTO progressUpdateDTO = new ProgressUpdateDTO();
            if (Objects.isNull(data)) {
                progressUpdateDTO.setState(true);
            }
            if (Objects.nonNull(data) && data.containsKey(TableColumnNameDef.RATIO)) {
                progressUpdateDTO.setRatio(DoubleUtils.roundTwo(Double.parseDouble(data.get(TableColumnNameDef.RATIO).toString())));
                progressUpdateDTO.setState(progressUpdateDTO.getRatio() >= 1);
                if (data.containsKey(TableColumnNameDef.START_TIME)) {
                    progressUpdateDTO.setStartTime(TimeUtil.localDateTime2timestamp(TimeUtil.parse(data.get(TableColumnNameDef.START_TIME).toString().trim(), TimeUtil.DATE_TIME_FORMAT)));
                }
                if (data.containsKey(TableColumnNameDef.END_TIME)) {
                    progressUpdateDTO.setEndTime(TimeUtil.localDateTime2timestamp(TimeUtil.parse(data.get(TableColumnNameDef.END_TIME).toString().trim(), TimeUtil.DATE_TIME_FORMAT)));
                }
            }
            return progressUpdateDTO;
        }
    }

    /**
     * 删除进度
     *
     * @param taskName 任务名称
     */
    public void deleteProgress(String taskName, Long projectId) {
        String[] array = {NodeLabelDef.PROJECT + CommonUtils.UNDER_LINE + projectId};
        redisService.deleteHashSet(taskName, array);
    }
}
