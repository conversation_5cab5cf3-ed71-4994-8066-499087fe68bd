package com.cet.eem.solution.common.feign;

import com.cet.eem.fusion.common.entity.Result;
import com.cet.eem.fusion.common.feign.model.cetml.dynamometer.DynamometerSum;
import com.cet.eem.solution.common.entity.dto.cetml.*;
import com.cet.eem.solution.common.entity.vo.cetml.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * 算法服务接口
 *
 * <AUTHOR>
 */
@FeignClient(value = "cetml-service", url = "${cet.eem.service.url.cetml-service:''}")
public interface CetmlService {

    /**
     * 产液查询
     */
    @PostMapping(value = {"/cetml/dynamometer/liquid"})
    Result<ProductLiquidVO> liquid(@RequestBody ProProgram proProgram);

    /**
     * 功图数据预测
     */
    @PostMapping(value = {"/cetml/dynamometer/predict/{well_no}"})
    Result<List<List<Object>>> predict(@RequestBody DynamometerSum dynamometerSum, @PathVariable("well_no") Long wellId);

    /**
     * 示功图数据增删
     */
    @PostMapping(value = {"/cetml/dynamometer/images/{well_no}"})
    Result<String> addTrain(@RequestBody Train train, @PathVariable("well_no") Long wellId);

    /**
     * 示功图数据删除（images接口在传递参数时，如果携带了train_data，但是设置为null，会因为python的序列化异常报错，所以单独方法进行调用）
     */
    @PostMapping(value = {"/cetml/dynamometer/images/{well_no}"})
    Result<String> deleteTrain(@RequestBody DeleteTrain deleteTrain, @PathVariable("well_no") Long wellId);

    /**
     * 示功图数据卷积神经网络训练
     */
    @PostMapping(value = {"/cetml/dynamometer/train/{well_no}"})
    Result<String> trainSimple(@RequestBody TrainCount trainCount, @PathVariable("well_no") Long wellId);

    /**
     * 判断数据趋势
     */
    @PostMapping(value = {"/cetml/dynamometer/loadtrend"})
    Result<CetmlTrendVO> loadtrend(List<Double> trainCount);

    /**
     * 机采优化训练模型
     */
    @PostMapping(value = {"/cetml/mechanical_optimization/train/{well_no}"})
    Result<CetmlModelTrainVO> mechanicalTrain(@RequestBody Map<String, Object> mechanicalTrainDTOMap, @PathVariable("well_no") Long wellId);

    /**
     * 机采优化模型回归
     */
    @PostMapping(value = {"/cetml/mechanical_optimization/regression/{well_no}/{is_online}/{target_factor}"})
    Result<List<Double>> mechanicalTrainRegression(@RequestBody Map<String, List<Double>> regressionParamMap,
                                                   @PathVariable("well_no") Long wellId,
                                                   @PathVariable("is_online") String isOnline,
                                                   @PathVariable("target_factor") String targetFactor);

    /**
     * 热力图请求
     */
    @PostMapping(value = {"/cetml/thermodynamic"})
    Result<List<List<CoordinateValueVO>>> thermodynamic(@RequestBody Map<String, List<Double>> regressionParamMap);

    /**
     * 间抽周期数据查询
     */
    @PostMapping(value = {"/cetml/intermissivepumping"})
    Result<InterMissivePumpingCycle> interMissivePumping(@RequestBody InterMissivePumping interMissivePumping);

    /**
     * 功图拐点预测接口
     */
    @PostMapping(value = {"/cetml/dynamometer/pointrecognition"})
    Result<List<PointRecognitionVO>> pointrecognition(@RequestBody List<PointRecognitionDTO> pointRecognitionDTOList);

    /**
     * 数据拟合
     */
    @PostMapping(value = {"/cetml/input_data_regression"})
    Result<DataFittingVO> inputDataRegression(@RequestBody DataFittingDTO dataFittingDTO);

    /**
     * 数据多维度拟合接口，并排序
     */
    @PostMapping(value = {"/cetml/multi_curve_fitting"})
    Result<List<MultiCurveFittingVO>> multiCurveFitting(@RequestBody DataFittingDTO dataFittingDTO);
}