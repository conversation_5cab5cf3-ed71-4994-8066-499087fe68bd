﻿package com.cet.eem.fusion.refrigeration.core.entity.po;

import com.cet.eem.annotation.ModelLabel;
import com.cet.eem.fusion.refrigeration.core.model.def.ColdOptimizationLabelDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.model.model.BaseEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : StrategyObjectMap
 * @Description : 策略和节点关联关系表
 * <AUTHOR> jiangzixuan
 * @Date: 2022-04-15 11:39
 */
@Getter
@Setter
@ModelLabel(ColdOptimizationLabelDef.STRATEGY_OBJECT_MAP)
public class StrategyObjectMapPO extends BaseEntity {
    /**
     * 策略
     */
    @JsonProperty(ColdOptimizationLabelDef.STRATEGY_TYPE)
    private Integer strategyType;
    /**
     * 节点对象id
     */
    @JsonProperty(ColumnDef.C_OBJECT_ID)
    private Long objectId;
    /**
     * 节点对象label
     */
    @JsonProperty(ColumnDef.C_OBJECT_Label)
    private String objectLabel;
    /**
     * 规则对象id
     */
    @JsonProperty(ColdOptimizationLabelDef.STRATEGY_ID)
    private Long strategyId;


    /**
     * 频率
     */
    private Double frequency;


    private Double temp;
    /**
     * 冷冻水供回水压差设定值
     */
    @JsonProperty(ColdOptimizationLabelDef.PRESSURE_DIFF)
    private Double pressureDiff;

    public StrategyObjectMap() {
        this.modelLabel = ColdOptimizationLabelDef.STRATEGY_OBJECT_MAP;
    }
}
