package com.cet.eem.fusion.maintenance.core.entity.sign;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * @ClassName : SortSignInPointRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-03-31 16:00
 */
@Getter
@Setter
@ApiModel(value = "SortSignInPointRequest", description = "签到点排序")
public class SortSignInPointRequest {

    @NotNull(message = "签到点id不能为空")
    @ApiModelProperty("签到点id")
    private Long id;

    @NotNull(message = "签到点排序字段不能为空")
    @ApiModelProperty("签到点排序字段,升序排序")
    private Integer sort;

}
