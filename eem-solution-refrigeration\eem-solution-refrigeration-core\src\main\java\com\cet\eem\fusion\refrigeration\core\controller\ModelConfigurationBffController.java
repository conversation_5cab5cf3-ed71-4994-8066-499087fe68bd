﻿package com.cet.eem.fusion.refrigeration.core.controller;

import com.cet.eem.auth.aspect.OperationPermission;
import com.cet.eem.auth.service.NodeAuthCheckService;
import com.cet.eem.auth.service.NodeManageWithAuthService;
import com.cet.eem.bll.common.def.OperationAuthDef;
import com.cet.eem.bll.common.log.annotation.OperationLog;
import com.cet.eem.bll.common.log.constant.EEMOperationLogType;
import com.cet.eem.bll.common.log.constant.EnumOperationSubType;
import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.fusion.refrigeration.core.model.config.DeviceChainParam;
import com.cet.eem.fusion.refrigeration.core.model.config.RefrigeratingSystemVo;
import com.cet.eem.fusion.refrigeration.core.model.config.StartAndStopParam;
import com.cet.eem.fusion.refrigeration.core.model.config.StartAndStopVo;
import com.cet.eem.fusion.refrigeration.core.model.weather.PumpVo;
import com.cet.eem.fusion.refrigeration.core.service.trend.ModelConfigurationService;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * @ClassName : ModelConfigurationController
 * @Description : 建模配置
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-20 19:54
 */
@Api(value = "ModelConfigurationController", tags = {"建模配置接口"})
@RestController
@RequestMapping(value = "/eem/v1/modelConfiguration")
public class ModelConfigurationBffController {
    @Autowired
    ModelConfigurationService modelConfigurationService;
    @Autowired
    NodeManageWithAuthService nodeManageBffService;
    @Autowired
    NodeAuthCheckService nodeAuthCheckService;

    @ApiOperation(value = "校验用户有无全部项目节点权限")
    @PostMapping(value = "/checkOperationAuth", produces = "application/json")
    public Result<Boolean> checkOperationAuth() {
        return Result.ok(nodeAuthCheckService.checkCompleteAuth(new BaseVo(GlobalInfoUtils.getProjectId(), NodeLabelDef.PROJECT), GlobalInfoUtils.getUserId()));
    }

    @ApiOperation(value = "查询所有启用预测的空调机房")
    @PostMapping(value = "/aiUseSystem", produces = "application/json")
    public Result<List<RefrigeratingSystemVo>> queryAiUseSystem() {
        List<RefrigeratingSystemVo> refrigeratingSystemVos = modelConfigurationService.queryAiUseSystem(GlobalInfoUtils.getProjectId(),
                GlobalInfoUtils.getUserId());
        return Result.ok(refrigeratingSystemVos);
    }


    @ApiOperation(value = "根据id查询空调机房（启用预测）")
    @PostMapping(value = "/aiUseSystem/roomId", produces = "application/json")
    public Result<List<RefrigeratingSystemVo>> queryAiUseSystemByRoomId(@RequestParam @ApiParam(name = "roomId", value = "roomId", required = true) Long roomId) {

        return Result.ok(modelConfigurationService.queryAiUseSystemByRoomId(roomId));
    }

    @ApiOperation(value = "写入需要ai预测的空调机房id")
    @OperationPermission(authNames = {OperationAuthDef.MODEL_CONFIG_AI_USE_UPDATE})
    @OperationLog(operationType = EEMOperationLogType.AI_PREDICT_USE, subType = EnumOperationSubType.UPDATE, description = "【新增/修改启用ai系统优化的空调机房】")
    @PostMapping(value = "/aiUseSystem/write", produces = "application/json")
    public Result<Object> writeOrUpdateAiUseSystem(@RequestBody @ApiParam(value = "ids", name = "ids", required = true) List<Long> ids) {
        modelConfigurationService.writeOrUpdateAiUseSystem(ids);
        return Result.ok();
    }

    @ApiOperation(value = "更新板式xxx温度")
    @OperationPermission(authNames = {OperationAuthDef.MODEL_CONFIG_TEMP_UPDATE})
    @OperationLog(operationType = EEMOperationLogType.AI_PREDICT_USE, subType = EnumOperationSubType.UPDATE, description = "【更新板式换热器温度】")
    @PostMapping(value = "/setTemp", produces = "application/json")
    public Result<Object> setOrUpdateTemp(@RequestParam @ApiParam(name = "roomId", value = "roomId", required = true) Long roomId,
                                          @RequestParam @ApiParam(name = "temp", value = "温度", required = true) Double temp,
                                          @RequestParam @ApiParam(name = "outsideTempLimit", value = "板换开启温度限制", required = true) Double outsideTempLimit,
                                          @RequestParam @ApiParam(name = "coolingLoadDemandLimit", value = "板换开启冷机制冷需求限制", required = true) Double coolingLoadDemandLimit) {
        modelConfigurationService.setOrUpdateTemp(roomId, temp,outsideTempLimit,coolingLoadDemandLimit);
        return Result.ok();
    }


    @ApiOperation(value = "写入连锁顺序")
    @OperationLog(operationType = EEMOperationLogType.AI_PREDICT_USE, subType = EnumOperationSubType.UPDATE, description = "【新增/更新连锁顺序】")
    @OperationPermission(authNames = {OperationAuthDef.MODEL_CONFIG_DEVICE_CHAIN_UPDATE})
    @PostMapping(value = "/writeDeviceChain", produces = "application/json")
    public Result<Map<String, Object>> insertDeviceChain(@RequestBody DeviceChainParam deviceChainParam) {
        return Result.ok(modelConfigurationService.insertDeviceChain(deviceChainParam));
    }

    @ApiOperation(value = "查询所有连锁")
    @PostMapping(value = "/allDeviceChain", produces = "application/json")
    public Result<List<DeviceChainParam>> queryAllDeviceChain(@RequestParam @ApiParam(name = "roomId", value = "roomId", required = true) Long roomId) {

        return Result.ok(modelConfigurationService.queryAllDeviceChain(roomId,GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "查询空调机房下的需要连锁的设备")
    @PostMapping(value = "/device/roomId", produces = "application/json")
    public Result<List<PumpVo>> queryChainDevice(@RequestParam @ApiParam(name = "roomId", value = "roomId", required = true) Long roomId) {

        return Result.ok(modelConfigurationService.queryChainDevice(roomId));
    }

    @ApiOperation(value = "删除连锁")
    @OperationPermission(authNames = {OperationAuthDef.MODEL_CONFIG_DEVICE_CHAIN_DELETE})
    @OperationLog(operationType = EEMOperationLogType.AI_PREDICT_USE, subType = EnumOperationSubType.DELETE, description = "【删除连锁】")
    @DeleteMapping(value = "/deviceChain/delete", produces = "application/json")
    public Result<Object> deleteChain(@RequestBody @ApiParam(value = "ids", name = "ids", required = true) List<Long> ids) {
        modelConfigurationService.deleteChain(ids);
        return Result.ok();
    }

    @ApiOperation(value = "根据连锁id查询连锁")
    @PostMapping(value = "/deviceChain/single", produces = "application/json")
    public Result<List<DeviceChainParam>> queryDeviceChainWithDetail(@RequestParam @ApiParam(name = "chainId", value = "chainId", required = true) Long chainId) {

        return Result.ok(modelConfigurationService.queryDeviceChainWithDetail(chainId,GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "写入启停设备操作规则（前端友好版）")
    @OperationPermission(authNames = {OperationAuthDef.MODEL_CONFIG_START_AND_STOP_UPDATE})
    @OperationLog(operationType = EEMOperationLogType.AI_PREDICT_USE, subType = EnumOperationSubType.UPDATE, description = "【新增/修改启停设备操作规则】")
    @PostMapping(value = "/update/stopOrStart", produces = "application/json")
    public Result<Object> writeStartAndStopOperationRule(@RequestBody StartAndStopParam startAndStopParam) {
        modelConfigurationService.writeStartAndStopOperationRule(startAndStopParam);
        return Result.ok();
    }

    @ApiOperation(value = "查询启停顺序（前端友好版）")
    @PostMapping(value = "/device/startOrStop", produces = "application/json")
    public Result<StartAndStopVo> queryStartAndStopOperationRulep(@RequestParam @ApiParam(name = "roomId", value = "roomId", required = true) Long roomId) {

        return Result.ok(modelConfigurationService.queryStartAndStopOperationRule(roomId));
    }

}
