﻿package com.cet.eem.fusion.refrigeration.core.service;

import com.cet.eem.bll.common.model.domain.subject.energysaving.ColdPredict;
import com.cet.eem.bll.common.model.domain.subject.energysaving.WeatherPredict;
import com.cet.eem.fusion.refrigeration.core.model.aioptimization.*;
import com.cet.eem.fusion.refrigeration.core.model.weather.PumpVo;
import com.cet.eem.common.model.ResultWithTotal;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName : AiOptimizationService
 * @Description : ai预测的service
 * <AUTHOR> jiangzixuan
 * @Date: 2022-04-18 13:50
 */
public interface AiOptimizationService {
    /**
     * 气象与负荷预测
     *
     * @param roomId
     * @return
     */
    List<WeatherAndLoadPredictVo> queryWeatherAndLoadPredictVo(Long roomId);

    /**
     * 冷站和末端水温
     *
     * @param roomId
     * @return
     */
    WaterTempVo queryWaterTempVo(Long roomId);

    /**
     * 智能启停策略查询
     *
     * @param roomId
     * @param refrigeratingSystemId
     * @return
     */
    List<AiStartStopStrategyVo> queryAiStartStopStrategyVo(Long roomId, Long refrigeratingSystemId);



    /**
     * 设备状态信息
     *
     * @param roomId
     * @return
     */
    List<DeviceCurrentStatus> queryDeviceCurrentStatus(Long roomId);

    /**
     * 设备工作区间信息
     *
     * @param roomId
     * @return
     */
    WorkSectionListVo queryDeviceWorkSection(Long roomId);

    /**
     * cop
     *
     * @param roomId
     * @return
     */
    EnergySavingEffectVo queryEnergySavingEffectVo(Long roomId);

    /**
     * 查询策略建议
     * @param roomId
     * @return
     */
    StrategyAdviceVo queryStrategyAdvice(Long roomId);

    /**
     * 查询策略
     * @param param
     * @return
     */
    ResultWithTotal<List<StrategyInfoVo>> queryStrategyInfo(StrategyQueryParam param);

    /**
     * 查询节点
     * @param queryLabel
     * @param id
     * @param modelLabel
     * @return
     */
    List<PumpVo> queryNodesByParent(String queryLabel, Long id, String modelLabel);


}

