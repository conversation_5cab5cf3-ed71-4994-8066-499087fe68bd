package com.cet.eem.fusion.maintenance.core.def;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/4/29
 */
public class WorkSheetStatusDef {
    private WorkSheetStatusDef(){}
    /**
     * 全部工单
     */
    public static final Integer ALL = 0;
    /**
     * 待派
     */
    public static final Integer TO_BE_SENT = 1;

    /**
     * 已派
     */
    public static final Integer ALREADY_SENT = 2;

    /**
     * 待审核
     */
    public static final Integer AUDITED = 3;

    /**
     * 审核未通过
     */
    public static final Integer TO_BE_AUDITED = 4;

    /**
     * 废弃
     */
    public static final Integer WASTED = 5;

    /**
     * 已完成
     */
    public static final Integer ACCOMPLISHED = 6;

    /**
     * 异常
     */
    public static final Integer ABNORMAL = 7;

    /**
     * 超时
     */
    public static final Integer OVERTIME = 8;

    public static final Map<Integer, String> MAINTENANCE_ID_NAME_MAP = Collections.unmodifiableMap(getMaintenance());

    public static Map<Integer, String> getMaintenance() {
        Map<Integer, String> result = new HashMap<>();
        result.put(AUDITED, "待审核");
        result.put(TO_BE_SENT, "待维保");
        result.put(TO_BE_AUDITED, "已退回");
        result.put(ACCOMPLISHED, "已完成");
        return result;
    }


}
