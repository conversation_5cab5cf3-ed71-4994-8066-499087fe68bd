﻿package com.cet.eem.fusion.refrigeration.core.entity.vo;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @ClassName : OperationTrendVo
 * @Description : 运行趋势返回值
 * <AUTHOR> jiang<PERSON><PERSON><PERSON>
 * @Date: 2021-12-15 17:09
 */
@Getter
@Setter
@NoArgsConstructor
public class OperationTrendVO {
    /**
     * 供水温度
     */
    private Double supplyTemp;
    /**
     * 回水温度
     */
    private Double returnTemp;
    private Double difference;
    private Long time;
    private Double value;
    /**
     * 功率
     */
    private Double totalSystemPower;
    /**
     * 冷负荷
     */
    private Double coolingLoad;
    /**
     * 供水温度设定值
     */
    private Double supplyTempSetting;
    /**
     * 回水温度设定值
     */
    private Double returnTempSetting;
    public OperationTrendVo(Long time) {
        this.time = time;
    }
}
