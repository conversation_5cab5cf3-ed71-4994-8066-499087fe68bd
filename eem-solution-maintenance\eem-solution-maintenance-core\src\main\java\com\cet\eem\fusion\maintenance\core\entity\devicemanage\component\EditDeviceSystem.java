package com.cet.eem.fusion.maintenance.core.entity.devicemanage.component;

import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@ApiModel(value = "EditDeviceSystem", description = "編輯系統")
public class EditDeviceSystem {
    @Length(max = 50,message = "系统名称长度不能超过50")
    @NotNull(message = "系统名称不能为空")
    private String name;
    @NotNull(message ="系统id不能为空")
    private Long id;
}


