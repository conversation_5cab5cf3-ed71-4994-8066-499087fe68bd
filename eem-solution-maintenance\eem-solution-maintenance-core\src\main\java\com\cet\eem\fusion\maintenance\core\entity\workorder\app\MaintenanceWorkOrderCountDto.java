package com.cet.eem.fusion.maintenance.core.entity.workorder.app;

import com.cet.eem.fusion.maintenance.core.entity.workorder.maintenance.MaintenanceWorkOrderDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;

/**
 * @ClassName : MaintenanceItemExtend
 * @Description : 维保工单计数
 * <AUTHOR> jiangzixuan
 * @Date: 2021-06-10 17:36
 */
@ApiModel(description = "维保工单计数")
@Getter
@Setter
public class MaintenanceWorkOrderCountDto {

    @ApiModelProperty("工单状态")
    private Integer workOrderStatus;

    @ApiModelProperty("工单状态名称")
    private String workOrderStatusName;

    @ApiModelProperty("数量")
    private Integer count;

    @ApiModelProperty("工单详情")
    private List<MaintenanceWorkOrderDto> workOrders;

    public MaintenanceWorkOrderCountDto(Integer workOrderStatus) {
        this.workOrderStatus = workOrderStatus;
        this.workOrders = Collections.singletonList(new MaintenanceWorkOrderDto());
    }

    public MaintenanceWorkOrderCountDto() {
    }
}