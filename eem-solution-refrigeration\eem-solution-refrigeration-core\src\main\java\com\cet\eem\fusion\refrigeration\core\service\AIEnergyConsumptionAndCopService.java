﻿package com.cet.eem.fusion.refrigeration.core.service;

import com.cet.eem.bll.common.model.CompareResult;
import com.cet.eem.bll.common.model.energy.EnergyParam;
import com.cet.eem.fusion.refrigeration.core.model.aiconsumption.AiConsumptionParam;
import com.cet.eem.fusion.refrigeration.core.model.aiconsumption.AiConsumptionSearchVo;
import com.cet.eem.fusion.refrigeration.core.model.aiconsumption.AiEnergyResult;
import com.cet.eem.fusion.refrigeration.core.model.aiconsumption.AiTbHbEnergyVo;
import com.cet.eem.common.model.BaseVo;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @ClassName : AIEnergyConsumptionAndCopService
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-05-31 13:40
 */
public interface AIEnergyConsumptionAndCopService {
    /**
     * 查询制冷节点树
     *
     * @return
     */
    List<BaseVo> treeQuery();

    /**
     * 查询同环比趋势图
     *
     * @param energyParam
     * @return
     */
    AiEnergyResult tbhbEnergyData(AiConsumptionParam energyParam, Long userId);

    /**
     * 查询下方同环比
     *
     * @param searchVo
     * @return
     */
    List<AiTbHbEnergyVo> getTbHbEnergy(AiConsumptionSearchVo searchVo);

    /**
     * 导出数据
     *
     * @param analysisType
     * @param energyParam
     * @param response
     * @param userId
     */
    void exportEnergyData(Integer analysisType, AiConsumptionParam energyParam, HttpServletResponse response, Long userId) throws IOException;

    /**
     * 比较
     * @param energyParam
     * @param userId
     * @return
     */
    List<CompareResult> compareEnergyWithCopData(AiConsumptionParam energyParam, Long userId);
}
