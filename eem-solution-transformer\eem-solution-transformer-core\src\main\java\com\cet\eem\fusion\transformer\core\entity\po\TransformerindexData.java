package com.cet.eem.fusion.transformer.core.entity.po;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @created 28-3月-2022 10:22:11
 */
@Data
public class TransformerindexData extends BaseEntity {

	private Integer aggregationcycle;
	private Double value;
	private Long logtime;
	private Long powertransformerid;
	private Integer type;
	private Long updatetime;

	public TransformerindexData(){
		this.modelLabel="transformerindexdata";
	}

	public TransformerindexData( Long powertransformerid, Integer type) {
		this.powertransformerid = powertransformerid;
		this.type = type;
		this.modelLabel="transformerindexdata";
	}

	public TransformerindexData(Integer aggregationcycle, Double value, Long logtime, Long powertransformerid, Integer type, Long updatetime) {
		this.aggregationcycle = aggregationcycle;
		this.value = value;
		this.logtime = logtime;
		this.powertransformerid = powertransformerid;
		this.type = type;
		this.updatetime = updatetime;
		this.modelLabel="transformerindexdata";
	}
}