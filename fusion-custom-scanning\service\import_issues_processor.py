#!/usr/bin/env python3
"""
Import导入问题处理器
处理split_analysis文件夹中的导入问题扫描结果
"""
import json
import os
from pathlib import Path
from typing import List, Dict, Any
from datetime import datetime


class ImportIssuesProcessor:
    """Import导入问题处理器"""
    
    def __init__(self, split_analysis_dir: str = "output/split_analysis"):
        """
        初始化导入问题处理器
        
        Args:
            split_analysis_dir: split_analysis文件夹路径
        """
        self.split_analysis_dir = split_analysis_dir
        self.issues = []
        
    def scan_import_issues(self) -> List[Dict[str, Any]]:
        """
        扫描split_analysis文件夹中的所有导入问题文件
        
        Returns:
            导入问题列表
        """
        print(f"开始扫描导入问题文件: {self.split_analysis_dir}")
        
        split_dir = Path(self.split_analysis_dir)
        if not split_dir.exists():
            print(f"⚠️ split_analysis目录不存在: {self.split_analysis_dir}")
            return []
            
        # 查找符合新命名规则的文件：{类名}_problems_analysis_report.json
        json_files = list(split_dir.glob("*_problems_analysis_report.json"))
        if not json_files:
            print(f"⚠️ 在{self.split_analysis_dir}中未找到符合命名规则的JSON文件")
            print(f"   预期文件命名格式: {{类名}}_problems_analysis_report.json")
            return []

        print(f"找到 {len(json_files)} 个导入问题文件")
        
        all_import_issues = []
        for json_file in json_files:
            try:
                issues = self.parse_import_issue_file(str(json_file))
                all_import_issues.extend(issues)
                print(f"  - {json_file.name}: {len(issues)} 个导入问题")
            except Exception as e:
                print(f"⚠️ 解析文件 {json_file} 时出错: {e}")
                
        self.issues = all_import_issues
        print(f"总共发现 {len(all_import_issues)} 个导入问题")
        return all_import_issues
        
    def parse_import_issue_file(self, file_path: str) -> List[Dict[str, Any]]:
        """
        解析单个导入问题文件
        
        Args:
            file_path: JSON文件路径
            
        Returns:
            该文件中的导入问题列表
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        # 提取文件基本信息
        file_info = data.get('file', '')
        module = data.get('module', '')
        package = data.get('package', '')
        
        # 解析analysis_results部分
        analysis_results = data.get('analysis_results', [])
        
        issues = []
        for result in analysis_results:
            issue = self.convert_to_standard_format(result, file_info, module, package)
            issues.append(issue)
            
        return issues
        
    def convert_to_standard_format(self, result: Dict[str, Any], file_info: str,
                                 module: str, package: str) -> Dict[str, Any]:
        """
        将导入问题转换为标准格式

        Args:
            result: 原始问题数据
            file_info: 文件信息
            module: 模块名
            package: 包名

        Returns:
            标准格式的问题
        """
        class_name = result.get('class_name', '')
        line = result.get('line', 0)
        description = result.get('description', '')
        matched_class_paths = result.get('matched_class_paths', [])
        suggest = result.get('suggest', '')
        vector_suggest = result.get('vector_suggest', [])  # 提取向量搜索建议

        # 确定严重程度
        severity = self.determine_severity(matched_class_paths, suggest)

        # 构建详细消息
        message = f"导入问题: {description}"
        if matched_class_paths:
            message += f" (建议路径: {', '.join(matched_class_paths)})"

        # 构建建议
        suggestion = suggest
        if matched_class_paths and "请使用新的类路径替换" in suggest:
            if len(matched_class_paths) == 1:
                suggestion = f"建议为类 '{class_name}' 使用: import {matched_class_paths[0]};"
            else:
                suggestion = f"建议为类 '{class_name}' 使用以下路径之一: {', '.join(matched_class_paths)}"
        elif "类已经废弃" in suggest:
            suggestion = f"类 '{class_name}' 已废弃，请寻找替代方案或移除相关代码"

        standard_issue = {
            "file": file_info,
            "type": "idea_check",
            "category": "类问题",
            "problemtype": "类问题",  # 添加problemtype字段
            "severity": severity,
            "line": line,
            "message": message,
            "suggestion": suggestion,
            "details": {
                "class_name": class_name,
                "module": module,
                "package": package,
                "matched_class_paths": matched_class_paths,
                "original_suggest": suggest
            }
        }

        # 添加向量搜索建议到标准格式中
        if vector_suggest:
            standard_issue["vector_suggest"] = vector_suggest
        return standard_issue
        
    def determine_severity(self, matched_class_paths: List[str], suggest: str) -> str:
        """
        根据匹配的类路径和建议确定问题严重程度
        
        Args:
            matched_class_paths: 匹配的类路径列表
            suggest: 建议文本
            
        Returns:
            严重程度 (high/warning/info)
        """
        if "类已经废弃" in suggest or not matched_class_paths:
            return "high"  # 废弃的类或无匹配路径是高优先级问题
        elif len(matched_class_paths) == 1:
            return "warning"  # 有唯一匹配路径是警告级别
        else:
            return "info"  # 有多个匹配路径是信息级别
            
    def export_issues(self, output_file: str):
        """
        导出导入问题到文件
        
        Args:
            output_file: 输出文件路径
        """
        if not self.issues:
            print("⚠️ 没有导入问题需要导出")
            return
            
        # 确保输出目录存在
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            
        export_data = {
            "scan_info": {
                "timestamp": datetime.now().isoformat(),
                "scan_type": "import_issues_scan",
                "total_issues": len(self.issues),
                "source_directory": self.split_analysis_dir
            },
            "statistics": self.generate_statistics(),
            "issues": self.issues
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
            
        print(f"导入问题已导出到: {output_file}")
        
    def generate_statistics(self) -> Dict[str, Any]:
        """
        生成导入问题统计信息
        
        Returns:
            统计信息字典
        """
        if not self.issues:
            return {}
            
        stats = {
            "total_issues": len(self.issues),
            "by_severity": {},
            "by_file": {},
            "by_module": {},
            "by_class": {}
        }
        
        # 按严重程度统计
        for issue in self.issues:
            severity = issue.get('severity', 'unknown')
            stats["by_severity"][severity] = stats["by_severity"].get(severity, 0) + 1
            
        # 按文件统计
        for issue in self.issues:
            file_path = issue.get('file', 'unknown')
            file_name = os.path.basename(file_path) if file_path else 'unknown'
            stats["by_file"][file_name] = stats["by_file"].get(file_name, 0) + 1
            
        # 按模块统计
        for issue in self.issues:
            module = issue.get('details', {}).get('module', 'unknown')
            stats["by_module"][module] = stats["by_module"].get(module, 0) + 1
            
        # 按类名统计
        for issue in self.issues:
            class_name = issue.get('details', {}).get('class_name', 'unknown')
            stats["by_class"][class_name] = stats["by_class"].get(class_name, 0) + 1
            
        return stats
        
    def get_issues(self) -> List[Dict[str, Any]]:
        """
        获取当前的导入问题列表
        
        Returns:
            导入问题列表
        """
        return self.issues
