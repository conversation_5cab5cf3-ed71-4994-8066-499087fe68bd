package com.cet.eem.fusion.maintenance.core.dao;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInStatusRecord;
import com.cet.eem.bll.maintenance.model.sign.SignGroupStatusGroup;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface SignInStatusRecordDao extends BaseModelDao<SignInStatusRecord> {
    /**
     * 根据签到点和签到分组查询记录
     *
     * @param signPointId
     * @param signGroupId
     * @return
     */
    SignInStatusRecord queryRecord(Long signPointId, Long signGroupId);


    /**
     * 查询签到点数量
     *
     * @param signPointStatus
     * @param signPointIds
     * @return
     */
    List<SignInStatusRecord> queryRecord(Collection<Integer> signPointStatus, Collection<Long> signPointIds, Long singGroupId);

    List<SignInStatusRecord> queryRecord(Long signPointId, Collection<Long> singGroupIds);

    /**
     * 根据签到点和签到分组查询记录
     *
     * @param groups
     * @return
     */
    List<SignInStatusRecord> queryRecord(List<SignGroupStatusGroup> groups);
}
