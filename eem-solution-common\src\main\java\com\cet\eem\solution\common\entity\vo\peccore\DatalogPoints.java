package com.cet.eem.solution.common.entity.vo.peccore;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/18 19:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DatalogPoints {
    @ApiModelProperty("场站id")
    private Long stationId;

    @ApiModelProperty("通道id")
    private Long channelId;

    @ApiModelProperty("设备id")
    private Long deviceId;

    @ApiModelProperty("测点名称")
    private String paraName;

    private Long paraHandle;

    private Long dataId;

    private Long dataTypeId;

    @ApiModelProperty("回路号")
    private Integer logicalDeviceIndex;
}
