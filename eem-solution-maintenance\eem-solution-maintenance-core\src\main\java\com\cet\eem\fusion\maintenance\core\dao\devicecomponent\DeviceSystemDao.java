package com.cet.eem.fusion.maintenance.core.dao.devicecomponent;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.DeviceSystem;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.DeviceSystemWithSubLayer;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;

import java.util.List;
import java.util.Map;

public interface DeviceSystemDao extends BaseModelDao<DeviceSystem> {
    List<DeviceSystem> queryAllSystem();

    List<Map<String, Object>> queryDeviceTree();

    List<Map<String, Object>> queryDeviceTreeByProjectId(Long projectId);

    DeviceSystem queryByName(String name);
    DeviceSystem queryByNameAndId(String name,Long id);
    List<DeviceSystemWithSubLayer> queryBySystemId(Long id);
    List<DeviceSystemWithSubLayer> queryBySystemIds(List<Long> ids);
    DeviceSystemWithSubLayer queryByDeviceModelAndLabel(String model ,String objectLabel);
}
