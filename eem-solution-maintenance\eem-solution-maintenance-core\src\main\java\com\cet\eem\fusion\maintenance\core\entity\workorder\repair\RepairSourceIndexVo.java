package com.cet.eem.fusion.maintenance.core.entity.workorder.repair;

import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @ClassName : RepairSourceIndexVo
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2023-11-23 13:56
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RepairSourceIndexVo {
    /**
     * peccore设备的id
     */
    @JsonProperty(ColumnDef.DEVICE_ID)
    private Long deviceId;
    /**
     * 事件发生的时间
     */
    @JsonProperty(ColumnDef.EVENT_TIME)
    private Long eventTime;
    /**
     * 事件类型
     */
    @JsonProperty(ColumnDef.PEC_EVENT_TYPE)
    private Integer pecEventType;
    @JsonProperty(ColumnDef.EVENT_BYTE)
    private Integer eventByte;

    private Integer code1;

    private Integer code2;


}