package com.cet.eem.solution.common.entity.dto.cetml;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据拟合参数
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class DataFittingDTO {
    @ApiModelProperty("x轴相关散点集合")
    private List<Double> x;

    @ApiModelProperty("y轴相关散点集合")
    private List<Double> y;

    @ApiModelProperty("拟合函数类型 1:线性 2:幂拟合 3:指数拟合 4:对数拟合")
    private Integer func;

    public DataFittingDTO() {
        this.x = new ArrayList<>();
        this.y = new ArrayList<>();
    }
}
