<problems is_local_tool="true">
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesConfigDaoImpl.java</file>
  <line>14</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.ClassesConfigDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>55</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesConfigDaoImpl.java</file>
  <line>14</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.ClassesConfigDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfigDao</description>
  <highlighted_element>ClassesConfigDao</highlighted_element>
  <language>JAVA</language>
  <offset>81</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/HolidayConfigDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/HolidayConfigDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/HolidayConfigDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/HolidayConfigDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dao</description>
  <highlighted_element>dao</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/HolidayConfigDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/HolidayConfigDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/HolidayConfigDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/HolidayConfigDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 HolidayConfigDao</description>
  <highlighted_element>HolidayConfigDao</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/HolidayConfigDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/HolidayConfigDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/HolidayConfigDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/HolidayConfigDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/HolidayConfigDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/HolidayConfigDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/HolidayConfigDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/HolidayConfigDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 HolidayConfig</description>
  <highlighted_element>HolidayConfig</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java</file>
  <line>21</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.ClassesSchemeDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>55</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java</file>
  <line>21</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.ClassesSchemeDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeDao</description>
  <highlighted_element>ClassesSchemeDao</highlighted_element>
  <language>JAVA</language>
  <offset>81</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupInfoDaoImpl.java</file>
  <line>14</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.TeamGroupInfoDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>55</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupInfoDaoImpl.java</file>
  <line>14</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.TeamGroupInfoDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoDao</description>
  <highlighted_element>TeamGroupInfoDao</highlighted_element>
  <language>JAVA</language>
  <offset>81</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/ClassesConfigDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/ClassesConfigDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/ClassesConfigDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/ClassesConfigDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/ClassesConfigDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/ClassesConfigDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/ClassesConfigDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/ClassesConfigDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupInfoDaoImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupInfoDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupInfoDaoImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupInfoDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dao</description>
  <highlighted_element>dao</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupInfoDaoImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupInfoDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupInfoDaoImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupInfoDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoDao</description>
  <highlighted_element>TeamGroupInfoDao</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupInfoDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupInfoDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupInfoDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupInfoDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupInfoDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupInfoDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupInfoDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupInfoDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java</file>
  <line>30</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.ClassesSchemeDaoImpl ClassesScheme queryById(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>11</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java</file>
  <line>39</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.ClassesSchemeDaoImpl ClassesScheme queryById(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java</file>
  <line>39</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.ClassesSchemeDaoImpl ClassesScheme queryById(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>90</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/HolidayConfigDaoImpl.java</file>
  <line>28</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.HolidayConfigDaoImpl java.util.List&lt;HolidayConfig&gt; queryBySchedulingScheme(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 HolidayConfig</description>
  <highlighted_element>HolidayConfig</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/HolidayConfigDaoImpl.java</file>
  <line>34</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.HolidayConfigDaoImpl java.util.List&lt;HolidayConfig&gt; queryBySchedulingScheme(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 HolidayConfig</description>
  <highlighted_element>HolidayConfig</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/HolidayConfigDaoImpl.java</file>
  <line>34</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.HolidayConfigDaoImpl java.util.List&lt;HolidayConfig&gt; queryBySchedulingScheme(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 HolidayConfig</description>
  <highlighted_element>HolidayConfig</highlighted_element>
  <language>JAVA</language>
  <offset>74</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java</file>
  <line>65</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingClassesDaoImpl java.util.List&lt;SchedulingClasses&gt; queryByTimeRange(java.lang.Long startTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java</file>
  <line>70</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingClassesDaoImpl java.util.List&lt;SchedulingClasses&gt; queryByTimeRange(java.lang.Long startTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java</file>
  <line>70</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingClassesDaoImpl java.util.List&lt;SchedulingClasses&gt; queryByTimeRange(java.lang.Long startTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>78</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java</file>
  <line>30</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingClassesDaoImpl java.util.List&lt;SchedulingClasses&gt; queryByClassesConfig(java.util.List&lt;java.lang.Long&gt; classesConfigIds)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java</file>
  <line>34</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingClassesDaoImpl java.util.List&lt;SchedulingClasses&gt; queryByClassesConfig(java.util.List&lt;java.lang.Long&gt; classesConfigIds)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java</file>
  <line>34</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingClassesDaoImpl java.util.List&lt;SchedulingClasses&gt; queryByClassesConfig(java.util.List&lt;java.lang.Long&gt; classesConfigIds)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>78</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/TeamGroupEnergyDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/TeamGroupEnergyDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/TeamGroupEnergyDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/TeamGroupEnergyDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/TeamGroupEnergyDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/TeamGroupEnergyDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/TeamGroupEnergyDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/TeamGroupEnergyDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>98</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl java.util.List&lt;SchedulingScheme&gt; queryProduceSchedulingScheme(java.lang.Integer classTeamType)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>102</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl java.util.List&lt;SchedulingScheme&gt; queryProduceSchedulingScheme(java.lang.Integer classTeamType)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>57</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingClassesDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingClassesDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingClassesDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingClassesDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingClassesDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingClassesDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingClassesDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingClassesDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesConfigDaoImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesConfigDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesConfigDaoImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesConfigDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dao</description>
  <highlighted_element>dao</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesConfigDaoImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesConfigDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesConfigDaoImpl.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesConfigDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfigDao</description>
  <highlighted_element>ClassesConfigDao</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesConfigDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesConfigDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesConfigDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesConfigDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesConfigDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesConfigDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesConfigDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesConfigDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingClassesDao.java</file>
  <line>48</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.SchedulingClassesDao java.util.List&lt;SchedulingClasses&gt; queryByTimeRange(java.lang.Long startTime, java.lang.Long endTime)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dao</description>
  <highlighted_element>dao</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDao</description>
  <highlighted_element>SchedulingSchemeDao</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>10</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>10</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>10</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>10</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>11</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>11</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>11</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dto</description>
  <highlighted_element>dto</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>11</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>11</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeQueryDTO</description>
  <highlighted_element>SchedulingSchemeQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/TeamGroupEnergyDao.java</file>
  <line>40</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.TeamGroupEnergyDao java.util.List&lt;TeamGroupEnergy&gt; queryClassesConfigDayEnergy(java.lang.Long startTime, java.lang.Long endTime, java.lang.Integer energyType, java.lang.Long nodeId, java.lang.String nodeLabel, java.util.List&lt;java.lang.Long&gt; classesConfigIds)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/TeamGroupEnergyDao.java</file>
  <line>54</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.TeamGroupEnergyDao java.util.List&lt;TeamGroupEnergy&gt; queryClassesConfigDayEnergy(java.lang.Long startTime, java.lang.Long endTime, java.lang.Integer energyType, java.lang.Long nodeId, java.lang.String nodeLabel, java.util.List&lt;java.lang.Long&gt; classesConfigIds, java.util.List&lt;java.lang.Long&gt; teamGroupIdList)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/TeamGroupEnergyDao.java</file>
  <line>27</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.TeamGroupEnergyDao java.util.List&lt;TeamGroupEnergy&gt; queryTeamGroupEnergy(java.lang.Long startTime, java.lang.Long endTime, java.lang.Integer energyType, java.lang.Long nodeId, java.lang.String nodeLabel, java.util.List&lt;java.lang.Long&gt; teamGroupIds, java.lang.Integer cycle)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java</file>
  <line>85</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingClassesDaoImpl java.util.List&lt;SchedulingClasses&gt; queryByTimeRange(java.lang.Long startTime, java.lang.Long endTime)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java</file>
  <line>90</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingClassesDaoImpl java.util.List&lt;SchedulingClasses&gt; queryByTimeRange(java.lang.Long startTime, java.lang.Long endTime)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java</file>
  <line>90</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingClassesDaoImpl java.util.List&lt;SchedulingClasses&gt; queryByTimeRange(java.lang.Long startTime, java.lang.Long endTime)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>78</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/HolidayConfigDaoImpl.java</file>
  <line>19</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.HolidayConfigDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 HolidayConfig</description>
  <highlighted_element>HolidayConfig</highlighted_element>
  <language>JAVA</language>
  <offset>55</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/HolidayConfigDaoImpl.java</file>
  <line>19</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.HolidayConfigDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 HolidayConfigDao</description>
  <highlighted_element>HolidayConfigDao</highlighted_element>
  <language>JAVA</language>
  <offset>81</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dao</description>
  <highlighted_element>dao</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeDao</description>
  <highlighted_element>ClassesSchemeDao</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/ClassesSchemeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/ClassesConfigDao.java</file>
  <line>11</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.ClassesConfigDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>55</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java</file>
  <line>48</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingClassesDaoImpl java.util.List&lt;SchedulingClasses&gt; queryByTeamGroup(java.util.List&lt;java.lang.Long&gt; teamGroupIds)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java</file>
  <line>52</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingClassesDaoImpl java.util.List&lt;SchedulingClasses&gt; queryByTeamGroup(java.util.List&lt;java.lang.Long&gt; teamGroupIds)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java</file>
  <line>52</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingClassesDaoImpl java.util.List&lt;SchedulingClasses&gt; queryByTeamGroup(java.util.List&lt;java.lang.Long&gt; teamGroupIds)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>78</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/TeamGroupEnergyDao.java</file>
  <line>13</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.TeamGroupEnergyDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>57</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>67</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl java.util.List&lt;SchedulingScheme&gt; queryAll()" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>70</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl java.util.List&lt;SchedulingScheme&gt; queryAll()" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>57</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>26</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>58</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>26</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDao</description>
  <highlighted_element>SchedulingSchemeDao</highlighted_element>
  <language>JAVA</language>
  <offset>87</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>45</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl ResultWithTotal&lt;List&lt;SchedulingScheme&gt;&gt; pageQuery(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ResultWithTotal</description>
  <highlighted_element>ResultWithTotal</highlighted_element>
  <language>JAVA</language>
  <offset>11</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>45</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl ResultWithTotal&lt;List&lt;SchedulingScheme&gt;&gt; pageQuery(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>45</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl ResultWithTotal&lt;List&lt;SchedulingScheme&gt;&gt; pageQuery(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeQueryDTO</description>
  <highlighted_element>SchedulingSchemeQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>61</offset>
  <length>24</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>55</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl ResultWithTotal&lt;List&lt;SchedulingScheme&gt;&gt; pageQuery(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>55</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl ResultWithTotal&lt;List&lt;SchedulingScheme&gt;&gt; pageQuery(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>93</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>57</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl ResultWithTotal&lt;List&lt;SchedulingScheme&gt;&gt; pageQuery(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>80</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl SchedulingScheme queryAssociationNodeById(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>11</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>85</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl SchedulingScheme queryAssociationNodeById(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeDaoImpl.java</file>
  <line>85</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeDaoImpl SchedulingScheme queryAssociationNodeById(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>93</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingClassesDao.java</file>
  <line>39</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.SchedulingClassesDao java.util.List&lt;SchedulingClasses&gt; queryByTimeRange(java.lang.Long startTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingClassesDao.java</file>
  <line>30</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.SchedulingClassesDao java.util.List&lt;SchedulingClasses&gt; queryByTeamGroup(java.util.List&lt;java.lang.Long&gt; teamGroupIds)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingClassesDao.java</file>
  <line>13</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.SchedulingClassesDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>59</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingClassesDao.java</file>
  <line>21</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.SchedulingClassesDao java.util.List&lt;SchedulingClasses&gt; queryByClassesConfig(java.util.List&lt;java.lang.Long&gt; classesConfigIds)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java</file>
  <line>20</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingClassesDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>59</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java</file>
  <line>20</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingClassesDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesDao</description>
  <highlighted_element>SchedulingClassesDao</highlighted_element>
  <language>JAVA</language>
  <offset>89</offset>
  <length>20</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dao</description>
  <highlighted_element>dao</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesDao</description>
  <highlighted_element>SchedulingClassesDao</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>20</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingClassesDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/SchedulingSchemeVO.java</file>
  <line>32</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.vo</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.entity.vo.SchedulingSchemeVO SchedulingSchemeVO(SchedulingScheme schedulingScheme)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/ClassesSchemeDao.java</file>
  <line>18</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.ClassesSchemeDao ClassesScheme queryById(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>4</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/TeamGroupInfoVO.java</file>
  <line>39</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.vo</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.entity.vo.TeamGroupInfoVO TeamGroupInfoVO(TeamGroupInfo teamGroupInfo)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/SchedulingSchemeVO.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.vo</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/SchedulingSchemeVO.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/SchedulingSchemeVO.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.vo</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/SchedulingSchemeVO.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/SchedulingSchemeVO.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.vo</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/SchedulingSchemeVO.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/SchedulingSchemeVO.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.vo</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/SchedulingSchemeVO.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/TeamGroupInfoVO.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.vo</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/TeamGroupInfoVO.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/TeamGroupInfoVO.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.vo</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/TeamGroupInfoVO.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/TeamGroupInfoVO.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.vo</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/TeamGroupInfoVO.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/TeamGroupInfoVO.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.vo</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/TeamGroupInfoVO.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/ClassesSchemeDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/ClassesSchemeDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/ClassesSchemeDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/ClassesSchemeDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/ClassesSchemeDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/ClassesSchemeDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/ClassesSchemeDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/ClassesSchemeDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/ClassesConfigVO.java</file>
  <line>36</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.vo</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesConfigVO ClassesConfigVO(ClassesConfig classesConfig)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/ClassesSchemeDao.java</file>
  <line>11</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.ClassesSchemeDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>55</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/ClassesConfigVO.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.vo</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/ClassesConfigVO.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/ClassesConfigVO.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.vo</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/ClassesConfigVO.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/ClassesConfigVO.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.vo</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/ClassesConfigVO.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/ClassesConfigVO.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.vo</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/ClassesConfigVO.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/HolidayConfigDao.java</file>
  <line>21</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.HolidayConfigDao java.util.List&lt;HolidayConfig&gt; queryBySchedulingScheme(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 HolidayConfig</description>
  <highlighted_element>HolidayConfig</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/HolidayConfigDao.java</file>
  <line>13</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.HolidayConfigDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 HolidayConfig</description>
  <highlighted_element>HolidayConfig</highlighted_element>
  <language>JAVA</language>
  <offset>55</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/HolidayConfigDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/HolidayConfigDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/HolidayConfigDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/HolidayConfigDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/HolidayConfigDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/HolidayConfigDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/HolidayConfigDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/HolidayConfigDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 HolidayConfig</description>
  <highlighted_element>HolidayConfig</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/ClassesSchemeVO.java</file>
  <line>34</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.vo</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.entity.vo.ClassesSchemeVO ClassesSchemeVO(ClassesScheme classesScheme)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/ClassesSchemeVO.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.vo</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/ClassesSchemeVO.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/ClassesSchemeVO.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.vo</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/ClassesSchemeVO.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/ClassesSchemeVO.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.vo</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/ClassesSchemeVO.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/ClassesSchemeVO.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.vo</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/vo/ClassesSchemeVO.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/TeamGroupInfoDao.java</file>
  <line>11</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.TeamGroupInfoDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>55</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/TeamGroupInfoDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/TeamGroupInfoDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/TeamGroupInfoDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/TeamGroupInfoDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/TeamGroupInfoDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/TeamGroupInfoDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/TeamGroupInfoDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/TeamGroupInfoDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeToNodeDao.java</file>
  <line>21</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeToNodeDao java.util.List&lt;SchedulingSchemeToNode&gt; queryBySchedulingSchemeId(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>22</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>33</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamEnergyService java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>26</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>33</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamEnergyService java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoQueryDTO</description>
  <highlighted_element>TeamGroupEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>67</offset>
  <length>27</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java</file>
  <line>15</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>58</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeToNodeDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeToNodeDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeToNodeDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeToNodeDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeToNodeDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeToNodeDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeToNodeDao.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeToNodeDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>22</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeToNodeDao.java</file>
  <line>13</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeToNodeDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>64</offset>
  <length>22</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dto</description>
  <highlighted_element>dto</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeQueryDTO</description>
  <highlighted_element>SchedulingSchemeQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java</file>
  <line>39</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeDao SchedulingScheme queryAssociationNodeById(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>4</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java</file>
  <line>46</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeDao java.util.List&lt;SchedulingScheme&gt; queryProduceSchedulingScheme(java.lang.Integer classTeamType)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dto</description>
  <highlighted_element>dto</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>3</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoQueryDTO</description>
  <highlighted_element>ClassesEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>25</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dto</description>
  <highlighted_element>dto</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>4</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoQueryDTO</description>
  <highlighted_element>TeamGroupEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>27</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 vo</description>
  <highlighted_element>vo</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>2</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 vo</description>
  <highlighted_element>vo</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>2</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>26</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 vo</description>
  <highlighted_element>vo</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>2</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoVO</description>
  <highlighted_element>TeamGroupEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>21</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>31</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ResultWithTotal</description>
  <highlighted_element>ResultWithTotal</highlighted_element>
  <language>JAVA</language>
  <offset>4</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>31</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>24</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>31</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeQueryDTO</description>
  <highlighted_element>SchedulingSchemeQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>74</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java</file>
  <line>31</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeDao java.util.List&lt;SchedulingScheme&gt; queryAll()" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>23</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.lang.Boolean addOrUpdateSchedulingScheme(SchedulingSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeAddUpdateDTO</description>
  <highlighted_element>SchedulingSchemeAddUpdateDTO</highlighted_element>
  <language>JAVA</language>
  <offset>40</offset>
  <length>28</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>41</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamEnergyService java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>41</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamEnergyService java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoQueryDTO</description>
  <highlighted_element>TeamGroupEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>56</offset>
  <length>27</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>144</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java</file>
  <line>24</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeDao ResultWithTotal&lt;List&lt;SchedulingScheme&gt;&gt; pageQuery(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ResultWithTotal</description>
  <highlighted_element>ResultWithTotal</highlighted_element>
  <language>JAVA</language>
  <offset>4</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java</file>
  <line>24</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeDao ResultWithTotal&lt;List&lt;SchedulingScheme&gt;&gt; pageQuery(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/SchedulingSchemeDao.java</file>
  <line>24</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.SchedulingSchemeDao ResultWithTotal&lt;List&lt;SchedulingScheme&gt;&gt; pageQuery(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeQueryDTO</description>
  <highlighted_element>SchedulingSchemeQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>54</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>87</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeAddUpdateDTO</description>
  <highlighted_element>ClassesSchemeAddUpdateDTO</highlighted_element>
  <language>JAVA</language>
  <offset>37</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>25</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamEnergyService TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoVO</description>
  <highlighted_element>TeamGroupEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>4</offset>
  <length>21</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>25</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamEnergyService TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoQueryDTO</description>
  <highlighted_element>TeamGroupEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>51</offset>
  <length>27</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>71</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.lang.Boolean saveSchedulingSchemeRelatedNode(SchedulingSchemeRelatedNodeDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeRelatedNodeDTO</description>
  <highlighted_element>SchedulingSchemeRelatedNodeDTO</highlighted_element>
  <language>JAVA</language>
  <offset>44</offset>
  <length>30</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>37</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamEnergyController com.cet.electric.commons.ApiResult&lt;TeamGroupEnergyInfoVO&gt; queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoVO</description>
  <highlighted_element>TeamGroupEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>21</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>37</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamEnergyController com.cet.electric.commons.ApiResult&lt;TeamGroupEnergyInfoVO&gt; queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoQueryDTO</description>
  <highlighted_element>TeamGroupEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>82</offset>
  <length>27</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>49</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamEnergyService ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>4</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamEnergyService.java</file>
  <line>49</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamEnergyService ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoQueryDTO</description>
  <highlighted_element>ClassesEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>43</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dto</description>
  <highlighted_element>dto</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoQueryDTO</description>
  <highlighted_element>ClassesEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>25</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dto</description>
  <highlighted_element>dto</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoQueryDTO</description>
  <highlighted_element>TeamGroupEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>27</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 vo</description>
  <highlighted_element>vo</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>2</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 vo</description>
  <highlighted_element>vo</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>2</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>8</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>26</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 vo</description>
  <highlighted_element>vo</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>2</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>9</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoVO</description>
  <highlighted_element>TeamGroupEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>21</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>43</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamEnergyController com.cet.electric.commons.ApiResult&lt;java.util.List&lt;TeamGroupEnergyHistogramVO&gt;&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>26</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>43</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamEnergyController com.cet.electric.commons.ApiResult&lt;java.util.List&lt;TeamGroupEnergyHistogramVO&gt;&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoQueryDTO</description>
  <highlighted_element>TeamGroupEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>98</offset>
  <length>27</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>49</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamEnergyController com.cet.electric.commons.ApiResult&lt;java.util.List&lt;ClassesEnergyInfoVO&gt;&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>49</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamEnergyController com.cet.electric.commons.ApiResult&lt;java.util.List&lt;ClassesEnergyInfoVO&gt;&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoQueryDTO</description>
  <highlighted_element>TeamGroupEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>87</offset>
  <length>27</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>55</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.lang.Boolean saveSchedulingSchemeRelatedHoliday(SchedulingSchemeRelatedHolidayDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeRelatedHolidayDTO</description>
  <highlighted_element>SchedulingSchemeRelatedHolidayDTO</highlighted_element>
  <language>JAVA</language>
  <offset>47</offset>
  <length>33</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>55</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamEnergyController com.cet.electric.commons.ApiResult&lt;ClassesEnergyInfoVO&gt; queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamEnergyController.java</file>
  <line>55</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamEnergyController com.cet.electric.commons.ApiResult&lt;ClassesEnergyInfoVO&gt; queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoQueryDTO</description>
  <highlighted_element>ClassesEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>74</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>17</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeToNodeDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>64</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>17</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeToNodeDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNodeDao</description>
  <highlighted_element>SchedulingSchemeToNodeDao</highlighted_element>
  <language>JAVA</language>
  <offset>99</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/知识库/03 节点操作.md</file>
  <line>13</line>
  <module>eem-solution-group-energy</module>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/知识库/03 节点操作.md" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 Autowired</description>
  <highlighted_element>Autowired</highlighted_element>
  <language>JAVA</language>
  <offset>5</offset>
  <length>9</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/知识库/03 节点操作.md</file>
  <line>14</line>
  <module>eem-solution-group-energy</module>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/知识库/03 节点操作.md" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 EemNodeService</description>
  <highlighted_element>EemNodeService</highlighted_element>
  <language>JAVA</language>
  <offset>4</offset>
  <length>14</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/知识库/03 节点操作.md</file>
  <line>73</line>
  <module>eem-solution-group-energy</module>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/知识库/03 节点操作.md" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 Api</description>
  <highlighted_element>Api</highlighted_element>
  <language>JAVA</language>
  <offset>1</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/知识库/03 节点操作.md</file>
  <line>74</line>
  <module>eem-solution-group-energy</module>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/知识库/03 节点操作.md" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 RequestMapping</description>
  <highlighted_element>RequestMapping</highlighted_element>
  <language>JAVA</language>
  <offset>1</offset>
  <length>14</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/知识库/03 节点操作.md</file>
  <line>75</line>
  <module>eem-solution-group-energy</module>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/知识库/03 节点操作.md" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 RestController</description>
  <highlighted_element>RestController</highlighted_element>
  <language>JAVA</language>
  <offset>1</offset>
  <length>14</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/知识库/03 节点操作.md</file>
  <line>76</line>
  <module>eem-solution-group-energy</module>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/知识库/03 节点操作.md" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 Validated</description>
  <highlighted_element>Validated</highlighted_element>
  <language>JAVA</language>
  <offset>1</offset>
  <length>9</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/知识库/03 节点操作.md</file>
  <line>77</line>
  <module>eem-solution-group-energy</module>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/知识库/03 节点操作.md" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 EemBaseNodeManageController</description>
  <highlighted_element>EemBaseNodeManageController</highlighted_element>
  <language>JAVA</language>
  <offset>45</offset>
  <length>27</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/知识库/03 节点操作.md</file>
  <line>110</line>
  <module>eem-solution-group-energy</module>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/知识库/03 节点操作.md" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 Autowired</description>
  <highlighted_element>Autowired</highlighted_element>
  <language>JAVA</language>
  <offset>5</offset>
  <length>9</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/知识库/03 节点操作.md</file>
  <line>111</line>
  <module>eem-solution-group-energy</module>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/知识库/03 节点操作.md" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 EemTreeConfigService</description>
  <highlighted_element>EemTreeConfigService</highlighted_element>
  <language>JAVA</language>
  <offset>4</offset>
  <length>20</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/知识库/03 节点操作.md</file>
  <line>146</line>
  <module>eem-solution-group-energy</module>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/知识库/03 节点操作.md" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 Api</description>
  <highlighted_element>Api</highlighted_element>
  <language>JAVA</language>
  <offset>1</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/知识库/03 节点操作.md</file>
  <line>147</line>
  <module>eem-solution-group-energy</module>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/知识库/03 节点操作.md" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 RequestMapping</description>
  <highlighted_element>RequestMapping</highlighted_element>
  <language>JAVA</language>
  <offset>1</offset>
  <length>14</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/知识库/03 节点操作.md</file>
  <line>148</line>
  <module>eem-solution-group-energy</module>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/知识库/03 节点操作.md" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 RestController</description>
  <highlighted_element>RestController</highlighted_element>
  <language>JAVA</language>
  <offset>1</offset>
  <length>14</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/知识库/03 节点操作.md</file>
  <line>149</line>
  <module>eem-solution-group-energy</module>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/知识库/03 节点操作.md" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 Validated</description>
  <highlighted_element>Validated</highlighted_element>
  <language>JAVA</language>
  <offset>1</offset>
  <length>9</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/知识库/03 节点操作.md</file>
  <line>150</line>
  <module>eem-solution-group-energy</module>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/知识库/03 节点操作.md" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 EemBaseTreeConfigController</description>
  <highlighted_element>EemBaseTreeConfigController</highlighted_element>
  <language>JAVA</language>
  <offset>45</offset>
  <length>27</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/po/SchedulingScheme.java</file>
  <line>45</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.po</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme SchedulingScheme(SchedulingSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeAddUpdateDTO</description>
  <highlighted_element>SchedulingSchemeAddUpdateDTO</highlighted_element>
  <language>JAVA</language>
  <offset>28</offset>
  <length>28</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/po/SchedulingScheme.java</file>
  <line>45</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.po</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.entity.po.SchedulingScheme SchedulingScheme(SchedulingSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeAddUpdateDTO</description>
  <highlighted_element>SchedulingSchemeAddUpdateDTO</highlighted_element>
  <language>JAVA</language>
  <offset>28</offset>
  <length>28</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/po/SchedulingScheme.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.po</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/po/SchedulingScheme.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/po/SchedulingScheme.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.po</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/po/SchedulingScheme.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/po/SchedulingScheme.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.po</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/po/SchedulingScheme.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dto</description>
  <highlighted_element>dto</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/po/SchedulingScheme.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.po</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/po/SchedulingScheme.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/po/SchedulingScheme.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.entity.po</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/entity/po/SchedulingScheme.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeAddUpdateDTO</description>
  <highlighted_element>SchedulingSchemeAddUpdateDTO</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>28</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>17</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeToNodeDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>64</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>17</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeToNodeDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNodeDao</description>
  <highlighted_element>SchedulingSchemeToNodeDao</highlighted_element>
  <language>JAVA</language>
  <offset>99</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dao</description>
  <highlighted_element>dao</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>5</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNodeDao</description>
  <highlighted_element>SchedulingSchemeToNodeDao</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>25</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>22</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>26</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeToNodeDaoImpl java.util.List&lt;SchedulingSchemeToNode&gt; queryBySchedulingSchemeId(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>27</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeToNodeDaoImpl java.util.List&lt;SchedulingSchemeToNode&gt; queryBySchedulingSchemeId(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/SchedulingSchemeToNodeDaoImpl.java</file>
  <line>27</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.SchedulingSchemeToNodeDaoImpl java.util.List&lt;SchedulingSchemeToNode&gt; queryBySchedulingSchemeId(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>83</offset>
  <length>22</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>31</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ResultWithTotal</description>
  <highlighted_element>ResultWithTotal</highlighted_element>
  <language>JAVA</language>
  <offset>4</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>31</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>24</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>31</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeQueryDTO</description>
  <highlighted_element>SchedulingSchemeQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>74</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>23</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.lang.Boolean addOrUpdateSchedulingScheme(SchedulingSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeAddUpdateDTO</description>
  <highlighted_element>SchedulingSchemeAddUpdateDTO</highlighted_element>
  <language>JAVA</language>
  <offset>40</offset>
  <length>28</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>144</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>87</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeAddUpdateDTO</description>
  <highlighted_element>ClassesSchemeAddUpdateDTO</highlighted_element>
  <language>JAVA</language>
  <offset>37</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>71</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.lang.Boolean saveSchedulingSchemeRelatedNode(SchedulingSchemeRelatedNodeDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeRelatedNodeDTO</description>
  <highlighted_element>SchedulingSchemeRelatedNodeDTO</highlighted_element>
  <language>JAVA</language>
  <offset>44</offset>
  <length>30</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>55</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.lang.Boolean saveSchedulingSchemeRelatedHoliday(SchedulingSchemeRelatedHolidayDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeRelatedHolidayDTO</description>
  <highlighted_element>SchedulingSchemeRelatedHolidayDTO</highlighted_element>
  <language>JAVA</language>
  <offset>47</offset>
  <length>33</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>38</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.util.List&lt;SchedulingSchemeDetailVO&gt; allSchedulingScheme()" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>127</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.util.List&lt;TeamGroupInfoVO&gt; queryTeamGroupInfo(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoVO</description>
  <highlighted_element>TeamGroupInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>111</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoAddUpdateDTO</description>
  <highlighted_element>TeamGroupInfoAddUpdateDTO</highlighted_element>
  <language>JAVA</language>
  <offset>37</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>135</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.lang.Boolean saveSchedulingClasses(SchedulingClassesSaveDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesSaveDTO</description>
  <highlighted_element>SchedulingClassesSaveDTO</highlighted_element>
  <language>JAVA</language>
  <offset>34</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>154</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>95</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.util.List&lt;ClassesSchemeVO&gt; queryClassesScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeVO</description>
  <highlighted_element>ClassesSchemeVO</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/TeamConfigService.java</file>
  <line>161</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.TeamConfigService java.util.List&lt;SchedulingSchemeDetailVO&gt; queryProduceSchedulingSchemeByType(java.lang.Integer classTeamType)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>9</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java</file>
  <line>20</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.TeamGroupEnergyDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>57</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java</file>
  <line>20</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.TeamGroupEnergyDaoImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyDao</description>
  <highlighted_element>TeamGroupEnergyDao</highlighted_element>
  <language>JAVA</language>
  <offset>85</offset>
  <length>18</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java</file>
  <line>34</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.TeamGroupEnergyDaoImpl java.util.List&lt;TeamGroupEnergy&gt; queryTeamGroupEnergy(java.lang.Long startTime, java.lang.Long endTime, java.lang.Integer energyType, java.lang.Long nodeId, java.lang.String nodeLabel, java.util.List&lt;java.lang.Long&gt; teamGroupIds, java.lang.Integer cycle)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java</file>
  <line>39</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.TeamGroupEnergyDaoImpl java.util.List&lt;TeamGroupEnergy&gt; queryTeamGroupEnergy(java.lang.Long startTime, java.lang.Long endTime, java.lang.Integer energyType, java.lang.Long nodeId, java.lang.String nodeLabel, java.util.List&lt;java.lang.Long&gt; teamGroupIds, java.lang.Integer cycle)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java</file>
  <line>39</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.TeamGroupEnergyDaoImpl java.util.List&lt;TeamGroupEnergy&gt; queryTeamGroupEnergy(java.lang.Long startTime, java.lang.Long endTime, java.lang.Integer energyType, java.lang.Long nodeId, java.lang.String nodeLabel, java.util.List&lt;java.lang.Long&gt; teamGroupIds, java.lang.Integer cycle)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>76</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dao</description>
  <highlighted_element>dao</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java</file>
  <line>6</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyDao</description>
  <highlighted_element>TeamGroupEnergyDao</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>18</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java</file>
  <line>7</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>35</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java</file>
  <line>94</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.TeamGroupEnergyDaoImpl java.util.List&lt;TeamGroupEnergy&gt; queryClassesConfigDayEnergy(java.lang.Long startTime, java.lang.Long endTime, java.lang.Integer energyType, java.lang.Long nodeId, java.lang.String nodeLabel, java.util.List&lt;java.lang.Long&gt; classesConfigIds, java.util.List&lt;java.lang.Long&gt; teamGroupIdList)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java</file>
  <line>99</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.TeamGroupEnergyDaoImpl java.util.List&lt;TeamGroupEnergy&gt; queryClassesConfigDayEnergy(java.lang.Long startTime, java.lang.Long endTime, java.lang.Integer energyType, java.lang.Long nodeId, java.lang.String nodeLabel, java.util.List&lt;java.lang.Long&gt; classesConfigIds, java.util.List&lt;java.lang.Long&gt; teamGroupIdList)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java</file>
  <line>99</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.TeamGroupEnergyDaoImpl java.util.List&lt;TeamGroupEnergy&gt; queryClassesConfigDayEnergy(java.lang.Long startTime, java.lang.Long endTime, java.lang.Integer energyType, java.lang.Long nodeId, java.lang.String nodeLabel, java.util.List&lt;java.lang.Long&gt; classesConfigIds, java.util.List&lt;java.lang.Long&gt; teamGroupIdList)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>76</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java</file>
  <line>64</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.TeamGroupEnergyDaoImpl java.util.List&lt;TeamGroupEnergy&gt; queryClassesConfigDayEnergy(java.lang.Long startTime, java.lang.Long endTime, java.lang.Integer energyType, java.lang.Long nodeId, java.lang.String nodeLabel, java.util.List&lt;java.lang.Long&gt; classesConfigIds)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java</file>
  <line>68</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.TeamGroupEnergyDaoImpl java.util.List&lt;TeamGroupEnergy&gt; queryClassesConfigDayEnergy(java.lang.Long startTime, java.lang.Long endTime, java.lang.Integer energyType, java.lang.Long nodeId, java.lang.String nodeLabel, java.util.List&lt;java.lang.Long&gt; classesConfigIds)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/dao/impl/TeamGroupEnergyDaoImpl.java</file>
  <line>68</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.dao.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.dao.impl.TeamGroupEnergyDaoImpl java.util.List&lt;TeamGroupEnergy&gt; queryClassesConfigDayEnergy(java.lang.Long startTime, java.lang.Long endTime, java.lang.Integer energyType, java.lang.Long nodeId, java.lang.String nodeLabel, java.util.List&lt;java.lang.Long&gt; classesConfigIds)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>76</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>372</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>11</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>372</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoQueryDTO</description>
  <highlighted_element>ClassesEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>50</offset>
  <length>25</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>373</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>373</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>37</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>376</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>382</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>383</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>396</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>407</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>427</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>436</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>436</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesName</description>
  <highlighted_element>ClassesName</highlighted_element>
  <language>JAVA</language>
  <offset>33</offset>
  <length>11</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>437</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>441</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>446</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>446</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesName</description>
  <highlighted_element>ClassesName</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>11</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>446</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>62</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>446</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl ClassesEnergyInfoVO queryClassesEnergy(ClassesEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesName</description>
  <highlighted_element>ClassesName</highlighted_element>
  <language>JAVA</language>
  <offset>82</offset>
  <length>11</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>47</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl teamGroupEnergyDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyDao</description>
  <highlighted_element>TeamGroupEnergyDao</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>18</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>468</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;java.util.Map&lt;java.lang.String,java.lang.Object&gt;&gt; classesEnergyProjectTree(java.lang.Integer energyType, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>22</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>44</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl schedulingSchemeDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDao</description>
  <highlighted_element>SchedulingSchemeDao</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>56</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl schemeToNodeDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNodeDao</description>
  <highlighted_element>SchedulingSchemeToNodeDao</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>153</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>26</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>153</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoQueryDTO</description>
  <highlighted_element>TeamGroupEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>74</offset>
  <length>27</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>161</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>167</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>170</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>178</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>190</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>26</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>193</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>26</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>193</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>48</offset>
  <length>26</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>199</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>204</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>26</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>204</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>44</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>205</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>208</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>211</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>216</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>217</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>26</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>217</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>51</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>217</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>93</offset>
  <length>26</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>217</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>120</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>219</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>33</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>232</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>26</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>232</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>47</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>232</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>89</offset>
  <length>26</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>232</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;TeamGroupEnergyHistogramVO&gt; queryTeamGroupEnergyHistogram(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>116</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>53</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl nodeService" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 NodeServiceImpl</description>
  <highlighted_element>NodeServiceImpl</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>50</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl unitService" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 UnitService</description>
  <highlighted_element>UnitService</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>11</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>65</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoVO</description>
  <highlighted_element>TeamGroupEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>11</offset>
  <length>21</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>65</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoQueryDTO</description>
  <highlighted_element>TeamGroupEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>58</offset>
  <length>27</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>67</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoVO</description>
  <highlighted_element>TeamGroupEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>21</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>67</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoVO</description>
  <highlighted_element>TeamGroupEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>21</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>70</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>76</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>80</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>98</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>23</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>101</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyCard</description>
  <highlighted_element>TeamGroupEnergyCard</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>102</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>34</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>103</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyCard</description>
  <highlighted_element>TeamGroupEnergyCard</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>103</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyCard</description>
  <highlighted_element>TeamGroupEnergyCard</highlighted_element>
  <language>JAVA</language>
  <offset>43</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>106</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>108</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl TeamGroupEnergyInfoVO queryTeamGroupEnergyInfo(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>271</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>271</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoQueryDTO</description>
  <highlighted_element>TeamGroupEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>63</offset>
  <length>27</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>272</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>275</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>281</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>282</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>294</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>305</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>23</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>310</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>310</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>41</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>316</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergy</description>
  <highlighted_element>TeamGroupEnergy</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>319</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>319</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesName</description>
  <highlighted_element>ClassesName</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>11</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>319</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>62</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>319</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesName</description>
  <highlighted_element>ClassesName</highlighted_element>
  <language>JAVA</language>
  <offset>82</offset>
  <length>11</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>320</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl java.util.List&lt;ClassesEnergyInfoVO&gt; queryClassesEnergyCompare(TeamGroupEnergyInfoQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>13</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>13</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dao</description>
  <highlighted_element>dao</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>13</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>13</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDao</description>
  <highlighted_element>SchedulingSchemeDao</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>14</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>14</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dao</description>
  <highlighted_element>dao</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>14</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>14</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNodeDao</description>
  <highlighted_element>SchedulingSchemeToNodeDao</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>25</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>15</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>15</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dao</description>
  <highlighted_element>dao</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>15</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>24</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>15</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyDao</description>
  <highlighted_element>TeamGroupEnergyDao</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>18</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>17</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>17</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>17</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dto</description>
  <highlighted_element>dto</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>17</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>17</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoQueryDTO</description>
  <highlighted_element>ClassesEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>25</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>18</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>18</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>18</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 dto</description>
  <highlighted_element>dto</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>3</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>18</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>18</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoQueryDTO</description>
  <highlighted_element>TeamGroupEnergyInfoQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>27</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>19</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>19</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>19</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 vo</description>
  <highlighted_element>vo</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>2</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>19</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>19</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesEnergyInfoVO</description>
  <highlighted_element>ClassesEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>20</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>20</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>20</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 vo</description>
  <highlighted_element>vo</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>2</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>20</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>20</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyCard</description>
  <highlighted_element>TeamGroupEnergyCard</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>21</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>21</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>21</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 vo</description>
  <highlighted_element>vo</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>2</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>21</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>21</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyHistogramVO</description>
  <highlighted_element>TeamGroupEnergyHistogramVO</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>26</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>22</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>22</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 entity</description>
  <highlighted_element>entity</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>6</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>22</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 vo</description>
  <highlighted_element>vo</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>2</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>22</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 classes</description>
  <highlighted_element>classes</highlighted_element>
  <language>JAVA</language>
  <offset>30</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>22</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupEnergyInfoVO</description>
  <highlighted_element>TeamGroupEnergyInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>38</offset>
  <length>21</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>23</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>23</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 service</description>
  <highlighted_element>service</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>23</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 UnitService</description>
  <highlighted_element>UnitService</highlighted_element>
  <language>JAVA</language>
  <offset>28</offset>
  <length>11</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>24</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>24</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 service</description>
  <highlighted_element>service</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>24</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 impl</description>
  <highlighted_element>impl</highlighted_element>
  <language>JAVA</language>
  <offset>28</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>24</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 NodeServiceImpl</description>
  <highlighted_element>NodeServiceImpl</highlighted_element>
  <language>JAVA</language>
  <offset>33</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>25</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>25</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 teamenergy</description>
  <highlighted_element>teamenergy</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>10</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>25</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 service</description>
  <highlighted_element>service</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>25</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamEnergyService</description>
  <highlighted_element>TeamEnergyService</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java</file>
  <line>41</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamEnergyServiceImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamEnergyService</description>
  <highlighted_element>TeamEnergyService</highlighted_element>
  <language>JAVA</language>
  <offset>46</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamConfigController.java</file>
  <line>171</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamConfigController com.cet.electric.commons.ApiResult&lt;java.util.List&lt;SchedulingClassesVO&gt;&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamConfigController.java</file>
  <line>131</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamConfigController com.cet.electric.commons.ApiResult&lt;java.lang.Boolean&gt; addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoAddUpdateDTO</description>
  <highlighted_element>TeamGroupInfoAddUpdateDTO</highlighted_element>
  <language>JAVA</language>
  <offset>68</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamConfigController.java</file>
  <line>62</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamConfigController com.cet.electric.commons.ApiResult&lt;java.util.List&lt;SchedulingSchemeDetailVO&gt;&gt; allSchedulingScheme()" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamConfigController.java</file>
  <line>145</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamConfigController com.cet.electric.commons.ApiResult&lt;java.util.List&lt;TeamGroupInfoVO&gt;&gt; queryTeamGroupInfo(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoVO</description>
  <highlighted_element>TeamGroupInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamConfigController.java</file>
  <line>108</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamConfigController com.cet.electric.commons.ApiResult&lt;java.lang.Boolean&gt; addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeAddUpdateDTO</description>
  <highlighted_element>ClassesSchemeAddUpdateDTO</highlighted_element>
  <language>JAVA</language>
  <offset>68</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamConfigController.java</file>
  <line>114</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamConfigController com.cet.electric.commons.ApiResult&lt;java.util.List&lt;ClassesSchemeVO&gt;&gt; queryClassesScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeVO</description>
  <highlighted_element>ClassesSchemeVO</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamConfigController.java</file>
  <line>54</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamConfigController com.cet.electric.commons.ApiResult&lt;java.util.List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingSchemeByType(java.lang.Integer classTeamType)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamConfigController.java</file>
  <line>160</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamConfigController com.cet.electric.commons.ApiResult&lt;java.util.List&lt;SchedulingClassesVO&gt;&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>26</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamConfigController.java</file>
  <line>154</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamConfigController com.cet.electric.commons.ApiResult&lt;java.lang.Boolean&gt; saveSchedulingClasses(SchedulingClassesSaveDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesSaveDTO</description>
  <highlighted_element>SchedulingClassesSaveDTO</highlighted_element>
  <language>JAVA</language>
  <offset>65</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamConfigController.java</file>
  <line>42</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamConfigController com.cet.electric.commons.ApiResult&lt;java.lang.Boolean&gt; addOrUpdateSchedulingScheme(SchedulingSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeAddUpdateDTO</description>
  <highlighted_element>SchedulingSchemeAddUpdateDTO</highlighted_element>
  <language>JAVA</language>
  <offset>71</offset>
  <length>28</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamConfigController.java</file>
  <line>48</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamConfigController ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ResultWithTotal</description>
  <highlighted_element>ResultWithTotal</highlighted_element>
  <language>JAVA</language>
  <offset>11</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamConfigController.java</file>
  <line>48</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamConfigController ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>24</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamConfigController.java</file>
  <line>48</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamConfigController ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeQueryDTO</description>
  <highlighted_element>SchedulingSchemeQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>94</offset>
  <length>24</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamConfigController.java</file>
  <line>93</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamConfigController com.cet.electric.commons.ApiResult&lt;java.lang.Boolean&gt; saveSchedulingSchemeRelatedNode(SchedulingSchemeRelatedNodeDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeRelatedNodeDTO</description>
  <highlighted_element>SchedulingSchemeRelatedNodeDTO</highlighted_element>
  <language>JAVA</language>
  <offset>75</offset>
  <length>30</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/controller/TeamConfigController.java</file>
  <line>78</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.controller</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.controller.TeamConfigController com.cet.electric.commons.ApiResult&lt;java.lang.Boolean&gt; saveSchedulingSchemeRelatedHoliday(SchedulingSchemeRelatedHolidayDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeRelatedHolidayDTO</description>
  <highlighted_element>SchedulingSchemeRelatedHolidayDTO</highlighted_element>
  <language>JAVA</language>
  <offset>78</offset>
  <length>33</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>237</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingSchemeRelatedNode(SchedulingSchemeRelatedNodeDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeRelatedNodeDTO</description>
  <highlighted_element>SchedulingSchemeRelatedNodeDTO</highlighted_element>
  <language>JAVA</language>
  <offset>51</offset>
  <length>30</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>239</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingSchemeRelatedNode(SchedulingSchemeRelatedNodeDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>250</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingSchemeRelatedNode(SchedulingSchemeRelatedNodeDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>252</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingSchemeRelatedNode(SchedulingSchemeRelatedNodeDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>22</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>252</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingSchemeRelatedNode(SchedulingSchemeRelatedNodeDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>48</offset>
  <length>22</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>416</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl void deleteClassesScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>427</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl void deleteClassesScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>64</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateSchedulingScheme(SchedulingSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeAddUpdateDTO</description>
  <highlighted_element>SchedulingSchemeAddUpdateDTO</highlighted_element>
  <language>JAVA</language>
  <offset>47</offset>
  <length>28</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>66</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateSchedulingScheme(SchedulingSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>79</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateSchedulingScheme(SchedulingSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>79</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateSchedulingScheme(SchedulingSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>48</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>567</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingClasses(SchedulingClassesSaveDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesSaveDTO</description>
  <highlighted_element>SchedulingClassesSaveDTO</highlighted_element>
  <language>JAVA</language>
  <offset>41</offset>
  <length>24</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>569</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingClasses(SchedulingClassesSaveDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>575</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingClasses(SchedulingClassesSaveDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>576</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingClasses(SchedulingClassesSaveDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesConfigDTO</description>
  <highlighted_element>SchedulingClassesConfigDTO</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>26</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>577</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingClasses(SchedulingClassesSaveDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>577</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingClasses(SchedulingClassesSaveDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>54</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>273</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;com.cet.eem.fusion.common.model.BaseVo&gt; querySchedulingSchemeRelatedNode(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>22</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>745</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; queryProduceSchedulingSchemeByType(java.lang.Integer classTeamType)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>24</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>746</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; queryProduceSchedulingSchemeByType(java.lang.Integer classTeamType)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>51</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl teamGroupInfoDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoDao</description>
  <highlighted_element>TeamGroupInfoDao</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>668</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>675</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>690</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>23</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>693</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>694</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>696</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>697</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>34</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>698</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>698</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>41</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>701</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesConfigVO</description>
  <highlighted_element>SchedulingClassesConfigVO</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>25</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>702</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>703</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesConfigVO</description>
  <highlighted_element>SchedulingClassesConfigVO</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>25</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>703</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesConfigVO</description>
  <highlighted_element>SchedulingClassesConfigVO</highlighted_element>
  <language>JAVA</language>
  <offset>64</offset>
  <length>25</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>705</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>715</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClassesTeamGroupInfo(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>500</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl void deleteTeamGroupInfo(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>42</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl classesSchemeDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeDao</description>
  <highlighted_element>ClassesSchemeDao</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>36</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl holidayConfigDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 HolidayConfigDao</description>
  <highlighted_element>HolidayConfigDao</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>191</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingSchemeRelatedHoliday(SchedulingSchemeRelatedHolidayDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeRelatedHolidayDTO</description>
  <highlighted_element>SchedulingSchemeRelatedHolidayDTO</highlighted_element>
  <language>JAVA</language>
  <offset>54</offset>
  <length>33</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>194</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingSchemeRelatedHoliday(SchedulingSchemeRelatedHolidayDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 HolidayConfig</description>
  <highlighted_element>HolidayConfig</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>203</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingSchemeRelatedHoliday(SchedulingSchemeRelatedHolidayDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 HolidayConfig</description>
  <highlighted_element>HolidayConfig</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>205</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingSchemeRelatedHoliday(SchedulingSchemeRelatedHolidayDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 HolidayConfig</description>
  <highlighted_element>HolidayConfig</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>205</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean saveSchedulingSchemeRelatedHoliday(SchedulingSchemeRelatedHolidayDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 HolidayConfig</description>
  <highlighted_element>HolidayConfig</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>109</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; allSchedulingScheme()" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>24</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>110</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; allSchedulingScheme()" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>514</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;TeamGroupInfoVO&gt; queryTeamGroupInfo(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoVO</description>
  <highlighted_element>TeamGroupInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>515</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;TeamGroupInfoVO&gt; queryTeamGroupInfo(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoVO</description>
  <highlighted_element>TeamGroupInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>516</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;TeamGroupInfoVO&gt; queryTeamGroupInfo(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>521</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;TeamGroupInfoVO&gt; queryTeamGroupInfo(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>522</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;TeamGroupInfoVO&gt; queryTeamGroupInfo(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoVO</description>
  <highlighted_element>TeamGroupInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>522</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;TeamGroupInfoVO&gt; queryTeamGroupInfo(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoVO</description>
  <highlighted_element>TeamGroupInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>50</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>531</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;TeamGroupInfoVO&gt; queryTeamGroupInfo(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 Result</description>
  <highlighted_element>Result</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>6</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>390</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;ClassesSchemeVO&gt; queryClassesScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeVO</description>
  <highlighted_element>ClassesSchemeVO</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>392</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;ClassesSchemeVO&gt; queryClassesScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>400</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;ClassesSchemeVO&gt; queryClassesScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeVO</description>
  <highlighted_element>ClassesSchemeVO</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>401</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;ClassesSchemeVO&gt; queryClassesScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>402</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;ClassesSchemeVO&gt; queryClassesScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeVO</description>
  <highlighted_element>ClassesSchemeVO</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>402</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;ClassesSchemeVO&gt; queryClassesScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeVO</description>
  <highlighted_element>ClassesSchemeVO</highlighted_element>
  <language>JAVA</language>
  <offset>37</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>48</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl schedulingClassesDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesDao</description>
  <highlighted_element>SchedulingClassesDao</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>20</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>33</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl schedulingSchemeDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDao</description>
  <highlighted_element>SchedulingSchemeDao</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>91</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ResultWithTotal</description>
  <highlighted_element>ResultWithTotal</highlighted_element>
  <language>JAVA</language>
  <offset>11</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>91</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>32</offset>
  <length>24</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>91</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeQueryDTO</description>
  <highlighted_element>SchedulingSchemeQueryDTO</highlighted_element>
  <language>JAVA</language>
  <offset>81</offset>
  <length>24</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>93</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ResultWithTotal</description>
  <highlighted_element>ResultWithTotal</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>93</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>29</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>94</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>24</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>95</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ResultWithTotal</description>
  <highlighted_element>ResultWithTotal</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>95</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>29</offset>
  <length>24</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>95</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl ResultWithTotal&lt;List&lt;SchedulingSchemeDetailVO&gt;&gt; querySchedulingScheme(SchedulingSchemeQueryDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ResultWithTotal</description>
  <highlighted_element>ResultWithTotal</highlighted_element>
  <language>JAVA</language>
  <offset>69</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>438</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoAddUpdateDTO</description>
  <highlighted_element>TeamGroupInfoAddUpdateDTO</highlighted_element>
  <language>JAVA</language>
  <offset>44</offset>
  <length>25</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>442</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>447</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>454</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>454</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>46</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>463</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>471</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>479</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>479</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateTeamGroupInfo(TeamGroupInfoAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>46</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>289</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeAddUpdateDTO</description>
  <highlighted_element>ClassesSchemeAddUpdateDTO</highlighted_element>
  <language>JAVA</language>
  <offset>44</offset>
  <length>25</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>290</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfigDTO</description>
  <highlighted_element>ClassesConfigDTO</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>292</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfigDTO</description>
  <highlighted_element>ClassesConfigDTO</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>293</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfigDTO</description>
  <highlighted_element>ClassesConfigDTO</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>305</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>311</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>319</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>320</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfigDTO</description>
  <highlighted_element>ClassesConfigDTO</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>321</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>321</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>43</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>330</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>330</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>46</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>340</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>348</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>354</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>358</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>366</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>367</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfigDTO</description>
  <highlighted_element>ClassesConfigDTO</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>368</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>368</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.lang.Boolean addOrUpdateClassesScheme(ClassesSchemeAddUpdateDTO dto)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>43</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>222</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;java.lang.Long&gt; querySchedulingSchemeRelatedHoliday(java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 HolidayConfig</description>
  <highlighted_element>HolidayConfig</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>39</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl schedulingSchemeToNodeDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNodeDao</description>
  <highlighted_element>SchedulingSchemeToNodeDao</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>25</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>598</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>604</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>615</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>23</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>618</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>619</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>621</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>622</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>34</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>623</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>623</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesVO</description>
  <highlighted_element>SchedulingClassesVO</highlighted_element>
  <language>JAVA</language>
  <offset>41</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>626</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesConfigVO</description>
  <highlighted_element>SchedulingClassesConfigVO</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>25</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>627</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>628</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesConfigVO</description>
  <highlighted_element>SchedulingClassesConfigVO</highlighted_element>
  <language>JAVA</language>
  <offset>16</offset>
  <length>25</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>628</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClassesConfigVO</description>
  <highlighted_element>SchedulingClassesConfigVO</highlighted_element>
  <language>JAVA</language>
  <offset>64</offset>
  <length>25</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>630</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>640</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingClassesVO&gt; querySchedulingClasses(java.lang.Long starTime, java.lang.Long endTime, java.lang.Long schedulingSchemeId)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfig</description>
  <highlighted_element>ClassesConfig</highlighted_element>
  <language>JAVA</language>
  <offset>25</offset>
  <length>13</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>761</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>24</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>761</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>77</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>762</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>24</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>763</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>764</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>24</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>764</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeDetailVO</description>
  <highlighted_element>SchedulingSchemeDetailVO</highlighted_element>
  <language>JAVA</language>
  <offset>46</offset>
  <length>24</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>772</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeVO</description>
  <highlighted_element>ClassesSchemeVO</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>773</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>774</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeVO</description>
  <highlighted_element>ClassesSchemeVO</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>774</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesSchemeVO</description>
  <highlighted_element>ClassesSchemeVO</highlighted_element>
  <language>JAVA</language>
  <offset>58</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>781</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoVO</description>
  <highlighted_element>TeamGroupInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>782</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfo</description>
  <highlighted_element>TeamGroupInfo</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>783</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoVO</description>
  <highlighted_element>TeamGroupInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>15</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>783</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl java.util.List&lt;SchedulingSchemeDetailVO&gt; getSchedulingSchemeDetailVOS(java.util.List&lt;SchedulingScheme&gt; schedulingSchemes)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamGroupInfoVO</description>
  <highlighted_element>TeamGroupInfoVO</highlighted_element>
  <language>JAVA</language>
  <offset>58</offset>
  <length>15</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>11</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 service</description>
  <highlighted_element>service</highlighted_element>
  <language>JAVA</language>
  <offset>19</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>11</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 EemCloudAuthService</description>
  <highlighted_element>EemCloudAuthService</highlighted_element>
  <language>JAVA</language>
  <offset>27</offset>
  <length>19</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>12</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 piem</description>
  <highlighted_element>piem</highlighted_element>
  <language>JAVA</language>
  <offset>15</offset>
  <length>4</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>12</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 teamenergy</description>
  <highlighted_element>teamenergy</highlighted_element>
  <language>JAVA</language>
  <offset>20</offset>
  <length>10</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>12</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 service</description>
  <highlighted_element>service</highlighted_element>
  <language>JAVA</language>
  <offset>31</offset>
  <length>7</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>12</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="file" FQNAME="file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamConfigService</description>
  <highlighted_element>TeamConfigService</highlighted_element>
  <language>JAVA</language>
  <offset>39</offset>
  <length>17</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>54</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl cloudAuthService" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 EemCloudAuthService</description>
  <highlighted_element>EemCloudAuthService</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>19</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>45</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="field" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl classesConfigDao" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesConfigDao</description>
  <highlighted_element>ClassesConfigDao</highlighted_element>
  <language>JAVA</language>
  <offset>12</offset>
  <length>16</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>126</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl void deleteSchedulingScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingScheme</description>
  <highlighted_element>SchedulingScheme</highlighted_element>
  <language>JAVA</language>
  <offset>8</offset>
  <length>16</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>138</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl void deleteSchedulingScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>147</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl void deleteSchedulingScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 ClassesScheme</description>
  <highlighted_element>ClassesScheme</highlighted_element>
  <language>JAVA</language>
  <offset>17</offset>
  <length>13</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>156</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl void deleteSchedulingScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingClasses</description>
  <highlighted_element>SchedulingClasses</highlighted_element>
  <language>JAVA</language>
  <offset>21</offset>
  <length>17</length>
</problem><problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>164</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="method" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl void deleteSchedulingScheme(java.lang.Long id)" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 SchedulingSchemeToNode</description>
  <highlighted_element>SchedulingSchemeToNode</highlighted_element>
  <language>JAVA</language>
  <offset>13</offset>
  <length>22</length>
</problem>
<problem>
  <file>file://$PROJECT_DIR$/eem-solution-group-energy-core/src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamConfigServiceImpl.java</file>
  <line>30</line>
  <module>eem-solution-group-energy-core</module>
  <package>com.cet.eem.fusion.groupenergy.core.service.impl</package>
  <entry_point TYPE="class" FQNAME="com.cet.eem.fusion.groupenergy.core.service.impl.TeamConfigServiceImpl" />
  <problem_class id="QodanaJavaSanity" severity="ERROR" attribute_key="WRONG_REFERENCES_ATTRIBUTES">Java 健全性</problem_class>
  <description>未解析的引用 TeamConfigService</description>
  <highlighted_element>TeamConfigService</highlighted_element>
  <language>JAVA</language>
  <offset>46</offset>
  <length>17</length>
</problem>
</problems>