package com.cet.eem.fusion.maintenance.core.service.maintenance.impl;

import com.cet.eem.auth.service.AuthUtils;
import com.cet.eem.fusion.config.sdk.service.EemNodeService;
import com.cet.eem.fusion.common.def.quantity.FrequencyDef;
import com.cet.eem.fusion.common.def.quantity.PhasorDef;
import com.cet.eem.fusion.common.def.quantity.QuantityCategoryDef;
import com.cet.eem.fusion.common.def.quantity.QuantityTypeDef;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.*;
import com.cet.eem.bll.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.PlanSheetWithSubLayer;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.bll.maintenance.dao.PlanSheetDao;
import com.cet.eem.bll.maintenance.dao.WorkOrderCheckInfoDao;
import com.cet.eem.bll.maintenance.dao.WorkOrderDao;
import com.cet.eem.bll.maintenance.dao.devicecomponent.DeviceComponentDao;
import com.cet.eem.bll.maintenance.dao.devicecomponent.DeviceSystemDao;
import com.cet.eem.bll.maintenance.dao.devicecomponent.SparePartsDao;
import com.cet.eem.bll.maintenance.dao.devicecomponent.SparePartsReplaceRecordDao;
import com.cet.eem.bll.maintenance.dao.maintenance.MaintenanceGroupDao;
import com.cet.eem.bll.maintenance.dao.maintenance.MaintenanceItemDao;
import com.cet.eem.bll.maintenance.dao.maintenance.MaintenanceTypeDefineDao;
import com.cet.eem.bll.maintenance.def.WorkOrderDef;
import com.cet.eem.bll.maintenance.def.WorkOrderKeyDef;
import com.cet.eem.bll.maintenance.def.WorkSheetStatusDef;
import com.cet.eem.bll.maintenance.model.devicemanage.component.DeviceWithName;
import com.cet.eem.bll.maintenance.model.plan.MaintenancePlanSheetVo;
import com.cet.eem.bll.maintenance.model.plan.QueryMaintenancePlanRequest;
import com.cet.eem.bll.maintenance.model.workorder.DevicePlanRelationshipSaveVo;
import com.cet.eem.bll.maintenance.model.workorder.MaintenanceContent;
import com.cet.eem.bll.maintenance.model.workorder.WorkOrderCountDto;
import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.bll.maintenance.model.workorder.inspection.WorkOrderBatchReviewVo;
import com.cet.eem.bll.maintenance.model.workorder.inspection.WorkOrderReviewVo;
import com.cet.eem.bll.maintenance.model.workorder.maintenance.*;
import com.cet.eem.bll.maintenance.schedule.event.CreateOrderCommand;
import com.cet.eem.bll.maintenance.service.WorkOrderService;
import com.cet.eem.bll.maintenance.service.WorkOrderServiceCallBackParam;
import com.cet.eem.bll.maintenance.service.device.SparePartsReplaceRecordService;
import com.cet.eem.bll.maintenance.service.inspection.InspectorService;
import com.cet.eem.bll.maintenance.service.maintenance.MaintenancePlanService;
import com.cet.eem.bll.maintenance.service.maintenance.MaintenanceServcie;
import com.cet.eem.bll.maintenance.service.maintenance.MaintenanceWorkOrderService;
import com.cet.eem.bll.maintenance.utils.InspectorUserCheckUtils;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.common.utils.ErrorUtils;
import com.cet.eem.fusion.common.utils.ParamUtils;
import com.cet.eem.fusion.common.def.base.EnumDataTypeId;
import com.cet.eem.fusion.common.def.base.ExcelType;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.common.def.auth.LoginDef;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.def.exception.WorkOrderErrorCodeEnum;
import com.cet.eem.fusion.common.exception.ValidationException;
import com.cet.eem.fusion.common.utils.file.FileUtils;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.common.model.auth.user.UserGroupVo;
import com.cet.electric.matterhorn.cloud.authservice.common.entity.vo.UserVo;
import com.cet.eem.fusion.common.model.datalog.TrendDataVo;
import com.cet.eem.fusion.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.common.util.JsonUtil;
import com.cet.eem.fusion.common.utils.excel.PoiExcelUtils;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import com.cet.eem.fusion.common.modelutils.model.base.ConditionBlock;
import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;
import com.cet.eem.fusion.common.modelutils.model.tool.QueryResultContentTaker;
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
import com.cet.eem.fusion.common.model.quantity.QuantitySearchVo;
import com.cet.eem.quantity.service.QuantityManageService;
import com.cet.eem.service.EemCloudAuthService;
import com.cet.eem.service.EemModelDataService;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import com.cet.electric.workflow.api.TriggerRestApi;
import com.cet.electric.workflow.api.UserTaskRestApi;
import com.cet.electric.workflow.common.constants.ProcessVariableDefinition;
import com.cet.electric.workflow.common.model.ProcessInstanceResponse;
import com.cet.electric.workflow.common.model.node.config.UserTaskConfig;
import com.cet.electric.workflow.common.model.params.MultiTableTriggerParams;
import com.cet.electric.workflow.common.model.params.TableTriggerParams;
import com.cet.electric.workflow.common.model.params.UserTaskParams;
import com.cet.futureblue.i18n.LanguageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StopWatch;

import javax.servlet.http.HttpServletResponse;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : MaintenanceWorkOrderService
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2021-05-20 16:40
 */
@Service
@Slf4j
public class MaintenanceWorkOrderServiceImpl implements MaintenanceWorkOrderService {

    @Autowired
    private WorkOrderDao workOrderDao;

    @Autowired
    private UserTaskRestApi workflowService;

    @Autowired
    TriggerRestApi triggerRestApi;

    @Autowired
    private PlanSheetDao planSheetDao;

    @Autowired
    private NodeDao nodeDao;

    @Autowired
    private InspectorService inspectorService;

    @Autowired
    private AuthUtils authUtils;
    @Autowired
    private EemModelDataService modelService;
    @Autowired
    private ModelServiceUtils modelServiceUtils;

    @Autowired
    private EemCloudAuthService eemCloudAuthService;

    @Autowired
    WorkOrderService workOrderService;
    @Autowired
    SparePartsReplaceRecordDao sparePartsReplaceRecordDao;
    @Autowired
    WorkOrderCheckInfoDao workOrderCheckInfoDao;
    @Autowired
    SparePartsDao sparePartsDao;
    @Autowired
    MaintenanceServcie maintenanceServcie;
    @Autowired
    MaintenanceGroupDao maintenanceGroupDao;
    @Autowired
    MaintenanceItemDao maintenanceItemDao;
    @Autowired
    DeviceComponentDao componentDao;
    @Autowired
    DeviceSystemDao deviceSystemDao;

    @Autowired
    MaintenanceTypeDefineDao maintenanceTypeDefineDao;

    @Autowired
    SparePartsReplaceRecordService sparePartsReplaceRecordService;

    @Autowired
    InspectorUserCheckUtils inspectorUserCheckUtils;
    @Autowired
    MaintenancePlanService maintenancePlanService;
    @Autowired
    QuantityManageService quantityManageService;
    /**
     * 导出工单最大数量
     */
    @Value("${cet.eem.event.inspection.export-max-size: 10000}")
    private int exportMaxCount;
    @Value("${cet.eem.work-order.maintenance.generate-work-order.device-work-time}")
    private int workTime;
    /**
     * 预计开始时间
     */
    private static final String START_TIME_PLAN_DESC = "预计开始时间";

    @Override
    public ResultWithTotal<List<MaintenanceWorkOrderDto>> queryMaintenanceWorkOrderList(QueryMaintenanceWorkOrderRequest queryMaintenanceWorkOrderRequest) {
        // 查询用户信息和班组信息
        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        // 判断当前用户是否为巡检用户，对于巡检用户只能看自己班组的工单
        boolean inspectorUser = inspectorService.isMaintenanceUser(user);
        if (inspectorUser) {
            Long relativeGroup = authUtils.getRelativeGroup(user);
            if (queryMaintenanceWorkOrderRequest.getTeamId() == null) {
                queryMaintenanceWorkOrderRequest.setTeamId(relativeGroup);
            } else {
                if (!relativeGroup.equals(queryMaintenanceWorkOrderRequest.getTeamId())) {
                    return ResultWithTotal.ok();
                }
            }
        }
        // 判断用户所属班组，如果班组用户，那么只允许查询本班组的工单
        QueryConditionBuilder<BaseEntity> queryConditionBuilder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET);
        queryConditionBuilder.where(WorkOrderDef.TASK_TYPE, ConditionBlock.OPERATOR_EQ, WorkSheetTaskType.MAINTENANCE)
                .where(WorkOrderDef.EXECUTE_TIME_PLAN, ConditionBlock.OPERATOR_GE, queryMaintenanceWorkOrderRequest.getStartTime())
                .where(WorkOrderDef.EXECUTE_TIME_PLAN, ConditionBlock.OPERATOR_LT, queryMaintenanceWorkOrderRequest.getEndTime())
                .where(ColumnDef.PROJECT_ID, ConditionBlock.OPERATOR_EQ, GlobalInfoUtils.getTenantId())
                .leftJoin(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP))
                .orderByDescending(ColumnDef.EXECUTE_TIME_PLAN)
                .limit(queryMaintenanceWorkOrderRequest.getPage());

        if (StringUtils.isNotEmpty(queryMaintenanceWorkOrderRequest.getWorkOrder())) {
            queryConditionBuilder.where(WorkOrderDef.CODE, ConditionBlock.OPERATOR_LIKE, queryMaintenanceWorkOrderRequest.getWorkOrder());
        }
        if (Objects.nonNull(queryMaintenanceWorkOrderRequest.getWorkSheetStatus())) {
            queryConditionBuilder.where(WorkOrderDef.WORKSHEET_STATUS, ConditionBlock.OPERATOR_EQ, queryMaintenanceWorkOrderRequest.getWorkSheetStatus());
        }
        if (Objects.nonNull(queryMaintenanceWorkOrderRequest.getTeamId())) {
            queryConditionBuilder.where(WorkOrderDef.TEAM_ID, ConditionBlock.OPERATOR_EQ, queryMaintenanceWorkOrderRequest.getTeamId());
        }
        if (Objects.nonNull(queryMaintenanceWorkOrderRequest.getTaskLevel())) {
            queryConditionBuilder.where(WorkOrderDef.TASK_LEVEL, ConditionBlock.OPERATOR_EQ, queryMaintenanceWorkOrderRequest.getTaskLevel());
        }
        ResultWithTotal<List<Map<String, Object>>> modelEntityList = workOrderDao.getModelEntityList(queryConditionBuilder.build(), GlobalInfoUtils.getUserId());
        List<MaintenanceWorkOrderDto> tmpDataList = JsonTransferUtils.transferList(modelEntityList.getData(), MaintenanceWorkOrderDto.class);
        assemblyCommonData(tmpDataList, queryMaintenanceWorkOrderRequest.getTenantId());
        List<MaintenanceWorkOrderDto> collect = tmpDataList.stream().sorted(Comparator.comparing(MaintenanceWorkOrderDto::getExecuteTimePlan).reversed()).collect(Collectors.toList());
        return ResultWithTotal.ok(collect, modelEntityList.getTotal());

    }

    /**
     * 查询用户信息
     *
     * @param tenantId
     * @param executeUserIds
     * @param userMap
     * @param groupMap
     */
    private void queryUserInfo(Long tenantId, Set<Long> executeUserIds, Map<Long, String> userMap, Map<Long, String> groupMap) {
        authUtils.getUserAndGroupMsg(tenantId, userMap, groupMap);

        for (Long executeUserId : executeUserIds) {
            if (userMap.containsKey(executeUserId)) {
                continue;
            }

            UserVo userVo = authUtils.queryUserWithOutThrowException(executeUserId);
            if (userVo != null) {
                userMap.put(userVo.getId(), userVo.getName());
            }
        }
    }

    /**
     * 组装
     *
     * @param workOrderList
     * @param tenantId
     */
    @Override
    public void assemblyCommonData(List<MaintenanceWorkOrderDto> workOrderList, Long tenantId) {
        if (CollectionUtils.isEmpty(workOrderList)) {
            return;
        }
        Set<Long> creatorIds = workOrderList.stream().map(MaintenanceWorkOrderDto::getCreator).collect(Collectors.toSet());
        Set<Long> executeUserIds = new HashSet<>(creatorIds);
        Set<BaseVo> tmpNodes = new HashSet<>();
        resolveData(workOrderList, executeUserIds, tmpNodes);
        StopWatch sw = new StopWatch();

        // 查询节点
        Map<BaseVo, String> nodeMap = nodeDao.queryNodeNameMap(tmpNodes);
        // 查询用户信息
        sw.start();
        Map<Long, String> userMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        Map<Long, String> groupMap = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        queryUserInfo(tenantId, executeUserIds, userMap, groupMap);
        sw.stop();
        log.info("查询用户信息耗时：{}ms", sw.getLastTaskTimeMillis());

        sw.start();
        Set<Long> workOrderIds = workOrderList.stream().map(BaseEntity::getId).collect(Collectors.toSet());
        Map<String, UserTaskConfig> taskConfigMap = workOrderDao.queryTaskConfigList(workOrderIds, null);
        sw.stop();
        log.info("查询工单状态耗时：{}ms", sw.getLastTaskTimeMillis());

        sw.start();
        List<ProcessFlowUnit> processFlowUnits = workOrderDao.queryProcessFlowUnit(workOrderIds);
        sw.stop();
        log.info("查询流程状态耗时：{}ms", sw.getLastTaskTimeMillis());

        Map<Long, List<ProcessFlowUnit>> processFlowUnitMap = processFlowUnits.stream().collect(Collectors.groupingBy(ProcessFlowUnit::getEventid));
        Map<Integer, String> enumByLabel = modelServiceUtils.getEnumByLabel(ModelLabelDef.WORKSHEET_TASK_LEVEL);
        Map<Long, String> maintenanceTypeMap = queryMaintenanceType(GlobalInfoUtils.getTenantId());
        Set<Long> sparePartIds = workOrderList.stream().map(it -> JsonUtil.parseObject(it.getMaintenanceContent(), MaintenanceContent.class))
                .filter(it -> it != null && CollectionUtils.isNotEmpty(it.getItemExtends()))
                .flatMap(it -> it.getItemExtends().stream().map(MaintenanceItem::getSparePartId)).collect(Collectors.toSet());
        List<SpareParts> spareParts = sparePartsDao.selectBatchIds(sparePartIds);
        Map<Long, SpareParts> sparePartMap = spareParts.stream().collect(Collectors.toMap(BaseEntity::getId, it -> it));

        for (MaintenanceWorkOrderDto dto : workOrderList) {
            dto.setTeamName(groupMap.get(dto.getTeamId()));
            dto.setWorkSheetStatusName(WorkSheetStatusDef.MAINTENANCE_ID_NAME_MAP.get(dto.getWorkSheetStatus()));
            dto.setProcessFlowUnits(processFlowUnitMap.get(dto.getId()));
            dto.setTaskLevelName(enumByLabel.get(dto.getTaskLevel()));
            dto.setCreatorName(userMap.get(dto.getCreator()));
            dto.setUserTaskConfig(taskConfigMap.get(dto.getCode()));
            if (Objects.isNull(dto.getUserTaskConfig()) && Objects.equals(WorkSheetStatusDef.ACCOMPLISHED, dto.getWorkSheetStatus())) {
                UserTaskConfig userTaskConfig = new UserTaskConfig();
                userTaskConfig.setName("工单完成");
                dto.setUserTaskConfig(userTaskConfig);
            }
            List<DevicePlanRelationship> devicePlanRelationshipList = dto.getDevicePlanRelationshipList();
            if (CollectionUtils.isEmpty(devicePlanRelationshipList)) {
                continue;
            }
            for (DevicePlanRelationship devicePlanRelationship : devicePlanRelationshipList) {
                devicePlanRelationship.setDeviceName(nodeMap.get(new BaseVo(devicePlanRelationship.getDeviceId(), devicePlanRelationship.getDeviceLabel())));
            }
            if (dto.getTimeConsume() == null && dto.getFinishTime() != null) {
                long et = dto.getFinishTime().atZone(TimeUtil.getZoneId()).toInstant().toEpochMilli();
                long st = dto.getExecuteTime().atZone(TimeUtil.getZoneId()).toInstant().toEpochMilli();
                dto.setTimeConsume((et - st));
            }

            // 组装维保项目数据
            dto.setMaintenanceItemExtendVos(assemblyMaintenanceContent(userMap, maintenanceTypeMap, dto, sparePartMap));
        }
    }

    private void resolveData(List<MaintenanceWorkOrderDto> workOrderList, Set<Long> executeUserIds, Set<BaseVo> tmpNodes) {
        for (MaintenanceWorkOrderDto maintenanceWorkOrderDto : workOrderList) {
            MaintenanceContent maintenanceWorkOrderContent = JsonUtil.parseObject(maintenanceWorkOrderDto.getMaintenanceContent(), MaintenanceContent.class);
            //获得维保项所有的执行人id
            if (null != maintenanceWorkOrderContent && null != maintenanceWorkOrderContent.getItemExtends()) {
                Set<Long> exectorIds = maintenanceWorkOrderContent.getItemExtends().stream()
                        .filter(maintenanceItemExtend -> CollectionUtils.isNotEmpty(maintenanceItemExtend.getExecutor()))
                        .flatMap(maintenanceItemExtend -> maintenanceItemExtend.getExecutor().stream())
                        .collect(Collectors.toSet());
                executeUserIds.addAll(exectorIds);
            }
            List<DevicePlanRelationship> ships = maintenanceWorkOrderDto.getDevicePlanRelationshipList();
            if (CollectionUtils.isNotEmpty(ships)) {
                for (DevicePlanRelationship ship : ships) {
                    tmpNodes.add(new BaseVo(ship.getDeviceId(), ship.getDeviceLabel()));
                }
            }
        }
    }

    /**
     * 组装维保数据
     *
     * @param userMap
     * @param maintenanceTypeMap
     * @param dto
     * @return
     */
    private List<MaintenanceItemExtend> assemblyMaintenanceContent(Map<Long, String> userMap, Map<Long, String> maintenanceTypeMap, MaintenanceWorkOrderDto dto, Map<Long, SpareParts> sparePartMap) {
        MaintenanceContent maintenanceWorkOrderContent = JsonUtil.parseObject(dto.getMaintenanceContent(), MaintenanceContent.class);
        if (maintenanceWorkOrderContent == null) {
            return Collections.emptyList();
        }

        dto.setSparePartNumbers(maintenanceWorkOrderContent.getSparePartNumbers());
        List<MaintenanceItemExtend> itemExtends = maintenanceWorkOrderContent.getItemExtends();
        if (itemExtends == null) {
            return itemExtends;
        }
        for (MaintenanceItemExtend maintenanceItemExtend : itemExtends) {
            maintenanceItemExtend.setMaintenanceTypeName(maintenanceTypeMap.get(maintenanceItemExtend.getMaintenanceType()));
            SpareParts spareParts = sparePartMap.get(maintenanceItemExtend.getSparePartId());
            if (spareParts != null) {
                maintenanceItemExtend.setUnit(spareParts.getUnit());
                maintenanceItemExtend.setSparePartName(spareParts.getName());
            }

            List<Long> executor = maintenanceItemExtend.getExecutor();
            if (CollectionUtils.isEmpty(executor)) {
                continue;
            }
            List<String> executorName = new ArrayList<>();
            for (Long id : executor) {
                executorName.add(userMap.get(id));
            }
            maintenanceItemExtend.setExecutorName(executorName);
        }
        Set<Long> exectorIds = itemExtends.stream().filter(maintenanceItemExtend -> CollectionUtils.isNotEmpty(maintenanceItemExtend.getExecutor()))
                .flatMap(maintenanceItemExtend -> maintenanceItemExtend.getExecutor().stream()).collect(Collectors.toSet());
        List<String> staffName = new ArrayList<>();
        for (Long id : exectorIds) {
            staffName.add(userMap.get(id));
        }
        String join = String.join("、", staffName);
        //执行人的名称的list
        dto.setStaffName(join);
        sortMaintenanceItem(itemExtends);
        return itemExtends;
    }

    private void sortMaintenanceItem(List<MaintenanceItemExtend> itemExtends) {
        itemExtends.sort((v1, v2) -> {
            if (v1.getGroupId() == null) {
                return 1;
            }
            if (v2.getGroupId() == null) {
                return -1;
            }

            int sort = CommonUtils.sort(v1.getGroupId(), v2.getGroupId(), true);
            if (sort != 0) {
                return sort;
            }

            if (v1.getSort() == null) {
                return 1;
            }
            if (v2.getSort() == null) {
                return -1;
            }

            return CommonUtils.sort(v1.getSort(), v2.getSort(), true);
        });
    }

    @Override
    public MaintenanceWorkOrderDto queryMaintenanceWorkOrderByCode(String code) {
        if (StringUtils.isBlank(code)) {
            throw new ValidationException("工单号不允许为空!");
        }
        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        QueryConditionBuilder<BaseEntity> queryConditionBuilder = ParentQueryConditionBuilder.of(ModelLabelDef.PM_WORK_SHEET);
        queryConditionBuilder.where(WorkOrderDef.TASK_TYPE, ConditionBlock.OPERATOR_EQ, WorkSheetTaskType.MAINTENANCE)
                .where(ColumnDef.CODE, ConditionBlock.OPERATOR_EQ, code)
                .leftJoin(Collections.singletonList(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP));
        ResultWithTotal<List<Map<String, Object>>> modelEntityList = workOrderDao.getModelEntityList(queryConditionBuilder.build(), GlobalInfoUtils.getUserId());
        List<MaintenanceWorkOrderDto> tmpDataList = JsonTransferUtils.transferList(modelEntityList.getData(), MaintenanceWorkOrderDto.class);
        if (CollectionUtils.isEmpty(tmpDataList)) {
            throw new ValidationException(LanguageUtil.getMessage(WorkOrderErrorCodeEnum.RUNTIME_WORK_ORDER_NOT_FOUNT.getMsg()));
        }
        MaintenanceWorkOrderDto maintenanceWorkOrderDto = tmpDataList.get(0);
        assemblyCommonData(Collections.singletonList(maintenanceWorkOrderDto), user.getTenantId());
        return maintenanceWorkOrderDto;
    }

    /**
     * 返回工单时，删除掉对应的消耗备件记录
     *
     * @param workOrderIds
     */
    @Override
    public void deleteSparePartsRecord(List<Long> workOrderIds) {
        List<SparePartsReplaceRecord> sparePartsReplaceRecords = sparePartsReplaceRecordDao.queryByWorkOderId(workOrderIds);
        if (CollectionUtils.isNotEmpty(sparePartsReplaceRecords)) {
            List<Long> collect = sparePartsReplaceRecords.stream().map(SparePartsReplaceRecord::getId).collect(Collectors.toList());
            sparePartsReplaceRecordDao.deleteBatchIds(collect);
        }
    }

    @Override
    public void judgeDeleteSparePartsRecord(WorkOrderReviewVo workOrderReviewVo) {
        InspectionWorkOrderDto workOrder = workOrderService.queryRuntimeWorkOrder(workOrderReviewVo.getCode());
        if (workOrderReviewVo.getParams().isRejected() || Boolean.FALSE.equals(workOrderReviewVo.getParams().getTaskResult())) {
            deleteSparePartsRecord(Collections.singletonList(workOrder.getId()));
        }
    }

    @Override
    public void judgeDeleteSparePartsRecord(WorkOrderBatchReviewVo workOrderReviewVo) {
        Long userId = GlobalInfoUtils.getUserId();
        List<InspectionWorkOrderDto> workOrders = workOrderDao.queryRuntimeWorkOrders(workOrderReviewVo.getCodes(), userId, null);
        if (Boolean.FALSE.equals(workOrderReviewVo.getParams().getTaskResult())) {
            deleteSparePartsRecord(workOrders.stream().map(InspectionWorkOrderDto::getId).collect(Collectors.toList()));
        }
    }

    private List<SparePartsReplaceRecord> assemblyWorkOrder(MaintenanceWorkOrderDto workOrder) {
        if (CollectionUtils.isEmpty(workOrder.getDevicePlanRelationshipList())) {
            return Collections.emptyList();
        }
        DevicePlanRelationship devicePlanRelationship = workOrder.getDevicePlanRelationshipList().get(0);
        Long deviceId = devicePlanRelationship.getDeviceId();
        String deviceLabel = devicePlanRelationship.getDeviceLabel();
        List<BaseVo> baseVos = new ArrayList<>();
        BaseVo b = new BaseVo(deviceId, deviceLabel);
        baseVos.add(b);
        //获得具体的设备名称
        List<DeviceWithName> deviceWithNames = nodeDao.queryNodes(baseVos, DeviceWithName.class);
        if (CollectionUtils.isEmpty(deviceWithNames)) {
            return Collections.emptyList();
        }

        List<SparePartNumber> sparePartNumber = new ArrayList<>();
        List<MaintenanceItemExtend> itemExtends = workOrder.getMaintenanceItemExtendVos();
        if (CollectionUtils.isNotEmpty(itemExtends)) {
            itemExtends.stream().filter(it -> it.getSparePartId() != null && it.getConsumeAmount() != null)
                    .map(it -> new SparePartNumber(it.getSparePartId(), it.getConsumeAmount()))
                    .forEach(sparePartNumber::add);
        }

        if (CollectionUtils.isNotEmpty(workOrder.getSparePartNumbers())) {
            sparePartNumber.addAll(workOrder.getSparePartNumbers());
        }
        // 查询备件所属的备件系统
        Set<Long> storageIds = sparePartNumber.stream().map(SparePartNumber::getId).collect(Collectors.toSet());
        List<Map<String, Object>> storageListMap = sparePartsDao.querySparePartsStorageWithSystem(storageIds);
        List<BaseVo> storageList = JsonTransferUtils.transferList(storageListMap, BaseVo.class);
        Map<Long, Long> sparePartsStorageWithSystemMap = new HashMap<>();
        for (BaseVo baseVo : storageList) {
            if (CollectionUtils.isNotEmpty(baseVo.getChildren())) {
                sparePartsStorageWithSystemMap.put(baseVo.getId(), baseVo.getChildren().get(0).getId());
            }
        }

        List<SparePartsReplaceRecord> replaceRecords = new ArrayList<>();
        Map<Long, List<SparePartNumber>> sparePartNumberMap = sparePartNumber.stream().collect(Collectors.groupingBy(BaseEntity::getId));
        sparePartNumberMap.forEach((key, val) -> {
            double sum = val.stream().filter(it -> it.getNumber() != null).mapToDouble(SparePartNumber::getNumber).sum();
            SparePartsReplaceRecord replaceRecord = new SparePartsReplaceRecord();
            replaceRecords.add(replaceRecord);
            replaceRecord.setNumber(sum);
            replaceRecord.setSparePartsStorageId(key);
            replaceRecord.setWorkOrderId(workOrder.getId());
            replaceRecord.setObjectId(deviceId);
            replaceRecord.setObjectLabel(deviceLabel);
            replaceRecord.setDeviceSystemId(sparePartsStorageWithSystemMap.get(key));
            replaceRecord.setLogtime(workOrder.getFinishTime().atZone(TimeUtil.getZoneId()).toInstant().toEpochMilli());
        });

        return replaceRecords;
    }

    private List<SparePartsReplaceRecord> assemblyWorkOrderWithSpareParts(MaintenanceWorkOrderDto workOrderDto) {
        MaintenanceContent maintenanceWorkOrderContent = JsonUtil.parseObject(workOrderDto.getMaintenanceContent(), MaintenanceContent.class);
        if (maintenanceWorkOrderContent == null) {
            return Collections.emptyList();
        }

        workOrderDto.setSparePartNumbers(maintenanceWorkOrderContent.getSparePartNumbers());
        workOrderDto.setMaintenanceItemExtendVos(maintenanceWorkOrderContent.getItemExtends());
        return assemblyWorkOrder(workOrderDto);

    }

    @Override
    public void insertSparePartsReplaceRecord(WorkOrderServiceCallBackParam param) {
        if (!ParamUtils.checkPrimaryKeyValid(param.getId())) {
            throw new ValidationException("工单id不合法!");
        }
        InspectionWorkOrderDto workOrder = workOrderDao.queryWorkOrder(param.getId(), InspectionWorkOrderDto.class);
        MaintenanceWorkOrderDto workOrderDto = new MaintenanceWorkOrderDto();
        BeanUtils.copyProperties(workOrder, workOrderDto);
        Assert.notNull(workOrder, "未查询到指定的工单！");
        List<SparePartsReplaceRecord> sparePartsReplaceRecords = assemblyWorkOrderWithSpareParts(workOrderDto);
        if (CollectionUtils.isNotEmpty(sparePartsReplaceRecords)) {
            sparePartsReplaceRecordService.writeSparePartsReplaceRecord(sparePartsReplaceRecords, workOrder.getId());
        }
    }

    @Override
    public void createWorkOrderByDeviceWorkTime() {
        log.info("开始执行根据设备运行时长生成维保工单任务任务！");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        QueryMaintenancePlanRequest queryInspectionPlanRequest = new QueryMaintenancePlanRequest();
        queryInspectionPlanRequest.setTenantId(1L);
        queryInspectionPlanRequest.setEnabled(Boolean.TRUE);
        queryInspectionPlanRequest.setPage(new Page(0, exportMaxCount));
        //查询维保计划信息
        ResultWithTotal<List<MaintenancePlanSheetVo>> listResultWithTotal = maintenancePlanService.queryMaintenancePlanBySchedule(queryInspectionPlanRequest);
        //筛选出周期策略为设备运行时长的计划
        List<MaintenancePlanSheetVo> maintenancePlanSheetVoList = listResultWithTotal.getData().stream().filter(maintenancePlanSheetVo -> Objects.equals("P0M0DT1H", maintenancePlanSheetVo.getCycle())).collect(Collectors.toList());
        List<BaseVo> deviceNodes = getDeviceNodes(maintenancePlanSheetVoList);
        Map<Integer, List<TrendDataVo>> dataLogResult = quantityManageService.queryDataLogBatch(createQuantityDataBatchSearchVo(deviceNodes));
        if (Objects.isNull(dataLogResult) || dataLogResult.isEmpty()) {
            log.info("没有查到符合条件的定时记录信息");
            return;
        }
        // 取出设备数据
        List<TrendDataVo> dataList = dataLogResult.get(CommonUtils.WORK_ORDER_SETTING_BEGIN);
        if (CollectionUtils.isEmpty(dataList)) {
            log.info("没有取出符合条件的设备定时记录信息");
            return;
        }
        List<ProcessInstanceResponse> processInstanceResponses = handleWriteData(dataList, maintenancePlanSheetVoList);
        stopWatch.stop();
        log.info("完成执行根据设备运行时长生成维保工单任务任务，本次更新信息为：{}，耗时{}ms", JsonTransferUtils.toJSONString(processInstanceResponses), stopWatch.getTotalTimeMillis());
    }

    private List<ProcessInstanceResponse> handleWriteData(List<TrendDataVo> dataList, List<MaintenancePlanSheetVo> maintenancePlanSheetVoList) {
        List<TrendDataVo> trendDataVos = new ArrayList<>();
        //先筛选运行时长
        for (TrendDataVo trendDataVo : dataList) {
            List<Long> collect = trendDataVo.getDataList().stream().sorted(Comparator.comparing(DatalogValue::getTime)).map(DatalogValue::getTime).collect(Collectors.toList());
            long duration = Duration.between(TimeUtil.timestamp2LocalDateTime(collect.get(0)), TimeUtil.timestamp2LocalDateTime(collect.get(collect.size() - 1))).toMillis();
            if (duration > workTime) {
                trendDataVos.add(trendDataVo);
            }
        }
        Long userId = 1L;
        UserVo userVo = authUtils.queryAndCheckUser(userId);
        // 设置工单
        MultiTableTriggerParams triggerParams = new MultiTableTriggerParams();
        triggerParams.setProcessDefinitionKey(WorkOrderKeyDef.MAINTENANCE_KEY);
        List<Map<String, Object>> formDataList = new ArrayList<>();
        Map<Long, String> maintenanceTypeMap = queryMaintenanceType(1L);
        List<MaintenanceItem> items = maintenanceItemDao.selectAll();
        List<SpareParts> spareParts = sparePartsDao.selectAll();
        List<MaintenanceGroup> groups = maintenanceGroupDao.selectAll();
        for (TrendDataVo item : trendDataVos) {
            for (MaintenancePlanSheetVo maintenancePlanSheetVo : maintenancePlanSheetVoList) {
                if (Objects.equals(item.getMonitoredid(), maintenancePlanSheetVo.getDevicePlanRelationshipList().get(0).getDeviceId()) &&
                        Objects.equals(item.getMonitoredlabel(), maintenancePlanSheetVo.getDevicePlanRelationshipList().get(0).getDeviceLabel())) {
                    Map<String, Object> maintenanceWorkOrder = createMaintenanceWorkOrder(maintenancePlanSheetVo, triggerParams, userVo,
                            maintenanceTypeMap, items, spareParts, groups);
                    formDataList.add(maintenanceWorkOrder);
                }
            }
        }
        triggerParams.setFormDataList(formDataList);
        ApiResult<List<ProcessInstanceResponse>> result = workOrderDao.startProcessesByManyTables(userId, triggerParams);
        return result.getData();
    }

    private List<MaintenanceItemExtend> handleMaintenanceExtend(MaintenanceExtend maintenanceExtend, List<MaintenanceItem> items, Map<Long, String> maintenanceTypeMap, List<SpareParts> spareParts, List<MaintenanceGroup> groups) {
        List<MaintenanceItemExtend> maintenanceItemExtends = new ArrayList<>();
        List<MaintenanceExtend.GroupItemMapping> maintenanceItems = maintenanceExtend.getMaintenanceItems();
        Map<Long, MaintenanceItem> itemMap = items.stream().collect(Collectors.toMap(BaseEntity::getId, it -> it));
        Map<Long, String> sparePartMap = spareParts.stream().collect(Collectors.toMap(BaseEntity::getId, BaseEntity::getName));
        Map<Long, String> groupMap = groups.stream().collect(Collectors.toMap(BaseEntity::getId, BaseEntity::getName));
        for (MaintenanceExtend.GroupItemMapping groupItemMapping : maintenanceItems) {
            Long groupId = groupItemMapping.getGroupId();
            List<Long> itemIds = groupItemMapping.getItemIds();
            if (CollectionUtils.isEmpty(itemIds)) {
                continue;
            }

            for (Long itemId : itemIds) {
                MaintenanceItem maintenanceItem = itemMap.get(itemId);
                MaintenanceItemExtend maintenanceItemExtend = new MaintenanceItemContent();
                maintenanceItemExtend.setId(maintenanceItem.getId());
                maintenanceItemExtend.setMaintenanceType(maintenanceItem.getMaintenanceType());
                maintenanceItemExtend.setMaintenanceTypeName(maintenanceTypeMap.get(maintenanceItem.getMaintenanceType()));
                maintenanceItemExtend.setContent(maintenanceItem.getContent());
                maintenanceItemExtend.setSparePartId(maintenanceItem.getSparePartId());
                maintenanceItemExtend.setSparePartName(sparePartMap.get(maintenanceItem.getSparePartId()));
                maintenanceItemExtend.setGroupId(groupId);
                maintenanceItemExtend.setGroupName(groupMap.get(groupId));
                maintenanceItemExtend.setSort(maintenanceItem.getSort());
                maintenanceItemExtend.setNumber(maintenanceItem.getNumber());
                maintenanceItemExtends.add(maintenanceItemExtend);
            }
        }
        return maintenanceItemExtends;
    }

    private Map<String, Object> createMaintenanceWorkOrder(MaintenancePlanSheetVo maintenancePlanSheetVo, MultiTableTriggerParams triggerParams, UserVo userVo,
                                                           Map<Long, String> maintenanceTypeMap, List<MaintenanceItem> items, List<SpareParts> spareParts, List<MaintenanceGroup> groups) {
        Map<String, Object> processVariables = new HashMap<>(CommonUtils.MAP_INIT_SIZE_4);
        processVariables.put(ProcessVariableDefinition.CANDICATE_GROUPS, String.valueOf(maintenancePlanSheetVo.getTeamId()));
        processVariables.put(ProcessVariableDefinition.TENANT_ID, userVo.getTenantId());
        triggerParams.setProcessVariables(processVariables);
        Map<String, Object> formData = new HashMap<>();
        formData.put(ColumnDef.MODEL_LABEL, ModelLabelDef.PM_WORK_SHEET);
        formData.put(WorkOrderDef.EXECUTE_TIME_PLAN, maintenancePlanSheetVo.getExecuteTime());
        formData.put(WorkOrderDef.TIME_CONSUME_PLAN, maintenancePlanSheetVo.getTimeConsumePlan());
        formData.put(WorkOrderDef.TEAM_ID, maintenancePlanSheetVo.getTeamId());
        formData.put(WorkOrderDef.TASK_LEVEL, maintenancePlanSheetVo.getWorksheetTaskLevel());
        formData.put(WorkOrderDef.PERSON_NUMBER, maintenancePlanSheetVo.getPopulation());
        formData.put(WorkOrderDef.TASK_TYPE, WorkSheetTaskType.MAINTENANCE);
        //维保项目
        MaintenanceContent maintenanceWorkOrderContent = new MaintenanceContent();
        List<MaintenanceItemExtend> maintenanceItemExtends = handleMaintenanceExtend(maintenancePlanSheetVo.getMaintenanceExtend(), items, maintenanceTypeMap, spareParts, groups);
        maintenanceWorkOrderContent.setItemExtends(maintenanceItemExtends);
        formData.put(WorkOrderDef.MAINTENANCE_CONTENT, JsonTransferUtils.toJSONString(maintenanceWorkOrderContent));
        formData.put(WorkOrderDef.WORKSHEET_STATUS, WorkSheetStatusDef.TO_BE_SENT);
        formData.put(ColumnDef.PROJECT_ID, 1L);
        formData.put(WorkOrderDef.SAFETY_MEASURE, maintenancePlanSheetVo.getSafetyMeasure());
        formData.put(WorkOrderDef.DEVICE_PLAN_RELATIONSHIP_MODEL,
                Collections.singletonList(new DevicePlanRelationshipSaveVo(maintenancePlanSheetVo.getDevicePlanRelationshipList().get(0).getDeviceId(), maintenancePlanSheetVo.getDevicePlanRelationshipList().get(0).getDeviceLabel())));
        // 创建者信息
        formData.put(WorkOrderDef.CREATOR, userVo.getId());
        formData.put(WorkOrderDef.CREATE_TIME, ProcessVariableDefinition.NOW_TIME_SYMBOL);
        formData.put(ColumnDef.TENANT_ID, userVo.getTenantId());
        // 工单服务自动生成字段
        formData.put(WorkOrderDef.CODE, ProcessVariableDefinition.CODE_SYMBOL);
        return formData;
    }

    private QuantityDataBatchSearchVo createQuantityDataBatchSearchVo(List<BaseVo> deviceNodes) {
        QuantityDataBatchSearchVo aggregationDataBatch = new QuantityDataBatchSearchVo();
        aggregationDataBatch.setDataTypeId(EnumDataTypeId.REALTIME.getId());
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime = TimeUtil.addDateTimeByCycle(now, AggregationCycle.ONE_HOUR, -1);
        aggregationDataBatch.setStartTime(TimeUtil.localDateTime2timestamp(startTime));
        aggregationDataBatch.setEndTime(TimeUtil.localDateTime2timestamp(now));
        aggregationDataBatch.setQuantitySettings(Collections.singletonList(getDeviceQuantitySetting()));
        aggregationDataBatch.setNodes(deviceNodes);
        aggregationDataBatch.setAggregationCycle(AggregationCycle.TEN_MINUTES);
        return aggregationDataBatch;
    }

    private QuantitySearchVo getDeviceQuantitySetting() {
        return new QuantitySearchVo(CommonUtils.WORK_ORDER_SETTING_BEGIN,
                QuantityCategoryDef.DEVICE_WORK_TIME,
                QuantityTypeDef.DEVICE_WORK_TIME,
                FrequencyDef.NONE,
                PhasorDef.NONE,
                null);
    }

    /**
     * 获得设备节点信息
     *
     * @param maintenancePlanSheetVoList
     * @return
     */
    private List<BaseVo> getDeviceNodes(List<MaintenancePlanSheetVo> maintenancePlanSheetVoList) {
        List<BaseVo> deviceNodes = new ArrayList<>();
        for (MaintenancePlanSheetVo maintenancePlanSheetVo : maintenancePlanSheetVoList) {
            Long deviceId = maintenancePlanSheetVo.getDevicePlanRelationshipList().get(0).getDeviceId();
            String deviceLabel = maintenancePlanSheetVo.getDevicePlanRelationshipList().get(0).getDeviceLabel();
            String deviceName = maintenancePlanSheetVo.getDevicePlanRelationshipList().get(0).getDeviceName();
            deviceNodes.add(new BaseVo(deviceId, deviceLabel, deviceName));
        }
        return deviceNodes;
    }

    @Override
    public void createWorkOrderAutomatically(CreateOrderCommand createOrderCommand) {
        PlanSheetWithSubLayer planSheetWithSubLayer = planSheetDao.selectRelatedById(PlanSheetWithSubLayer.class, createOrderCommand.getId());
        if (Objects.isNull(planSheetWithSubLayer)) {
            log.error("id={}的维保计划已被删除", createOrderCommand.getId());
            return;
        }
        if (CollectionUtils.isEmpty(planSheetWithSubLayer.getDevicePlanRelationshipList())) {
            log.error("id={}的维保计划无维保对象！", createOrderCommand.getId());
            return;
        }
        UserVo userVo = authUtils.queryAndCheckUser(planSheetWithSubLayer.getCreator());
        if (Objects.isNull(userVo)) {
            log.error("id={}的维保计划无创建者！", createOrderCommand.getId());
            return;
        }
        List<Map<String, Object>> workOrders = new ArrayList<>();
        for (DevicePlanRelationship devicePlanRelationship : planSheetWithSubLayer.getDevicePlanRelationshipList()) {
            Map<String, Object> workOrder = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
            workOrder.put(ColumnDef.MODEL_LABEL, ModelLabelDef.PM_WORK_SHEET);
            workOrder.put(ColumnDef.TASK_TYPE, WorkSheetTaskType.MAINTENANCE);
            workOrder.put(WorkOrderDef.EXECUTE_TIME_PLAN, createOrderCommand.getScheduledFireTime());
            workOrder.put(WorkOrderDef.TIME_CONSUME_PLAN, planSheetWithSubLayer.getTimeConsumePlan());
            workOrder.put(WorkOrderDef.TEAM_ID, planSheetWithSubLayer.getTeamId());
            workOrder.put(ColumnDef.PROJECT_ID, planSheetWithSubLayer.getProjectId());
            workOrder.put(WorkOrderDef.PLANSHEET_ID, planSheetWithSubLayer.getId());
            workOrder.put(WorkOrderDef.CREATE_TIME, ProcessVariableDefinition.NOW_TIME_SYMBOL);
            workOrder.put(WorkOrderDef.CODE, ProcessVariableDefinition.CODE_SYMBOL);
            workOrder.put(WorkOrderDef.WORKSHEET_STATUS, WorkSheetStatusDef.TO_BE_SENT);
            workOrder.put(WorkOrderDef.TASK_LEVEL, planSheetWithSubLayer.getWorksheetTaskLevel());
            workOrder.put(WorkOrderDef.PERSON_NUMBER, planSheetWithSubLayer.getPopulation());
            workOrder.put(ColumnDef.TENANT_ID, userVo.getTenantId());
            workOrder.put(WorkOrderDef.SAFETY_MEASURE, planSheetWithSubLayer.getSafetyMeasure());
            workOrder.put(WorkOrderDef.SAFETY_MEASURE_ATTACHMENT, planSheetWithSubLayer.getSafetyMeasureAttachment());
            workOrder.put(WorkOrderDef.DEVICE_PLAN_RELATIONSHIP_MODEL,
                    Collections.singletonList(new DevicePlanRelationshipSaveVo(devicePlanRelationship.getDeviceId(), devicePlanRelationship.getDeviceLabel())));
            workOrders.add(workOrder);
            // 添加维保项
            MaintenanceContent maintenanceWorkOrderContent = new MaintenanceContent();
            List<MaintenanceItemExtend> maintenanceItemExtends = handleMaintenanceExtend(planSheetWithSubLayer.getMaintenanceItemList(), planSheetWithSubLayer.getProjectId());
            maintenanceWorkOrderContent.setItemExtends(maintenanceItemExtends);
            workOrder.put(WorkOrderDef.MAINTENANCE_CONTENT, JsonTransferUtils.toJSONString(maintenanceWorkOrderContent));
        }
        MultiTableTriggerParams multiTableTriggerParams = new MultiTableTriggerParams();
        multiTableTriggerParams.setProcessDefinitionKey(WorkOrderKeyDef.MAINTENANCE_KEY);
        multiTableTriggerParams.setFormDataList(workOrders);
        Map<String, Object> processVariables = new HashMap<>(CommonUtils.MAP_INIT_SIZE_4);
        // 班组格式为字符串，多个班组使用英文,分割
        String groups = String.valueOf(planSheetWithSubLayer.getTeamId());
        processVariables.put(ProcessVariableDefinition.CANDICATE_GROUPS, groups);
        multiTableTriggerParams.setProcessVariables(processVariables);
        multiTableTriggerParams.setUserName(LoginDef.USER_SYSTEM_NAME);
        workOrderDao.startProcessesByManyTables(LoginDef.USER_SYSTEM_ID, multiTableTriggerParams);
    }

    private List<MaintenanceItemExtend> handleMaintenanceExtend(List<MaintenanceItem> items, Long projectId) {
        List<MaintenanceItemExtend> maintenanceItemExtends = new ArrayList<>();
        Map<Long, String> maintenanceTypeMap = queryMaintenanceType(projectId);

        List<Long> itemIds = items.stream().map(BaseEntity::getId).collect(Collectors.toList());
        Set<Long> sparePartIds = items.stream().map(MaintenanceItem::getSparePartId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<SpareParts> spareParts = sparePartsDao.selectBatchIds(sparePartIds);
        Map<Long, String> sparePartMap = spareParts.stream().collect(Collectors.toMap(BaseEntity::getId, BaseEntity::getName));

        QueryCondition condition = ParentQueryConditionBuilder.of(ModelLabelDef.MAINTENANCE_ITEM, itemIds)
                .leftJoin(ModelLabelDef.MAINTENANCE_GROUP)
                .queryAsTree()
                .build();
        List<Map<String, Object>> dataList = modelServiceUtils.query(condition);
        Map<Long, MaintenanceGroup> groupMap = new HashMap<>(dataList.size());
        for (Map<String, Object> item : dataList) {
            List<Map<String, Object>> children = QueryResultContentTaker.getChildren(item);
            if (CollectionUtils.isEmpty(children)) {
                continue;
            }

            groupMap.put(QueryResultContentTaker.getId(item), JsonTransferUtils.transferList(children, MaintenanceGroup.class).get(0));
        }

        for (MaintenanceItem maintenanceItem : items) {
            MaintenanceGroup group = groupMap.get(maintenanceItem.getId());
            if (group == null) {
                continue;
            }

            MaintenanceItemExtend maintenanceItemExtend = new MaintenanceItemContent();
            maintenanceItemExtend.setId(maintenanceItem.getId());
            maintenanceItemExtend.setMaintenanceType(maintenanceItem.getMaintenanceType());
            maintenanceItemExtend.setMaintenanceTypeName(maintenanceTypeMap.get(maintenanceItem.getMaintenanceType()));
            maintenanceItemExtend.setContent(maintenanceItem.getContent());
            maintenanceItemExtend.setSparePartId(maintenanceItem.getSparePartId());
            maintenanceItemExtend.setSparePartName(sparePartMap.get(maintenanceItem.getSparePartId()));
            maintenanceItemExtend.setGroupId(group.getId());
            maintenanceItemExtend.setGroupName(group.getName());
            maintenanceItemExtend.setSort(maintenanceItem.getSort());
            maintenanceItemExtend.setNumber(maintenanceItem.getNumber());
            maintenanceItemExtends.add(maintenanceItemExtend);
        }
        return maintenanceItemExtends;
    }

    private List<MaintenanceItemExtend> handleMaintenanceExtend(AddMaintenanceWorkOrderRequest addMaintenanceWorkOrderRequest) {
        List<MaintenanceItemExtend> maintenanceItemExtends = new ArrayList<>();
        MaintenanceExtend maintenanceExtend = addMaintenanceWorkOrderRequest.getMaintenanceExtend();
        List<MaintenanceExtend.GroupItemMapping> maintenanceItems = maintenanceExtend.getMaintenanceItems();
        Map<Long, String> maintenanceTypeMap = queryMaintenanceType(GlobalInfoUtils.getTenantId());

        Collection<Long> maintenanceItemIds = maintenanceItems.stream()
                .filter(it -> CollectionUtils.isNotEmpty(it.getItemIds())).flatMap(it -> it.getItemIds().stream())
                .collect(Collectors.toSet());
        List<MaintenanceItem> items = maintenanceItemDao.selectBatchIds(maintenanceItemIds);
        Map<Long, MaintenanceItem> itemMap = items.stream().collect(Collectors.toMap(BaseEntity::getId, it -> it));
        Set<Long> sparePartIds = items.stream().map(MaintenanceItem::getSparePartId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<SpareParts> spareParts = sparePartsDao.selectBatchIds(sparePartIds);
        Map<Long, String> sparePartMap = spareParts.stream().collect(Collectors.toMap(BaseEntity::getId, BaseEntity::getName));

        Set<Long> groupIds = maintenanceItems.stream().map(MaintenanceExtend.GroupItemMapping::getGroupId)
                .filter(Objects::nonNull).collect(Collectors.toSet());
        List<MaintenanceGroup> groups = maintenanceGroupDao.selectBatchIds(groupIds);
        Map<Long, String> groupMap = groups.stream().collect(Collectors.toMap(BaseEntity::getId, BaseEntity::getName));

        for (MaintenanceExtend.GroupItemMapping groupItemMapping : maintenanceItems) {
            Long groupId = groupItemMapping.getGroupId();
            List<Long> itemIds = groupItemMapping.getItemIds();
            if (CollectionUtils.isEmpty(itemIds)) {
                continue;
            }

            for (Long itemId : itemIds) {
                MaintenanceItem maintenanceItem = itemMap.get(itemId);
                MaintenanceItemExtend maintenanceItemExtend = new MaintenanceItemContent();
                maintenanceItemExtend.setId(maintenanceItem.getId());
                maintenanceItemExtend.setMaintenanceType(maintenanceItem.getMaintenanceType());
                maintenanceItemExtend.setMaintenanceTypeName(maintenanceTypeMap.get(maintenanceItem.getMaintenanceType()));
                maintenanceItemExtend.setContent(maintenanceItem.getContent());
                maintenanceItemExtend.setSparePartId(maintenanceItem.getSparePartId());
                maintenanceItemExtend.setSparePartName(sparePartMap.get(maintenanceItem.getSparePartId()));
                maintenanceItemExtend.setGroupId(groupId);
                maintenanceItemExtend.setGroupName(groupMap.get(groupId));
                maintenanceItemExtend.setSort(maintenanceItem.getSort());
                maintenanceItemExtend.setNumber(maintenanceItem.getNumber());
                maintenanceItemExtends.add(maintenanceItemExtend);
            }
        }
        return maintenanceItemExtends;
    }

    private Map<Long, String> queryMaintenanceType(Long projectId) {
        List<MaintenanceTypeDefine> maintenanceTypes = maintenanceTypeDefineDao.queryTypeByName(null, projectId);
        return maintenanceTypes.stream().collect(Collectors.toMap(BaseEntity::getId, BaseEntity::getName));
    }

    @Override
    public List<ProcessInstanceResponse> createWorkOrderManuallyBatch(AddMaintenanceWorkOrderRequest addMaintenanceWorkOrderRequest) {
        Long userId = GlobalInfoUtils.getUserId();
        UserVo userVo = authUtils.queryAndCheckUser(userId);

        // 设置工单
        MultiTableTriggerParams multiTableTriggerParams = new MultiTableTriggerParams();
        // 设置流程key值
        multiTableTriggerParams.setProcessDefinitionKey(WorkOrderKeyDef.MAINTENANCE_KEY);
        // 设置流程变量数据
        Map<String, Object> processVariables = new HashMap<>(CommonUtils.MAP_INIT_SIZE_4);
        processVariables.put(ProcessVariableDefinition.CANDICATE_GROUPS, String.valueOf(addMaintenanceWorkOrderRequest.getTeamId()));
        processVariables.put(ProcessVariableDefinition.TENANT_ID, userVo.getTenantId());
        multiTableTriggerParams.setProcessVariables(processVariables);
        // 设置工单数据
        List<Map<String, Object>> formDataList = new ArrayList<>();
        for (BaseVo obj : addMaintenanceWorkOrderRequest.getObjects()) {
            Map<String, Object> formData = assemblyWorkOrder(addMaintenanceWorkOrderRequest, userVo, obj);
            formDataList.add(formData);
        }
        multiTableTriggerParams.setFormDataList(formDataList);

        ApiResult<List<ProcessInstanceResponse>> result = workOrderDao.startProcessesByManyTables(userId, multiTableTriggerParams);
        return result.getData();
    }

    /**
     * 组装工单数据
     *
     * @param addMaintenanceWorkOrderRequest
     * @param userVo
     * @param obj
     * @return
     */
    private Map<String, Object> assemblyWorkOrder(AddMaintenanceWorkOrderRequest addMaintenanceWorkOrderRequest, UserVo userVo, BaseVo obj) {
        Map<String, Object> formData = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        formData.put(ColumnDef.MODEL_LABEL, ModelLabelDef.PM_WORK_SHEET);
        formData.put(WorkOrderDef.EXECUTE_TIME_PLAN, addMaintenanceWorkOrderRequest.getExecuteTimePlan());
        formData.put(WorkOrderDef.TIME_CONSUME_PLAN, addMaintenanceWorkOrderRequest.getTimeConsumePlan());
        formData.put(WorkOrderDef.TEAM_ID, addMaintenanceWorkOrderRequest.getTeamId());
        formData.put(WorkOrderDef.TASK_LEVEL, addMaintenanceWorkOrderRequest.getTaskLevel());
        formData.put(WorkOrderDef.PERSON_NUMBER, addMaintenanceWorkOrderRequest.getPersonNumber());
        formData.put(WorkOrderDef.TASK_TYPE, WorkSheetTaskType.MAINTENANCE);
        //维保项目
        MaintenanceContent maintenanceWorkOrderContent = new MaintenanceContent();
        List<MaintenanceItemExtend> maintenanceItemExtends = handleMaintenanceExtend(addMaintenanceWorkOrderRequest);
        maintenanceWorkOrderContent.setItemExtends(maintenanceItemExtends);
        formData.put(WorkOrderDef.MAINTENANCE_CONTENT, JsonTransferUtils.toJSONString(maintenanceWorkOrderContent));
        formData.put(WorkOrderDef.WORKSHEET_STATUS, WorkSheetStatusDef.TO_BE_SENT);
        formData.put(ColumnDef.PROJECT_ID, GlobalInfoUtils.getTenantId());
        formData.put(WorkOrderDef.SAFETY_MEASURE, addMaintenanceWorkOrderRequest.getSafetyMeasure());
        formData.put(WorkOrderDef.DEVICE_PLAN_RELATIONSHIP_MODEL,
                Collections.singletonList(new DevicePlanRelationshipSaveVo(obj.getId(), obj.getModelLabel())));
        // 创建者信息
        formData.put(WorkOrderDef.CREATOR, userVo.getId());
        formData.put(WorkOrderDef.CREATE_TIME, ProcessVariableDefinition.NOW_TIME_SYMBOL);
        formData.put(ColumnDef.TENANT_ID, userVo.getTenantId());
        // 工单服务自动生成字段
        formData.put(WorkOrderDef.CODE, ProcessVariableDefinition.CODE_SYMBOL);
        return formData;
    }

    /**
     * @param addMaintenanceWorkOrderRequest
     * @return
     * @deprecated 创建工单的时候只支持单个对象，不在使用
     */
    @Override
    @Deprecated
    public ProcessInstanceResponse createWorkOrderManually(AddMaintenanceWorkOrderRequest addMaintenanceWorkOrderRequest) {
        Long userId = GlobalInfoUtils.getUserId();
        UserVo userVo = authUtils.queryAndCheckUser(userId);
        // 设置工单
        TableTriggerParams tableTriggerParams = new TableTriggerParams();
        tableTriggerParams.setProcessDefinitionKey(WorkOrderKeyDef.MAINTENANCE_KEY);
        Map<String, Object> processVariables = new HashMap<>(CommonUtils.MAP_INIT_SIZE_4);
        processVariables.put(ProcessVariableDefinition.CANDICATE_GROUPS, String.valueOf(addMaintenanceWorkOrderRequest.getTeamId()));
        processVariables.put(ProcessVariableDefinition.TENANT_ID, userVo.getTenantId());
        tableTriggerParams.setProcessVariables(processVariables);
        Map<String, Object> formData = new HashMap<>();
        formData.put(ColumnDef.MODEL_LABEL, ModelLabelDef.PM_WORK_SHEET);
        formData.put(WorkOrderDef.EXECUTE_TIME_PLAN, addMaintenanceWorkOrderRequest.getExecuteTimePlan());
        formData.put(WorkOrderDef.TIME_CONSUME_PLAN, addMaintenanceWorkOrderRequest.getTimeConsumePlan());
        formData.put(WorkOrderDef.TEAM_ID, addMaintenanceWorkOrderRequest.getTeamId());
        formData.put(WorkOrderDef.TASK_LEVEL, addMaintenanceWorkOrderRequest.getTaskLevel());
        formData.put(WorkOrderDef.PERSON_NUMBER, addMaintenanceWorkOrderRequest.getPersonNumber());
        formData.put(WorkOrderDef.TASK_TYPE, WorkSheetTaskType.MAINTENANCE);
        //维保项目
        MaintenanceContent maintenanceWorkOrderContent = new MaintenanceContent();
        List<MaintenanceItemExtend> maintenanceItemExtends = handleMaintenanceExtend(addMaintenanceWorkOrderRequest);
        maintenanceWorkOrderContent.setItemExtends(maintenanceItemExtends);
        formData.put(WorkOrderDef.MAINTENANCE_CONTENT, JsonTransferUtils.toJSONString(maintenanceWorkOrderContent));
        formData.put(WorkOrderDef.WORKSHEET_STATUS, WorkSheetStatusDef.TO_BE_SENT);
        formData.put(ColumnDef.PROJECT_ID, GlobalInfoUtils.getTenantId());
        formData.put(WorkOrderDef.SAFETY_MEASURE, addMaintenanceWorkOrderRequest.getSafetyMeasure());
        formData.put(WorkOrderDef.DEVICE_PLAN_RELATIONSHIP_MODEL,
                Collections.singletonList(new DevicePlanRelationshipSaveVo(addMaintenanceWorkOrderRequest.getObjectId(), addMaintenanceWorkOrderRequest.getObjectLabel())));
        // 创建者信息
        formData.put(WorkOrderDef.CREATOR, userVo.getId());
        formData.put(WorkOrderDef.CREATE_TIME, ProcessVariableDefinition.NOW_TIME_SYMBOL);
        formData.put(ColumnDef.TENANT_ID, userVo.getTenantId());
        // 工单服务自动生成字段
        formData.put(WorkOrderDef.CODE, ProcessVariableDefinition.CODE_SYMBOL);
        tableTriggerParams.setFormData(formData);
        ApiResult<ProcessInstanceResponse> result = workOrderDao.startProcessByTable(userId, tableTriggerParams);
        return result.getData();
    }

    @Override
    public void saveInputWorkOrder(InputMaintenanceWorkOrderRequest inputMaintenanceWorkOrderRequest) {
        Map<String, Object> params = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        params.put(WorkOrderDef.ID, inputMaintenanceWorkOrderRequest.getId());
        params.put(ColumnDef.MODEL_LABEL, ModelLabelDef.PM_WORK_SHEET);
        params.put(WorkOrderDef.ATTACHMENT, JsonTransferUtils.toJSONString(inputMaintenanceWorkOrderRequest.getAttachment()));
        if (inputMaintenanceWorkOrderRequest.getFinishTime() == null) {
            //app端的完成时间是工单提交的时间
            params.put(WorkOrderDef.FINISH_TIME, ProcessVariableDefinition.NOW_TIME_SYMBOL);
        } else {
            params.put(WorkOrderDef.FINISH_TIME, inputMaintenanceWorkOrderRequest.getFinishTime());
            params.put(WorkOrderDef.TIME_CONSUME, inputMaintenanceWorkOrderRequest.getFinishTime() - inputMaintenanceWorkOrderRequest.getExecuteTime());
        }
        params.put(WorkOrderDef.EXECUTE_TIME, inputMaintenanceWorkOrderRequest.getExecuteTime());
        MaintenanceContent maintenanceWorkOrderContent = new MaintenanceContent();
        handleMaintenanceWorkOrderContent(inputMaintenanceWorkOrderRequest);
        maintenanceWorkOrderContent.setSparePartNumbers(inputMaintenanceWorkOrderRequest.getSparePartNumbers());
        maintenanceWorkOrderContent.setItemExtends(inputMaintenanceWorkOrderRequest.getItemExtends());
        params.put(WorkOrderDef.MAINTENANCE_CONTENT, JsonTransferUtils.toJSONString(maintenanceWorkOrderContent));
        params.put(WorkOrderDef.HANDLE_DESCRIPTION, inputMaintenanceWorkOrderRequest.getDescription());
        ApiResult<List<Map<String, Object>>> result = workOrderDao.saveModelEntityList(Collections.singletonList(params));
    }

    private void handleMaintenanceWorkOrderContent(InputMaintenanceWorkOrderRequest inputMaintenanceWorkOrderRequest) {
        if (CollectionUtils.isEmpty(inputMaintenanceWorkOrderRequest.getSparePartNumbers())) {
            return;
        }
        List<Long> collect = inputMaintenanceWorkOrderRequest.getSparePartNumbers().stream().map(SparePartNumber::getId).collect(Collectors.toList());
        List<SpareParts> sparePartsList = sparePartsDao.selectBatchIds(collect);
        for (SparePartNumber item : inputMaintenanceWorkOrderRequest.getSparePartNumbers()) {
            for (SpareParts itemNow : sparePartsList) {
                if (item.getId().equals(itemNow.getId())) {
                    item.setName(itemNow.getName());
                }
            }
        }
    }

    @Override
    public void submitInputWorkOrder(InputMaintenanceWorkOrderRequest inputMaintenanceWorkOrderRequest) {
        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        InspectionWorkOrderDto workOrder = workOrderService.queryRuntimeWorkOrder(inputMaintenanceWorkOrderRequest.getCode());

        UserTaskParams userTaskParams = new UserTaskParams();
        Map<String, Object> params = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        params.put(WorkOrderDef.ID, inputMaintenanceWorkOrderRequest.getId());
        params.put(ColumnDef.MODEL_LABEL, ModelLabelDef.PM_WORK_SHEET);
        params.put(WorkOrderDef.ATTACHMENT, JsonTransferUtils.toJSONString(inputMaintenanceWorkOrderRequest.getAttachment()));
        if (inputMaintenanceWorkOrderRequest.getFinishTime() == null) {
            //app端的完成时间是工单提交的时间
            params.put(WorkOrderDef.FINISH_TIME, ProcessVariableDefinition.NOW_TIME_SYMBOL);
        } else {
            params.put(WorkOrderDef.FINISH_TIME, inputMaintenanceWorkOrderRequest.getFinishTime());
            params.put(WorkOrderDef.TIME_CONSUME, inputMaintenanceWorkOrderRequest.getFinishTime() - inputMaintenanceWorkOrderRequest.getExecuteTime());
        }
        // 写入维保内容
        MaintenanceContent maintenanceWorkOrderContent = new MaintenanceContent();
        handleMaintenanceWorkOrderContent(inputMaintenanceWorkOrderRequest);
        maintenanceWorkOrderContent.setSparePartNumbers(inputMaintenanceWorkOrderRequest.getSparePartNumbers());
        maintenanceWorkOrderContent.setItemExtends(inputMaintenanceWorkOrderRequest.getItemExtends());
        params.put(WorkOrderDef.EXECUTE_TIME, inputMaintenanceWorkOrderRequest.getExecuteTime());
        params.put(WorkOrderDef.MAINTENANCE_CONTENT, JsonTransferUtils.toJSONString(maintenanceWorkOrderContent));
        params.put(WorkOrderDef.HANDLE_DESCRIPTION, inputMaintenanceWorkOrderRequest.getDescription());
        userTaskParams.setFormData(params);
        workOrderDao.submitForm(user.getId(), workOrder.getTaskId(), userTaskParams);
    }


    @Override
    public List<WorkOrderCountDto> queryMaintenanceWorkOrderCount(QueryMaintenanceWorkOrderCountRequest queryMaintenanceWorkOrderCountRequest) {
        UserVo user = authUtils.queryAndCheckUser(GlobalInfoUtils.getUserId());
        Long teamId = inspectorUserCheckUtils.getAndCheckTeamId(queryMaintenanceWorkOrderCountRequest.getTeamId(), user);
        queryMaintenanceWorkOrderCountRequest.setTeamId(teamId);
        List<Map<String, Object>> maps = workOrderDao.queryWorkOrderCountByStatus(queryMaintenanceWorkOrderCountRequest);
        List<WorkOrderCountDto> resultList = new ArrayList<>(4);
        // 解析结果
        for (Map<String, Object> map : maps) {
            Integer status = NumberUtils.parseInteger(map.get(WorkOrderDef.WORKSHEET_STATUS));
            Integer count = NumberUtils.parseInteger(map.get(ColumnDef.COUNT_ID));
            String workOrderStatusName = WorkSheetStatusDef.MAINTENANCE_ID_NAME_MAP.get(status);
            if (Objects.isNull(workOrderStatusName)) {
                continue;
            }
            WorkOrderCountDto workOrderCountDto = new WorkOrderCountDto();
            workOrderCountDto.setCount(count);
            workOrderCountDto.setWorkOrderStatus(status);
            workOrderCountDto.setWorkOrderStatusName(workOrderStatusName);
            resultList.add(workOrderCountDto);
        }
        return resultList;
    }


    @Override
    public MaintenanceWorkOrderDetail queryMaintenanceWorkOrderDetail(Long id, Long tenantId) {
        InspectionWorkOrderDto inspectionWorkOrderDto = workOrderDao.queryWorkOrder(id, InspectionWorkOrderDto.class);
        if (Objects.isNull(inspectionWorkOrderDto)) {
            return null;
        }
        MaintenanceWorkOrderDetail maintenanceWorkOrderDetail = new MaintenanceWorkOrderDetail();
        BeanUtils.copyProperties(inspectionWorkOrderDto, maintenanceWorkOrderDetail);
        handleJsonContent(maintenanceWorkOrderDetail);
        setTargetName(maintenanceWorkOrderDetail);
        List<UserVo> userVos = getAllRelatedUsers(maintenanceWorkOrderDetail, tenantId);
        setExecutorNames(maintenanceWorkOrderDetail, userVos);
        maintenanceWorkOrderDetail.generateSourceTypeName();
        setCreateUserName(maintenanceWorkOrderDetail);
        setTeamName(maintenanceWorkOrderDetail);
        setTaskLevelName(maintenanceWorkOrderDetail);
        setProcessFlowUnits(maintenanceWorkOrderDetail);
        setTimeInfo(maintenanceWorkOrderDetail, inspectionWorkOrderDto);
        return maintenanceWorkOrderDetail;
    }


    private void setTimeInfo(MaintenanceWorkOrderDetail maintenanceWorkOrderDetail, InspectionWorkOrderDto inspectionWorkOrderDto) {
        if (Objects.nonNull(inspectionWorkOrderDto.getExecuteTimePlan())) {
            maintenanceWorkOrderDetail.setExecuteTimePlan(TimeUtil.localDateTime2timestamp(inspectionWorkOrderDto.getExecuteTime()));
            if (Objects.nonNull(inspectionWorkOrderDto.getTimeConsumePlan())) {
                LocalDateTime plus = inspectionWorkOrderDto.getExecuteTime().plus(inspectionWorkOrderDto.getTimeConsumePlan(), ChronoUnit.MILLIS);
                maintenanceWorkOrderDetail.setExecuteEndTimePlan(TimeUtil.localDateTime2timestamp(plus));
            }
        }
        if (Objects.nonNull(inspectionWorkOrderDto.getExecuteTime())) {
            maintenanceWorkOrderDetail.setExecuteTime(TimeUtil.localDateTime2timestamp(inspectionWorkOrderDto.getExecuteTime()));
            if (Objects.nonNull(inspectionWorkOrderDto.getTimeConsume())) {
                LocalDateTime plus = inspectionWorkOrderDto.getExecuteTime().plus(inspectionWorkOrderDto.getTimeConsume(), ChronoUnit.MILLIS);
                maintenanceWorkOrderDetail.setExecuteEndTime(TimeUtil.localDateTime2timestamp(plus));
            }
        }
    }

    private void setProcessFlowUnits(MaintenanceWorkOrderDetail maintenanceWorkOrderDetail) {
        List<ProcessFlowUnit> processFlowUnits = workOrderDao.queryProcessFlowUnit(Collections.singletonList(maintenanceWorkOrderDetail.getId()));
        maintenanceWorkOrderDetail.setProcessFlowUnits(processFlowUnits);
    }

    private void setTaskLevelName(MaintenanceWorkOrderDetail maintenanceWorkOrderDetail) {
        if (Objects.nonNull(maintenanceWorkOrderDetail.getTaskLevel())) {
            Map<Integer, String> enumByLabel = modelServiceUtils.getEnumByLabel(ModelLabelDef.WORKSHEET_TASK_LEVEL);
            maintenanceWorkOrderDetail.setTaskLevelName(enumByLabel.get(maintenanceWorkOrderDetail.getTaskLevel()));
        }

    }

    private void setTeamName(MaintenanceWorkOrderDetail maintenanceWorkOrderDetail) {
        if (Objects.nonNull(maintenanceWorkOrderDetail.getTeamId())) {
            Result<UserGroupVo> userGroupVoResult = eemCloudAuthService.queryUserGroupById(maintenanceWorkOrderDetail.getTeamId());
            userGroupVoResult.throwExceptionIfFailed();
            UserGroupVo userGroupVo = userGroupVoResult.getData();
            maintenanceWorkOrderDetail.setTeamName(userGroupVo.getName());
        }
    }

    private void setCreateUserName(MaintenanceWorkOrderDetail maintenanceWorkOrderDetail) {
        if (Objects.isNull(maintenanceWorkOrderDetail.getCreator())) {
            return;
        }
        UserVo userVo = authUtils.queryUser(maintenanceWorkOrderDetail.getCreator());
        maintenanceWorkOrderDetail.setCreatorName(userVo.getName());
    }

    private void setExecutorNames(MaintenanceWorkOrderDetail maintenanceWorkOrderDetail, List<UserVo> userVos) {
        if (CollectionUtils.isEmpty(userVos)) {
            return;
        }
        maintenanceWorkOrderDetail.setExecutorNames(userVos.stream().map(UserVo::getName).collect(Collectors.toList()));
        List<MaintenanceItemExtendVo> maintenanceItemExtendList = maintenanceWorkOrderDetail.getMaintenanceItemExtendList();
        if (CollectionUtils.isNotEmpty(maintenanceWorkOrderDetail.getMaintenanceItemExtendList())) {
            Map<Long, String> idNameMap = userVos.stream().collect(Collectors.toMap(UserVo::getId, UserVo::getName));
            for (MaintenanceItemExtendVo maintenanceItemExtendVo : maintenanceItemExtendList) {
                if (CollectionUtils.isEmpty(maintenanceItemExtendVo.getExecutor())) {
                    continue;
                }
                List<String> executorName = maintenanceItemExtendVo.getExecutor().stream().map(idNameMap::get).filter(Objects::nonNull).collect(Collectors.toList());
                maintenanceItemExtendVo.setExecutorName(executorName);
            }
        }
    }

    private void handleJsonContent(MaintenanceWorkOrderDetail maintenanceWorkOrderDetail) {
        maintenanceWorkOrderDetail.setAttachmentList(JsonUtil.parseList(maintenanceWorkOrderDetail.getAttachment(), Attachment.class));
        MaintenanceContent maintenanceWorkOrderContent = JsonUtil.parseObject(maintenanceWorkOrderDetail.getMaintenanceContent(), MaintenanceContent.class);
        if (Objects.nonNull(maintenanceWorkOrderContent)) {
            List<MaintenanceItemExtend> itemExtends = maintenanceWorkOrderContent.getItemExtends();
            if (CollectionUtils.isNotEmpty(itemExtends)) {
                List<MaintenanceItemExtendVo> maintenanceItemExtendVoList = itemExtends.stream().map(s -> {
                    MaintenanceItemExtendVo maintenanceItemExtendVo = new MaintenanceItemExtendVo();
                    BeanUtils.copyProperties(s, maintenanceItemExtendVo);
                    return maintenanceItemExtendVo;
                }).collect(Collectors.toList());
                maintenanceWorkOrderDetail.setMaintenanceItemExtendList(maintenanceItemExtendVoList);
            }
            maintenanceWorkOrderDetail.setSparePartNumbers(maintenanceWorkOrderContent.getSparePartNumbers());
        }
    }

    private List<UserVo> getAllRelatedUsers(MaintenanceWorkOrderDetail maintenanceWorkOrderDetail, Long tenantId) {
        List<MaintenanceItemExtendVo> maintenanceItemExtendList = maintenanceWorkOrderDetail.getMaintenanceItemExtendList();
        if (CollectionUtils.isEmpty(maintenanceItemExtendList)) {
            return Collections.emptyList();
        }
        List<Long> allUserIds = maintenanceItemExtendList.stream().map(MaintenanceItemExtendVo::getExecutor).filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allUserIds)) {
            return Collections.emptyList();
        }
        return authUtils.queryBatchUser(allUserIds, tenantId);
    }

    private void setTargetName(MaintenanceWorkOrderDetail maintenanceWorkOrderDetail) {
        if (CollectionUtils.isEmpty(maintenanceWorkOrderDetail.getDevicePlanRelationshipList())) {
            return;
        }
        DevicePlanRelationship devicePlanRelationship = maintenanceWorkOrderDetail.getDevicePlanRelationshipList().get(0);
        BaseVo baseVo = new BaseVo(devicePlanRelationship.getDeviceId(), devicePlanRelationship.getDeviceLabel());
        Map<BaseVo, String> baseVoStringMap = nodeDao.queryNodeNameMap(Collections.singletonList(baseVo));
        String name = baseVoStringMap.get(baseVo);
        baseVo.setName(name);
        maintenanceWorkOrderDetail.setTarget(baseVo);
    }

    @Override
    public void exportWorkOrder(QueryMaintenanceWorkOrderRequest queryMaintenanceWorkOrderRequest, HttpServletResponse response) {
        String fileName = "维保工单";
        queryMaintenanceWorkOrderRequest.setPage(new Page(0, exportMaxCount));
        ResultWithTotal<List<MaintenanceWorkOrderDto>> listResultWithTotal = queryMaintenanceWorkOrderList(queryMaintenanceWorkOrderRequest);
        List<MaintenanceWorkOrderDto> maintenanceWorkOrderDtos = listResultWithTotal.getData();

        try (Workbook workBook = PoiExcelUtils.createWorkBook(ExcelType.BIG_DATA)) {
            List<Integer> colWidth = Arrays.asList(18, 18, 18, 18, 18, 18, 18);
            PoiExcelUtils.createSheet(workBook, fileName, (sheet, baseCellStyle, rowIndex) -> {
                int rowNum = 0;
                if (null == queryMaintenanceWorkOrderRequest.getWorkSheetStatus()) {
                    writeHeaderAll(sheet, baseCellStyle, rowNum++);
                    writeDataAll(sheet, baseCellStyle, rowNum, maintenanceWorkOrderDtos);
                } else if (queryMaintenanceWorkOrderRequest.getWorkSheetStatus().equals(WorkSheetStatusDef.AUDITED) || queryMaintenanceWorkOrderRequest.getWorkSheetStatus().equals(WorkSheetStatusDef.ACCOMPLISHED)) {
                    writeHeader(sheet, baseCellStyle, rowNum++);
                    writeData(sheet, baseCellStyle, rowNum, maintenanceWorkOrderDtos);
                } else if (queryMaintenanceWorkOrderRequest.getWorkSheetStatus().equals(WorkSheetStatusDef.TO_BE_SENT) || queryMaintenanceWorkOrderRequest.getWorkSheetStatus().equals(WorkSheetStatusDef.TO_BE_AUDITED)) {
                    writeHeaderReturn(sheet, baseCellStyle, rowNum++);
                    writeDataReturn(sheet, baseCellStyle, rowNum, maintenanceWorkOrderDtos);
                }

            }, colWidth);
            FileUtils.downloadExcel(response, workBook, "维保工单" + LocalDateTime.now().format(TimeUtil.SECONDTIMEFORMAT), ContentTypeDef.APPLICATION_MSEXCEL);
        } catch (Exception e) {
            ErrorUtils.exportError("维保工单", e);
        }
    }

    private void writeHeader(Sheet sheet, CellStyle baseCellStyle, int startRow) {
        LinkedHashMap<String, CellStyle> headerMap = new LinkedHashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        headerMap.put("工单号", baseCellStyle);
        headerMap.put(START_TIME_PLAN_DESC, baseCellStyle);
        headerMap.put("预计耗时", baseCellStyle);
        headerMap.put("实际耗时", baseCellStyle);
        headerMap.put("维保对象", baseCellStyle);
        headerMap.put("责任班组", baseCellStyle);
        headerMap.put("人员数量", baseCellStyle);
        headerMap.put("等级", baseCellStyle);
        PoiExcelUtils.createHeaderName(sheet, startRow, headerMap);
    }

    private void writeHeaderAll(Sheet sheet, CellStyle baseCellStyle, int startRow) {
        LinkedHashMap<String, CellStyle> headerMap = new LinkedHashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        headerMap.put("工单号", baseCellStyle);
        headerMap.put(START_TIME_PLAN_DESC, baseCellStyle);
        headerMap.put("预计耗时", baseCellStyle);
        headerMap.put("实际耗时", baseCellStyle);
        headerMap.put("维保对象", baseCellStyle);
        headerMap.put("责任班组", baseCellStyle);
        headerMap.put("人员数量", baseCellStyle);
        headerMap.put("等级", baseCellStyle);
        headerMap.put("工单状态", baseCellStyle);
        PoiExcelUtils.createHeaderName(sheet, startRow, headerMap);
    }

    private void writeHeaderReturn(Sheet sheet, CellStyle baseCellStyle, int startRow) {
        LinkedHashMap<String, CellStyle> headerMap = new LinkedHashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        headerMap.put("工单号", baseCellStyle);
        headerMap.put(START_TIME_PLAN_DESC, baseCellStyle);
        headerMap.put("预计耗时", baseCellStyle);
        headerMap.put("维保对象", baseCellStyle);
        headerMap.put("责任班组", baseCellStyle);
        headerMap.put("人员数量", baseCellStyle);
        headerMap.put("等级", baseCellStyle);
        PoiExcelUtils.createHeaderName(sheet, startRow, headerMap);
    }

    private void writeData(Sheet sheet, CellStyle baseCellStyle, int rowNum, List<MaintenanceWorkOrderDto> maintenanceWorkOrderDtos) {
        int col;

        for (MaintenanceWorkOrderDto item : maintenanceWorkOrderDtos) {
            col = 0;
            Row row = PoiExcelUtils.createRow(sheet, rowNum);
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getCode());

            PoiExcelUtils.createCell(row, col++, baseCellStyle, TimeUtil.format(item.getExecuteTimePlan(), TimeUtil.LONG_TIME_FORMAT));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, TimeUtil.parseTime(item.getTimeConsumePlan()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, TimeUtil.parseTime(item.getTimeConsume()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, getDeviceName(item));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getTeamName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getPersonNumber());
            PoiExcelUtils.createCell(row, col, baseCellStyle, item.getTaskLevelName());
            rowNum++;
        }
    }

    private void writeDataAll(Sheet sheet, CellStyle baseCellStyle, int rowNum, List<MaintenanceWorkOrderDto> maintenanceWorkOrderDtos) {
        int col;

        for (MaintenanceWorkOrderDto item : maintenanceWorkOrderDtos) {
            col = 0;
            Row row = PoiExcelUtils.createRow(sheet, rowNum);
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getCode());

            PoiExcelUtils.createCell(row, col++, baseCellStyle, TimeUtil.format(item.getExecuteTimePlan(), TimeUtil.LONG_TIME_FORMAT));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, TimeUtil.parseTime(item.getTimeConsumePlan()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, TimeUtil.parseTime(item.getTimeConsume()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, getDeviceName(item));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getTeamName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getPersonNumber());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getTaskLevelName());
            PoiExcelUtils.createCell(row, col, baseCellStyle, item.getWorkSheetStatusName());
            rowNum++;
        }
    }

    private void writeDataReturn(Sheet sheet, CellStyle baseCellStyle, int rowNum, List<MaintenanceWorkOrderDto> maintenanceWorkOrderDtos) {
        int col;

        for (MaintenanceWorkOrderDto item : maintenanceWorkOrderDtos) {
            col = 0;
            Row row = PoiExcelUtils.createRow(sheet, rowNum);
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getCode());

            PoiExcelUtils.createCell(row, col++, baseCellStyle, TimeUtil.format(item.getExecuteTimePlan(), TimeUtil.LONG_TIME_FORMAT));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, TimeUtil.parseTime(item.getTimeConsumePlan()));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, getDeviceName(item));
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getTeamName());
            PoiExcelUtils.createCell(row, col++, baseCellStyle, item.getPersonNumber());
            PoiExcelUtils.createCell(row, col, baseCellStyle, item.getTaskLevelName());
            rowNum++;
        }
    }

    private String getDeviceName(MaintenanceWorkOrderDto inspectionWorkOrderDto) {
        Assert.notEmpty(inspectionWorkOrderDto.getDevicePlanRelationshipList(), "维保或者巡检对象不存在");

        for (DevicePlanRelationship item : inspectionWorkOrderDto.getDevicePlanRelationshipList()) {
            if (null != item) {
                return item.getDeviceName();
            }
        }

        return null;
    }
}
