﻿package com.cet.eem.fusion.refrigeration.core.handler;

import com.cet.eem.bll.common.dao.pipenetwork.PipeNetworkConnectionModelDao;
import com.cet.eem.bll.common.log.service.CommonUtilsService;
import com.cet.eem.bll.common.model.domain.object.entitymap.PipeNetworkConnectionModel;
import com.cet.eem.bll.common.model.domain.subject.energysaving.DeviceChain;
import com.cet.eem.bll.demand.model.entity.Project;
import com.cet.eem.fusion.refrigeration.core.dao.weather.ColdPredictDao;
import com.cet.eem.fusion.refrigeration.core.handle.optimizationstrategy.JudgeParam;
import com.cet.eem.fusion.refrigeration.core.model.aioptimization.ColdWaterMainEngineVo;
import com.cet.eem.fusion.refrigeration.core.model.config.DeviceChainParam;
import com.cet.eem.fusion.refrigeration.core.model.config.system.BooleanCondition;
import com.cet.eem.fusion.refrigeration.core.model.config.system.ControlScheme;
import com.cet.eem.fusion.refrigeration.core.model.config.system.NumericalCondition;
import com.cet.eem.fusion.refrigeration.core.model.config.system.SingleCondition;
import com.cet.eem.fusion.refrigeration.core.model.def.*;
import com.cet.eem.fusion.refrigeration.core.model.weather.QueryParam;
import com.cet.eem.fusion.refrigeration.core.service.trend.ModelConfigurationService;
import com.cet.eem.fusion.refrigeration.core.service.trend.OperationTrendService;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.constant.EnumDataTypeId;
import com.cet.eem.common.constant.EnumOperationType;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.ModelLabelDef;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.model.realtime.RealTimeValue;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
import com.cet.eem.quantity.model.quantity.QuantitySearchVo;
import com.cet.eem.quantity.service.QuantityManageService;
import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/*
* 加减机判断
* */
@Configuration
@Slf4j
public class JudgeConfiguration {

    //制冷系统ID
    private Long refrigeratingSystemID;

    //数值判断条件
    private List<NumericalCondition> numericalConditionData;

    //布尔判断条件
    private List<BooleanCondition> booleanConditionData;

    private static final String LOG_KEY = "[冷机运行优化策略计算-加减机判断]";

    public  List<JudgeConfiguration.MachineJudgeService> judgeChain = new ArrayList<JudgeConfiguration.MachineJudgeService>();

    private JudgeParam judgeParam;

    private LocalDateTime time;

    private Integer tempDiffDataID = 524300;

    @Value("${cet.eem.optimizationstrategy.query-interval}")
    private int interval;

    public static final Double unit = 0.0036D;

    public static final Double UP = 1.0;
    public static final Double DOWN = 0.0;
    public static final Integer OPERATION_TYPE = 65;
    public static final Long userId = 1L;

    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Autowired
    ModelConfigurationService modelConfigurationService;

    @Autowired
    OperationTrendService operationTrendService;

    @Autowired
    PipeNetworkConnectionModelDao connectionModelDao;

    @Autowired
    QuantityManageService quantityManageService;

    @Autowired
    ColdPredictDao coldPredictDao;

    @Autowired
    CommonUtilsService commonUtilsService;

    public void initAddMachineConfiguration(Long refrigeratingSystemID, Integer controlType, LocalDateTime time, JudgeParam judgeParam){
        this.refrigeratingSystemID = refrigeratingSystemID;
        this.time = time;
        this.judgeParam = judgeParam;
        getControlScheme(controlType);
        judgeChain.clear();
        judgeChain.add(loadRateJudge(controlType));
        judgeChain.add(coldLoadRateJudge(controlType));
        judgeChain.add(supplyTempJudge(controlType));
        judgeChain.add(supplyTempDiffJudge(controlType));
        judgeParam.setIsOptimization(false);
        judgeParam.setColdWaterMainEnginesInTarget(null);
        judgeParam.setOptimizationType(null);
    }

    public interface MachineJudgeService {
        /*
         * 加机条件判断
         * @param coldWaterMainEnginesInOperation 在运行的冷机id
         * */
        public JudgeParam machineJudge(JudgeParam param);
    }

    public JudgeParam executeJudge(){
        log.info("{}：开始执行", LOG_KEY);
        for (JudgeConfiguration.MachineJudgeService x : judgeChain){
            judgeParam = x.machineJudge(judgeParam);
            if (judgeParam.getIsOptimization()){break;}
        }
        return judgeParam;
    }

    /**
     * 判断数值判断条件是否为空
     * 并返回指定参数类型的记录
     * @return*/
    private List<NumericalCondition> getAssignDataTypeRecord(List<NumericalCondition> numericalConditionData, Integer dataType) {
        if (CollectionUtils.isEmpty(numericalConditionData)){
            return  null;
        }
        return numericalConditionData.stream().filter(x -> Objects.equals(x.getDataType(),dataType)).collect(Collectors.toList());

    }

    private Long getProjectID(){
        QueryCondition queryCondition = new QueryConditionBuilder<>(NodeLabelDef.PROJECT).build();
        return modelServiceUtils.query(queryCondition, Project.class).get(0).getId();
    }

    /**
     * 查询加机条件
     * 获取到加机条件中具体的数值条件和布尔条件id
     * */
    private void getControlScheme(Integer controlType){
        QueryCondition condition = new QueryConditionBuilder<>(ColdOptimizationLabelDef.CONTROL_SCHEME)
                .eq(ColdOptimizationLabelDef.REFRIGERATING_SYSTEM_ID, refrigeratingSystemID)
                .eq(ColdOptimizationLabelDef.CONTROL_TYPE, controlType).build();
        List<ControlScheme> controlSchemes = modelServiceUtils.query(condition, ControlScheme.class);
        if (CollectionUtils.isEmpty(controlSchemes)){
            log.info("{}制冷系统：{}，没有找到控制方案", LOG_KEY, refrigeratingSystemID);
        }
        List<Long> numericalConditionID = new ArrayList<>();
        List<Long> booleanConditionID = new ArrayList<>();
        for (ControlScheme item : controlSchemes){
            List<SingleCondition> expressions = item.getExpression().getExpressions();
            //从条件表达式中过滤出数值条件和布尔条件
            List<Long> numericalConditionTemp = expressions.stream().filter(x -> x.getObjectLabel().equals(ColdOptimizationLabelDef.NUMERICAL_CONDITION))
                    .map(SingleCondition :: getObjectId).collect(Collectors.toList());
            List<Long> booleanConditionTemp = expressions.stream().filter(x -> x.getObjectLabel().equals(ColdOptimizationLabelDef.BOOLEAN_CONDITION))
                    .map(SingleCondition :: getObjectId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(numericalConditionTemp)){
                numericalConditionID.addAll(numericalConditionTemp);
            }
            if (!CollectionUtils.isEmpty(booleanConditionTemp)){
                booleanConditionID.addAll(booleanConditionTemp);
            }
        }
        getControlCondition(numericalConditionID, booleanConditionID);
    }

    /*
     * 根据数值条件模型id和布尔条件id查询模型记录
     * */
    private void getControlCondition(List<Long> numericalConditionID, List<Long> booleanConditionID){
        QueryCondition conditionForNumericalCondition  = new QueryConditionBuilder<>(ColdOptimizationLabelDef.NUMERICAL_CONDITION)
                .in(ColumnDef.ID, numericalConditionID).build();
        QueryCondition conditionForBooleanConditions  = new QueryConditionBuilder<>(ColdOptimizationLabelDef.BOOLEAN_CONDITION)
                .in(ColumnDef.ID, booleanConditionID).build();
        numericalConditionData = modelServiceUtils.query(conditionForNumericalCondition, NumericalCondition.class);
        booleanConditionData = modelServiceUtils.query(conditionForBooleanConditions, BooleanCondition.class);
    }

    /**
     * 判断过程
     * @param coldWaterMainEnginesInOperation 在运行的冷机id
     * @param dataMap 冷机运行数据
     * @param dataType 比较的数据类型 1-负载率，2-冷负荷率，3-供水温度，4-供回水温差
     * @param assignDataTypeRecord 条件
     * @param dataID
     * */
    public boolean judgeProcess(List<Long> coldWaterMainEnginesInOperation, Map<Integer, List<TrendDataVo>> dataMap,
                                 Integer dataType, List<NumericalCondition> assignDataTypeRecord, Integer dataID){

        boolean res = true;
        if(CollectionUtils.isEmpty(coldWaterMainEnginesInOperation) || dataMap.isEmpty() || Objects.isNull(dataMap)){
            log.info("{}制冷系统：{}，没有运行中冷机或者缺少数据", LOG_KEY, refrigeratingSystemID);
            return false;
        }

        //同一类型的条件，需要全部满足才可加减机，所以这里以条件为标准进行循环
        //如负载率>70%，需要所有在运行冷机全部满足，才可加机
        List<NumericalCondition> conditions = assignDataTypeRecord.stream().filter(x -> Objects.equals(x.getDataType(), dataType)).collect(Collectors.toList());

        for (NumericalCondition item : conditions){
            List<Long> ids = coldWaterMainEnginesInOperation.stream().filter(x -> Objects.equals(x, item.getObjectId())).collect(Collectors.toList());
            List<TrendDataVo> realTimeValues = dataMap.get(dataID);
            if (CollectionUtils.isEmpty(ids) || Objects.isNull(realTimeValues)){
                continue;
            }
            Long coldWaterMainEngineID = ids.get(0);
            List<TrendDataVo> dataValues = realTimeValues.stream().filter(x -> Objects.equals(x.getMonitoredid(), coldWaterMainEngineID))
                    .collect(Collectors.toList());
            //false，说明该条件下不是所有冷机都满足，即该条件冷机无需加减
            if (!judge(item, dataValues)){
                res = false;
            }
        }

        return res;
    }
    /**
     * 判断冷机某参数在持续时间要求内，大于（小于、等于）对应设定值
     * 比如连续30分钟，负载率大于70%，定时记录间隔为5min
     * 在valueList上取连续的7条记录，当它们均大于70%时，上面的条件即可满足
     * 所以窗口内定时记录数 =[（持续时间 / 定时记录间隔）+ 1]再向上取整
     * @param condition 条件
     * @param valueList 冷机运行数据
     * */
    private boolean judge(NumericalCondition condition, List<TrendDataVo> valueList){
        long timeValueForMin = condition.getTimeValue() / 1000L / 60;
        int count = (int)Math.ceil((timeValueForMin / AggregationCycle.FIVE_MINUTES) + 1);
        if (CollectionUtils.isEmpty(valueList)){
            return false;
        }
        TrendDataVo trendDataVo = valueList.get(0);
        for (int i = 0;i <= trendDataVo.getDataList().size()-count;i++){
            List<DatalogValue> valueSubList = trendDataVo.getDataList().subList(i, i + count);
            switch (condition.getOperationSymbol()){
                case OperationSymbolDef.EQ:
                    if (valueSubList.stream().filter(x -> !Objects.equals(x.getValue(), condition.getValue())).count() > 0L){
                        return true;
                    }
                    break;
                case OperationSymbolDef.GT:
                    if (valueSubList.stream().filter(x -> (x.getValue() >= condition.getValue())).count() == count){
                        return true;
                    }
                    break;
                case OperationSymbolDef.LT:
                    if (valueSubList.stream().filter(x -> (x.getValue() <= condition.getValue())).count() == count){
                        return true;
                    }
                    break;
                default:
                    return false;
            }
        }

        return false;
    }

    /**
     * 根据指定设备查询对应的连锁和设备
     * */
    public List<BaseVo> getDeviceChain(String objectLabel, Long objectID, Long roomid){
        QueryCondition condition  = new QueryConditionBuilder<>(ModelLabelDef.DEVICE_CHAIN)
                .eq(ColumnDef.ROOM_ID, roomid).build();
        List<DeviceChain> query = modelServiceUtils.query(condition, DeviceChain.class);
        if (CollectionUtils.isEmpty(query)){
            return null;
        }
        List<Long> deviceChainIDs = query.stream().map(DeviceChain::getId).collect(Collectors.toList());

        List<BaseVo> res = new ArrayList<>();
        for (Long deviceChainID : deviceChainIDs){
            List<DeviceChainParam> deviceChainParams = modelConfigurationService.queryDeviceChainWithDetail(deviceChainID,getProjectID());
            if (CollectionUtils.isEmpty(deviceChainParams)){continue;}
            List<BaseVo> chainList = deviceChainParams.get(0).getNodes().stream().filter(x -> (x.getId().equals(objectID) && x.getModelLabel().equals(objectLabel))).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(chainList)){
                return deviceChainParams.get(0).getNodes().stream().map(x -> new BaseVo(x.getId(), x.getModelLabel())).collect(Collectors.toList());
            }
        }
        return res;
    }

    private QuantityDataBatchSearchVo getQuantityDataBatchSearchVo(List<BaseVo> nodes, List<QuantitySearchVo> quantitySearchVo) {
        QuantityDataBatchSearchVo searchVo = new QuantityDataBatchSearchVo();
        searchVo.setStartTime(TimeUtil.localDateTime2timestamp(time.plusMinutes(-interval)));
        searchVo.setEndTime(TimeUtil.localDateTime2timestamp(time));
        searchVo.setAggregationCycle(AggregationCycle.FIVE_MINUTES);
        searchVo.setDataTypeId(EnumDataTypeId.REALTIME.getId());
        searchVo.setNodes(nodes);
        searchVo.setQuantitySettings(quantitySearchVo);
        return searchVo;
    }

    private Map<Integer, List<TrendDataVo>> getIntegerListMap(List<BaseVo> coldWaterMainEngines, List<QuantitySearchVo> mainCoolingLoadQuantitySetting) {
        QuantityDataBatchSearchVo searchVo = getQuantityDataBatchSearchVo(coldWaterMainEngines, mainCoolingLoadQuantitySetting);
        return quantityManageService.queryDataLogBatch(searchVo);
    }

    private Double getOperationMachineTotalRatedRefrigeration(JudgeParam param){
        QueryCondition condition  = new QueryConditionBuilder<>(NodeLabelDef.COLD_WATER_MAINENGINE)
                .in(ColumnDef.ID, param.getColdWaterMainEnginesInOperation()).build();
        List<ColdWaterMainEngineVo> coldWaterMainEngineVos = modelServiceUtils.query(condition, ColdWaterMainEngineVo.class);
        return coldWaterMainEngineVos.stream().collect(Collectors.summingDouble(x -> x.getRatedrefrigeration()));
    }

    /**
     * 根据总管冷量计算平均冷负荷率
     * 总管冷量读数 / 运行中冷机的额定冷负荷率之和 = 平均冷负荷率
     * */
    private Map<Integer, List<TrendDataVo>> getAverageColdLoadRate(JudgeParam param, List<TrendDataVo> trendDataVos) {
        if (CollectionUtils.isEmpty(trendDataVos)){
            return null;
        }
        TrendDataVo trendDataForColdRate = trendDataVos.get(0);

        Double operationMachineTotalRatedRefrigeration = getOperationMachineTotalRatedRefrigeration(param);


        List<DatalogValue> averageColdLoadRate = new ArrayList<>();
        for (DatalogValue i : trendDataForColdRate.getDataList()){
            Double kw = CommonUtils.calcDouble(i.getValue(), unit, EnumOperationType.DIVISION.getId());
            i.setValue(Math.abs(CommonUtils.calcDouble(kw, operationMachineTotalRatedRefrigeration, EnumOperationType.DIVISION.getId())) * 100);
            averageColdLoadRate.add(i);
        }

        List<TrendDataVo> coldLoadRateList = new ArrayList<>();
        for (Long id : judgeParam.getColdWaterMainEnginesInOperation()){
            TrendDataVo item = new TrendDataVo();
            item.setDataId(Long.valueOf(QuantityDef.getFreezingWaterPipelineForStream().getId()));
            item.setMonitoredid(id);
            item.setMonitoredlabel(NodeLabelDef.COLD_WATER_MAINENGINE);
            item.setDataList(averageColdLoadRate);
            coldLoadRateList.add(item);
        }
        Map<Integer, List<TrendDataVo>> integerListMap = new HashMap<>();
        integerListMap.put(QuantityDef.getFreezingWaterPipelineForStream().getId(), coldLoadRateList);
        return integerListMap;
    }

    //查询冷机关联的冷冻管,一个房间下只有一个冷冻水总管
    private List<BaseVo> getPipelines(JudgeParam param) {
        List<BaseVo> mains = operationTrendService.queryColdWaterMainEngine(
                new QueryParam(param.getRoomID(), NodeLabelDef.ROOM), NodeLabelDef.COLD_WATER_MAINENGINE);
        List<PipeNetworkConnectionModel> connectionModels = connectionModelDao.getTopOrDownBatchWithNodes(mains, false, getProjectID());
        return connectionModels.stream().filter(model -> Objects.equals(model.getOutflowLabel(), NodeLabelDef.PIPELINE))
                .map(model -> new BaseVo(model.getOutflowId(), model.getOutflowLabel()))
                .collect(Collectors.toList());
    }

    /**
     * 计算供回水温差
     * 因为没有供回水温差测点，所以需要|供水温度 - 回水温度|得到温差
     * */
    public Map<Integer, List<TrendDataVo>> calcTempDiff(Map<Integer, List<TrendDataVo>> integerListMap){
        TrendDataVo supplyTempData = integerListMap.get(QuantityDef.getEndSupplyTemp().getId()).get(0);
        TrendDataVo returnTempData = integerListMap.get(QuantityDef.getEndReturnTemp().getId()).get(0);
        if (Objects.isNull(supplyTempData) || Objects.isNull(returnTempData)){
            log.info("{}制冷系统：数据缺失，无法计算供回水温差", LOG_KEY);
            return null;
        }
        Map<Integer, List<TrendDataVo>> res = new HashMap<>();
        List<DatalogValue> DatalogValueList = new ArrayList<>();
        for (DatalogValue item : supplyTempData.getDataList()){
            List<DatalogValue> collect = returnTempData.getDataList().stream().filter(x -> Objects.equals(x.getTime(),item.getTime())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)){
                continue;
            }
            DatalogValue DatalogValue= new DatalogValue();
            DatalogValue.setTime(item.getTime());
            DatalogValue.setValue(Math.abs(item.getValue() - collect.get(0).getValue()));
            DatalogValueList.add(DatalogValue);
        }
        supplyTempData.setDataList(DatalogValueList);
        res.put(tempDiffDataID, Collections.singletonList(supplyTempData));

        return res;
    }

    /*
    * 分界一下，上面是处理方法，下面是判断的方法
    * */

    /*
     * 冷机负载率的判断
     * ps：冷机没有负载率测点，使用“当前运行电流百分比”来代替表示冷机的单台负载率
     * */
    public MachineJudgeService loadRateJudge(Integer controlType){
        return new MachineJudgeService() {
            @Override
            public JudgeParam machineJudge(JudgeParam param) {
                List<NumericalCondition> assignDataTypeRecord = getAssignDataTypeRecord(numericalConditionData, DataTypeDef.LOAD_RATE);
                Map<Integer, List<TrendDataVo>> integerListMap = getIntegerListMap(param.getColdWaterMainEngines(), Collections.singletonList(QuantityDef.getMachineI()));
                if (Objects.isNull(assignDataTypeRecord)){
                    param.setIsOptimization(false);
                    log.info("{}:没有找到冷机负载率的条件记录，退出该判断", LOG_KEY);
                    return param;
                }
                param.setIsOptimization(judgeProcess(param.getColdWaterMainEnginesInOperation(), integerListMap, DataTypeDef.LOAD_RATE, assignDataTypeRecord, QuantityDef.getMachineI().getId()));
                log.info("{}:冷机负载率的判断结果:{}", LOG_KEY, param.getIsOptimization());
                commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE, LOG_KEY + "冷机负载率的判断结果：" + param.getIsOptimization(), null, userId);
                param.setOptimizationType(controlType);
                //当需要减机时，目标操作冷机就是运行中的冷机
                if (controlType.equals(ColdControlTypeDef.SUB)){
                    param.setColdWaterMainEnginesInTarget(param.getColdWaterMainEnginesInOperation());
                }
                //当需要加机时，目标操作冷机就是待机中的冷机
                else {
                    param.setColdWaterMainEnginesInTarget(param.getColdWaterMainEnginesInStandby());
                }
                return param;
            }

        };
    }

    /**
     * 冷机冷负荷率判断
     * 判断过程类似，但鲍学超意见，后续判断规则可能细化，要修改
     * 所以暂时不考虑抽取方法
     * ps：这个项目只有一个总冷量表在冷冻水总管上，所以用平均冷负荷率代替，测点6008008
     * */
    public MachineJudgeService coldLoadRateJudge(Integer controlType){
        return new MachineJudgeService() {
            @Override
            public JudgeParam machineJudge(JudgeParam param) {
                List<NumericalCondition> assignDataTypeRecord = getAssignDataTypeRecord(numericalConditionData, DataTypeDef.COLD_LOAD_RATE);
                List<BaseVo> pipelines = getPipelines(param);
                if (CollectionUtils.isEmpty(pipelines)){
                    param.setIsOptimization(false);
                    log.info("{}:没有找到冷机冷负荷率的条件记录，退出该判断", LOG_KEY);
                    return param;
                }
                Map<Integer, List<TrendDataVo>> pipelinesIntegerListMap = getIntegerListMap(pipelines, Collections.singletonList(QuantityDef.getFreezingWaterPipelineForStream()));

                List<TrendDataVo> trendDataVos = pipelinesIntegerListMap.get(QuantityDef.getFreezingWaterPipelineForStream().getId());
                if (CollectionUtils.isEmpty(trendDataVos)){
                    param.setIsOptimization(false);
                    return param;
                }
                Map<Integer, List<TrendDataVo>> integerListMap = getAverageColdLoadRate(param, trendDataVos);


                if (Objects.isNull(assignDataTypeRecord)){
                    param.setIsOptimization(false);
                    return param;
                }
                param.setIsOptimization(judgeProcess(param.getColdWaterMainEnginesInOperation(), integerListMap, DataTypeDef.COLD_LOAD_RATE, assignDataTypeRecord, QuantityDef.getFreezingWaterPipelineForStream().getId()));
                log.info("{}:冷机冷负荷率负载率的判断结果:{}", LOG_KEY, param.getIsOptimization());
                commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE, LOG_KEY + "冷机冷负荷率负载率的判断结果：" + param.getIsOptimization(), null, userId);
                param.setOptimizationType(controlType);
                if (controlType.equals(ColdControlTypeDef.SUB)){
                    param.setColdWaterMainEnginesInTarget(param.getColdWaterMainEnginesInOperation());
                }else {
                    param.setColdWaterMainEnginesInTarget(param.getColdWaterMainEnginesInStandby());
                }
                return param;
            }

        };
    }



    /**
     * 冷机供水温度判断
     * 温度的判断与上不同，因为温度的对象来自于冷机关联的冷冻管
     * */
    public MachineJudgeService supplyTempJudge(Integer controlType){
        return new MachineJudgeService() {
            @Override
            public JudgeParam machineJudge(JudgeParam param) {
                List<NumericalCondition> assignDataTypeRecord = getAssignDataTypeRecord(numericalConditionData, DataTypeDef.SUPPLY_TEMP);
                if (Objects.isNull(assignDataTypeRecord)){
                    param.setIsOptimization(false);
                    log.info("{}:没有找到冷机供水温度的条件记录，退出该判断", LOG_KEY);
                    return param;
                }

                //查询冷机关联的冷冻管
                List<BaseVo> pipelines = getPipelines(param);
                if (CollectionUtils.isEmpty(pipelines)){
                    param.setIsOptimization(false);
                    log.info("{}:没有找到冷机对应的冷冻水管道，退出该判断", LOG_KEY);
                    return param;
                }

                Map<Integer, List<TrendDataVo>> integerListMap = getIntegerListMap(pipelines, Collections.singletonList(QuantityDef.getEndSupplyTemp()));

                Long piplineID = pipelines.get(0).getId();
                param.setIsOptimization(judgeProcess(Collections.singletonList(piplineID), integerListMap, DataTypeDef.SUPPLY_TEMP, assignDataTypeRecord, QuantityDef.getEndSupplyTemp().getId()));
                log.info("{}:冷机供水温度的判断结果:{}", LOG_KEY, param.getIsOptimization());
                commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE, LOG_KEY + "冷机供水温度的判断结果：" + param.getIsOptimization(), null, userId);
                param.setOptimizationType(controlType);
                if (controlType.equals(ColdControlTypeDef.SUB)){
                    param.setColdWaterMainEnginesInTarget(param.getColdWaterMainEnginesInOperation());
                }else {
                    param.setColdWaterMainEnginesInTarget(param.getColdWaterMainEnginesInStandby());
                }
                return param;
            }

        };
    }

    /**
     * 冷机供水温差判断
     * 该判断，仅适用于减机判断中
     * */
    public MachineJudgeService supplyTempDiffJudge(Integer controlType){
        return new MachineJudgeService() {
            @Override
            public JudgeParam machineJudge(JudgeParam param) {
                if (Objects.equals(controlType, ColdControlTypeDef.ADD)){
                    param.setIsOptimization(false);
                    return param;
                }
                List<NumericalCondition> assignDataTypeRecord = getAssignDataTypeRecord(numericalConditionData, DataTypeDef.SUPPLY_TEMP_DIFF);
                if (Objects.isNull(assignDataTypeRecord)){
                    param.setIsOptimization(false);
                    log.info("{}:没有找到冷机供水温差的条件记录，退出该判断", LOG_KEY);
                    return param;
                }
                List<BaseVo> pipelines = getPipelines(param);
                if (CollectionUtils.isEmpty(pipelines)){
                    param.setIsOptimization(false);
                    log.info("{}:没有找到冷机对应的冷冻水管道，退出该判断", LOG_KEY);
                    return param;
                }

                Map<Integer, List<TrendDataVo>> integerListMap = getIntegerListMap(pipelines, Arrays.asList(QuantityDef.getEndSupplyTemp(), QuantityDef.getEndReturnTemp()));
                Map<Integer, List<TrendDataVo>> tempDiff = calcTempDiff(integerListMap);

                Long piplineID = pipelines.get(0).getId();
                param.setIsOptimization(judgeProcess(Collections.singletonList(piplineID), tempDiff, DataTypeDef.SUPPLY_TEMP_DIFF, assignDataTypeRecord, tempDiffDataID));
                log.info("{}:冷机供水温差的判断结果:{}", LOG_KEY, param.getIsOptimization());
                commonUtilsService.writeUpdateOperationLogs(OPERATION_TYPE, LOG_KEY + "冷机供水温差的判断结果：" + param.getIsOptimization(), null, userId);
                param.setOptimizationType(controlType);
                if (controlType.equals(ColdControlTypeDef.SUB)){
                    param.setColdWaterMainEnginesInTarget(param.getColdWaterMainEnginesInOperation());
                }else {
                    param.setColdWaterMainEnginesInTarget(param.getColdWaterMainEnginesInStandby());
                }
                return param;
            }

        };
    }

    /**
     * 故障信号判断
     * 故障、停止信号有一为true，即可减机
     * */
    public JudgeParam deviceAbnormalJudge(JudgeParam param, LocalDateTime time) {
        List<Long> targetList = new ArrayList<>();
        for (Long coldWaterMainEnginesID : param.getColdWaterMainEngines().stream().map(BaseVo::getId).collect(Collectors.toList())){
            //查询出冷机对应连锁下的所有设备
            List<BaseVo> deviceChains = getDeviceChain(NodeLabelDef.COLD_WATER_MAINENGINE, coldWaterMainEnginesID, param.getRoomID());
            Map<Integer, List<RealTimeValue>> signalData = quantityManageService.queryRealTimeBath(
                    new QuantityDataBatchSearchVo(Arrays.asList(QuantityDef.getFaultSignal(), QuantityDef.getDeviceStatus()),
                            deviceChains, TimeUtil.localDateTime2timestamp(time.plusMinutes(-interval)), TimeUtil.localDateTime2timestamp(time),
                            AggregationCycle.FIVE_MINUTES, 5,EnumDataTypeId.REALTIME.getId())
            );

            List<RealTimeValue> faultSignalData = signalData.get(QuantityDef.getFaultSignal().getId());
            List<RealTimeValue> DeviceStatusData = signalData.get(QuantityDef.getDeviceStatus().getId());
            //过滤所有设备的故障、停止信号，只要有一个为1，说明存在异常，需要减机，并将对应冷机id，添加到“待操作的冷机id”list中
            List<RealTimeValue> collectForFaultSignal = faultSignalData.stream().filter(x -> Objects.equals(x.getValue(), UP)).collect(Collectors.toList());
            List<RealTimeValue> collectForDeviceStatus = DeviceStatusData.stream().filter(x -> Objects.equals(x.getValue(), DOWN)).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(collectForFaultSignal) || !CollectionUtils.isEmpty(collectForDeviceStatus)){
                param.setIsOptimization(true);
                param.setOptimizationType(ColdControlTypeDef.SUB);
                targetList.add(coldWaterMainEnginesID);
                log.info("{}制冷系统：{}，冷机：{}及其连锁，发现设备故障信号", LOG_KEY, refrigeratingSystemID,coldWaterMainEnginesID);
            }

        }
        if (CollectionUtils.isEmpty(targetList)){
            log.info("{}制冷系统：{}，没有发现设备故障，可以进行加减机判断", LOG_KEY, refrigeratingSystemID);
        }
        List<Long> coldWaterMainEnginesInStandby = param.getColdWaterMainEnginesInStandby();
        List<Long> target = targetList.stream().distinct().collect(Collectors.toList());
        param.setColdWaterMainEnginesInStandby(coldWaterMainEnginesInStandby);
        param.setColdWaterMainEnginesInTarget(target);

        return param;
    }
}

