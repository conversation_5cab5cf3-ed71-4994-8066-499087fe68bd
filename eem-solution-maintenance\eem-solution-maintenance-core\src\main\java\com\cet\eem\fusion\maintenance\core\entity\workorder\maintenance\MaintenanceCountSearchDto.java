package com.cet.eem.fusion.maintenance.core.entity.workorder.maintenance;

import com.cet.eem.fusion.maintenance.core.def.WorkSheetTaskType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @ClassName : MaintenanceCountSearchDto
 * @Description : 维保工单数量统计查询条件
 * <AUTHOR> jiangzixuan
 * @Date: 2021-06-19 09:07
 */
@Getter
@Setter
@ApiModel(description = "维保工单数量统计查询条件")
public class MaintenanceCountSearchDto {

        @ApiModelProperty("开始时间")
        private LocalDateTime startTime;

        @ApiModelProperty("结束时间")
        private LocalDateTime endTime;

        @ApiModelProperty("班组id")
        private Long teamId;

        @ApiModelProperty("工单状态")
        private List<Integer> workSheetStatuses;

        @ApiModelProperty("工单类型")
        private Integer taskType = WorkSheetTaskType.MAINTENANCE;


    }