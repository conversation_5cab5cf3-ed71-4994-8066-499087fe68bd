package com.cet.eem.fusion.maintenance.core.schedule.job;


import com.cet.eem.fusion.config.sdk.def.OperationLogType;
import com.cet.eem.bll.common.log.service.CommonUtilsService;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.PlanSheet;
import com.cet.eem.bll.common.model.enumeration.subject.powermaintenance.WorkSheetTaskType;
import com.cet.eem.bll.maintenance.config.MaintenanceRabbitMqConfig;
import com.cet.eem.bll.maintenance.dao.PlanSheetDao;
import com.cet.eem.bll.maintenance.schedule.event.CheckInspectionPlanIsOverCommand;
import com.cet.eem.bll.maintenance.schedule.event.CheckPlanIsOverEvent;
import com.cet.eem.bll.maintenance.schedule.event.CreateOrderCommand;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.common.def.auth.LoginDef;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.common.service.EemRabbitmqTemplate;
import com.cet.eem.common.util.JsonUtil;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.quartz.QuartzJobBean;

import java.time.LocalDate;
import java.util.List;
import java.util.Objects;


/**
 * 巡检计划JOB
 * 主要负责推送事件，记录日志等
 *
 * <AUTHOR>
 */
@Slf4j
public class PlanSheetJob extends QuartzJobBean {

    @Autowired
    private PlanSheetDao planSheetDao;
    /**
     * rabbitmq 推送给工单
     */
    @Autowired
    @Qualifier(EemRabbitmqTemplate.EEM_NOTICE_RABBIT_TEMPLATE)
    private AmqpTemplate amqpTemplate;

    @Autowired
    private CommonUtilsService commonUtilsService;

    /**
     * spring事件推送
     * 异步处理模型修改
     */
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Override
    protected void executeInternal(JobExecutionContext context) {
        try {
            log.info("巡检计划生成巡检工单信息:Trigger:{},NextFireTime:{}", JsonUtil.toJSONString(context.getTrigger()), context.getTrigger().getNextFireTime());
            JobDataMap jobDataMap = context.getJobDetail().getJobDataMap();
            Long id = (Long) jobDataMap.get(ModelLabelDef.PLAN_SHEET);
            if (Objects.isNull(id)) {
                log.error("JobDataMap缺少巡检计划信息");
                return;
            }
            PlanSheet planSheet = planSheetDao.selectById(id);
            pushCheckInspectionPlanIsOverEvent(context, planSheet);
            if (Objects.nonNull(planSheet.getEnabledDays()) && CollectionUtils.isNotEmpty(planSheet.getEnabledDays().getWeekDays())) {
                List<Integer> weekDays = planSheet.getEnabledDays().getWeekDays();
                LocalDate now = LocalDate.now();
                int weekValue = now.getDayOfWeek().getValue();
                if (!weekDays.contains(weekValue)) {
                    log.info("巡检计划预定执行星期为:{},今天是星期{}", weekDays, weekValue);
                    return;
                }
            }
            pushCreateOrderCommand(context, planSheet);
        } catch (Exception e) {
            log.error("巡检计划生成巡检工单Job异常", e);
        }
    }

    /**
     * 推送创建工单指令
     *
     * @param context
     * @param planSheet
     */
    private void pushCreateOrderCommand(JobExecutionContext context, PlanSheet planSheet) {
        if (Objects.isNull(planSheet.getWorkSheetType())) {
            log.error("未知的计划类型,无法生成工单,计划详细信息:{}", JsonUtil.toJSONString(planSheet));
            return;
        }
        CreateOrderCommand createOrderCommand = new CreateOrderCommand();
        createOrderCommand.setId(planSheet.getId());
        if (Objects.nonNull(context.getTrigger().getNextFireTime())) {
            createOrderCommand.setNextFireTime(context.getTrigger().getNextFireTime().getTime());
        }
        if (Objects.nonNull(context.getScheduledFireTime())) {
            if (Objects.nonNull(planSheet.getAheadDuration())) {
                createOrderCommand.setScheduledFireTime(context.getScheduledFireTime().getTime() + planSheet.getAheadDuration() * TimeUtil.MINUTE);
            } else {
                createOrderCommand.setScheduledFireTime(context.getScheduledFireTime().getTime());
            }
        }
        if (Objects.nonNull(context.getFireTime())) {
            createOrderCommand.setFireTime(context.getFireTime().getTime());
        }
        commonUtilsService.writeAddOperationLogs(EEMOperationLogType.INSPECTOR_WORK_ORDER, "推送创建巡检工单进入消息队列" + CommonUtils.getIP(), createOrderCommand, LoginDef.USER_SYSTEM_ID);
        amqpTemplate.convertAndSend(MaintenanceRabbitMqConfig.ORDER_EXCHANGE, getRoutingKeyByWorkSheetTaskType(planSheet.getWorkSheetType()), JsonUtil.toJSONString(createOrderCommand));
    }

    private String getRoutingKeyByWorkSheetTaskType(Integer workSheetTaskType) {
        if (WorkSheetTaskType.MAINTENANCE == workSheetTaskType) {
            return MaintenanceRabbitMqConfig.MAINTENANCE_ORDER;
        }
        if (WorkSheetTaskType.INSPECTION == workSheetTaskType) {
            return MaintenanceRabbitMqConfig.INSPECTION_ORDER;
        }
        //不会执行到这
        return null;
    }

    /**
     * 推送检测检测计划事件
     *
     * @param context
     * @param planSheet
     */
    private void pushCheckInspectionPlanIsOverEvent(JobExecutionContext context, PlanSheet planSheet) {
        CheckInspectionPlanIsOverCommand checkInspectionPlanIsOverCommand = new CheckInspectionPlanIsOverCommand();
        checkInspectionPlanIsOverCommand.setMayFireAgain(context.getTrigger().mayFireAgain());
        checkInspectionPlanIsOverCommand.setPlanSheet(planSheet);
        applicationEventPublisher.publishEvent(new CheckPlanIsOverEvent(checkInspectionPlanIsOverCommand));
    }
}
