﻿package com.cet.eem.fusion.refrigeration.core.task;

import com.cet.eem.bll.common.task.TaskSchedule;
import com.cet.eem.fusion.refrigeration.core.service.task.WeatherPredictDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * @ClassName : WeatherPredictTask
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-08-04 16:09
 */
@Component
@Slf4j
public class WeatherPredictTask implements TaskSchedule {
    @Autowired
    WeatherPredictDataService weatherPredictDataService;

    @Scheduled(cron = "${cet.eem.task.energy-saving.weather.interval}")
    @Override
    public void execute() throws IOException, InstantiationException, IllegalAccessException {
        weatherPredictDataService.saveWeatherPredictData();
    }
}
