package com.cet.eem.fusion.maintenance.core.entity.workorder.repair;

import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.common.model.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 4/12/2021
 */
@Getter
@Setter
@ApiModel(description = "巡检工单查询条件")
public class RepairByNodeSearchVo {
    @ApiModelProperty("分页")
    private Page page;

    @ApiModelProperty("节点")
    private BaseVo node;
}
