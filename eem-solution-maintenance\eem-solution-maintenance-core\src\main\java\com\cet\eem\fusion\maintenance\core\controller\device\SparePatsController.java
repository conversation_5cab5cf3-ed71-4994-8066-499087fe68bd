package com.cet.eem.fusion.maintenance.core.controller.device;

import com.cet.eem.maintenanceservice.controller.bff.device.SparePartsBffController;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName : SparePatsController
 * @Description : 备件管理的接口
 * <AUTHOR> Administrator
 * @Date: 2021-06-10 09:38
 */
@Api(value = "/eem/v1/device", tags = "设备管理备件接口")
@RequestMapping(value = "/eem/v1/device/spareParts")
@RestController
@Validated
public class SparePatsController extends SparePartsBffController {

}