package com.cet.eem.solution.common.entity.vo.cetml;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 功图点位判断
 * <AUTHOR>
 * @date 2024/3/26 19:50
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PointRecognitionVO {
    @ApiModelProperty("功图id")
    @JsonProperty("dynamometercard_id")
    private Long dynamometerCardId;

    @ApiModelProperty("x轴坐标")
    private Double x;

    @ApiModelProperty("y轴坐标")
    private Double y;
}