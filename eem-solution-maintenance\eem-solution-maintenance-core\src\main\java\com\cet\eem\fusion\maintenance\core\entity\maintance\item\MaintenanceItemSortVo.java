package com.cet.eem.fusion.maintenance.core.entity.maintance.item;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * @ClassName : SortSignInPointRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-03-31 16:00
 */
@Getter
@Setter
@ApiModel(description = "维保项目排序")
public class MaintenanceItemSortVo {

    @NotNull(message = "维保项目id不能为空")
    @ApiModelProperty("维保项目id")
    private Long id;

    @NotNull(message = "维保项目排序字段不能为空")
    @ApiModelProperty("维保项目排序字段,升序排序")
    private Integer sort;

}
