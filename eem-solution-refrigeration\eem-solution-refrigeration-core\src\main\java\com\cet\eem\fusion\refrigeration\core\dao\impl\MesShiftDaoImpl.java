﻿package com.cet.eem.fusion.refrigeration.core.impl;

import com.cet.eem.fusion.refrigeration.core.dao.aioptimization.MesShiftDao;
import com.cet.eem.fusion.refrigeration.core.model.config.MesShift;
import com.cet.eem.fusion.refrigeration.core.model.dataentryquery.ProductionDataDocking;
import com.cet.eem.fusion.refrigeration.core.model.def.ColdOptimizationLabelDef;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import com.cet.eem.model.model.BaseEntity;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : MesShiftDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-08-25 10:00
 */
@Repository
public class MesShiftDaoImpl extends ModelDaoImpl<MesShift> implements MesShiftDao {
    @Autowired
    ModelServiceUtils modelServiceUtils;

    @Override
    public List<MesShift> queryMesShift(BaseVo baseVo) {
        LambdaQueryWrapper<MesShift> wrapper = LambdaQueryWrapper.of(MesShift.class);
        wrapper.eq(MesShift::getObjectId, baseVo.getId())
                .eq(MesShift::getObjectLabel, baseVo.getModelLabel());
        List<MesShift> mesShifts = this.selectList(wrapper);
        if (CollectionUtils.isEmpty(mesShifts)) {
            return Collections.emptyList();
        }
        //返回最新的数据，因为历史数据也保留了
        List<MesShift> sort = mesShifts.stream().sorted(Comparator.comparing(MesShift::getLogTime).reversed()).collect(Collectors.toList());
        Long logTime = sort.get(0).getLogTime();
        return mesShifts.stream().filter(mesShift -> Objects.equals(logTime, mesShift.getLogTime())).collect(Collectors.toList());

    }

    @Override
    public List<MesShift> queryMesShift(List<BaseVo> nodes) {
        if (CollectionUtils.isEmpty(nodes)) {
            return Collections.emptyList();
        }
        Map<String, List<BaseVo>> nodeMap = nodes.stream().collect(Collectors.groupingBy(BaseVo::getModelLabel));
        int group = 1;

        QueryConditionBuilder<BaseEntity> builder = new QueryConditionBuilder<>(ColdOptimizationLabelDef.MES_SHIFT)
                .composeMethod(true);
        for (Map.Entry<String, List<BaseVo>> entry : nodeMap.entrySet()) {
            String label = entry.getKey();
            List<Long> ids = nodeMap.get(label).stream().map(BaseVo::getId).distinct().collect(Collectors.toList());
            builder.eq(ColumnDef.C_OBJECT_Label, label, group);
            builder.in(ColumnDef.C_OBJECT_ID, ids, group);
            group++;
        }
        List<MesShift> query = modelServiceUtils.query(builder.build(), MesShift.class);
        if (CollectionUtils.isEmpty(query)) {
            return Collections.emptyList();
        }
        return query;
    }

}
