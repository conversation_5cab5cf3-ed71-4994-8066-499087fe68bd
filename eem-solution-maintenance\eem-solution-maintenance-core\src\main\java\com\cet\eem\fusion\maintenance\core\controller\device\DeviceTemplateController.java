package com.cet.eem.fusion.maintenance.core.controller.device;/**
 * <AUTHOR>
 * @date 2021/4/29 17:09
 */

import com.cet.eem.maintenanceservice.controller.bff.device.DeviceTemplateBffController;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-04-29
 */
@Api(value = "/eem/v1/device", tags = "设备管理模板接口")
@RequestMapping(value = "/eem/v1/device/template")
@RestController
@Validated
public class DeviceTemplateController extends DeviceTemplateBffController {
}
