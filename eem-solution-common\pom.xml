<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.cet.electric</groupId>
        <artifactId>matterhorn-basic-service-parent</artifactId>
        <version>0.0.13</version>
        <relativePath/>
    </parent>

    <artifactId>eem-solution-common</artifactId>
    <version>4.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <fusion-matrix-v2-client>1.0.10</fusion-matrix-v2-client>
        <eem-base-fusion.version>5.0.108.Alpha</eem-base-fusion.version>
    </properties>

    <dependencies>
        <!--能管基础配置-->
        <dependency>
            <groupId>com.cet.electric</groupId>
            <artifactId>eem-base-fusion-config-sdk</artifactId>
        </dependency>
        <!--能管能耗包sdk-->
        <dependency>
            <groupId>com.cet.electric</groupId>
            <artifactId>eem-base-fusion-energy-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cet.electric</groupId>
            <artifactId>eem-base-fusion-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cet.electric</groupId>
            <artifactId>fusion-matrix-v2-client</artifactId>
            <version>${fusion-matrix-v2-client}</version>
        </dependency>
        <!--FlinkRedisConfig需要依赖jedis运行，后期重构这部分-->
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.cet.electric</groupId>
                <artifactId>eem-base-fusion-sdk-parent</artifactId>
                <version>${eem-base-fusion.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!--发布配置管理-->
    <distributionManagement>
        <repository>
            <id>10.12.135.233_9086</id>
            <name>releases</name>
            <url>http://10.12.135.233:9086/repository/cet/</url>
        </repository>
        <snapshotRepository>
            <id>10.12.135.233_9086</id>
            <name>snapshots</name>
            <url>http://10.12.135.233:9086/repository/cet/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <fork>false</fork>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
        <finalName>${project.artifactId}-${project.version}</finalName>
    </build>
</project>