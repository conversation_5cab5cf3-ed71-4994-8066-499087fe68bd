package com.cet.eem.fusion.maintenance.core.controller.bff.inspect;

import com.cet.eem.fusion.config.sdk.service.log.OperationLog;
import com.cet.eem.fusion.config.sdk.def.OperationLogType;
import com.cet.eem.fusion.common.utils.EnumOperationSubType;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.InspectionParameter;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.InspectionScheme;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.InspectionSchemeDetail;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.InspectionSchemeDetailVo;
import com.cet.eem.bll.maintenance.model.param.AddInspectionParameterRequest;
import com.cet.eem.bll.maintenance.model.param.QueryInspectionParameterByDevice;
import com.cet.eem.bll.maintenance.model.param.QueryInspectionParameterRequest;
import com.cet.eem.bll.maintenance.model.param.UpdateInspectionParameterRequest;
import com.cet.eem.bll.maintenance.service.inspection.InspectionParameterService;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.commons.ApiResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName : InspectionParameterController
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-14 14:19
 */

public class InspectionParameterBffController {

    @Autowired
    private InspectionParameterService inspectionParameterService;

    @ApiOperation(value = "新增巡检参数")
    @OperationLog(operationType = EEMOperationLogType.INSPECTOR_PARAM, subType = EnumOperationSubType.ADD, description = "【新增巡检参数】")
    @PostMapping
    public ApiResult<InspectionParameter> addInspectionParameter(@Valid @RequestBody AddInspectionParameterRequest addInspectionParameterRequest) {
        return Result.ok(inspectionParameterService.addInspectionParameter(addInspectionParameterRequest));
    }

    @ApiOperation(value = "查询当前项目巡检参数")
    @PostMapping("/query")
    public ResultWithTotal<List<InspectionParameter>> queryAllInspectionParameter(@Valid @RequestBody QueryInspectionParameterRequest queryInspectionParameterRequest) {
        return inspectionParameterService.queryAllInspectionParameter(queryInspectionParameterRequest);
    }

    /**
     * 删除巡检参数
     *
     * @param ids
     */
    @ApiOperation(value = "删除巡检参数")
    @OperationLog(operationType = EEMOperationLogType.INSPECTOR_SCHEME, subType = EnumOperationSubType.DELETE, description = "【删除巡检参数】")
    @DeleteMapping
    public ApiResult<Void> deleteInspectionParameter(@RequestBody List<Long> ids) {
        inspectionParameterService.deleteInspectionParameter(ids);
        return Result.ok();
    }

    /**
     * 更新巡检参数
     *
     * @param updateInspectionParameterRequest
     * @return
     */
    @ApiOperation(value = "编辑巡检参数")
    @OperationLog(operationType = EEMOperationLogType.INSPECTOR_SCHEME, subType = EnumOperationSubType.UPDATE, description = "【更新巡检参数】")
    @PatchMapping
    public ApiResult<InspectionParameter> updateInspectionParameter(@Valid @RequestBody UpdateInspectionParameterRequest updateInspectionParameterRequest) {
        return Result.ok(inspectionParameterService.updateInspectionParameter(updateInspectionParameterRequest));
    }

    @ApiOperation(value = "根据设备查询巡检参数")
    @PostMapping("/queryByDevice")
    public ApiResult<List<InspectionSchemeDetailVo>> queryInspectionParameterByDevice(@RequestBody @Validated QueryInspectionParameterByDevice queryInspectionParameterByDevice) {
        List<InspectionSchemeDetailVo> inspectionSchemeDetailVos = inspectionParameterService.queryInspectionParameterByDevice(queryInspectionParameterByDevice);
        return Result.ok(inspectionSchemeDetailVos);
    }

    @ApiOperation(value = "根据设备查询巡检方案")
    @PostMapping("/querySchemeByDevice")
    public ApiResult<List<InspectionScheme>> queryInspectionSchemeByDevice(@RequestBody @Validated QueryInspectionParameterByDevice queryInspectionParameterByDevice) {
        List<InspectionScheme> inspectionSchemes = inspectionParameterService.queryInspectionSchemeByDevice(queryInspectionParameterByDevice);
        return Result.ok(inspectionSchemes);
    }

    @ApiOperation(value = "根据巡检方案查询巡检参数")
    @GetMapping("/queryDetailByScheme")
    public ApiResult<List<InspectionSchemeDetail>> queryDetailByScheme(@RequestParam Long inspectionSchemeId,@RequestParam Integer inspectionParameterType) {
        List<InspectionSchemeDetail> inspectionSchemeDetails = inspectionParameterService.queryDetailByScheme(inspectionSchemeId, inspectionParameterType);
        return Result.ok(inspectionSchemeDetails);
    }
}
