package com.cet.eem.fusion.maintenance.core.schedule.event;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.PlanSheet;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : CheckInpectionPlanCommand
 * @Description : 当计划不能继续执行的时候，需要将状态修改为禁用，并且将job挂起，等待编辑再次执行
 * <AUTHOR> zhangh
 * @Date: 2021-05-07 09:48
 */
@Getter
@Setter
public class CheckInspectionPlanIsOverCommand {

    /**
     * 是否能够再次执行
     */
    private boolean mayFireAgain;

    /**
     * 当前计划
     */
    private PlanSheet planSheet;
}
