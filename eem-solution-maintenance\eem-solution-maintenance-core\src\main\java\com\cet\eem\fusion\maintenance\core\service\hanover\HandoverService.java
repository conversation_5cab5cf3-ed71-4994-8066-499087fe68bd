package com.cet.eem.fusion.maintenance.core.service.hanover;

import com.cet.eem.bll.maintenance.model.handover.*;
import com.cet.electric.commons.ApiResult;

import java.io.IOException;
import java.util.List;

/**
 * 交接班
 *
 * <AUTHOR>
 * @date 2020年6月22日 14:22:10
 */
public interface HandoverService {

    /**
     * 交接班列表
     *
     * @param dto userId
     * @return
     * @throws IOException
     */
    ResultWithTotal<List<ShiftingDutyVo>> getHandoverList(ShiftingDutySearchDto dto);

    /**
     * 交接班详情
     *
     * @param id id
     * @return
     * @throws Exception
     */
    ShiftingDutyVo getHandoverDetailInfo(Long id, Long tenantId);

    /**
     * 交班
     *
     * @return
     */
    ShiftingDutyPo handOverToNext(ShiftingDutyUpdateVo shiftingDutyUpdateVo);

    /**
     * 接班
     *
     * @param successionDto
     * @return
     */
    ShiftingDutyPo succession(SuccessionDto successionDto);

    /**
     * 获取交接事项
     *
     * @param tenantId
     * @return
     */
    HandoverMatterVo getHandoverMatter(Long tenantId);

    /**
     * 获取当前值班记录
     *
     * @param tenantId
     * @return
     */
    ShiftingDutyVo queryOnDutyRecord(Long tenantId);

    /**
     * 获取当前用户的工作状态，针对非班组的用户为离线状态，班组用户根据
     *
     * @return 返回
     * @throws Exception
     */
    String getUserWorkingStatus();

}
