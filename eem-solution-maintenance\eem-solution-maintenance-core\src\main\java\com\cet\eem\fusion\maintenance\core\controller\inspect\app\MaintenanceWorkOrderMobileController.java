package com.cet.eem.fusion.maintenance.core.controller.inspect.app;

import com.cet.eem.maintenanceservice.controller.bff.app.MaintenanceWorkOrderMobileBffController;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName : MaintenanceWorkOrderMobileController
 * @Description : 查询维保工单
 * <AUTHOR> jiangzixuan
 * @Date: 2021-06-08 11:05
 */
@Api(value = "/eem/v1/mobile/workorder/maintenance", tags = "APP：工单-维保工单")
@RequestMapping(value = "/eem/v1/mobile/workorder/maintenance")
@RestController
@Validated
public class MaintenanceWorkOrderMobileController extends MaintenanceWorkOrderMobileBffController {

}