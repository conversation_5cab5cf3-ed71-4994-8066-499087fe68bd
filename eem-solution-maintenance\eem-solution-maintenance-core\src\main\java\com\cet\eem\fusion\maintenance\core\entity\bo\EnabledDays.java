package com.cet.eem.fusion.maintenance.core.entity.bo;

import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;
import java.util.List;

/**
 * @ClassName : EnabledDays
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-16 09:11
 */
@Getter
@Setter
public class EnabledDays {

    public static EnabledDays allDaysInstance() {
        EnabledDays enabledDays = new EnabledDays();
        enabledDays.weekDays = Arrays.asList(1, 2, 3, 4, 5, 6, 7);
        return enabledDays;
    }

    private List<Integer> weekDays;

}
