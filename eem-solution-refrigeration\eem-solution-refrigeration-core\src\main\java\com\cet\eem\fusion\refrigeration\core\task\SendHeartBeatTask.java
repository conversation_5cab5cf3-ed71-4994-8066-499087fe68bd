﻿package com.cet.eem.fusion.refrigeration.core.task;

import com.cet.eem.bll.common.task.TaskSchedule;
import com.cet.eem.fusion.refrigeration.core.service.aioptimization.AiPlcControlService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * @ClassName : SendHeartBeatTask
 * @Description : 发送心跳包
 * <AUTHOR> jiangzixuan
 * @Date: 2022-09-07 19:25
 */
@Component
@Slf4j
public class SendHeartBeatTask implements TaskSchedule {
    @Autowired
    AiPlcControlService aiPlcControlService;

    @Scheduled(cron = "${cet.eem.task.energy-saving.control.heartBeat.interval}")
    @Override
    public void execute() throws IOException, InstantiationException, IllegalAccessException {
        aiPlcControlService.sendHeartBeat();
    }
}
