﻿package com.cet.eem.fusion.refrigeration.core.impl;


import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.fusion.refrigeration.core.dao.weather.RefrigeratingSystemDao;
import com.cet.eem.fusion.refrigeration.core.model.config.system.RefrigeratingSystem;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @ClassName : RefrigeratingSystemDaoImpl
 * @Description : 制冷系统
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-17 14:03
 */
@Repository
public class RefrigeratingSystemDaoImpl extends ModelDaoImpl<RefrigeratingSystem> implements RefrigeratingSystemDao {

    @Override
    public List<RefrigeratingSystem> querySystemByIds(List<Long> ids, Long projectId) {
        LambdaQueryWrapper<RefrigeratingSystem> wrapper = LambdaQueryWrapper.of(RefrigeratingSystem.class);
        wrapper.in(RefrigeratingSystem::getRoomId, ids)
                .eq(RefrigeratingSystem::getProjectId, projectId);
        return this.selectList(wrapper);
    }

    @Override
    public List<RefrigeratingSystem> querySystemByRoomId(Long id,Long projectId) {
        LambdaQueryWrapper<RefrigeratingSystem> wrapper = LambdaQueryWrapper.of(RefrigeratingSystem.class);
        wrapper.eq(RefrigeratingSystem::getProjectId, projectId).eq(RefrigeratingSystem::getUseAi,true).eq(RefrigeratingSystem::getRoomId,id);
        return this.selectList(wrapper);
    }

    @Override
    public List<RefrigeratingSystem> querySystem(Long projectId) {
        LambdaQueryWrapper<RefrigeratingSystem> wrapper = LambdaQueryWrapper.of(RefrigeratingSystem.class);
        wrapper.eq(RefrigeratingSystem::getProjectId, projectId).eq(RefrigeratingSystem::getUseAi,true);
        return this.selectList(wrapper);
    }
}
