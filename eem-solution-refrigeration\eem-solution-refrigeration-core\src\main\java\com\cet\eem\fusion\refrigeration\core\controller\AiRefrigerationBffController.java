﻿package com.cet.eem.fusion.refrigeration.core.controller;

import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.fusion.refrigeration.core.model.weather.*;
import com.cet.eem.fusion.refrigeration.core.service.trend.OperationTrendService;
import com.cet.eem.common.model.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName : AIRefrigerationBffController
 * @Description : ai制冷优化
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-20 19:21
 */
@Api(value = "AiRefrigerationController", tags = {"ai预测接口"})
@RestController
@RequestMapping(value = "/eem/v1/aiRefrigeration")
public class AiRefrigerationBffController {
    @Autowired
    OperationTrendService operationTrendService;

    @ApiOperation(value = "冷冻水供回水温度趋势")
    @PostMapping(value = "/freezingWater/trend", produces = "application/json")
    public Result<List<OperationTrendVo>> queryCoolingWaterTrend(
            @RequestBody @ApiParam(name = "queryParam", value = "查询条件", required = true) QueryParam queryParam) throws Exception {

        return Result.ok(operationTrendService.queryCoolingWaterTrend(queryParam));
    }

    @ApiOperation(value = "冷却水供回水温度趋势")
    @PostMapping(value = "/coolingWater/trend", produces = "application/json")
    public Result<List<OperationTrendVo>> queryFreezingWaterTrend(
            @RequestBody @ApiParam(name = "queryParam", value = "查询条件", required = true) QueryParam queryParam) throws Exception {

        return Result.ok(operationTrendService.queryCoolingTowerTrend(queryParam));
    }

    @ApiOperation(value = "制冷系统能效分析")
    @PostMapping(value = "/systemCoolingWater/trend", produces = "application/json")
    public Result<List<OperationTrendVo>> querySystemCoolingWaterTrend(
            @RequestBody @ApiParam(name = "queryParam", value = "查询条件", required = true) QueryParam queryParam) throws Exception {

        return Result.ok(operationTrendService.querySystemCoolingWaterTrend(queryParam,GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "设备组能耗")
    @PostMapping(value = "/equipmentGroup/consumption", produces = "application/json")
    public Result<EquipmentGroupConsumption> queryEquipmentGroupConsumption(
            @RequestBody @ApiParam(name = "queryParam", value = "查询条件", required = true) QueryParam queryParam)  {

        return Result.ok(operationTrendService.queryEquipmentGroupConsumption(queryParam));
    }

    @ApiOperation(value = "室外温度预测")
    @PostMapping(value = "/predict/temp", produces = "application/json")
    public Result<List<AIPredictWeatherVo>> queryAiPredictForTemp(
            @RequestBody @ApiParam(name = "queryParam", value = "查询条件", required = true) QueryParam queryParam) throws Exception {

        return Result.ok(operationTrendService.queryAiPredictForTemp(queryParam,GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "室外湿度预测")
    @PostMapping(value = "/predict/humidity", produces = "application/json")
    public Result<List<AIPredictWeatherVo>> queryAiPredictForHum(
            @RequestBody @ApiParam(name = "queryParam", value = "查询条件", required = true) QueryParam queryParam) throws Exception {

        return Result.ok(operationTrendService.queryAiPredictForHum(queryParam,GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "总冷负荷需求和温度")
    @PostMapping(value = "/predictEnergy/temp", produces = "application/json")
    public Result<List<AIPredictWeatherWithEnergyVo>> queryAiPredictWithEnergyForTemp(
            @RequestBody @ApiParam(name = "queryParam", value = "查询条件", required = true) QueryParam queryParam) throws Exception {

        return Result.ok(operationTrendService.queryAiPredictWithEnergyForTemp(queryParam, GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "总冷负荷需求和湿度")
    @PostMapping(value = "/predictEnergy/humidity", produces = "application/json")
    public Result<List<AIPredictWeatherWithEnergyVo>> queryAiPredictWithEnergyForhum(
            @RequestBody @ApiParam(name = "queryParam", value = "查询条件", required = true) QueryParam queryParam) throws Exception {

        return Result.ok(operationTrendService.queryAiPredictWithEnergyForHum(queryParam,GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "系统总功率预测曲线")
    @PostMapping(value = "/predictEnergy", produces = "application/json")
    public Result<List<AIPredictWeatherVo>> queryAiPredictWithEnergy(
            @RequestBody @ApiParam(name = "queryParam", value = "查询条件", required = true) QueryParam queryParam) throws Exception {

        return Result.ok(operationTrendService.queryAiPredictWithEnergy(queryParam));
    }

    @ApiOperation(value = "系统cop预测曲线")
    @PostMapping(value = "/predict/energyCop", produces = "application/json")
    public Result<List<AIPredictWeatherVo>> queryAiPredictWithEnergyCop(
            @RequestBody @ApiParam(name = "queryParam", value = "查询条件", required = true) QueryParam queryParam) throws Exception {

        return Result.ok(operationTrendService.queryAiPredictWithEnergyCop(queryParam,GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "预测开关机时间")
    @PostMapping(value = "/predict/startOrStopTime", produces = "application/json")
    public Result<List<AiPredictWithTime>> queryAiPredictWithTime(
            @RequestBody @ApiParam(name = "queryParam", value = "查询条件", required = true) QueryParam queryParam) throws Exception {

        return Result.ok(operationTrendService.queryAiPredictWithTime(queryParam));
    }
    @ApiOperation(value = "末端冷负荷需求预测")
    @PostMapping(value = "/predict/endCold", produces = "application/json")
    public Result<List<AIPredictWeatherVo>> queryEndColdTrend(
            @RequestBody @ApiParam(name = "queryParam", value = "查询条件", required = true) QueryParam queryParam) throws IllegalAccessException, InstantiationException {

        return Result.ok(operationTrendService.queryEndColdTrend(queryParam,GlobalInfoUtils.getProjectId()));
    }
    @ApiOperation(value = "冷冻水管损失需求预测")
    @PostMapping(value = "/predict/freezingPipeline/loss", produces = "application/json")
    public Result<List<AIPredictWeatherVo>> queryFreezingPipeline(
            @RequestBody @ApiParam(name = "queryParam", value = "查询条件", required = true) QueryParam queryParam) throws InstantiationException, IllegalAccessException {

        return Result.ok(operationTrendService.queryFreezingPipeline(queryParam,GlobalInfoUtils.getProjectId()));
    }

    @ApiOperation(value = "总冷负荷需求预测")
    @PostMapping(value = "/predict/totalColdLoad", produces = "application/json")
    public Result<List<AIPredictWeatherVo>> queryTotalColdLoadingTrend(
            @RequestBody @ApiParam(name = "queryParam", value = "查询条件", required = true) QueryParam queryParam) throws IllegalAccessException, InstantiationException {

        return Result.ok(operationTrendService.queryTotalColdLoadingTrend(queryParam,GlobalInfoUtils.getProjectId()));
    }

}
