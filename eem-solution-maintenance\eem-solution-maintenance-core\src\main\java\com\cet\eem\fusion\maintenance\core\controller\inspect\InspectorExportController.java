package com.cet.eem.fusion.maintenance.core.controller.inspect;


import com.cet.eem.maintenanceservice.controller.bff.inspect.InspectorParameterExportController;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName : InspectorExportController
 * @Description :
 * <AUTHOR> jiang<PERSON><PERSON>uan
 * @Date: 2021-08-30 13:39
 */
@Api(value = "/eem/v1/inspector/parameter", tags = "定制功能-导出点检数据")
@RequestMapping(value = "/eem/v1/inspector")
@RestController
@Validated
public class InspectorExportController extends InspectorParameterExportController {

}