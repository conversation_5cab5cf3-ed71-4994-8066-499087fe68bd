# Java代码检索工具
python main.py --directory E:\work\project\ai-solution-eem-service\code-compare\old-code\cet-eem-bll-core --exclude-controller

基于CodeBERT和Milvus的Java代码向量检索工具，支持代码相似性搜索和批量分析。

## 功能特性

- **智能向量化**: 使用CodeBERT模型将Java代码转换为768维向量表示
- **高效存储**: 使用Milvus向量数据库存储和管理代码向量
- **精准检索**: 基于向量相似度快速检索相关代码片段
- **批量分析**: 支持目录级别的批量代码相似性分析
- **智能报告**: 自动生成结构化的Markdown分析报告
- **灵活配置**: 支持自定义相似度阈值和匹配数量
- **包名识别**: 自动提取Java包名和类名，提供完整类路径

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置Milvus

确保Milvus服务正常运行，并在 `config/settings.py` 中配置连接信息：

```python
MILVUS_CONFIG = {
    "host": "************",  # 修改为你的Milvus服务地址
    "port": "19530",         # 修改为你的Milvus服务端口
    "alias": "default"
}
```

### 3. 配置项目路径

在 `config/settings.py` 中设置要扫描的Java项目路径：

```python
DEFAULT_PROJECT_PATH = r"E:\work\project\energy-base\cet-eem-bll-base"
```

## 使用方法

### 1. 初始化向量库（首次使用）

```bash
# 使用默认配置初始化
python init_database.py

# 自定义项目路径
python init_database.py --project-path /path/to/your/java/project

# 重新创建向量库（清空现有数据）
python init_database.py --project-path /path/to/your/java/project --recreate

# 详细输出
python init_database.py --project-path /path/to/your/java/project --verbose

# 指定自定义集合名称
python init_database.py --project-path /path/to/your/java/project --collection-name my_project_vectors

# 组合使用
python init_database.py --project-path /path/to/your/java/project --recreate --verbose --collection-name my_custom_collection
```

### 2. 单文件检索

```bash
# 基本检索
python search_file.py --file your_file.java

# 自定义返回数量
python search_file.py --file your_file.java --limit 10

# 指定输出报告文件名
python search_file.py --file your_file.java --output my_report.md

# 不生成报告，只在控制台显示
python search_file.py --file your_file.java --no-report
```

### 3. 批量检索和报告生成（主要功能）

```bash
# 基本批量检索
python main.py --directory /path/to/search/directory

# 自定义相似度阈值和返回数量
python main.py --directory /path/to/search/directory --threshold 0.8 --limit 5

# 指定输出报告文件名
python main.py --directory /path/to/search/directory --output analysis_report.md

# 跳过Controller类型文件的匹配
python main.py --directory /path/to/search/directory --exclude-controller

# 详细输出
python main.py --directory /path/to/search/directory --verbose
```

## 工具参数说明

### init_database.py 参数

| 参数 | 短参数 | 类型 | 默认值 | 说明 |
|------|--------|------|--------|------|
| --project-path | -p | str | 必需参数 | 要扫描的Java项目路径 |
| --recreate | -r | flag | False | 重新创建向量库（删除现有数据），用于清空旧数据重新初始化 |
| --verbose | -v | flag | False | 详细输出 |
| --collection-name | -c | str | 使用配置默认值 | 指定向量库集合名称 |

**--recreate参数使用场景**：
- 项目代码发生重大变更时
- 向量库数据损坏或异常时
- 需要更新向量化模型或配置时
- 首次初始化失败需要重新开始时

**--collection-name参数使用场景**：
- 为不同项目创建独立的向量库集合
- 同一项目的不同版本或分支使用不同集合
- 测试环境与生产环境分离
- 多租户场景下的数据隔离

### search_file.py 参数

| 参数 | 短参数 | 类型 | 默认值 | 说明 |
|------|--------|------|--------|------|
| --file | -f | str | 必需 | 要检索的Java文件路径 |
| --limit | -k | int | 3 | 返回结果数量 |
| --output | -o | str | 自动生成 | 输出报告文件名 |
| --no-report | - | flag | False | 不生成报告文件 |
| --verbose | -v | flag | False | 详细输出 |

### main.py 参数

| 参数 | 短参数 | 类型 | 默认值 | 说明 |
|------|--------|------|--------|------|
| --directory | -d | str | 必需 | 要检索的目录路径 |
| --limit | -k | int | 3 | 每个文件返回的结果数量 |
| --threshold | -t | float | 0.7 | 相似度阈值 |
| --output | -o | str | 自动生成 | 输出报告文件名 |
| --verbose | -v | flag | False | 详细输出 |

## 项目结构

```text
rule-generation/
├── config/                 # 配置包
│   ├── __init__.py
│   └── settings.py        # 所有配置项
├── service/               # 业务服务包
│   ├── __init__.py
│   ├── vector_service.py  # 向量库服务
│   ├── search_service.py  # 搜索服务
│   └── report_service.py  # 报告生成服务
├── utils/                 # 工具包
│   ├── __init__.py
│   ├── code_embedding.py  # CodeBERT代码向量化工具
│   ├── file_utils.py      # Java文件扫描和类名提取工具
│   └── milvus_utils.py    # Milvus向量数据库操作工具
├── output/                # 报告输出目录（自动创建）
├── init_database.py       # 向量库初始化脚本
├── search_file.py         # 单文件检索脚本
├── main.py                # 批量检索和报告生成主程序
├── requirements.txt       # 依赖包列表
└── README.md              # 使用说明文档
```

## 报告格式说明

批量检索会生成结构化的Markdown报告，包含以下内容：

### 统计摘要
- 总匹配数：所有检索到的匹配结果总数
- 高相似度匹配：超过阈值的匹配数量
- 高相似度比例：高相似度匹配占总匹配的百分比

### 详细结果表格

每个查询类都会生成一个表格，包含以下字段：

| 字段 | 说明 |
|------|------|
| **匹配类名** | 找到的相似类的类名 |
| **完整类路径** | 包名.类名的完整类路径 |
| **相似度** | 0-1之间的相似度分数，越接近1越相似 |
| **超过阈值** | ✅表示超过设定阈值，❌表示未超过 |

### 示例报告格式

```markdown
#### 1. UserService

**文件路径**: `src/main/java/com/example/UserService.java`

| 匹配类名 | 完整类路径 | 相似度 | 超过阈值 |
|---------|-----------|--------|----------|
| CustomerService | `com.example.service.CustomerService` | 0.833 | ✅ |
| OrderService | `com.example.service.OrderService` | 0.714 | ✅ |
| ProductService | `com.example.service.ProductService` | 0.625 | ❌ |
```

## 相似度阈值设置建议

| 阈值范围 | 适用场景 | 说明 |
|---------|---------|------|
| 0.9-1.0 | 查找几乎相同的代码 | 极高相似度，可能是重复代码 |
| 0.8-0.9 | 查找高度相似的代码 | 高相似度，功能相近 |
| 0.7-0.8 | 查找相关的代码 | 中等相似度，设计模式相似 |
| 0.6-0.7 | 查找可能相关的代码 | 较低相似度，可能有参考价值 |

## 核心功能说明

### 类名自动提取

工具会自动从Java文件中提取类名，支持以下格式：
- `public class ClassName`
- `abstract class ClassName`
- `class ClassName`

如果无法提取类名，将使用文件名（去掉.java扩展名）作为类名。

### 向量化存储

每个Java文件会被存储为包含以下信息的向量记录：
- **class_name**: 提取的Java类名
- **code_content**: Java代码内容（大文件会被智能截断）
- **code_vector**: 768维的CodeBERT向量表示

### 大文件处理策略

- **文件大小限制**: 超过2MB的文件会被自动跳过
- **智能截断**: 超过500K字符的文件会在方法或类边界智能截断
- **大文件检测**: 自动识别和统计大文件（>500KB）
- **预分析**: 初始化前分析文件大小分布
- **保守截断**: 预留空间确保截断后不超过字段限制

### 相似度计算

使用L2距离（欧氏距离）计算向量相似度：
- 距离越小表示越相似
- 相似度分数 = 1 / (1 + 距离)，范围在0-1之间
- 分数越接近1表示越相似

## 使用注意事项

1. **环境要求**: Python 3.8+，确保Milvus服务正常运行
2. **首次使用**: 必须先调用 `initialize_database()` 初始化向量库
3. **模型下载**: 首次运行会自动下载CodeBERT模型（约500MB）
4. **文件编码**: 自动支持UTF-8和GBK编码的Java文件
5. **内存使用**: 处理大量文件时注意内存使用情况

## 配置参数说明

### 主要配置项

```python
# 默认项目路径
DEFAULT_PROJECT_PATH = r"E:\work\project\energy-base\cet-eem-bll-base"

# 默认返回结果数量
SEARCH_CONFIG = {
    "default_limit": 5
}

# Milvus连接配置
MILVUS_CONFIG = {
    "host": "************",
    "port": "19530"
}
```

### 性能调优

- **nlist**: 索引聚类数量，建议为数据量的平方根
- **nprobe**: 搜索时访问的聚类数，影响精度和速度平衡
- **max_content_length**: 代码内容最大长度限制（默认10000字符）

## 常见问题

**Q: 出现"exceeds max length"错误怎么办？**
A: 这是因为Java文件内容超过了字段长度限制。解决方案：
1. 使用 `initialize_database(recreate=True)` 重新创建集合
2. 或者在 `config.py` 中增加 `max_content_length` 值

**Q: 检索结果不准确怎么办？**
A: 可以尝试增加返回结果数量k，或者重新训练向量库。

**Q: 如何处理大型项目？**
A: 建议分批处理，或者增加nlist参数值以提高索引效率。工具会自动检测和处理大文件。

**Q: 支持其他编程语言吗？**
A: 目前专门针对Java优化，其他语言需要修改文件扫描和类名提取逻辑。

**Q: 如何检查集合状态？**
A: 使用 `check_collection_status()` 函数检查集合是否存在及记录数量。
