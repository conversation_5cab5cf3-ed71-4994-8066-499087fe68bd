﻿package com.cet.eem.fusion.refrigeration.core.entity.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @ClassName : SwitchLimit
 * @Description : 板换切换限制
 * <AUTHOR> jiangzixuan
 * @Date: 2023-06-08 09:26
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SwitchLimitBO {
    @JsonProperty("temp_limit")
    private Double tempLimit;
    @JsonProperty("cooling_load_demand_limit")
    private Double coolingLoadDemandLimit;

}
