{"file_extensions": [".java", ".properties"], "exclude_directories": ["target", "build", "dist", ".git", ".idea", ".vscode", "node_modules", "logs", "temp", "tmp", ".mvn", ".gradle"], "exclude_files": ["*.class", "*.jar", "*.war", "*.ear", "*.zip", "*.tar.gz", "*.log", "*.md"], "keywords": {"GlobalInfoUtils_issues": {"severity": "high", "case_sensitive": true, "cross_line": true, "description": "GlobalInfoUtils工具类修改", "suggest": "GlobalInfoUtils.getProjectId()需要修改为getTenantId()，GlobalInfoUtils.getHttpResponse()需要在Controller层显示声明", "problemtype": "类问题", "keywords": ["GlobalInfoUtils.getHttpResponse()", "GlobalInfoUtils.getProjectId()"]}, "BaseEntity_issues": {"severity": "high", "case_sensitive": true, "cross_line": true, "description": "BaseEntity工具类修改", "suggest": "请使用新的基础实体类替代BaseEntity", "problemtype": "类问题", "keywords": ["extends BaseEntity"]}, "resource_issues": {"severity": "high", "case_sensitive": true, "cross_line": true, "description": "@Resource修改成@Autowired", "suggest": "请使用@Autowired替代@Resource注解", "problemtype": "多租户", "keywords": ["@Resource"]}, "QuantityDataBatchSearchVo_issues": {"severity": "high", "case_sensitive": true, "cross_line": true, "description": "QuantityDataBatchSearchVo废弃", "suggest": "QuantityDataBatchSearchVo废弃", "problemtype": "类问题", "keywords": ["QuantityDataBatchSearchVo"]}, "DateUtil_issues": {"severity": "high", "case_sensitive": true, "cross_line": true, "description": "DateUtil不推荐使用", "suggest": "请使用TimeUtil重构", "problemtype": "类问题", "keywords": ["DateUtil"]}, "PecEventExtendVo_issues": {"severity": "high", "case_sensitive": true, "cross_line": true, "description": "PecEventExtendVo废弃", "suggest": "", "problemtype": "类问题", "keywords": ["PecEventExtendVo"]}, "ConnectionSearchVo_issues": {"severity": "high", "case_sensitive": true, "cross_line": true, "description": "ConnectionSearchVo废弃", "suggest": "", "problemtype": "类问题", "keywords": ["ConnectionSearchVo"]}, "PecEventCountVo_issues": {"severity": "high", "case_sensitive": true, "cross_line": true, "description": "PecEventCountVo废弃", "suggest": "", "problemtype": "类问题", "keywords": ["PecEventCountVo"]}, "JSONUtil_issues": {"severity": "high", "case_sensitive": true, "cross_line": true, "description": "JSONUtil废弃", "suggest": "JSONUtil废弃，考虑JsonTransferUtils重构", "problemtype": "类问题", "keywords": ["JSONUtil"]}, "CommonUtils_issues": {"severity": "high", "case_sensitive": true, "cross_line": true, "description": "CommonUtils部分方法废弃", "suggest": "CommonUtils部分方法废弃", "problemtype": "类问题", "keywords": ["CommonUtils.sort"]}, "CommonUtilsCalcDouble_issues": {"severity": "high", "case_sensitive": true, "cross_line": true, "description": "CommonUtils部分方法废弃", "suggest": "CommonUtils.calcDouble方法废弃，考虑使用NumberCalcUtils.calcDouble替换", "problemtype": "类问题", "keywords": ["CommonUtils.calcDouble"]}, "CommonUtils_parseInteger_issues": {"severity": "high", "case_sensitive": true, "cross_line": true, "description": "CommonUtils部分方法废弃", "suggest": "CommonUtils.parseInteger方法废弃，考虑使用NumberUtils.parseInteger替换", "problemtype": "类问题", "keywords": ["CommonUtils.parseInteger"]}, "TimeCoordinate_issues": {"severity": "high", "case_sensitive": true, "cross_line": true, "description": "TimeCoordinate废弃", "suggest": "TimeCoordinate废弃，使用TimeUtil重构", "problemtype": "类问题", "keywords": ["TimeCoordinate"]}, "DataCompareUtils_issues": {"severity": "high", "case_sensitive": true, "cross_line": true, "description": "DataCompareUtils部分方法废弃", "suggest": "DataCompareUtils部分方法废弃，使用TimeUtil重构", "problemtype": "类问题", "keywords": ["DataCompareUtils.generateCompareDataList"]}, "DateUtils_issues": {"severity": "high", "case_sensitive": true, "cross_line": true, "description": "DateUtils.formatDate方法废弃", "suggest": "DateUtils.formatDate方法废弃，使用TimeUtil重构", "problemtype": "类问题", "keywords": ["DateUtils.formatDate"]}, "EnergyResult_issues": {"severity": "high", "case_sensitive": true, "cross_line": true, "description": "EnergyResult废弃", "suggest": "EnergyResult废弃，需要利用EnergyTbHbResultVO进行重构，缺少了自定义单位，需要额外的sevice查询方法", "problemtype": "类问题", "keywords": ["EnergyResult"]}, "QueryConditionBuilder_issues": {"severity": "high", "case_sensitive": true, "cross_line": true, "description": "QueryConditionBuilder废弃", "suggest": "QueryConditionBuilder废弃，请使用ParentQueryConditionBuilder重构", "problemtype": "类问题", "keywords": ["QueryConditionBuilder"]}, "project_id_issues": {"severity": "high", "case_sensitive": true, "cross_line": true, "description": "project_id需要重构", "suggest": "project_id需要重构为tenant_id", "problemtype": "多租户", "keywords": ["project_id"]}}}