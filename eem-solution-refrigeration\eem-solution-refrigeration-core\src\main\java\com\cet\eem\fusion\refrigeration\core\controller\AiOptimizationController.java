﻿package com.cet.eem.fusion.refrigeration.core.controller;

import com.cet.eem.fusion.refrigeration.core.model.aioptimization.*;
import com.cet.eem.fusion.refrigeration.core.service.aioptimization.AiOptimizationService;
import com.cet.eem.common.model.Result;
import com.cet.eem.common.model.ResultWithTotal;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * @ClassName : AiOptimizationController
 * @Description : ai优化和策略界面接口
 * <AUTHOR> jiangzixuan
 * @Date: 2022-04-15 10:47
 */
@Api(value = "AiOptimizationController", tags = {"ai优化和策略查询接口"})
@RestController
@RequestMapping(value = "/eem/v1/aiOptimization")
public class AiOptimizationController {
    @Autowired
    AiOptimizationService aiOptimizationService;
    @ApiOperation(value = "气象与负荷预测")
    @PostMapping(value = "/weatherAndLoadPredict", produces = "application/json")
    public Result<List<WeatherAndLoadPredictVo>> queryWeatherAndLoadPredictVo(@RequestParam Long roomId) {
        return Result.ok(aiOptimizationService.queryWeatherAndLoadPredictVo(roomId));
    }
    @ApiOperation(value = "冷站和末端水温")
    @PostMapping(value = "/waterTemp", produces = "application/json")
    public Result<WaterTempVo> queryWaterTempVo(@RequestParam Long roomId) {
        return Result.ok(aiOptimizationService.queryWaterTempVo(roomId));
    }

    @ApiOperation(value = "设备当前状态查询")
    @PostMapping(value = "/deviceStatus", produces = "application/json")
    public Result<List<DeviceCurrentStatus>> queryDeviceCurrentStatus(@RequestParam Long roomId) {
        return Result.ok(aiOptimizationService.queryDeviceCurrentStatus(roomId));
    }
    @ApiOperation(value = "冷水主主机工作范围查询")
    @PostMapping(value = "/workRange", produces = "application/json")
    public Result<WorkSectionListVo> queryRefrigeratorWorkingRange(@RequestParam Long roomId) {
        return Result.ok(aiOptimizationService.queryDeviceWorkSection(roomId));
    }

    @ApiOperation(value = "节能效果")
    @PostMapping(value = "/energySavingEffect", produces = "application/json")
    public Result<EnergySavingEffectVo> queryEnergySavingEffectVo(@RequestParam Long roomId) {
        return Result.ok(aiOptimizationService.queryEnergySavingEffectVo(roomId));
    }
    @ApiOperation(value = "优化策略建议查询")
    @PostMapping(value = "/strategyAdvice", produces = "application/json")
    public Result<StrategyAdviceVo> queryStrategyAdvice(@RequestParam Long roomId) {
        return Result.ok(aiOptimizationService.queryStrategyAdvice(roomId));
    }
    @ApiOperation(value = "策略查询")
    @PostMapping(value = "/strategyType", produces = "application/json")
    public ResultWithTotal<List<StrategyInfoVo>> queryStrategyAdvice(@RequestBody StrategyQueryParam param) {
        return aiOptimizationService.queryStrategyInfo(param);
    }
}
