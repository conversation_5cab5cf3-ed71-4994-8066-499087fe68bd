package com.cet.eem.fusion.maintenance.core.entity.sign;

import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.maintenance.core.entity.bo.SignInPointWithSupLayer;
import com.cet.eem.fusion.maintenance.core.entity.po.SignInGroup;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : QueryCurrentProjectSignInGroupResult
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-08 14:29
 */
@Getter
@Setter
public class QueryCurrentProjectSignInGroupResult extends SignInGroup {

    @JsonProperty(ModelLabelDef.REGISTRATION_POINT + "_model")
    private List<SignInPointWithSupLayer> signInPointList;
}
