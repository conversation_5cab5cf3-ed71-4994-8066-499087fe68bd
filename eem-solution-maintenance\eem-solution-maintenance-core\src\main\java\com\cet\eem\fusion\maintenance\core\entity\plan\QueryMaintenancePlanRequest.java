package com.cet.eem.fusion.maintenance.core.entity.plan;

import com.cet.eem.fusion.common.model.Page;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * @ClassName : QueryMaintenancePlanRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-21 17:17
 */
@Getter
@Setter
@ApiModel(value = "QueryMaintenancePlanRequest", description = "查询维保计划")
public class QueryMaintenancePlanRequest {

    /**
     * 租户id
     */
    @NotNull(message = "租户id不能为空")
    private Long tenantId;

    /**
     * 巡检计划名称
     */
    private String name;

    /**
     * 隐藏结束方案
     */
    private boolean hide;

    /**
     * 班组id
     */
    @JsonProperty("teamid")
    private Long teamId;

    /**
     * 任务等级
     */
    @JsonProperty("worksheettasklevel")
    private Integer worksheetTaskLevel;

    /**
     * 分页参数
     */
    private Page page;
    /**
     * 是否启用
     */
    private Boolean enabled;
}
