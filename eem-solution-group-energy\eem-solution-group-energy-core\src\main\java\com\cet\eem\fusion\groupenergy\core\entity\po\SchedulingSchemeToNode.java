package com.cet.eem.fusion.groupenergy.core.entity.po;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.solution.common.def.common.label.ModelLabelDef;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 排班方案关联节点
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@ModelLabel(ModelLabelDef.SCHEDULING_SCHEME_TO_NODE)
public class SchedulingSchemeToNode extends EntityWithName {

    @ApiModelProperty("排班方案表")
    @JsonProperty("schedulingschemeid")
    private Long schedulingSchemeId;

    @ApiModelProperty("节点类型")
    @JsonProperty("objectlabel")
    private String objectLabel;

    @ApiModelProperty("节点id")
    @JsonProperty("objectid")
    private Long objectId;

    public SchedulingSchemeToNode() {
        this.modelLabel= ModelLabelDef.SCHEDULING_SCHEME_TO_NODE;
    }
}
