package com.cet.eem.solution.common.entity.dto.cetml;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ProProgram {

    @ApiModelProperty("位移")
    private List<String> displacement;

    @JsonProperty("disp_load")
    @ApiModelProperty("负荷")
    private List<String> dispLoad;

    @ApiModelProperty("频率")
    private List<Double> frequency;

    @JsonProperty("pump_r")
    @ApiModelProperty("泵径")
    private List<Double> pump;

    @ApiModelProperty("统计数据的周期, 单位为分钟")
    private Integer period = 60;

    public ProProgram(List<String> displacement, List<String> dispLoad, List<Double> frequency, List<Double> pump) {
        this.displacement = displacement;
        this.dispLoad = dispLoad;
        this.frequency = frequency;
        this.pump = pump;
    }
}
