# 背景描述
融合前，模型层面设计并没有考虑上层的项目配置和租户的配置隔离，在新的框架中，部分模型需要针对项目和租户做不同对象的隔离。第一层是租户，第二层是项目。
请注意：你只用输出md文件来告诉我详细的方案，不要自己修改代码，输出位置在：fusion-custom-scanning/output

# 注意事项

1、请认真分析逻辑，并基于相关的业务场景做分析，评估修改的范围，不要修改不必要的接口
2、修改的方案需要包含整个调用链路的修改，从Controller到Service，再到Dao层，都要做修改
3、输出相关方案清单为md文件，人工确认修改后再执行
4、输出时请附带上具体的修改逻辑，修改的地方，修改之前和修改之后
5、针对ServiceImpl和DaoImpl也要做相关的修改输出，不要省略任何步骤

# 租户隔离机制适配步骤

## 第一步：工程分析

目的：针对输入分析插件内对应的实体模型，并根据实体模型，分析Controller层各个接口的作用，了解基本的业务逻辑关系，方便后续针对业务的理解
具体步骤：你需要基于输入的文件夹，获取对应的模块内容，然后基于下面的模块变更模型清单，规划你需要修改的内容。

| 插件名 | 模块        | 变更模型                                                                                                           |
|-----|-----------|----------------------------------------------------------------------------------------------------------------|
|eem-solution-batch-energy| 批次能耗插件    | 批次属性模板batchtemplate、产品大类配置：productcategory、工序类型：proceduretype、工序阈值方案：procedurethresholdscheme、清洗方案：cleanscheme |
|eem-solution-group-energy| 班组能耗插件    |排班方案表：schedulingscheme|
|eem-solution-transformer| 变压器能效分析插件 ||



## 第二步：根据模型找到实体类，实体类修改，添加对应的字段

目的：部分的配置模型，之前并没有考虑这么复杂的隔离机制。需要针对相关模型做修改。

针对你需要做隔离的模型(大多数情况下，整个模型位于po下面，并且类名的命名规则是按照模型名称的驼峰命名规则)，需要新增三个字段，其中tenantId是租户id，用于租户级别的隔离，rootnode是用于存放隔离级别的节点，rootnodelabel是节点的类型，rootnodeid是节点的id。他需要根据前端传入。

```java
    @JsonProperty(ColumnDef.TENANT_ID)
    private Long tenantId;
    @JsonProperty(ColumnDef.ROOT_NODE_ID)
    private Long rootNodeId;
    @JsonProperty(ColumnDef.ROOT_NODE_LABEL)
    private String rootNodeLabel;
```

## 第三步：找到实体类对应的新增修改接口，修改入参，从Controller到Dao层

目的：针对模型修改之后，对应的模型的新增和修改的接口需要同步修改，这些字段信息需要前端传入，

Controller层的修改内容
新增修改接口修改
1、添加tenantId参数，你需要在对应的接口入参中添加对应参数
```java
@NotNull @RequestHeader(ColumnDef.TENANT_HEADER) Long tenantId,
```
2、针对rootnodeid和rootnodelabel，你需要在接口原本的dto入参实体类中中添加对应的参数
```java
    @ApiModelProperty("根节点id")
    private Long rootNodeId;

    @ApiModelProperty("根节点label")
    private String rootNodeLabel;
```

查询接口修改
1、基于原有的查询接口新增根节点查询入参，并添加非空校验
```java
    @ApiModelProperty("根节点")
    @NotNull(message = "根节点不能为空")
    private BaseEntity rootNode;
```

Dao层修改
1、确认写入接口的入参在调用链中已经很好的传递了需要的三个参数
2、查询功能的数据交互方法，你需要根据
```java
builder.eq(ColumnDef.ROOT_NODE_LABEL, node.getModelLabel())
.eq(ColumnDef.ROOT_NODE_ID, node.getId())
if (tenantId != null) {
    builder.where(ColumnDef.TENANT_ID, ConditionBlock.OPERATOR_EQ, tenantId);
}
```