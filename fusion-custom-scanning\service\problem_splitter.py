#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题分割器 - 将XML问题报告按文件分组
只保留 entry_point TYPE="file" 的问题，并按文件分别输出
"""

import xml.etree.ElementTree as ET
from collections import defaultdict
import os
import sys


class ProblemSplitter:
    """问题分割器类"""

    def __init__(self, input_file="QodanaJavaSanity.xml", output_dir="output/split_problems"):
        """
        初始化问题分割器

        Args:
            input_file: 输入的XML文件路径
            output_dir: 输出目录路径
        """
        self.input_file = input_file
        self.output_dir = output_dir

    def parse_problems_xml(self, xml_content):
        """解析XML内容并提取问题信息"""
        try:
            root = ET.fromstring(xml_content)
        except ET.ParseError as e:
            print(f"XML解析错误: {e}")
            return {}

        # 按文件分组存储问题
        problems_by_file = defaultdict(list)

        # 查找所有problem元素
        for problem in root.findall('.//problem'):
            # 检查是否有entry_point且TYPE="file"
#             entry_point = problem.find('entry_point')
#             if entry_point is None or entry_point.get('TYPE') != 'file':
#                 continue

            # 提取文件路径
            file_elem = problem.find('file')
            if file_elem is None:
                continue

            file_path = file_elem.text
            if not file_path:
                continue

            # 过滤条件：文件路径必须包含 src/main/java 且以 .java 结尾
            if 'src/main/java' not in file_path or not file_path.endswith('.java'):
                continue

            # 将问题添加到对应文件的列表中
            problems_by_file[file_path].append(problem)

        return problems_by_file

    def create_output_xml(self, problems, original_root_tag='problems'):
        """为单个文件的问题创建XML内容"""
        root = ET.Element(original_root_tag)

        for problem in problems:
            root.append(problem)

        return ET.tostring(root, encoding='unicode', xml_declaration=True)

    def sanitize_filename(self, file_path):
        """将文件路径转换为安全的文件名"""
        # 移除file://前缀和$PROJECT_DIR$
        clean_path = file_path.replace('file://', '').replace('$PROJECT_DIR$/', '')

        # 提取类名（最后一个.之前的部分）
        if '/' in clean_path:
            class_name = clean_path.split('/')[-1]
            if '.' in class_name:
                class_name = '.'.join(class_name.split('.')[:-1])  # 移除.java扩展名
        else:
            class_name = clean_path

        return f"{class_name}_problems.xml"

    def split_problems(self):
        """执行问题分割"""
        if not os.path.exists(self.input_file):
            print(f"错误: 找不到输入文件 {self.input_file}")
            return False

        try:
            with open(self.input_file, 'r', encoding='utf-8') as f:
                xml_content = f.read()
        except Exception as e:
            print(f"读取文件错误: {e}")
            return False

        # 解析问题
        problems_by_file = self.parse_problems_xml(xml_content)

        if not problems_by_file:
            print("没有找到符合条件的问题（entry_point TYPE='file'）")
            return False

        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)

        # 为每个文件生成单独的XML文件
        generated_files = []
        for file_path, problems in problems_by_file.items():
            output_filename = self.sanitize_filename(file_path)
            output_path = os.path.join(self.output_dir, output_filename)

            xml_content = self.create_output_xml(problems)

            try:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(xml_content)
                generated_files.append(output_path)
                print(f"已生成: {output_path} (包含 {len(problems)} 个问题)")
            except Exception as e:
                print(f"写入文件 {output_path} 时出错: {e}")

        print(f"\n总共处理了 {len(problems_by_file)} 个文件的问题")
        print(f"输出文件保存在 {self.output_dir} 目录中")

        return generated_files


def main():
    """主函数"""
    # 读取输入XML文件
    input_file = 'QodanaJavaSanity.xml'  # 默认输入文件

    if len(sys.argv) > 1:
        input_file = sys.argv[1]

    # 创建问题分割器并执行分割
    splitter = ProblemSplitter(input_file)
    generated_files = splitter.split_problems()

    if generated_files:
        print(f"✅ 问题分割完成，生成了 {len(generated_files)} 个文件")
    else:
        print("❌ 问题分割失败")


if __name__ == '__main__':
    main()