package com.cet.eem.fusion.maintenance.core.entity.po;

import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : MaintenanceItem
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-05-10 13:50
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.MAINTENANCE_ITEM)
public class MaintenanceItem extends EntityWithName {
    /**
     * 维保内容
     */
    private String content;

    /**
     * 零部件id
     */
    @JsonProperty("sparepartid")

    private Long sparePartId;

    /**
     * 零部件数量
     */
    @JsonProperty("number")
    private Double number;

    /**
     * 维保方式
     */
    @JsonProperty("maintenancetype")
    private Long maintenanceType;

    /**
     * 排序
     */
    private Integer sort;

    public MaintenanceItem() {
        this.modelLabel = ModelLabelDef.MAINTENANCE_ITEM;
    }
}
