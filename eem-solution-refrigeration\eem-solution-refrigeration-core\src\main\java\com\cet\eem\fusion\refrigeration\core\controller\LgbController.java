﻿package com.cet.eem.fusion.refrigeration.core.controller;

import com.cet.eem.bll.common.util.GlobalInfoUtils;
import com.cet.eem.fusion.refrigeration.core.model.dataentryquery.DataLogDataWrite;
import com.cet.eem.fusion.refrigeration.core.model.dataentryquery.EndColdDataEntry;
import com.cet.eem.fusion.refrigeration.core.model.dataentryquery.TotalSystemPowerEntry;
import com.cet.eem.fusion.refrigeration.core.model.weather.ForecastBasicWeatherDataVo;
import com.cet.eem.fusion.refrigeration.core.service.predict.LgbModelPredictService;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName : LgbController
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-08-26 09:18
 */
@Api(value = "LgbController", tags = {"算法调试接口"})
@RestController
@RequestMapping(value = "/eem/v1/lgb")
public class LgbController {
    @Autowired
    LgbModelPredictService lgbModelPredictService;

    @ApiOperation(value = "测试末端")
    @PostMapping(value = "/endCold", produces = "application/json")
    public Result<DataLogDataWrite> checkEndCold(@RequestBody Result<List<EndColdDataEntry>> entries) {
        return Result.ok(lgbModelPredictService.getEndColdPredictData(entries));
    }


    @ApiOperation(value = "测试功率")
    @PostMapping(value = "/power", produces = "application/json")
    public Result<DataLogDataWrite> checkPower(@RequestBody Result<List<TotalSystemPowerEntry>> entries) {
        return Result.ok(lgbModelPredictService.getTotalSystemPowerPredict(entries));
    }

    @ApiOperation(value = "测试湿度")
    @PostMapping(value = "/hum", produces = "application/json")
    public Result<DataLogDataWrite> checkHum(@RequestBody Result<List<ForecastBasicWeatherDataVo>> ok) {
        return Result.ok(lgbModelPredictService.getWeatherPredictData(ok));
    }
}
