package com.cet.eem.fusion.maintenance.core.entity.plan;

import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.maintenance.core.entity.po.DevicePlanRelationship;
import com.cet.eem.fusion.maintenance.core.entity.po.PlanSheet;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : PlanSheetBaseVo
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-05-14 17:37
 */
@Getter
@Setter
public class PlanSheetBaseVo extends PlanSheet {

    /**
     * 下次执行时间
     */
    private Long nextFireTime;

    /**
     * 用户组名
     */
    private String teamName;

    /**
     * 设备列表
     */
    @JsonProperty(ModelLabelDef.DEVICE_PLAN_RELATIONSHIP + "_model")
    private List<DevicePlanRelationship> devicePlanRelationshipList;
}
