﻿package com.cet.eem.fusion.refrigeration.core.task;

import com.cet.eem.bll.common.task.TaskSchedule;
import com.cet.eem.fusion.refrigeration.core.service.task.ColdPredictDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * @ClassName : ColdLossPredictTask
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-08-09 11:03
 */
@Component
@Slf4j
public class ColdLossPredictTask implements TaskSchedule {
    @Autowired
    ColdPredictDataService coldPredictDataService;
    @Scheduled(cron = "${cet.eem.task.energy-saving.pipeLoss.interval}")
    @Override
    public void execute() throws IOException, InstantiationException, IllegalAccessException {
        coldPredictDataService.savePipeLineLossPredictData();
    }
}
