package com.cet.eem.solution.common.def.common.constant;

/**
 * @ClassName : CommoField
 * <AUTHOR> yangy
 * @Date: 2021-08-13 10:07
 */
public class CommonField {
    private CommonField() {
        throw new IllegalStateException("CommonField class");
    }

    public static final Integer ZERO = 0;
    public static final String EXCEPTION = "exception：";
    public static final String S_J_L_R = "数据录入";
    public static final String SJ_FILENAME = "数据录入导出.xlsx";
    public static final String DEVICEMANAGEMENT_EXPORT = "设备管理导入导出模板.xlsx";
    public static final String MAINTAIN_DATA_EXPORT = "物料数据导入导出模板.xlsx";
    public static final String MAINTENANCEINFO_EXPORT = "维保信息导出.xlsx";
    public static final String WELL_WASHING_MANAG_TEMPLATE = "洗井管理导入导出模板.xlsx";
    public static final String WELL_WASHING_PLATE = "洗井计划数据.xlsx";

    public static final String PRODUCT_CONNECT_TEMPLATE = "生产设备关联导入模板.xlsx";
    public static final String SHEET_PRODUCT_CONNECT_CONNECT = "生产设备关联";
    public static final String SHEET_GATHER_DEVICE_NODE_INFO = "采集设备信息";
    public static final String HEADER_PRODUCT_CONNECT_MANAEGE_NODE_INFO = "管理层级节点";
    public static final String HEADER_PRODUCT_CONNECT_PECCORE_NODE_INFO = "采集设备节点-测点类型-回路号";
    public static final String HEADER_PRODUCT_CONNECT_PRODUCTION_TYPE = "生产数据类型";
    public static final String HEADER_PRODUCT_CONNECT_PRODUCT_TYPE = "生产类型";
    public static final String PRODUCT_SUPPLEMENT_TEMPLATE = "生产补充数据录入模板.xlsx";
    public static final String HEADER_PRODUCT_SUPPLEMENT_AGGREGATIONCCYCLE = "聚合周期";
    public static final String HEADER_PRODUCT_SUPPLEMENT_LOGTIME = "聚合时间(格式如：2024/08/21)";
    public static final String HEADER_PRODUCT_SUPPLEMENT_DOCKINGTYPE = "修正类型";
    public static final String HEADER_PRODUCT_SUPPLEMENT_VALUE = "修正值";
    public static final String SHEET_PRODUCT_SUPPLEMENT_DATA = "生产补充数据";
    public static final String FILE_PRODUCT_SUPPLEMENT_DATA = "生产补充数据.xlsx";
}