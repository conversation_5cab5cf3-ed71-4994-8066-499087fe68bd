﻿package com.cet.eem.fusion.refrigeration.core.task;

import com.cet.eem.bll.common.task.TaskSchedule;
import com.cet.eem.fusion.refrigeration.core.service.task.ColdLoadPredictCetMlService;
import com.cet.eem.fusion.refrigeration.core.service.task.ColdPredictDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * @ClassName : EndColdPredictTask
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-08-12 09:38
 */
@Component
@Slf4j
public class EndColdPredictTask implements TaskSchedule {
    @Autowired
    ColdPredictDataService coldPredictDataService;
    @Autowired
    ColdLoadPredictCetMlService coldLoadPredictCetMlService;
    @Scheduled(cron = "${cet.eem.task.energy-saving.endCold.interval}")
    @Override
    public void execute() throws IOException, InstantiationException, IllegalAccessException {
        coldLoadPredictCetMlService.saveColdPredictData();
    }
}
