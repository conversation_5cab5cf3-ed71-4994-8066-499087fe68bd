package com.cet.eem.fusion.maintenance.core.dao.repair;

import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionCountSearchDto;
import com.cet.eem.bll.maintenance.model.workorder.inspection.InspectionWorkOrderDto;
import com.cet.eem.bll.maintenance.model.workorder.repair.RepairByNodeSearchVo;
import com.cet.eem.bll.maintenance.model.workorder.repair.RepairSearchVo;
import com.cet.electric.commons.ApiResult;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/5/21
 */
public interface RepairWorkOrderDao {
    /**
     * 查询异常工单数量
     *
     * @param dto
     * @return
     */
    Integer queryAbnormalWorkOrderCount(InspectionCountSearchDto dto);

    /**
     * 查询工单
     *
     * @param dto
     * @param userId
     * @return
     */
    <T extends InspectionWorkOrderDto> ResultWithTotal<List<T>> queryWorkOrder(RepairSearchVo dto, Long userId, Class<T> clazz);

    /**
     * 根据节点查询工单
     *
     * @param repairByNodeSearchVo
     * @param clazz
     * @param <T>
     * @return
     */
    <T extends InspectionWorkOrderDto> ResultWithTotal<List<T>> queryWorkOrderByNode(RepairByNodeSearchVo repairByNodeSearchVo, Class<T> clazz);

    /**
     * 根据工单状态统计数量
     *
     * @param dto
     * @return
     */
    List<Map<String, Object>> queryWorkOrderCountByStatus(InspectionCountSearchDto dto);

    /**
     * 根据工单状态查询工单
     *
     * @param st
     * @param et
     * @param taskType
     * @param workOrderStatus
     * @param teamId
     * @param userId
     * @return
     */
    ResultWithTotal<List<Map<String, Object>>> queryWorkOrderByWorkStatus(LocalDateTime st, LocalDateTime et, Integer taskType,
                                                                          List<Integer> workOrderStatus, Long teamId, Long userId, boolean isInspectUser);

    /**
     * 根据工单状态查询工单
     *
     * @param workOrderStatus
     * @param taskType
     * @param teamId
     * @param et
     * @param userId
     * @param clazz
     * @param <T>
     * @return
     */
    <T extends InspectionWorkOrderDto> List<T> queryWorkOrderByWorkStatus(List<Integer> workOrderStatus, Integer taskType,
                                                                          Long teamId, LocalDateTime et, Long userId, boolean isInspectUser, Class<T> clazz);

    <T extends InspectionWorkOrderDto> List<T> queryRuntimeWorkOrderByWorkStatus(List<Integer> workOrderStatus, Integer taskType,
                                                                          Long teamId, LocalDateTime et, Long userId, boolean isInspectUser, Class<T> clazz);
}
