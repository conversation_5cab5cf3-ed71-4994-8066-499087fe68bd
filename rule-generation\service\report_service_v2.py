"""
报告服务 V2
重新设计的报告生成服务，支持分离高相似度和无匹配结果
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path

from config.settings import REPORT_CONFIG
from service.java_parser_service import JavaParserService

logger = logging.getLogger(__name__)


class ReportServiceV2:
    """报告服务类 V2"""
    
    def __init__(self):
        """初始化服务"""
        self.output_dir = REPORT_CONFIG["output_dir"]
        self.max_results = REPORT_CONFIG["max_results_per_file"]
        self.similarity_threshold = REPORT_CONFIG["similarity_threshold"]
        self.include_code_preview = REPORT_CONFIG["include_code_preview"]
        self.code_preview_length = REPORT_CONFIG["code_preview_length"]
        self.java_parser = JavaParserService()
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
    
    def generate_search_report(self, search_results: Dict[str, Any], output_filename: str = None) -> str:
        """
        生成搜索报告（新版本：分离高相似度和无匹配）
        
        Args:
            search_results (Dict[str, Any]): 搜索结果数据
            output_filename (str): 输出文件名，默认自动生成
            
        Returns:
            str: 主报告文件路径
        """
        if output_filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"匹配结果_{timestamp}.md"

        # 分析搜索结果，分离高相似度和无匹配
        analysis_result = self._analyze_search_results_v2(search_results)
        
        # 生成分离的JSON文件
        self._generate_separated_json_files(analysis_result, output_filename)
        
        # 生成主报告
        report_path = os.path.join(self.output_dir, output_filename)
        self._generate_main_report(analysis_result, report_path)
        
        # 生成高相似度报告
        self._generate_high_similarity_report(analysis_result, output_filename)
        
        # 生成无匹配报告
        self._generate_no_match_report(analysis_result, output_filename)
        
        # 生成后置处理的导入语句文件
        self._generate_import_statements(analysis_result, output_filename)
        
        return report_path
    
    def _analyze_search_results_v2(self, search_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析搜索结果（新版本：分离高相似度和无匹配）
        
        Args:
            search_results (Dict[str, Any]): 搜索结果数据
            
        Returns:
            Dict[str, Any]: 分析结果
        """
        high_similarity_classes = []  # 高相似度类（单一+多重）
        no_match_classes = []  # 无匹配类
        
        total_matches = 0
        high_similarity_matches = 0
        
        for batch_result in search_results.get("batch_results", []):
            if not batch_result.get("success", False):
                # 检索失败的类
                query_file = batch_result.get("query_file", "")
                class_name = self._extract_class_name_from_path(query_file)
                no_match_classes.append({
                    "class_name": class_name,
                    "query_file": query_file,
                    "error": batch_result.get("error", "检索失败"),
                    "type": "failed"
                })
                continue

            results = batch_result.get("results", [])
            total_matches += len(results)
            
            query_file = batch_result.get("query_file", "")
            class_name = self._extract_class_name_from_path(query_file)
            
            # 筛选高相似度结果
            high_similarity_results = [r for r in results if r.get("similarity_score", 0) >= self.similarity_threshold]
            high_similarity_matches += len(high_similarity_results)
            
            if len(high_similarity_results) == 0:
                # 无高相似度匹配
                no_match_classes.append({
                    "class_name": class_name,
                    "query_file": query_file,
                    "reason": "无高相似度匹配",
                    "type": "no_match",
                    "all_results": results
                })
            else:
                # 有高相似度匹配
                # 提取完整类路径信息
                processed_matches = []
                for match in high_similarity_results:
                    package_name = self._extract_package_name("", match.get("code_content", ""))
                    full_class_path = f"{package_name}.{match.get('class_name', '')}" if package_name != "未知包名" else match.get('class_name', '')
                    
                    processed_matches.append({
                        "class_name": match.get("class_name", ""),
                        "full_class_path": full_class_path,
                        "similarity_score": match.get("similarity_score", 0),
                        "distance": match.get("distance", 0),
                        "code_content": match.get("code_content", "")
                    })
                
                # 构建原始类的完整路径
                original_package = self._extract_package_from_file(query_file)
                original_full_path = f"{original_package}.{class_name}" if original_package != "未知包名" else class_name
                
                high_similarity_classes.append({
                    "class_name": class_name,
                    "query_file": query_file,
                    "original_full_path": original_full_path,
                    "match_count": len(high_similarity_results),
                    "matches": processed_matches,
                    "type": "single" if len(high_similarity_results) == 1 else "multiple"
                })
        
        return {
            "high_similarity_classes": high_similarity_classes,
            "no_match_classes": no_match_classes,
            "statistics": {
                "total_classes": len(high_similarity_classes) + len(no_match_classes),
                "high_similarity_count": len(high_similarity_classes),
                "no_match_count": len(no_match_classes),
                "total_matches": total_matches,
                "high_similarity_matches": high_similarity_matches
            }
        }
    
    def _generate_separated_json_files(self, analysis_result: Dict[str, Any], base_filename: str):
        """
        生成分离的JSON文件（高相似度和无匹配）
        
        Args:
            analysis_result (Dict[str, Any]): 分析结果
            base_filename (str): 基础文件名
        """
        base_name = base_filename.replace('.md', '')
        
        # 生成高相似度JSON文件
        high_similarity_path = os.path.join(self.output_dir, f"{base_name}_高相似度.json")
        with open(high_similarity_path, 'w', encoding='utf-8') as f:
            json.dump({
                "type": "high_similarity",
                "generated_time": datetime.now().isoformat(),
                "statistics": {
                    "total_count": len(analysis_result["high_similarity_classes"]),
                    "single_match_count": len([c for c in analysis_result["high_similarity_classes"] if c["type"] == "single"]),
                    "multiple_match_count": len([c for c in analysis_result["high_similarity_classes"] if c["type"] == "multiple"])
                },
                "classes": analysis_result["high_similarity_classes"]
            }, f, ensure_ascii=False, indent=2)
        
        # 生成无匹配JSON文件
        no_match_path = os.path.join(self.output_dir, f"{base_name}_无匹配.json")
        with open(no_match_path, 'w', encoding='utf-8') as f:
            json.dump({
                "type": "no_match",
                "generated_time": datetime.now().isoformat(),
                "statistics": {
                    "total_count": len(analysis_result["no_match_classes"]),
                    "failed_count": len([c for c in analysis_result["no_match_classes"] if c["type"] == "failed"]),
                    "no_match_count": len([c for c in analysis_result["no_match_classes"] if c["type"] == "no_match"])
                },
                "classes": analysis_result["no_match_classes"]
            }, f, ensure_ascii=False, indent=2)
        
        logger.info(f"生成分离JSON文件: {high_similarity_path}, {no_match_path}")
    
    def _extract_class_name_from_path(self, file_path: str) -> str:
        """
        从文件路径中提取类名
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            str: 类名
        """
        filename = os.path.basename(file_path)
        if filename.endswith('.java'):
            return filename[:-5]  # 去掉.java扩展名
        return filename
    
    def _extract_package_name(self, file_path: str, code_content: str) -> str:
        """
        从代码内容中提取包名
        
        Args:
            file_path (str): 文件路径（已废弃，保留参数兼容性）
            code_content (str): 代码内容
            
        Returns:
            str: 包名
        """
        try:
            # 使用Java解析服务提取包名
            return self.java_parser.extract_package_name(code_content)
        except Exception:
            return "未知包名"
    
    def _extract_package_from_file(self, file_path: str) -> str:
        """
        从文件路径推断包名
        
        Args:
            file_path (str): 文件路径
            
        Returns:
            str: 包名
        """
        try:
            # 尝试从文件路径推断包名
            if 'src/main/java/' in file_path:
                java_path = file_path.split('src/main/java/')[-1]
                package_path = java_path.replace('\\', '.').replace('/', '.')
                if package_path.endswith('.java'):
                    package_path = package_path[:-5]  # 去掉.java
                # 去掉类名，只保留包名
                if '.' in package_path:
                    return '.'.join(package_path.split('.')[:-1])
            
            return "未知包名"
        except Exception:
            return "未知包名"

    def _generate_main_report(self, analysis_result: Dict[str, Any], report_path: str):
        """
        生成主报告（汇总报告）

        Args:
            analysis_result (Dict[str, Any]): 分析结果
            report_path (str): 报告文件路径
        """
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# Java代码相似性检索报告（汇总）\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # 统计信息
            stats = analysis_result["statistics"]
            f.write("## 整体统计\n\n")
            f.write(f"- **总类数**: {stats['total_classes']}\n")
            f.write(f"- **高相似度匹配类数**: {stats['high_similarity_count']} (占比{stats['high_similarity_count']/stats['total_classes']*100:.1f}%)\n")
            f.write(f"- **无匹配类数**: {stats['no_match_count']} (占比{stats['no_match_count']/stats['total_classes']*100:.1f}%)\n")
            f.write(f"- **总匹配数**: {stats['total_matches']}\n")
            f.write(f"- **高相似度匹配数**: {stats['high_similarity_matches']}\n\n")

            # 高相似度类统计
            high_sim_classes = analysis_result["high_similarity_classes"]
            single_count = len([c for c in high_sim_classes if c["type"] == "single"])
            multiple_count = len([c for c in high_sim_classes if c["type"] == "multiple"])

            f.write("## 高相似度匹配分析\n\n")
            f.write(f"- **单一匹配类**: {single_count}个\n")
            f.write(f"- **多重匹配类**: {multiple_count}个\n\n")

            # 无匹配类统计
            no_match_classes = analysis_result["no_match_classes"]
            failed_count = len([c for c in no_match_classes if c["type"] == "failed"])
            no_match_count = len([c for c in no_match_classes if c["type"] == "no_match"])

            f.write("## 无匹配类分析\n\n")
            f.write(f"- **检索失败类**: {failed_count}个\n")
            f.write(f"- **无高相似度匹配类**: {no_match_count}个\n\n")

            f.write("## 详细报告文件\n\n")
            base_name = os.path.basename(report_path).replace('.md', '')
            f.write(f"- **高相似度详细报告**: `{base_name}_高相似度报告.md`\n")
            f.write(f"- **无匹配详细报告**: `{base_name}_无匹配报告.md`\n")
            f.write(f"- **导入语句文件**: `{base_name}_导入语句.md`\n\n")

            f.write("## JSON数据文件\n\n")
            f.write(f"- **高相似度数据**: `{base_name}_高相似度.json`\n")
            f.write(f"- **无匹配数据**: `{base_name}_无匹配.json`\n")

        logger.info(f"生成主报告: {report_path}")

    def _generate_high_similarity_report(self, analysis_result: Dict[str, Any], base_filename: str):
        """
        生成高相似度报告

        Args:
            analysis_result (Dict[str, Any]): 分析结果
            base_filename (str): 基础文件名
        """
        base_name = base_filename.replace('.md', '')
        report_path = os.path.join(self.output_dir, f"{base_name}_高相似度报告.md")

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 高相似度匹配详细报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            high_sim_classes = analysis_result["high_similarity_classes"]

            if not high_sim_classes:
                f.write("## 无高相似度匹配类\n\n")
                f.write("未找到任何高相似度匹配的类。\n")
                return

            # 单一匹配类
            single_matches = [c for c in high_sim_classes if c["type"] == "single"]
            if single_matches:
                f.write(f"## 单一匹配类 ({len(single_matches)}个)\n\n")
                for cls in single_matches:
                    match = cls["matches"][0]
                    f.write(f"### {cls['class_name']}\n\n")
                    f.write(f"- **原始类**: `{cls['original_full_path']}`\n")
                    f.write(f"- **匹配类**: `{match['full_class_path']}`\n")
                    f.write(f"- **相似度**: {match['similarity_score']:.4f}\n")
                    f.write(f"- **距离**: {match['distance']:.4f}\n\n")

            # 多重匹配类
            multiple_matches = [c for c in high_sim_classes if c["type"] == "multiple"]
            if multiple_matches:
                f.write(f"## 多重匹配类 ({len(multiple_matches)}个)\n\n")
                for cls in multiple_matches:
                    f.write(f"### {cls['class_name']}\n\n")
                    f.write(f"- **原始类**: `{cls['original_full_path']}`\n")
                    f.write(f"- **匹配数量**: {cls['match_count']}个\n\n")

                    f.write("**匹配结果**:\n\n")
                    for i, match in enumerate(cls["matches"], 1):
                        f.write(f"{i}. `{match['full_class_path']}` (相似度: {match['similarity_score']:.4f})\n")
                    f.write("\n")

        logger.info(f"生成高相似度报告: {report_path}")

    def _generate_no_match_report(self, analysis_result: Dict[str, Any], base_filename: str):
        """
        生成无匹配报告

        Args:
            analysis_result (Dict[str, Any]): 分析结果
            base_filename (str): 基础文件名
        """
        base_name = base_filename.replace('.md', '')
        report_path = os.path.join(self.output_dir, f"{base_name}_无匹配报告.md")

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 无匹配类详细报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            no_match_classes = analysis_result["no_match_classes"]

            if not no_match_classes:
                f.write("## 所有类都有高相似度匹配\n\n")
                f.write("恭喜！所有类都找到了高相似度匹配。\n")
                return

            # 检索失败类
            failed_classes = [c for c in no_match_classes if c["type"] == "failed"]
            if failed_classes:
                f.write(f"## 检索失败类 ({len(failed_classes)}个)\n\n")
                for cls in failed_classes:
                    f.write(f"### {cls['class_name']}\n\n")
                    f.write(f"- **文件路径**: `{cls['query_file']}`\n")
                    f.write(f"- **失败原因**: {cls['error']}\n\n")

            # 无高相似度匹配类
            no_match_only = [c for c in no_match_classes if c["type"] == "no_match"]
            if no_match_only:
                f.write(f"## 无高相似度匹配类 ({len(no_match_only)}个)\n\n")
                for cls in no_match_only:
                    f.write(f"### {cls['class_name']}\n\n")
                    f.write(f"- **文件路径**: `{cls['query_file']}`\n")
                    f.write(f"- **原因**: {cls['reason']}\n")

                    # 显示低相似度结果（如果有）
                    if cls.get("all_results"):
                        f.write(f"- **低相似度结果数**: {len(cls['all_results'])}个\n")
                        f.write("\n**低相似度匹配**:\n\n")
                        for i, result in enumerate(cls["all_results"][:3], 1):  # 只显示前3个
                            f.write(f"{i}. `{result.get('class_name', 'N/A')}` (相似度: {result.get('similarity_score', 0):.4f})\n")
                    f.write("\n")

        logger.info(f"生成无匹配报告: {report_path}")

    def _generate_import_statements(self, analysis_result: Dict[str, Any], base_filename: str):
        """
        生成后置处理的导入语句文件

        Args:
            analysis_result (Dict[str, Any]): 分析结果
            base_filename (str): 基础文件名
        """
        base_name = base_filename.replace('.md', '')
        import_path = os.path.join(self.output_dir, f"{base_name}_导入语句.md")

        with open(import_path, 'w', encoding='utf-8') as f:
            f.write("# 导入语句汇总\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write("根据高相似度匹配结果生成的导入语句，可用于代码重构参考。\n\n")

            high_sim_classes = analysis_result["high_similarity_classes"]

            if not high_sim_classes:
                f.write("## 无导入语句\n\n")
                f.write("未找到任何高相似度匹配，无法生成导入语句。\n")
                return

            import_statements = []

            # 处理单一匹配类
            single_matches = [c for c in high_sim_classes if c["type"] == "single"]
            for cls in single_matches:
                match = cls["matches"][0]
                import_statements.append({
                    "original": cls["original_full_path"],
                    "matched": match["full_class_path"],
                    "type": "single",
                    "similarity": match["similarity_score"]
                })

            # 处理多重匹配类（后置处理：检查是否有同名匹配）
            multiple_matches = [c for c in high_sim_classes if c["type"] == "multiple"]
            for cls in multiple_matches:
                # 查找是否有与原始类同名的匹配
                same_name_match = None
                for match in cls["matches"]:
                    if match["class_name"] == cls["class_name"]:
                        same_name_match = match
                        break

                if same_name_match:
                    # 找到同名匹配，视为匹配上
                    import_statements.append({
                        "original": cls["original_full_path"],
                        "matched": same_name_match["full_class_path"],
                        "type": "multiple_same_name",
                        "similarity": same_name_match["similarity_score"],
                        "total_matches": cls["match_count"]
                    })
                else:
                    # 没有同名匹配，选择相似度最高的
                    best_match = max(cls["matches"], key=lambda x: x["similarity_score"])
                    import_statements.append({
                        "original": cls["original_full_path"],
                        "matched": best_match["full_class_path"],
                        "type": "multiple_best",
                        "similarity": best_match["similarity_score"],
                        "total_matches": cls["match_count"]
                    })

            # 按相似度排序
            import_statements.sort(key=lambda x: x["similarity"], reverse=True)

            # 生成导入语句
            f.write("## 导入语句列表\n\n")
            f.write("```java\n")
            for stmt in import_statements:
                f.write(f"import {stmt['original']};\n")
                f.write(f"import {stmt['matched']};\n")
                f.write("\n")
            f.write("```\n\n")

            # 详细说明
            f.write("## 详细说明\n\n")
            for i, stmt in enumerate(import_statements, 1):
                f.write(f"### {i}. {stmt['original'].split('.')[-1]}\n\n")
                f.write(f"- **原始类**: `{stmt['original']}`\n")
                f.write(f"- **匹配类**: `{stmt['matched']}`\n")
                f.write(f"- **相似度**: {stmt['similarity']:.4f}\n")

                if stmt["type"] == "single":
                    f.write(f"- **匹配类型**: 单一匹配\n")
                elif stmt["type"] == "multiple_same_name":
                    f.write(f"- **匹配类型**: 多重匹配（同名优先）\n")
                    f.write(f"- **总匹配数**: {stmt['total_matches']}个\n")
                elif stmt["type"] == "multiple_best":
                    f.write(f"- **匹配类型**: 多重匹配（最佳相似度）\n")
                    f.write(f"- **总匹配数**: {stmt['total_matches']}个\n")

                f.write("\n")

            # 统计信息
            f.write("## 统计信息\n\n")
            f.write(f"- **总导入语句对数**: {len(import_statements)}\n")
            f.write(f"- **单一匹配**: {len([s for s in import_statements if s['type'] == 'single'])}个\n")
            f.write(f"- **多重匹配（同名）**: {len([s for s in import_statements if s['type'] == 'multiple_same_name'])}个\n")
            f.write(f"- **多重匹配（最佳）**: {len([s for s in import_statements if s['type'] == 'multiple_best'])}个\n")
            f.write(f"- **平均相似度**: {sum(s['similarity'] for s in import_statements) / len(import_statements):.4f}\n")

        logger.info(f"生成导入语句文件: {import_path}")
        logger.info(f"生成了 {len(import_statements)} 对导入语句")
