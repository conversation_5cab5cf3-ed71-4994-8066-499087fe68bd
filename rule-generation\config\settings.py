"""
配置文件
包含Milvus连接配置、默认路径等设置
"""

# Milvus连接配置
MILVUS_CONFIG = {
    "host": "************",
    "port": "19530",
    "alias": "default"
}

# 集合配置
COLLECTION_CONFIG = {
    "name": "java_code_vectors",
    "description": "存储Java代码的向量数据",
    "vector_dim": 768,  # CodeBERT向量维度
    "max_content_length": 500000,  # 代码内容最大长度，大幅增加到500K字符
    "use_text_field": True  # 使用TEXT字段类型而不是VARCHAR
}

# 索引配置
INDEX_CONFIG = {
    "index_type": "IVF_FLAT",
    "metric_type": "L2",
    "params": {"nlist": 128}
}

# 搜索配置
SEARCH_CONFIG = {
    "metric_type": "L2",
    "params": {"nprobe": 10},
    "default_limit": 3  # 默认返回前3名结果
}

# 默认工程路径已删除，必须通过参数传入
# DEFAULT_PROJECT_PATH = None  # 不再提供默认路径

# CodeBERT模型配置
MODEL_CONFIG = {
    "model_name": "microsoft/codebert-base",
    "max_length": 512
}

# 文件扫描配置
FILE_CONFIG = {
    "java_extensions": [".java"],
    "exclude_dirs": [".git", ".idea", "target", "build", "out"],
    "encoding": "utf-8",
    "max_file_size": 2 * 1024 * 1024,  # 最大文件大小2MB，超过将被跳过
    "large_file_threshold": 500 * 1024,  # 大文件阈值500KB
    "smart_truncate": True  # 启用智能截断
}

# 报告生成配置
REPORT_CONFIG = {
    "output_dir": "output",  # 报告输出目录
    "template_dir": "templates",  # 模板目录
    "max_results_per_file": 10,  # 每个文件最大检索结果数
    "similarity_threshold": 0.7,  # 相似度阈值
    "include_code_preview": True,  # 是否包含代码预览
    "code_preview_length": 300  # 代码预览长度
}
