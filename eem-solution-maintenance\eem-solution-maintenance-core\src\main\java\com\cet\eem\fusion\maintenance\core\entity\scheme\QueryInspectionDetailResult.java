package com.cet.eem.fusion.maintenance.core.entity.scheme;

import com.cet.eem.fusion.maintenance.core.entity.po.InspectionSchemeDetail;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName : QueryInspectionDetailResult
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-15 10:37
 */
@Getter
public class QueryInspectionDetailResult {

    /**
     * 方案名称
     */
    @Setter
    private String name;

    /**
     * 状态量
     */
    private List<InspectionSchemeDetail> statusQuantity;

    /**
     * 模拟量
     */
    private List<InspectionSchemeDetail> analogQuantity;
    /**
     * 文本量量
     */
    private List<InspectionSchemeDetail> textQuantity;

    public void setStatusQuantity(List<InspectionSchemeDetail> statusQuantity) {
        if (CollectionUtils.isNotEmpty(statusQuantity)) {
            statusQuantity = statusQuantity.stream().sorted(Comparator.comparingInt(InspectionSchemeDetail::getSort)).collect(Collectors.toList());
            this.statusQuantity = statusQuantity;

        }
    }

    public void setAnalogQuantity(List<InspectionSchemeDetail> analogQuantity) {
        if (CollectionUtils.isNotEmpty(analogQuantity)) {
            analogQuantity = analogQuantity.stream().sorted(Comparator.comparingInt(InspectionSchemeDetail::getSort)).collect(Collectors.toList());
            this.analogQuantity = analogQuantity;
        }
    }
    public void setTextQuantity(List<InspectionSchemeDetail> textQuantity) {
        if (CollectionUtils.isNotEmpty(textQuantity)) {
            textQuantity = textQuantity.stream().sorted(Comparator.comparingInt(InspectionSchemeDetail::getSort)).collect(Collectors.toList());
            this.textQuantity = textQuantity;
        }
    }

    public List<InspectionSchemeDetail> getAllInspectionSchemeDetails(){
        LinkedList<InspectionSchemeDetail> result = new LinkedList<>();
        this.statusQuantity = this.statusQuantity==null?new LinkedList<>():this.statusQuantity;
        this.analogQuantity = this.analogQuantity == null ? new LinkedList<>(): this.analogQuantity;
        this.textQuantity = this.textQuantity == null ? new LinkedList<>(): this.textQuantity;
        result.addAll(this.statusQuantity);
        result.addAll(this.analogQuantity);
        result.addAll(this.textQuantity);
        return result;
    }
}
