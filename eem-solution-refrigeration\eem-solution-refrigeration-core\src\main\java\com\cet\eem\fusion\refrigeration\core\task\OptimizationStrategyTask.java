﻿package com.cet.eem.fusion.refrigeration.core.task;

import com.cet.eem.fusion.refrigeration.core.handle.optimizationstrategy.OptimizationStrategyHandle;
import com.cet.eem.common.utils.TimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Configuration
@EnableScheduling
public class OptimizationStrategyTask {

    @Autowired
    OptimizationStrategyHandle optimizationStrategyHandle;

    @Scheduled(cron = "${cet.eem.optimizationstrategy.cron:-}")
    public void calculate() {
        optimizationStrategyHandle.calcOptimizationStrategy(TimeUtil.timestamp2LocalDateTime(System.currentTimeMillis()));
    }
}

