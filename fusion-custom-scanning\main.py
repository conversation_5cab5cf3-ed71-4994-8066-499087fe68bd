#!/usr/bin/env python3
"""
Java代码扫描工具主程序
"""
import argparse
import sys
import os
from pathlib import Path
from typing import List, Dict, Any
import javalang
from service.structure_checker import StructureChecker
from service.global_matcher import GlobalMatcher
from service.report_exporter import ReportExporter
from service.issues_report_generator import IssuesReportGenerator
from service.import_issues_processor import ImportIssuesProcessor
from service.problem_splitter import ProblemSplitter
from service.precise_class_finder import BatchAnalyzer
from service.qodana_generator import QodanaGenerator


def setup_arguments():
    """
    设置命令行参数

    Returns:
        argparse.Namespace: 解析后的参数对象
    """
    # 默认扫描目录（相对路径）
    default_directory = "../eem-solution-group-energy/"
#     default_directory = "../eem-solution-transformer/eem-solution-transformer-core/"

    # 其他可选目录：
    # default_directory = "../eem-solution-transformer/"
    # default_directory = "../eem-solution-refrigeration/"

    # 默认QodanaJavaSanity.xml文件路径
    default_qodana_file = "QodanaJavaSanity.xml"

    # 默认split_problems目录
    default_split_problems_dir = "output/split_problems"
    # 默认split_analysis目录
    default_split_analysis_dir = "output/split_analysis"

    parser = argparse.ArgumentParser(
        description="Java代码扫描工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=f"""
使用示例:
  python main.py                                    # 使用默认目录扫描
  python main.py /path/to/java/project              # 指定目录扫描
  python main.py -o results.json                    # 使用默认目录，指定输出文件
  python main.py /path/to/java/project -o results.json
  python main.py --qodana-file /path/to/QodanaJavaSanity.xml  # 指定Qodana分析文件

默认扫描目录: {default_directory}
默认Qodana文件: {default_qodana_file}
        """
    )

    parser.add_argument(
        "directory",
        nargs='?',  # 可选参数
        default=default_directory,
        help=f"要扫描的Java项目目录路径 (默认: {default_directory})"
    )

    parser.add_argument(
        "--structure-rules",
        default="rules/structure_rules.json",
        help="结构检查规则配置文件路径 (默认: rules/structure_rules.json)"
    )

    parser.add_argument(
        "--global-rules",
        default="rules/global_match_rules.json",
        help="全局匹配规则配置文件路径 (默认: rules/global_match_rules.json)"
    )

    parser.add_argument(
        "--qodana-file",
        default=default_qodana_file,
        help=f"QodanaJavaSanity.xml文件路径 (默认: {default_qodana_file})"
    )

    parser.add_argument(
        "-o", "--output",
        default="output/scan_results.json",
        help="输出文件路径 (默认: output/scan_results.json)"
    )

    parser.add_argument(
        "--skip-global",
        action="store_true",
        help="跳过全局匹配检查"
    )

    parser.add_argument(
        "--skip-structure",
        action="store_true",
        help="跳过其他问题检查"
    )

    parser.add_argument(
        "--skip-import",
        action="store_true",
        help="跳过导入问题检查"
    )

    parser.add_argument(
        "--export-format",
        choices=["json"],
        default="json",
        help="导出格式 (默认: json)"
    )

    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="显示详细输出"
    )

    return parser.parse_args()


def validate_directory(directory_path):
    """
    验证目录路径是否有效

    Args:
        directory_path: 目录路径

    Returns:
        bool: 目录是否有效
    """
    if not os.path.exists(directory_path):
        print(f"错误: 目录不存在 - {directory_path}")
        return False

    if not os.path.isdir(directory_path):
        print(f"错误: 路径不是目录 - {directory_path}")
        return False

    return True


def scan_directory(directory_path: str) -> List[str]:
    """
    扫描目录，收集所有Java文件
    Args:
        directory_path: 要扫描的目录路径
    Returns:
        Java文件路径列表
    """
    print(f"开始扫描目录: {directory_path}")

    directory = Path(directory_path)
    if not directory.exists():
        raise FileNotFoundError(f"目录不存在: {directory_path}")

    java_files = []

    # 递归查找所有.java文件
    for java_file in directory.rglob("*.java"):
        if java_file.is_file():
            java_files.append(str(java_file))

    print(f"找到 {len(java_files)} 个Java文件")
    return java_files


def extract_java_structure(file_path: str) -> Dict[str, Any]:
    """
    提取Java文件的结构信息（类、属性、方法等）
    Args:
        file_path: Java文件路径
    Returns:
        包含文件结构信息的字典
    """
    file_info = {
        "file_path": file_path,
        "classes": [],
        "interfaces": [],
        "enums": [],
        "imports": [],
        "package": None,
        "parse_error": None
    }

    try:
        with open(file_path, 'r', encoding='utf-8-sig') as f:
            content = f.read()

        # 移除可能的BOM字符
        if content.startswith('\ufeff'):
            content = content[1:]

        # 解析Java代码
        tree = javalang.parse.parse(content)
        content_lines = content.split('\n')

        # 提取包名
        if tree.package:
            file_info["package"] = tree.package.name

        # 提取导入
        if tree.imports:
            for imp in tree.imports:
                file_info["imports"].append({
                    "path": imp.path,
                    "static": imp.static,
                    "wildcard": imp.wildcard
                })

        # 提取类信息
        for _, node in tree.filter(javalang.tree.ClassDeclaration):
            class_info = extract_class_info(node, content_lines)
            file_info["classes"].append(class_info)

        # 提取接口信息
        for _, node in tree.filter(javalang.tree.InterfaceDeclaration):
            interface_info = extract_interface_info(node, content_lines)
            file_info["interfaces"].append(interface_info)

        # 提取枚举信息
        for _, node in tree.filter(javalang.tree.EnumDeclaration):
            enum_info = extract_enum_info(node, content_lines)
            file_info["enums"].append(enum_info)

    except Exception as e:
        import traceback
        error_details = f"{str(e)}\n{traceback.format_exc()}"
        file_info["parse_error"] = error_details

    return file_info


def extract_class_info(class_node, content_lines: List[str]) -> Dict[str, Any]:
    """
    提取类的详细信息
    Args:
        class_node: javalang类节点
        content_lines: 文件内容行列表
    Returns:
        类信息字典
    """
    class_info = {
        "name": class_node.name,
        "modifiers": class_node.modifiers if class_node.modifiers else [],
        "extends": class_node.extends.name if class_node.extends else None,
        "implements": [impl.name for impl in class_node.implements] if class_node.implements else [],
        "annotations": extract_annotations(class_node),
        "fields": [],
        "methods": [],
        "constructors": [],
        "start_line": class_node.position.line if class_node.position else 0,
        "end_line": get_node_end_line(class_node, content_lines)
    }

    # 提取字段（属性）
    for field in class_node.fields:
        for declarator in field.declarators:
            field_info = {
                "name": declarator.name,
                "type": get_type_name(field.type),
                "modifiers": field.modifiers if field.modifiers else [],
                "annotations": extract_annotations(field),
                "start_line": field.position.line if field.position else 0,
                "end_line": field.position.line if field.position else 0  # 字段通常只有一行
            }
            class_info["fields"].append(field_info)

    # 提取方法
    for method in class_node.methods:
        method_info = extract_method_info(method, content_lines)
        class_info["methods"].append(method_info)

    # 提取构造函数
    for constructor in class_node.constructors:
        constructor_info = extract_constructor_info(constructor, content_lines)
        class_info["constructors"].append(constructor_info)

    return class_info


def extract_method_info(method_node, content_lines: List[str]) -> Dict[str, Any]:
    """
    提取方法的详细信息
    Args:
        method_node: javalang方法节点
        content_lines: 文件内容行列表
    Returns:
        方法信息字典
    """
    start_line = method_node.position.line if method_node.position else 0
    end_line = get_node_end_line(method_node, content_lines)

    method_info = {
        "name": method_node.name,
        "return_type": get_type_name(method_node.return_type) if method_node.return_type else "void",
        "modifiers": method_node.modifiers if method_node.modifiers else [],
        "annotations": extract_annotations(method_node),
        "parameters": [],
        "throws": [throw.name if hasattr(throw, 'name') else str(throw) for throw in
                   method_node.throws] if method_node.throws else [],
        "start_line": start_line,
        "end_line": end_line,
        "body": extract_method_body(start_line, end_line, content_lines)
    }

    # 提取参数信息
    if method_node.parameters:
        for param in method_node.parameters:
            param_info = {
                "name": param.name,
                "type": get_type_name(param.type),
                "modifiers": param.modifiers if param.modifiers else []
            }
            method_info["parameters"].append(param_info)

    return method_info


def extract_method_body(start_line: int, end_line: int, content_lines: List[str]) -> str:
    """
    提取方法体内容
    Args:
        start_line: 方法开始行号（1-based）
        end_line: 方法结束行号（1-based）
        content_lines: 文件内容行列表
    Returns:
        方法体内容字符串
    """
    if start_line <= 0 or end_line <= 0 or start_line > len(content_lines) or end_line > len(content_lines):
        return ""

    # 提取方法体内容（转换为0-based索引）
    method_lines = content_lines[start_line - 1:end_line]
    return "\n".join(method_lines)


def extract_constructor_info(constructor_node, content_lines: List[str]) -> Dict[str, Any]:
    """
    提取构造函数的详细信息
    Args:
        constructor_node: javalang构造函数节点
        content_lines: 文件内容行列表
    Returns:
        构造函数信息字典
    """
    constructor_info = {
        "name": constructor_node.name,
        "modifiers": constructor_node.modifiers if constructor_node.modifiers else [],
        "annotations": extract_annotations(constructor_node),
        "parameters": [],
        "throws": [throw.name if hasattr(throw, 'name') else str(throw) for throw in
                   constructor_node.throws] if constructor_node.throws else [],
        "start_line": constructor_node.position.line if constructor_node.position else 0,
        "end_line": get_node_end_line(constructor_node, content_lines)
    }

    # 提取参数信息
    if constructor_node.parameters:
        for param in constructor_node.parameters:
            param_info = {
                "name": param.name,
                "type": get_type_name(param.type),
                "modifiers": param.modifiers if param.modifiers else []
            }
            constructor_info["parameters"].append(param_info)

    return constructor_info


def extract_interface_info(interface_node, content_lines: List[str]) -> Dict[str, Any]:
    """
    提取接口的详细信息
    Args:
        interface_node: javalang接口节点
        content_lines: 文件内容行列表
    Returns:
        接口信息字典
    """
    interface_info = {
        "name": interface_node.name,
        "modifiers": interface_node.modifiers if interface_node.modifiers else [],
        "extends": [ext.name for ext in interface_node.extends] if interface_node.extends else [],
        "annotations": extract_annotations(interface_node),
        "methods": [],
        "start_line": interface_node.position.line if interface_node.position else 0,
        "end_line": get_node_end_line(interface_node, content_lines)
    }

    # 提取方法
    for method in interface_node.methods:
        method_info = extract_method_info(method, content_lines)
        interface_info["methods"].append(method_info)

    return interface_info


def extract_enum_info(enum_node, content_lines: List[str]) -> Dict[str, Any]:
    """
    提取枚举的详细信息
    Args:
        enum_node: javalang枚举节点
        content_lines: 文件内容行列表
    Returns:
        枚举信息字典
    """
    enum_info = {
        "name": enum_node.name,
        "modifiers": enum_node.modifiers if enum_node.modifiers else [],
        "implements": [impl.name for impl in enum_node.implements] if enum_node.implements else [],
        "annotations": extract_annotations(enum_node),
        "constants": [],
        "methods": [],
        "start_line": enum_node.position.line if enum_node.position else 0,
        "end_line": get_node_end_line(enum_node, content_lines)
    }

    # 提取枚举常量
    for constant in enum_node.body.constants:
        constant_info = {
            "name": constant.name,
            "annotations": extract_annotations(constant),
            "start_line": constant.position.line if constant.position else 0,
            "end_line": constant.position.line if constant.position else 0  # 常量通常只有一行
        }
        enum_info["constants"].append(constant_info)

    # 提取方法
    for method in enum_node.body.methods:
        method_info = extract_method_info(method, content_lines)
        enum_info["methods"].append(method_info)

    return enum_info


def extract_annotations(node) -> List[Dict[str, Any]]:
    """
    提取注解信息
    Args:
        node: javalang节点
    Returns:
        注解信息列表
    """
    annotations = []
    if hasattr(node, 'annotations') and node.annotations:
        for annotation in node.annotations:
            annotation_info = {
                "name": annotation.name,
                "arguments": [],
                # 添加注解的精确行号信息
                "line": annotation.position.line if hasattr(annotation, 'position') and annotation.position else 0,
                "start_line": annotation.position.line if hasattr(annotation,
                                                                  'position') and annotation.position else 0,
                "end_line": annotation.position.line if hasattr(annotation, 'position') and annotation.position else 0
            }

            # 提取注解参数
            if hasattr(annotation, 'element') and annotation.element:
                # 处理不同类型的注解参数结构
                annotation_info["arguments"] = _extract_annotation_arguments(annotation.element)

            annotations.append(annotation_info)

    return annotations


def _extract_annotation_arguments(element):
    """
    提取注解参数，处理各种复杂的参数结构
    """
    arguments = []

    # 检查是否是列表形式的参数 (如 [ElementValuePair(...)])
    if isinstance(element, list):
        for item in element:
            if hasattr(item, 'name') and hasattr(item, 'value'):
                arguments.append({
                    "name": item.name,
                    "value": _extract_annotation_value(item.value)
                })
    elif hasattr(element, 'values'):
        # 多个参数的情况
        for value in element.values:
            if hasattr(value, 'name') and hasattr(value, 'value'):
                arguments.append({
                    "name": value.name,
                    "value": _extract_annotation_value(value.value)
                })
    elif hasattr(element, 'name') and hasattr(element, 'value'):
        # 单个命名参数
        arguments.append({
            "name": element.name,
            "value": _extract_annotation_value(element.value)
        })
    else:
        # 单个默认参数
        arguments.append({
            "name": "value",
            "value": _extract_annotation_value(element)
        })

    return arguments


def _extract_annotation_value(value):
    """
    提取注解参数值，处理常量引用和字面量
    """
    if hasattr(value, 'member'):
        # 常量引用 (如 MemberReference(member=OPERATION_ADD))
        # 检查是否有qualifier（类名部分）
        if hasattr(value, 'qualifier') and value.qualifier:
            # 完整引用：ClassName.CONSTANT_NAME
            return f"{value.qualifier}.{value.member}"
        else:
            # 简单引用：CONSTANT_NAME
            return value.member
    elif hasattr(value, 'value'):
        # 字面量 (如 Literal(value=15000))
        return value.value
    else:
        # 其他情况，转为字符串
        return str(value)


def get_node_end_line(node, content_lines: List[str]) -> int:
    """
    获取节点的结束行号
    Args:
        node: javalang节点
        content_lines: 文件内容行列表
    Returns:
        结束行号
    """
    if not hasattr(node, 'position') or not node.position:
        return 0

    start_line = node.position.line

    # 简单的启发式方法：从开始行向下查找匹配的大括号
    brace_count = 0
    for i, line in enumerate(content_lines[start_line - 1:], start_line):
        for char in line:
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    return i

    # 如果没找到匹配的大括号，返回开始行
    return start_line


def get_type_name(type_node) -> str:
    """
    获取类型名称
    Args:
        type_node: javalang类型节点
    Returns:
        类型名称字符串
    """
    if type_node is None:
        return "unknown"

    # 如果是字符串，直接返回
    if isinstance(type_node, str):
        return type_node

    if hasattr(type_node, 'name'):
        # 检查是否有泛型参数
        if hasattr(type_node, 'arguments') and type_node.arguments:
            # 泛型类型
            args = []
            for arg in type_node.arguments:
                if hasattr(arg, 'type'):
                    args.append(get_type_name(arg.type))
                else:
                    args.append(get_type_name(arg))
            return f"{type_node.name}<{', '.join(args)}>"
        else:
            return type_node.name
    elif hasattr(type_node, 'element_type'):
        # 数组类型
        return f"{get_type_name(type_node.element_type)}[]"
    else:
        return str(type_node)


def analyze_all_files(java_files: List[str]) -> List[Dict[str, Any]]:
    """
    分析所有Java文件，提取结构信息
    Args:
        java_files: Java文件路径列表
    Returns:
        所有文件的结构信息列表
    """
    all_file_info = []

    for file_path in java_files:
        print(f"分析文件: {file_path}")
        file_info = extract_java_structure(file_path)
        all_file_info.append(file_info)
    return all_file_info


def main():
    """主函数"""
    # 初始化参数
    args = setup_arguments()

    # 验证目录
    if not validate_directory(args.directory):
        sys.exit(1)

    # 确保输出目录存在
    output_dir = os.path.dirname(args.output)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir, exist_ok=True)
        print(f"创建输出目录: {output_dir}")

    try:
        print("=" * 60)
        print("Java代码扫描工具")
        print("=" * 60)

        # 步骤1: 扫描目录，收集所有Java文件
        print("\n步骤1: 扫描目录...")
        java_files = scan_directory(args.directory)

        if not java_files:
            print("未找到Java文件，扫描结束。")
            return

        if args.verbose:
            print("找到的Java文件:")
            for file_path in java_files:
                print(f"  - {file_path}")

        # 步骤2: 文件结构分析 - 提取类、属性、方法等信息
        print("\n步骤2: 文件结构分析...")
        file_structures = analyze_all_files(java_files)

        # 显示结构分析统计信息
        if args.verbose:
            total_classes = sum(len(f.get("classes", [])) for f in file_structures)
            total_interfaces = sum(len(f.get("interfaces", [])) for f in file_structures)
            total_enums = sum(len(f.get("enums", [])) for f in file_structures)
            total_methods = sum(
                sum(len(cls.get("methods", [])) for cls in f.get("classes", [])) +
                sum(len(intf.get("methods", [])) for intf in f.get("interfaces", [])) +
                sum(len(enum.get("methods", [])) for enum in f.get("enums", []))
                for f in file_structures
            )
            print(f"结构分析完成:")
            print(f"  - 类总数: {total_classes}")
            print(f"  - 接口总数: {total_interfaces}")
            print(f"  - 枚举总数: {total_enums}")
            print(f"  - 方法总数: {total_methods}")

        # 步骤3: 全局匹配检查
        global_issues = []
        if not args.skip_global:
            print("\n步骤3: 全局匹配检查...")
            global_matcher = GlobalMatcher(rules_file=args.global_rules)
            global_issues = global_matcher.scan_project(args.directory)

            if args.verbose and global_issues:
                print("发现的全局匹配问题:")
                for issue in global_issues[:5]:  # 显示前5个问题
                    print(f"  - {issue['file']}:{issue['line']} [{issue['severity']}] {issue['message']}")
                if len(global_issues) > 5:
                    print(f"  ... 还有 {len(global_issues) - 5} 个全局匹配问题")

        # 步骤4: 动态问题检查
        structure_issues = []
        if not args.skip_structure:
            print("\n步骤4: 动态问题检查...")
            structure_checker = StructureChecker(rules_file=args.structure_rules, project_root=args.directory)
            structure_issues = structure_checker.check_file_structures(file_structures)

            if args.verbose and structure_issues:
                print("动态问题检查:")
                for issue in structure_issues[:5]:  # 显示前5个问题
                    print(f"  - {issue['file']}:{issue['line']} [{issue['severity']}] {issue['message']}")
                if len(structure_issues) > 5:
                    print(f"  ... 还有 {len(structure_issues) - 5} 个问题")

        # 步骤5: 导入问题检查
        import_issues = []
        if not args.skip_import:
            print("\n步骤5: 导入问题检查...")

            # 每次都重新生成QodanaJavaSanity.xml文件
            print("  🔄 开始重新生成QodanaJavaSanity.xml文件...")
            
            # 生成QodanaJavaSanity.xml
            try:
                # 获取输出目录
                qodana_output_dir = os.path.dirname(args.qodana_file) if os.path.dirname(args.qodana_file) else "."
                
                generator = QodanaGenerator(
                    project_path=args.directory,
                    inspection_profile=None,  # 使用默认的ai_class.xml
                    output_dir=qodana_output_dir,
                    verbose=args.verbose
                )
                
                if generator.generate():
                    print(f"  ✅ 成功生成QodanaJavaSanity.xml文件: {args.qodana_file}")
                else:
                    print("  ❌ QodanaJavaSanity.xml文件生成失败，跳过导入问题检查")
                    # 继续执行其他步骤，不退出整个程序
                    
            except Exception as e:
                print(f"  ❌ 生成QodanaJavaSanity.xml时出错: {e}")
                if args.verbose:
                    import traceback
                    traceback.print_exc()
                print("  跳过导入问题检查，继续执行其他步骤...")
                # 继续执行其他步骤，不退出整个程序
            
            # 检查文件是否存在
            if os.path.exists(args.qodana_file):
                # 步骤5.1: 拆分QodanaJavaSanity.xml
                print("  步骤5.1: 拆分QodanaJavaSanity.xml文件...")
                splitter = ProblemSplitter(
                    input_file=args.qodana_file,
                    output_dir="output/split_problems"
                )
                split_files = splitter.split_problems()

                if split_files:
                    print(f"  ✅ 成功拆分为 {len(split_files)} 个问题文件")

                    # 步骤5.2: 生成分析报告
                    print("  步骤5.2: 生成导入问题分析报告...")
                    import glob
                    # 构造core模块路径
                    project_name = os.path.basename(args.directory.rstrip('/').rstrip('\\'))
                    core_module_path = os.path.join(args.directory, project_name + "-core")
                    
                    # 检查构造的core路径是否存在，如果不存在则查找实际存在的core目录
                    if not os.path.exists(core_module_path):
                        core_dirs = glob.glob(os.path.join(args.directory, "*core*"))
                        if core_dirs:
                            core_module_path = core_dirs[0]
                        else:
                            # 如果找不到core目录，则使用默认目录
                            core_module_path = args.directory
                    
                    batch_analyzer = BatchAnalyzer(
                        split_problems_dir="output/split_problems",
                        project_path=core_module_path
                    )
                    analysis_files = batch_analyzer.process_all_problems()

                    if analysis_files:
                        print(f"  ✅ 成功生成 {len(analysis_files)} 个分析报告")

                        # 步骤5.3: 处理分析结果
                        print("  步骤5.3: 处理导入问题...")
                        import_processor = ImportIssuesProcessor(split_analysis_dir="output/split_analysis")
                        import_issues = import_processor.scan_import_issues()

                        if args.verbose and import_issues:
                            print("发现的导入问题:")
                            for issue in import_issues[:5]:  # 显示前5个问题
                                print(f"    - {issue['file']}:{issue['line']} [{issue['severity']}] {issue['message']}")
                            if len(import_issues) > 5:
                                print(f"    ... 还有 {len(import_issues) - 5} 个导入问题")
                    else:
                        print("  ❌ 分析报告生成失败")
                else:
                    print("  ❌ QodanaJavaSanity.xml文件拆分失败")

        # 步骤6: 问题汇总和输出
        print("\n步骤6: 问题汇总和输出...")
        all_issues = global_issues + structure_issues + import_issues

        # 使用报告导出器
        exporter = ReportExporter()

        # 导出各类问题到不同文件
        if global_issues and not args.skip_global:
            global_output = args.output.replace('.json', '_global.json')
            global_matcher.export_issues(global_output)

        if structure_issues and not args.skip_structure:
            structure_output = args.output.replace('.json', '_structure.json')
            structure_checker.export_issues(structure_output)

        if import_issues and not args.skip_import:
            import_output = args.output.replace('.json', '_import.json')
            import_processor.export_issues(import_output)

        # 导出汇总报告
        summary_output = args.output.replace('.json', '_summary.json')
        exporter.export_summary_report(all_issues, global_issues, structure_issues, import_issues, summary_output)

        # 生成问题报告
        print("\n步骤7: 生成问题报告...")
        report_generator = IssuesReportGenerator()

        # 读取刚生成的汇总数据
        import json
        try:
            with open(summary_output, 'r', encoding='utf-8') as f:
                summary_data = json.load(f)

            # 生成问题报告
            issues_report_output = args.output.replace('.json', '_issues_report.md')
            report_generator.generate_report(summary_data, issues_report_output)

        except Exception as e:
            print(f"⚠️ 生成问题报告时出错: {e}")

        print("\n扫描完成！")
        summary_output = args.output.replace('.json', '_summary.json')
        print(f"汇总报告已保存到: {summary_output}")
        issues_report_output = args.output.replace('.json', '_issues_report.md')
        print(f"问题报告已保存到: {issues_report_output}")
        print(f"文件结构数据已获取，可用于后续处理")

        # 返回结构数据和问题数据供后续处理使用
        return {
            "file_structures": file_structures,
            "global_issues": global_issues,
            "structure_issues": structure_issues,
            "import_issues": import_issues,
            "all_issues": all_issues
        }

    except KeyboardInterrupt:
        print("\n\n扫描被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n错误: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()