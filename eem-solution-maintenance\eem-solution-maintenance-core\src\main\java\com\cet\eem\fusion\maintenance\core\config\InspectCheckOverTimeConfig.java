package com.cet.eem.fusion.maintenance.core.config;

import lombok.Getter;
import lombok.Setter;

/**
 * 描述：
 *
 * <AUTHOR>
 * @date 2023/2/15
 */
@Getter
@Setter
//@Component
//@ConfigurationProperties(prefix = "cet.eem.work-order.inspect.check-over-time")
//@Slf4j
//@ToString
public class InspectCheckOverTimeConfig {
    /**
     * 执行周期
     */
    private String interval;

    /**
     * 指定月份之前的过期工单不在进行处理
     */
    private Integer handleOverTimeWoPreMonth = 3;
}
