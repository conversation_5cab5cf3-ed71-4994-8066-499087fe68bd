package com.cet.eem.fusion.maintenance.core.entity.po;

import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>  (2025/8/15 16:47)
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.DEVICE_SYSTEM)
public class DeviceSystem extends EntityWithName {
    @JsonProperty("projectid")
    private Long projectId;

    public DeviceSystem(){
        this.modelLabel=ModelLabelDef.DEVICE_SYSTEM;
    }

    public DeviceSystem(Long id, String name) {
        this.id = id;
        this.name = name;
        this.modelLabel=ModelLabelDef.DEVICE_SYSTEM;
    }
}