package com.cet.eem.solution.common.entity.vo.peccore;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/6 13:40
 */
@Data
public class EmergencyAlarmPoint {
    private Integer nodeType;

    @ApiModelProperty("事故报警点id")
    private Integer nodeId;

    private String nodeName;

    private Integer parentNodeType;

    private Integer parentNodeId;

    @ApiModelProperty("报警级别：1事故、2报警、3一般、4预警、0其他")
    private Integer level;

    @ApiModelProperty("分类：0设备定制越限、1开关量变位、2模拟量越限、4模拟量枚举值报警")
    private Integer type;

    private Boolean needSave;

    @ApiModelProperty("通道id")
    private Integer channelId;

    @ApiModelProperty("通道名称")
    private String channelName;

    @ApiModelProperty("设备id")
    private Long deviceId;

    @ApiModelProperty("设备名称")
    private String deviceName;

    @ApiModelProperty("测点id")
    private Integer measureId;

    @ApiModelProperty("测点名称")
    private String measureName;

    @ApiModelProperty("0越上限，1越下限")
    private Integer actionType;

    private Integer opDelay;

    private Integer reDelay;

    @ApiModelProperty("下限值")
    private Double lowLimit;

    @ApiModelProperty("上限值")
    private Double highLimit;
}