package com.cet.eem.fusion.transformer.core.service.impl;

import com.cet.eem.bll.common.dao.node.NodeDao;
import com.cet.eem.bll.common.dao.pipenetwork.PipeNetworkConnectionModelDao;
import com.cet.eem.bll.common.def.quantity.FrequencyDef;
import com.cet.eem.bll.common.def.quantity.PhasorDef;
import com.cet.eem.bll.common.def.quantity.QuantityCategoryDef;
import com.cet.eem.bll.common.def.quantity.QuantityTypeDef;

import com.cet.eem.bll.common.model.domain.object.entitymap.PipeNetworkConnectionModel;
import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityAggregationData;
import com.cet.eem.bll.common.model.domain.object.physicalquantity.QuantityObject;
import com.cet.eem.bll.common.model.domain.object.powersystem.PowerTransformerVo;
import com.cet.eem.bll.common.model.topology.vo.LinkNode;
import com.cet.eem.bll.common.model.topology.vo.PointNode;
import com.cet.eem.common.CommonUtils;
import com.cet.eem.common.TransformerConstantDef.AggregationType;
import com.cet.eem.common.TransformerConstantDef.EnergyTypeDef;
import com.cet.eem.common.TransformerConstantDef.EnumDataTypeId;
import com.cet.eem.common.TransformerConstantDef.EnumOperationType;
import com.cet.eem.common.definition.ColumnDef;
import com.cet.eem.common.definition.NodeLabelDef;
import com.cet.eem.common.model.BaseVo;
import com.cet.eem.common.model.datalog.DataLogData;
import com.cet.eem.common.model.datalog.TrendDataVo;
import com.cet.eem.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.eem.common.model.realtime.RealTimeValueVo;
import com.cet.eem.common.parse.JsonTransferUtils;
import com.cet.eem.common.utils.TimeUtil;
import com.cet.eem.model.base.ConditionBlock;
import com.cet.eem.model.base.QueryCondition;
import com.cet.eem.model.model.BaseEntity;
import com.cet.eem.model.tool.ModelServiceUtils;
import com.cet.eem.model.tool.QueryConditionBuilder;


import com.cet.eem.node.service.Topology1Service;
import com.cet.eem.quantity.dao.QuantityAggregationDataDao;
import com.cet.eem.quantity.dao.QuantityObjectDao;
import com.cet.eem.quantity.model.quantity.QuantityDataBatchSearchVo;
import com.cet.eem.quantity.model.quantity.QuantitySearchVo;
import com.cet.eem.quantity.service.QuantityManageService;
import com.cet.eem.fusion.transformer.core.entity.vo.HistoricalLoadVo;
import com.cet.eem.fusion.transformer.core.entity.dto.*;
import com.cet.eem.fusion.transformer.core.service.TransformerAnalysisService;
import com.cet.electric.matterhorn.devicedataservice.common.entity.datalog.DatalogValue;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @ClassName : TransformerAnalysisServiceImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2022-03-07 10:53
 */
@Service
public class TransformerAnalysisServiceImpl implements TransformerAnalysisService {
    private static final Logger log = LoggerFactory.getLogger(TransformerAnalysisServiceImpl.class);

    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    QuantityManageService quantityManageService;
    @Autowired
    Topology1Service topology1Service;
    @Autowired
    QuantityObjectDao quantityObjectDao;
    @Autowired
    QuantityAggregationDataDao quantityAggregationDataDao;
    @Autowired
    PipeNetworkConnectionModelDao pipeNetworkConnectionModelDao;
    @Autowired
    NodeDao nodeDao;
    public static final String DATAINFO = "dataInfo";
    public static final String DATA_LINK = "dataLink";
    public static final Double ZERO = 0.0;
    public static final Double NUM = 1.33;
    public static final Double NUM_1 = 0.75;
    public static final Double MAX = 1.0;
    public static final String LABEL = "historicalload";
    public static final String OBJECT_ID = "powertransformerid";
    public static final Integer HIGH = 1;
    public static final Integer LOW = 0;
    public static final Integer EACH = 100;

    @Override
    public EquipmentMonitorVo queryEquipmentMonitorInfo(Long id) {
        PowerTransformerDto powerTransformerVo = queryPowerTransformer(id);
        if (Objects.isNull(powerTransformerVo)) {
            return new EquipmentMonitorVo();
        }
        EquipmentMonitorVo equipmentMonitorVo = new EquipmentMonitorVo();
        equipmentMonitorVo.setPic(powerTransformerVo.getPic());
        equipmentMonitorVo.setOptimalLoadRate(calculateOptimalLoadRate(powerTransformerVo));
        equipmentMonitorVo.setRatedCapacity(powerTransformerVo.getRatedcapacity());
        equipmentMonitorVo.setTransformerLevel(powerTransformerVo.getTransformerlevel());
        assembleData(equipmentMonitorVo, id);
        return equipmentMonitorVo;
    }

    @Override
    public List<VoltageSideMonitorVo> queryVoltageSideMonitor(Long id,Long projectId) throws IllegalAccessException, InstantiationException {
        Map<String, Object> nodeRelation = topology1Service.queryTopology(projectId, EnergyTypeDef.ELECTRIC);
        List<PointNode> nodes = JsonTransferUtils.transferList(getList(DATAINFO, nodeRelation), PointNode.class);
        List<LinkNode> links = JsonTransferUtils.transferList(getList(DATA_LINK, nodeRelation), LinkNode.class);
        VoltageSideMonitorVo down = getDown(nodes, links, id);
        VoltageSideMonitorVo top = getTop(nodes, links, id);
        return Arrays.asList(top, down);
    }

    @Override
    public LoadInfoVo queryLoadInfo(Long id,Long projectId) throws IllegalAccessException, InstantiationException {
        PowerTransformerDto powerTransformerVo = queryPowerTransformer(id);
        if (Objects.isNull(powerTransformerVo)) {
            return new LoadInfoVo();
        }
        Map<String, Object> nodeRelation = topology1Service.queryTopology(projectId, EnergyTypeDef.ELECTRIC);

        List<PointNode> nodes = JsonTransferUtils.transferList(getList(DATAINFO, nodeRelation), PointNode.class);
        List<LinkNode> links = JsonTransferUtils.transferList(getList(DATA_LINK, nodeRelation), LinkNode.class);

        LoadInfoVo top = getLoadInfoVo(nodes, links, id, true);
        if (Objects.isNull(top.getLoadRealTime())) {
            top = getLoadInfoVo(nodes, links, id, false);
        }
        List<HistoricalLoadVo> historicalLoadVos = queryHistoricalLoadVo(id);
        if (CollectionUtils.isNotEmpty(historicalLoadVos)) {
            top.setLoadHis(historicalLoadVos.get(0).getHismaxload());
            top.setLoadHisRate(historicalLoadVos.get(0).getHismaxloadrate());
        }
        top.setSectionLowLimit(calculateOptimalLoadRate(powerTransformerVo));
        top.setSectionUpperLimit(NUM_1);
        top.setRatedCapacity(powerTransformerVo.getRatedcapacity());
        List<SectionVo> sectionVos = handLoadInfoSection(top.getSectionLowLimit());
        top.setSections(sectionVos);
        return top;
    }

    private List<T> getList(String s, Map<String, Object> nodeRelation) {
        return (List) nodeRelation.get(s);
    }

    private LocalDateTime getThisMonthStartTime() {
        long l = System.currentTimeMillis();
        Long firstDayOfThisMonth = TimeUtil.getFirstDayOfThisMonth(l);
        return TimeUtil.timestamp2LocalDateTime(TimeUtil.getFirstTimeOfDay(firstDayOfThisMonth));
    }

    @Override
    public RadarChartInfo queryRadarChartInfo(Long id,Long projectId) throws IllegalAccessException, InstantiationException {
        PowerTransformerVo powerTransformerVo = queryPowerTransformer(id);
        if (Objects.isNull(powerTransformerVo)) {
            return new RadarChartInfo();
        }
        Map<String, Object> nodeRelation = topology1Service.queryTopology(projectId, EnergyTypeDef.ELECTRIC);
        List<PointNode> nodes = JsonTransferUtils.transferList(getList(DATAINFO, nodeRelation), PointNode.class);
        List<LinkNode> links = JsonTransferUtils.transferList(getList(DATA_LINK, nodeRelation), LinkNode.class);
        List<BaseVo> top = getTopOrDownNodes(id, nodes, links, true);
        List<BaseVo> down = getTopOrDownNodes(id, nodes, links, false);
        List<BaseVo> allNodes = new ArrayList<>(top);
        allNodes.addAll(down);
        //先查出变压器的上下端节点，然后查他们正向有功正向无功对应的quantityobjectid
        List<QuantityObject> quantityObjects = quantityObjectDao.queryQuantityObject(allNodes);
        quantityObjects = filterQuantityObject(quantityObjects);
        List<Long> quantityObjectId = quantityObjects.stream().map(QuantityObject::getId).collect(Collectors.toList());
        //查询物理量聚合数据表，其中取的是表计的差值
        List<QuantityAggregationData> quantityAggregationData;
        if (CollectionUtils.isEmpty(quantityObjectId)) {
            quantityAggregationData = new ArrayList<>();
        } else {
            quantityAggregationData = quantityAggregationDataDao.queryQuantityData(
                    getThisMonthStartTime(), TimeUtil.addDateTimeByCycle(getThisMonthStartTime(), AggregationCycle.ONE_MONTH, 1),
                    quantityObjectId, AggregationCycle.ONE_MONTH, AggregationType.STEP_ACCUMULATION);
        }
        //先全查，再根据是上端数据还是下端数据进行分组
        //分组是先根据处理上端数据，根据节点分组，然后根据正向有功无功进行分组，
        Map<Integer, List<QuantityAggregationData>> map = assembleQuantityAggregationData(quantityObjects, top, quantityAggregationData);

        Map<Integer, List<QuantityAggregationData>> mapDown = assembleQuantityAggregationData(quantityObjects, down, quantityAggregationData);

        //高压侧的正向有功电能
        Double topActive = getQuantityAggregationData(map, true);
        //高压侧的正向无功电能
        Double topForward = getQuantityAggregationData(map, false);
        //低压侧的正向有功电能
        Double downActive = getQuantityAggregationData(mapDown, true);
        //低压侧的正向无功电能
        Double downForward = getQuantityAggregationData(mapDown, false);
        RadarChartInfo radarChartInfo = new RadarChartInfo();
        assembleRadarChartInfo(topActive, topForward, downActive, downForward,
                radarChartInfo);
        return radarChartInfo;
    }

    /**
     * 获得上或下端有功无功电度值
     *
     * @param map
     * @param isTop
     * @return
     */
    public Double getQuantityAggregationData(Map<Integer, List<QuantityAggregationData>> map, Boolean isTop) {
        if (map.isEmpty()) {
            return null;
        }
        //正向有功电度
        List<QuantityAggregationData> active = map.get(getPositiveActiveElectric().getId());
        List<QuantityAggregationData> reactive = map.get(getReactivePowerElectric().getId());
        if (CollectionUtils.isNotEmpty(active) && Boolean.TRUE.equals(isTop)) {
            return active.get(0).getValue();
        }
        if (CollectionUtils.isNotEmpty(reactive) && Boolean.FALSE.equals(isTop)) {
            return reactive.get(0).getValue();
        }
        return null;
    }

    /**
     * 计算
     *
     * @param activeData
     * @param reactiveData
     * @return
     */
    public Double calculateApparentPower(Double activeData, Double reactiveData) {
        if (Objects.isNull(activeData) || Objects.isNull(reactiveData)) {
            return null;
        }

        Double aDouble = CommonUtils.calcDouble(activeData, activeData, EnumOperationType.MULTIPLICATION.getId());
        Double aDouble1 = CommonUtils.calcDouble(reactiveData, reactiveData, EnumOperationType.MULTIPLICATION.getId());
        Double aDouble2 = CommonUtils.calcDouble(aDouble, aDouble1, EnumOperationType.ADD.getId());
        if (Objects.nonNull(aDouble2)) {
            double sqrt = Math.sqrt(aDouble2);
            return CommonUtils.calcDouble(activeData, sqrt, EnumOperationType.DIVISION.getId());
        }
        return null;
    }

    private Integer queryCycle(Integer cycle) {
        if (Objects.equals(AggregationCycle.ONE_DAY, cycle)) {
            return AggregationCycle.ONE_HOUR;
        } else if (Objects.equals(AggregationCycle.ONE_MONTH, cycle)) {
            return AggregationCycle.ONE_DAY;
        } else {
            return AggregationCycle.ONE_MONTH;
        }
    }

    @Override
    public LoadRateVo queryLoadRateTrend(LoadRateParam param,Long projectId) throws IllegalAccessException, InstantiationException {
        PowerTransformerDto powerTransformerVo = queryPowerTransformer(param.getPowerTransformerId());
        LoadRateVo loadRateVo = new LoadRateVo();
        if (Objects.isNull(powerTransformerVo)) {
            return loadRateVo;
        }
        Double aDouble = calculateOptimalLoadRate(powerTransformerVo);
        List<SectionVo> sectionVos = handLoadInfoSection(aDouble);
        loadRateVo.setSections(sectionVos);
        //查询记得写了，这块是我自己算额
        Map<String, Object> nodeRelation = topology1Service.queryTopology(projectId, EnergyTypeDef.ELECTRIC);
        List<PointNode> nodes = JsonTransferUtils.transferList(getList(DATAINFO, nodeRelation), PointNode.class);
        List<LinkNode> links = JsonTransferUtils.transferList(getList(DATA_LINK, nodeRelation), LinkNode.class);
        List<DataLogData> loadRate = getLoadRate(param.getPowerTransformerId(), nodes, links,
                createQuantityDataBatchSearchVo(null, TimeUtil.localDateTime2timestamp(param.getStartTime()),
                        TimeUtil.localDateTime2timestamp(param.getEndTime()), queryCycle(param.getCycle()), Arrays
                                .asList(getPositiveActiveElectric(), getReactivePowerElectric()))
                , powerTransformerVo.getRatedcapacity());
        loadRateVo.setTimeValues(assembleTimeValue(loadRate, param.getStartTime(), param.getEndTime(), queryCycle(param.getCycle())));
        return loadRateVo;
    }

    @Override
    public List<BaseVo> getTopOrDownNodes(Long id, List<PointNode> nodes, List<LinkNode> links, Boolean isTop) {
        List<LinkNode> target = getLinkNodes(nodes, links, id, isTop);
        return getQueryNodes(target, nodes, isTop);
    }


    @Override
    public List<DataLogData> getLoadRate(Long id, List<PointNode> nodes, List<LinkNode> links, QuantityDataBatchSearchVo aggregationDataBatch, Double ratedCapacity) {
        List<BaseVo> top = getTopOrDownNodes(id, nodes, links, true);
        List<BaseVo> down = getTopOrDownNodes(id, nodes, links, false);
        List<BaseVo> allNodes = new ArrayList<>(top);
        allNodes.addAll(down);
        //先查出变压器的上下端节点，然后查他们正向有功正向无功对应的quantityobjectid
        List<QuantityObject> quantityObjects = quantityObjectDao.queryQuantityObject(allNodes);
        quantityObjects = filterQuantityObject(quantityObjects);
        List<Long> quantityObjectId = quantityObjects.stream().map(QuantityObject::getId).collect(Collectors.toList());
        List<QuantityAggregationData> quantityAggregationData;
        //查询物理量聚合数据表，其中取的是表计的差值
        if (CollectionUtils.isEmpty(quantityObjectId)) {
            quantityAggregationData = new ArrayList<>();
        } else {
            quantityAggregationData = quantityAggregationDataDao.queryQuantityData(TimeUtil.timestamp2LocalDateTime(aggregationDataBatch.getStartTime())
                    , TimeUtil.timestamp2LocalDateTime(aggregationDataBatch.getEndTime()), quantityObjectId, aggregationDataBatch.getAggregationCycle(), AggregationType.STEP_ACCUMULATION);

        }
        //先全查，再根据是上端数据还是下端数据进行分组
        //分组是先根据处理上端数据，根据节点分组，然后根据正向有功无功进行分组，
        Map<Integer, List<QuantityAggregationData>> map = assembleQuantityAggregationData(quantityObjects, top, quantityAggregationData);
        if (map.isEmpty()) {
            map = assembleQuantityAggregationData(quantityObjects, down, quantityAggregationData);
        }
        List<Long> timeRange = TimeUtil.getTimeRange(aggregationDataBatch.getStartTime(), aggregationDataBatch.getEndTime(),
                aggregationDataBatch.getAggregationCycle());
        List<DataLogData> dataLogData = calculateQuantityAggregationData(map, timeRange, aggregationDataBatch.getAggregationCycle());
        divisionWithRatedCapacity(dataLogData, ratedCapacity);
        return dataLogData;
    }

    private void divisionWithRatedCapacity(List<DataLogData> dataLogData, Double ratedCapacity) {
        for (DataLogData data : dataLogData) {
            data.setValue(CommonUtils.calcDouble(data.getValue(), ratedCapacity, EnumOperationType.DIVISION.getId()));
        }
    }

    /**
     * 查询物理量聚合数据
     *
     * @param aggregationDataBatch
     * @param quantityObjectId
     * @return
     */
    private List<QuantityAggregationData> queryAggregationDataBatch(QuantityDataBatchSearchVo aggregationDataBatch, List<Long> quantityObjectId) {
        if (CollectionUtils.isEmpty(quantityObjectId)) {
            return Collections.emptyList();
        }
        return quantityAggregationDataDao.queryQuantityData(
                TimeUtil.timestamp2LocalDateTime(aggregationDataBatch.getStartTime())
                , TimeUtil.timestamp2LocalDateTime(aggregationDataBatch.getEndTime()),
                quantityObjectId,
                aggregationDataBatch.getAggregationCycle(),
                AggregationType.STEP_ACCUMULATION);
    }

    /**
     * 分批处理数据
     *
     * @param powerTransformerDtos
     * @param aggregationDataBatch
     * @param projectId
     * @return
     */
    private Map<Long, List<DataLogData>> assembleDataEachTime(List<PowerTransformerDto> powerTransformerDtos
            , QuantityDataBatchSearchVo aggregationDataBatch, Long projectId) {
        if (CollectionUtils.isEmpty(powerTransformerDtos)) {
            return Collections.emptyMap();
        }
        Map<Long, List<DataLogData>> result = new HashMap<>();
        List<PowerTransformerDto> idEach = new ArrayList<>();
        for (PowerTransformerDto powerTransformerDto : powerTransformerDtos) {
            if (idEach.size() >= EACH) {
                //处理数据，分批查完算完返回
                Map<Long, List<DataLogData>> longListMap = queryAndCalculateDataEachTime(idEach,
                        aggregationDataBatch, projectId);
                result.putAll(longListMap);
                idEach.clear();
            }
            idEach.add(powerTransformerDto);
        }
        //一开始ideach就小于each以及剩下的
        if (CollectionUtils.isNotEmpty(idEach)) {
            //和上面一样的方法
            Map<Long, List<DataLogData>> longListMap = queryAndCalculateDataEachTime(idEach,
                    aggregationDataBatch, projectId);
            result.putAll(longListMap);
        }
        return result;
    }

    /**
     * 过滤出正向有功或者无功
     *
     * @param quantityObjects
     * @return
     */
    private List<QuantityObject> filterQuantityObject(List<QuantityObject> quantityObjects) {
        //过滤正向有功和正向无功
        return quantityObjects.stream().filter(quantityObject -> (Objects.equals(quantityObject.getEnergytype(), getPositiveActiveElectric().getEnergytype())
                        && Objects.equals(getPositiveActiveElectric().getQuantitytype(), quantityObject.getQuantitytype())
                        && Objects.equals(getPositiveActiveElectric().getQuantitycategory(), quantityObject.getQuantitycategory())
                        && Objects.equals(getPositiveActiveElectric().getFrequency(), quantityObject.getFrequency())
                        && Objects.equals(getPositiveActiveElectric().getPhasor(), quantityObject.getPhasor()))
                        || (Objects.equals(quantityObject.getEnergytype(), getReactivePowerElectric().getEnergytype())
                        && Objects.equals(getReactivePowerElectric().getQuantitytype(), quantityObject.getQuantitytype())
                        && Objects.equals(getReactivePowerElectric().getQuantitycategory(), quantityObject.getQuantitycategory())
                        && Objects.equals(getReactivePowerElectric().getFrequency(), quantityObject.getFrequency())
                        && Objects.equals(getReactivePowerElectric().getPhasor(), quantityObject.getPhasor())))
                .collect(Collectors.toList());
    }

    /**
     * 批量处理每次数据
     *
     * @param powerTransformerDtos
     * @param aggregationDataBatch
     * @param projectId
     * @return
     */
    private Map<Long, List<DataLogData>> queryAndCalculateDataEachTime(List<PowerTransformerDto> powerTransformerDtos,
                                                                       QuantityDataBatchSearchVo aggregationDataBatch, Long projectId) {
        List<Long> ids = powerTransformerDtos.stream().map(PowerTransformerDto::getId).collect(Collectors.toList());
        //先查上端的关联节点
        List<PipeNetworkConnectionModel> topOrDownBatch = pipeNetworkConnectionModelDao.getTopOrDownBatch(ids, true, projectId);
        List<BaseVo> topNodes = topOrDownBatch.stream().map(model -> new BaseVo(model.getInflowId(), model.getInflowLabel()))
                .distinct().collect(Collectors.toList());
        List<QuantityObject> quantityObjects = quantityObjectDao.queryQuantityObject(topNodes);
        quantityObjects = filterQuantityObject(quantityObjects);
        List<Long> quantityObjectId = quantityObjects.stream().map(QuantityObject::getId).collect(Collectors.toList());
        //查询物理量聚合数据表
        List<QuantityAggregationData> quantityAggregationData =
                queryAggregationDataBatch(aggregationDataBatch, quantityObjectId);
        Map<Long, List<DataLogData>> map = new HashMap<>();
        List<PowerTransformerDto> useDown = new ArrayList<>();
        for (PowerTransformerDto powerTransformerDto : powerTransformerDtos) {
            //根据传入时间来拼接对应返回值
            List<DataLogData> dataLogData = assembleDataLogDataWithSingleNodes(powerTransformerDto, topOrDownBatch,
                    true, quantityObjects, quantityAggregationData,
                    aggregationDataBatch);
            if (Boolean.FALSE.equals(checkIfUseDown(dataLogData))) {
                map.put(powerTransformerDto.getId(), dataLogData);
            } else {
                useDown.add(powerTransformerDto);
            }
        }
        if (CollectionUtils.isEmpty(useDown)) {
            return map;
        }
        assembleMapDownData(useDown, aggregationDataBatch, map, projectId);
        return map;
    }

    @Override
    public Map<Long, List<DataLogData>> queryLoadRateBatch(List<PowerTransformerDto> powerTransformerDtos,
                                                           QuantityDataBatchSearchVo aggregationDataBatch, Long projectId) {
        return assembleDataEachTime(powerTransformerDtos, aggregationDataBatch, projectId);

    }

    /**
     * 处理下端的数据
     *
     * @param useDown
     * @param aggregationDataBatch
     * @param map
     * @param projectId
     */
    private void assembleMapDownData(List<PowerTransformerDto> useDown,
                                     QuantityDataBatchSearchVo aggregationDataBatch, Map<Long, List<DataLogData>> map, Long projectId) {
        //上端有的没有数据就查下端的
        List<Long> idUseDown = useDown.stream().map(PowerTransformerDto::getId).collect(Collectors.toList());
        //查下端的关联节点
        List<PipeNetworkConnectionModel> topOrDownBatch = pipeNetworkConnectionModelDao.getTopOrDownBatch(idUseDown, false, projectId);
        List<BaseVo> downNodesBatch = topOrDownBatch.stream().map(model -> new BaseVo(model.getOutflowId(), model.getOutflowLabel()))
                .distinct().collect(Collectors.toList());
        List<QuantityObject> quantityObjectDown = quantityObjectDao.queryQuantityObject(downNodesBatch);
        quantityObjectDown = filterQuantityObject(quantityObjectDown);
        List<Long> quantityObjectIdDown = quantityObjectDown.stream().map(QuantityObject::getId).collect(Collectors.toList());
        List<QuantityAggregationData> quantityAggregationDataDown = queryAggregationDataBatch(aggregationDataBatch,
                quantityObjectIdDown);
        for (PowerTransformerDto powerTransformerDto : useDown) {
            //根据传入时间来拼接对应返回值
            List<DataLogData> dataLogData = assembleDataLogDataWithSingleNodes(powerTransformerDto, topOrDownBatch,
                    false, quantityObjectDown, quantityAggregationDataDown,
                    aggregationDataBatch);
            map.put(powerTransformerDto.getId(), dataLogData);

        }

    }

    /**
     * 是否需要查询下端节点的数据
     *
     * @param dataLogData
     * @return
     */
    private Boolean checkIfUseDown(List<DataLogData> dataLogData) {
        if (CollectionUtils.isEmpty(dataLogData)) {
            return true;
        }
        // 换成找到任意不为空的
        DataLogData collect = dataLogData.stream().filter(dataLogData1 -> Objects.nonNull(dataLogData1.getValue()))
                .findAny().orElse(null);
        return Objects.isNull(collect);
    }

    /**
     * 每次处理单台变压器的数据
     *
     * @param powerTransformerDto
     * @param topOrDownBatch
     * @param isTop
     * @param quantityObjects
     * @param quantityAggregationData
     * @param aggregationDataBatch
     * @return
     */
    private List<DataLogData> assembleDataLogDataWithSingleNodes(PowerTransformerDto powerTransformerDto, List<PipeNetworkConnectionModel> topOrDownBatch,
                                                                 Boolean isTop, List<QuantityObject> quantityObjects, List<QuantityAggregationData> quantityAggregationData,
                                                                 QuantityDataBatchSearchVo aggregationDataBatch) {
        List<BaseVo> top;
        if (Boolean.FALSE.equals(isTop)) {
            top = topOrDownBatch.stream().filter(model -> Objects.equals(powerTransformerDto.getId(), model.getInflowId()))
                    .map(model -> new BaseVo(model.getOutflowId(), model.getOutflowLabel()))
                    .collect(Collectors.toList());
        } else {
            top = topOrDownBatch.stream().filter(model -> Objects.equals(powerTransformerDto.getId(), model.getOutflowId()))
                    .map(model -> new BaseVo(model.getInflowId(), model.getInflowLabel()))
                    .collect(Collectors.toList());
        }
        //先查出变压器的上下端节点，然后查他们正向有功正向无功对应的quantityobjectid
        //查询物理量聚合数据表，其中取的是表计的差值
        //先全查，再根据是上端数据还是下端数据进行分组
        //分组是先根据处理上端数据，根据节点分组，然后根据正向有功无功进行分组，
        Map<Integer, List<QuantityAggregationData>> map = assembleQuantityAggregationData(quantityObjects, top, quantityAggregationData);
        List<Long> timeRange = TimeUtil.getTimeRange(aggregationDataBatch.getStartTime(), aggregationDataBatch.getEndTime(),
                aggregationDataBatch.getAggregationCycle());
        return calculateQuantityAggregationData(map, timeRange, aggregationDataBatch.getAggregationCycle());
    }

    /**
     * 按传入的时间列表计算结果
     *
     * @param map
     * @param timeRange
     * @param cycle
     * @return
     */
    private List<DataLogData> calculateQuantityAggregationData(Map<Integer, List<QuantityAggregationData>> map, List<Long> timeRange, Integer cycle) {
        List<QuantityAggregationData> quantityAggregationData = map.get(getPositiveActiveElectric().getId());
        List<QuantityAggregationData> quantityAggregationData1 = map.get(getReactivePowerElectric().getId());
        if (CollectionUtils.isEmpty(quantityAggregationData)) {
            quantityAggregationData = new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(quantityAggregationData1)) {
            quantityAggregationData1 = new ArrayList<>();
        }
        List<DataLogData> result = new ArrayList<>();
        Map<Long, List<QuantityAggregationData>> mapActive = quantityAggregationData.stream()
                .collect(Collectors.groupingBy(QuantityAggregationData::getLogtime));
        Map<Long, List<QuantityAggregationData>> mapReactive = quantityAggregationData1.stream()
                .collect(Collectors.groupingBy(QuantityAggregationData::getLogtime));
        //外面还有一层节点，还是用map,不用filter过滤
        for (Long time : timeRange) {
            Double active = null;
            Double reactive = null;
            if (!mapActive.isEmpty()) {
                List<QuantityAggregationData> collect = mapActive.get(time);
                if (CollectionUtils.isNotEmpty(collect)) {
                    active = collect.get(0).getValue();
                }
            }
            if (CollectionUtils.isNotEmpty(quantityAggregationData1)) {
                List<QuantityAggregationData> collect = mapReactive.get(time);
                if (CollectionUtils.isNotEmpty(collect)) {
                    reactive = collect.get(0).getValue();
                }
            }
            Double aDouble = calculateLoadRate(active, reactive, cycle, time);
            result.add(new DataLogData(time, aDouble));
        }
        return result;
    }

    /**
     * 计算负载
     *
     * @param activeData
     * @param reactiveData
     * @return
     */
    private Double calculateSquareSumAndSqrt(Double activeData, Double reactiveData) {
        if (Objects.nonNull(activeData) && Objects.nonNull(reactiveData)) {
            Double value1 = activeData * activeData;
            Double value2 = reactiveData * reactiveData;
            double sum = value1 + value2;
            return Math.sqrt(sum);
        } else {
            return null;
        }
    }

    private Double calculateLoadRate(Double activeData, Double reactiveData, Integer queryCycle, Long time) {
        // 抽个方法 CommonUtils判断比较多
        Double aDouble = calculateSquareSumAndSqrt(activeData, reactiveData);
        return calculateLoadRateWith(aDouble, queryCycle, time);

    }

    private Double calculateLoad(Double activeData, Double reactiveData) {
        if (Objects.nonNull(activeData) && Objects.nonNull(reactiveData)) {
            Double value1 = CommonUtils.calcDouble(activeData, activeData, EnumOperationType.MULTIPLICATION.getId());
            Double value2 = CommonUtils.calcDouble(reactiveData, reactiveData, EnumOperationType.MULTIPLICATION.getId());
            Double aDouble2 = CommonUtils.calcDouble(value1, value2, EnumOperationType.ADD.getId());
            return Math.sqrt(aDouble2);
        }

        return null;
    }

    private Double calculateLoadRateWith(Double value, Integer queryCycle, Long time) {
        return CommonUtils.calcDouble(value,
                getHoursByCycle(time, TimeUtil.addDateTimeByCycle(time, queryCycle, 1)),
                EnumOperationType.DIVISION.getId());

    }

    /**
     * 拼接返回值
     *
     * @param quantityObjects
     * @param nodes
     * @param quantityAggregationData
     * @return
     */
    public Map<Integer, List<QuantityAggregationData>> assembleQuantityAggregationData(List<QuantityObject> quantityObjects, List<BaseVo> nodes,
                                                                                       List<QuantityAggregationData> quantityAggregationData) {
        Map<Integer, List<QuantityAggregationData>> baseVoMapMap = new HashMap<>();
        for (BaseVo baseVo : nodes) {
            //正向有功
            List<Long> activeList = fileQuantityData(quantityObjects, baseVo, getPositiveActiveElectric());
            //正向无功
            List<Long> reactiveList = fileQuantityData(quantityObjects, baseVo, getReactivePowerElectric());
            if (CollectionUtils.isNotEmpty(activeList) && CollectionUtils.isNotEmpty(reactiveList)) {
                assembleMap(quantityAggregationData,
                        baseVoMapMap, activeList, getPositiveActiveElectric().getId());
                assembleMap(quantityAggregationData,
                        baseVoMapMap, reactiveList, getReactivePowerElectric().getId());
                if (CollectionUtils.isEmpty(baseVoMapMap.get(getPositiveActiveElectric().getId()))
                        || CollectionUtils.isEmpty(baseVoMapMap.get(getReactivePowerElectric().getId()))) {
                    baseVoMapMap = new HashMap<>();
                }
            }

            if (!baseVoMapMap.isEmpty()) {
                break;
            }
        }
        return baseVoMapMap;
    }

    private void assembleMap(List<QuantityAggregationData> quantityAggregationData, Map<Integer,
            List<QuantityAggregationData>> baseVoMapMap, List<Long> reactiveList, Integer id) {
        HashSet<Long> longs = new HashSet<>(reactiveList);
        //findany替代tolist
        List<QuantityAggregationData> aggregationData = quantityAggregationData.stream()
                .filter(quantityAggregationData1 -> longs.contains(quantityAggregationData1.getQuantityobject_id())
                        && Objects.nonNull(quantityAggregationData1.getValue()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(aggregationData)) {
            //随便取个有值的Quantityobject对应的
            Long quantityobjectId = aggregationData.get(0).getQuantityobject_id();
            List<QuantityAggregationData> collect = aggregationData.stream().filter(quantityAggregationData1 ->
                            Objects.equals(quantityAggregationData1.getQuantityobject_id(), quantityobjectId))
                    .collect(Collectors.toList());
            baseVoMapMap.put(id, collect);

        }
    }

    private List<Long> fileQuantityData(List<QuantityObject> quantityObjects, BaseVo baseVo, QuantitySearchVo quantitySearchVo) {
        return quantityObjects.stream().filter(quantityObject -> Objects.equals(quantityObject.getEnergytype(), quantitySearchVo.getEnergytype())
                        && Objects.equals(quantitySearchVo.getQuantitytype(), quantityObject.getQuantitytype())
                        && Objects.equals(quantitySearchVo.getQuantitycategory(), quantityObject.getQuantitycategory())
                        && Objects.equals(quantitySearchVo.getFrequency(), quantityObject.getFrequency())
                        && Objects.equals(quantitySearchVo.getPhasor(), quantityObject.getPhasor())
                        && Objects.equals(baseVo.getModelLabel(), quantityObject.getMonitoredlabel())
                        && Objects.equals(baseVo.getId(), quantityObject.getMonitoredid()))
                .map(QuantityObject::getId)
                .distinct().collect(Collectors.toList());
    }


    private Double getHoursByCycle(Long st, Long et) {
        List<Long> timeRange = TimeUtil.getTimeRange(st, et, AggregationCycle.ONE_HOUR);
        return Double.valueOf(String.valueOf(timeRange.size()));
    }


    /**
     * 根据节点查询出对应的定时记录，然后计算出差值
     *
     * @param aggregationDataBatch
     * @return
     */
    public Map<Integer, Map<BaseVo, List<DatalogValue>>> queryDataLogByNodes(QuantityDataBatchSearchVo aggregationDataBatch) {
        Map<Integer, List<TrendDataVo>> map = quantityManageService.queryDataLogBatch(aggregationDataBatch);
        Map<Integer, Map<BaseVo, List<DatalogValue>>> result = new HashMap<>();
        for (Map.Entry<Integer, List<TrendDataVo>> entry : map.entrySet()) {
            Integer key = entry.getKey();
            List<TrendDataVo> value = entry.getValue();
            Map<BaseVo, List<TrendDataVo>> voListMap = value.stream().collect(Collectors.groupingBy(realTimeValue ->
                    new BaseVo(realTimeValue.getMonitoredid(), realTimeValue.getMonitoredlabel())));
            Map<BaseVo, List<DatalogValue>> dataLog = getDataLog(voListMap);
            Map<BaseVo, List<DatalogValue>> baseVoDoubleMap = calculateDiff(dataLog);
            result.put(key, baseVoDoubleMap);
        }
        return result;
    }

    /**
     * 计算表计差值
     *
     * @param map
     * @return
     */
    private Map<BaseVo, List<DatalogValue>> calculateDiff(Map<BaseVo, List<DatalogValue>> map) {
        Map<BaseVo, List<DatalogValue>> result = new HashMap<>();
        for (Map.Entry<BaseVo, List<DatalogValue>> entry : map.entrySet()) {
            BaseVo key = entry.getKey();
            List<DatalogValue> value = entry.getValue();
            if (CollectionUtils.isEmpty(value)) {
                result.put(key, null);
                continue;
            }
            List<DatalogValue> dataLogDataList = new ArrayList<>();
            for (int i = 0; i < value.size() - 1; i++) {
                DatalogValue data = value.get(i);
                DatalogValue dataNext = value.get(i + 1);
                DatalogValue e = new DatalogValue();
                e.setTime(data.getTime());
                e.setValue(CommonUtils.calcDouble(dataNext.getValue(),
                        data.getValue(), EnumOperationType.SUBTRACT.getId()));
                dataLogDataList.add(e);
            }

            result.put(key, dataLogDataList);
        }
        return result;
    }

    private Map<BaseVo, List<DatalogValue>> getDataLog(Map<BaseVo, List<TrendDataVo>> voListMap) {
        Map<BaseVo, List<DatalogValue>> map = new HashMap<>();
        for (Map.Entry<BaseVo, List<TrendDataVo>> entry : voListMap.entrySet()) {
            List<TrendDataVo> value = entry.getValue();
            List<DatalogValue> nonValueDataLog = getNonValueDataLog(value);
            map.put(entry.getKey(), nonValueDataLog);
        }
        return map;
    }

    private List<HistoricalLoadVo> queryHistoricalLoadVo(Long transFormerId) {
        QueryConditionBuilder<BaseEntity> condition = new QueryConditionBuilder<>(LABEL);
        condition.where(OBJECT_ID, ConditionBlock.OPERATOR_EQ, transFormerId);
        condition.where(ColumnDef.AGGREGATION_CYCLE, ConditionBlock.OPERATOR_EQ, AggregationCycle.ONE_HOUR);
        return modelServiceUtils.query(condition.build(), HistoricalLoadVo.class);
    }

    private List<TimeValue> assembleTimeValue(List<DataLogData> historicalLoadVos, LocalDateTime st, LocalDateTime et, Integer cycle) {
        List<Long> timeRange = TimeUtil.getTimeRange(TimeUtil.localDateTime2timestamp(st), TimeUtil.localDateTime2timestamp(et), cycle);
        List<TimeValue> timeValues = new ArrayList<>();
        for (Long time : timeRange) {
            TimeValue timeValue = new TimeValue();
            timeValue.setLogTime(time);
            timeValues.add(timeValue);
            if (CollectionUtils.isEmpty(historicalLoadVos)) {
                continue;
            }

            List<DataLogData> collect = historicalLoadVos.stream().filter(it -> Objects.equals(time, it.getTime())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                DataLogData historicalLoadVo = collect.get(0);
                timeValue.setValue(historicalLoadVo.getValue());
            }
        }
        return timeValues;
    }

    private void assembleRadarChartInfo(Double topActive, Double topForward, Double downActive, Double downForward,
                                        RadarChartInfo radarChartInfo) {
        Double top = calculateApparentPower(topActive, topForward);
        radarChartInfo.setPowerFactor(top);
        if (Objects.isNull(top)) {
            radarChartInfo.setPowerFactor(calculateApparentPower(downActive, downForward));
        }
        radarChartInfo.setConversionEfficiency(CommonUtils.calcDouble(downActive, topActive, EnumOperationType.DIVISION.getId()));
    }


    /**
     * 获得第一个不为null值的定时任务的数据
     *
     * @return
     */
    private List<DatalogValue> getNonValueDataLog(List<TrendDataVo> dataVoList) {
        if (CollectionUtils.isEmpty(dataVoList)) {
            return Collections.emptyList();
        }
        for (TrendDataVo trendDataVo : dataVoList) {
            if (CollectionUtils.isNotEmpty(trendDataVo.getDataList()) && Objects.nonNull(trendDataVo.getDataList().get(0).getValue())) {
                return trendDataVo.getDataList();
            }
        }
        return dataVoList.get(0).getDataList();
    }

    public QuantityDataBatchSearchVo createQuantityDataBatchSearchVo(List<BaseVo> deviceNodes, Long st, Long et, Integer cycle, List<QuantitySearchVo> quantitySearchVo) {
        QuantityDataBatchSearchVo aggregationDataBatch = new QuantityDataBatchSearchVo();
        aggregationDataBatch.setDataTypeId(EnumDataTypeId.REALTIME.getId());
        aggregationDataBatch.setStartTime(st);
        aggregationDataBatch.setEndTime(et);
        aggregationDataBatch.setQuantitySettings((quantitySearchVo));
        aggregationDataBatch.setNodes(deviceNodes);
        aggregationDataBatch.setAggregationCycle(cycle);
        return aggregationDataBatch;
    }

    private List<SectionVo> handLoadInfoSection(Double limit) {
        if (Objects.isNull(limit)) {
            return Collections.emptyList();
        }
        Double aDouble = CommonUtils.calcDouble(limit, limit, EnumOperationType.MULTIPLICATION.getId());
        SectionVo sectionVo = new SectionVo(ZERO, aDouble);
        Double aDouble1 = CommonUtils.calcDouble(NUM, aDouble, EnumOperationType.MULTIPLICATION.getId());
        SectionVo sectionVo1 = new SectionVo(aDouble, aDouble1);
        SectionVo sectionVo2 = new SectionVo(aDouble1, NUM_1);
        SectionVo sectionVo3 = new SectionVo(NUM_1, MAX);
        return Arrays.asList(sectionVo, sectionVo1, sectionVo2, sectionVo3);

    }

    private LoadInfoVo getLoadInfoVo(List<PointNode> nodes, List<LinkNode> links, Long id, Boolean isTop) {
        List<LinkNode> target = getLinkNodes(nodes, links, id, isTop);
        Long st = handleTime();
        Long et = TimeUtil.addDateTimeByCycle(st, AggregationCycle.ONE_MINUTE, 1);
        Map<Integer, List<RealTimeValue>> integerListMap = quantityManageService.queryRealTimeBath(
                createQuantityDataBatchSearchVoWithRealTime(getQueryNodes(target, nodes, isTop), st, et,
                        Arrays.asList(getActivePower(), getReactivePower())));
        return assembleLoadInfoVo(integerListMap);
    }

    public List<BaseVo> getTopAndDownNodesBatch(List<PointNode> nodes, List<LinkNode> links, List<Long> ids) {
        List<LinkNode> target = new ArrayList<>();
        for (PointNode pointNode : nodes) {
            if (Objects.equals(pointNode.getNodeLabel(), NodeLabelDef.POWER_TRANS_FORMER) && ids.contains(pointNode.getNodeId())) {
                target.addAll(links.stream().filter(linkNode1 -> Objects.equals(linkNode1.getTarget(), pointNode.getName()))
                        .collect(Collectors.toList()));
                target.addAll(links.stream().filter(linkNode1 -> Objects.equals(linkNode1.getSource(), pointNode.getName()))
                        .collect(Collectors.toList()));
            }
        }
        List<BaseVo> result = new ArrayList<>();
        for (LinkNode linkNode : target) {
            List<PointNode> collect = nodes.stream().filter(pointNode -> Objects.equals(pointNode.getName(), linkNode.getSource()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                result.add(new BaseVo(collect.get(0).getNodeId(), collect.get(0).getNodeLabel()));
            }
            List<PointNode> collect1 = nodes.stream().filter(pointNode -> Objects.equals(pointNode.getName(), linkNode.getTarget()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect1)) {
                result.add(new BaseVo(collect1.get(0).getNodeId(), collect1.get(0).getNodeLabel()));
            }


        }
        return result.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 根据拓扑配置获得上下端节点
     *
     * @param nodes
     * @param links
     * @param ids
     * @param isTop
     * @return
     */
    public List<BaseVo> getTopOrDownNodesBatch(List<PointNode> nodes, List<LinkNode> links, List<Long> ids, Boolean isTop) {
        List<LinkNode> target = new ArrayList<>();
        for (PointNode pointNode : nodes) {
            if (Objects.equals(pointNode.getNodeLabel(), NodeLabelDef.POWER_TRANS_FORMER) && ids.contains(pointNode.getNodeId())) {
                if (Boolean.TRUE.equals(isTop)) {
                    target.addAll(links.stream().filter(linkNode1 -> Objects.equals(linkNode1.getTarget(), pointNode.getName()))
                            .collect(Collectors.toList()));
                } else {
                    target.addAll(links.stream().filter(linkNode1 -> Objects.equals(linkNode1.getSource(), pointNode.getName()))
                            .collect(Collectors.toList()));
                }


            }
        }
        List<BaseVo> result = new ArrayList<>();
        handleResult(nodes, isTop, target, result);
        return result.stream().distinct().collect(Collectors.toList());
    }

    private void handleResult(List<PointNode> nodes, Boolean isTop, List<LinkNode> target, List<BaseVo> result) {
        for (LinkNode linkNode : target) {
            if (Boolean.TRUE.equals(isTop)) {
                List<PointNode> collect = nodes.stream().filter(pointNode -> Objects.equals(pointNode.getName(), linkNode.getSource()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    result.add(new BaseVo(collect.get(0).getNodeId(), collect.get(0).getNodeLabel()));
                }
            } else {
                List<PointNode> collect1 = nodes.stream().filter(pointNode -> Objects.equals(pointNode.getName(), linkNode.getTarget()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect1)) {
                    result.add(new BaseVo(collect1.get(0).getNodeId(), collect1.get(0).getNodeLabel()));
                }
            }
        }
    }

    private List<LinkNode> getLinkNodes(List<PointNode> nodes, List<LinkNode> links, Long id, Boolean isTop) {
        List<LinkNode> target = new ArrayList<>();
        for (PointNode pointNode : nodes) {
            if (Objects.equals(pointNode.getNodeLabel(), NodeLabelDef.POWER_TRANS_FORMER) && Objects.equals(id, pointNode.getNodeId())) {
                //变压器的上端
                if (Boolean.TRUE.equals(isTop)) {
                    target = links.stream().filter(linkNode1 -> Objects.equals(linkNode1.getTarget(), pointNode.getName()))
                            .collect(Collectors.toList());
                } else {
                    target = links.stream().filter(linkNode1 -> Objects.equals(linkNode1.getSource(), pointNode.getName()))
                            .collect(Collectors.toList());
                }

            }
        }
        return target;
    }

    private LoadInfoVo assembleLoadInfoVo(Map<Integer, List<RealTimeValue>> integerListMap) {
        LoadInfoVo vo = new LoadInfoVo();
        List<RealTimeValue> realTimeActive = realTimeValueList(integerListMap, getActivePower().getId());
        List<RealTimeValue> realTimeReactive = realTimeValueList(integerListMap, getReactivePower().getId());
        Map<Long, List<RealTimeValue>> active = realTimeActive.stream()
                .collect(Collectors.groupingBy(RealTimeValue::getDeviceId));
        Map<Long, List<RealTimeValue>> reActive = realTimeReactive.stream().collect(Collectors.groupingBy(RealTimeValue::getDeviceId));
        for (Map.Entry<Long, List<RealTimeValue>> entry : active.entrySet()) {
            List<RealTimeValue> realTimeValues = reActive.get(entry.getKey());
            if (CollectionUtils.isNotEmpty(realTimeValues)) {
                Double aDouble = calculateLoad(entry.getValue().get(0).getValue(), realTimeValues.get(0).getValue());
                vo.setLoadRealTime(aDouble);
                return vo;
            }
        }
        return vo;
    }

    private VoltageSideMonitorVo getDown(List<PointNode> nodes, List<LinkNode> links, Long id) {
        List<LinkNode> source = new ArrayList<>();
        for (PointNode pointNode : nodes) {
            if (Objects.equals(pointNode.getNodeLabel(), NodeLabelDef.POWER_TRANS_FORMER) && Objects.equals(id, pointNode.getNodeId())) {
                //变压器的下端
                source = links.stream().filter(linkNode1 -> Objects.equals(linkNode1.getSource(), pointNode.getName())).collect(Collectors.toList());
            }
        }
        Long st = handleTime();
        Long et = TimeUtil.addDateTimeByCycle(st, AggregationCycle.ONE_MINUTE, 1);
        Map<Integer, List<RealTimeValue>> integerListMap = quantityManageService.queryRealTimeBath(
                createQuantityDataBatchSearchVoWithRealTime(getQueryNodes(source, nodes, false), st, et,
                        Arrays.asList(getUab(), getUbc(), getUac(), getIa(), getIb(), getIc())));
        return assembleVoltageSideMonitorVo(integerListMap, false);
    }

    private VoltageSideMonitorVo assembleVoltageSideMonitorVo(Map<Integer, List<RealTimeValue>> integerListMap, Boolean isTop) {
        VoltageSideMonitorVo vo = new VoltageSideMonitorVo();
        vo.setUab(realTimeValue(integerListMap, getUab().getId()));
        vo.setUbc(realTimeValue(integerListMap, getUbc().getId()));
        vo.setUca(realTimeValue(integerListMap, getUac().getId()));
        vo.setIa(realTimeValue(integerListMap, getIa().getId()));
        vo.setIb(realTimeValue(integerListMap, getIb().getId()));
        vo.setIc(realTimeValue(integerListMap, getIc().getId()));
        if (Boolean.TRUE.equals(isTop)) {
            vo.setSide(HIGH);
        } else {
            vo.setSide(LOW);
        }
        return vo;
    }

    private List<BaseVo> getQueryNodes(List<LinkNode> source, List<PointNode> nodes, Boolean isTop) {
        if (CollectionUtils.isEmpty(source) || CollectionUtils.isEmpty(nodes)) {
            return Collections.emptyList();
        }
        List<BaseVo> result = new ArrayList<>();
        for (LinkNode linkNode : source) {
            if (Boolean.TRUE.equals(isTop)) {
                List<PointNode> collect = nodes.stream().filter(pointNode -> Objects.equals(pointNode.getName(), linkNode.getSource()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    result.add(new BaseVo(collect.get(0).getNodeId(), collect.get(0).getNodeLabel()));
                }
            } else {
                List<PointNode> collect = nodes.stream().filter(pointNode -> Objects.equals(pointNode.getName(), linkNode.getTarget()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    result.add(new BaseVo(collect.get(0).getNodeId(), collect.get(0).getNodeLabel()));
                }
            }

        }
        return result;
    }

    private VoltageSideMonitorVo getTop(List<PointNode> nodes, List<LinkNode> links, Long id) {
        List<LinkNode> target = new ArrayList<>();
        for (PointNode pointNode : nodes) {
            if (Objects.equals(pointNode.getNodeLabel(), NodeLabelDef.POWER_TRANS_FORMER) && Objects.equals(id, pointNode.getNodeId())) {
                //变压器的上端
                target = links.stream().filter(linkNode1 -> Objects.equals(linkNode1.getTarget(), pointNode.getName())).collect(Collectors.toList());

            }
        }
        Long st = handleTime();
        Long et = TimeUtil.addDateTimeByCycle(st, AggregationCycle.ONE_MINUTE, 1);
        Map<Integer, List<RealTimeValue>> integerListMap = quantityManageService.queryRealTimeBath(
                createQuantityDataBatchSearchVoWithRealTime(getQueryNodes(target, nodes, true), st, et,
                        Arrays.asList(getUab(), getUbc(), getUac(), getIa(), getIb(), getIc())));
        return assembleVoltageSideMonitorVo(integerListMap, true);
    }


    private void assembleData(EquipmentMonitorVo equipmentMonitorVo, Long id) {
        Long st = handleTime();
        Long et = TimeUtil.addDateTimeByCycle(st, AggregationCycle.ONE_MINUTE, 1);
        Map<Integer, List<RealTimeValue>> integerListMap = quantityManageService.queryRealTimeBath(
                createQuantityDataBatchSearchVoWithRealTime(Collections.singletonList(new BaseVo(id, NodeLabelDef.POWER_TRANS_FORMER)), st, et,
                        Arrays.asList(getAPowerTransformer(), getBPowerTransformer(),
                                getCPowerTransformer())));
        equipmentMonitorVo.setTemperatureA(realTimeValue(integerListMap, getAPowerTransformer().getId()));
        equipmentMonitorVo.setTemperatureB(realTimeValue(integerListMap, getBPowerTransformer().getId()));
        equipmentMonitorVo.setTemperatureC(realTimeValue(integerListMap, getCPowerTransformer().getId()));
    }

    private Double realTimeValue(Map<Integer, List<RealTimeValue>> integerListMapOfTemp, Integer id) {
        if (Objects.nonNull(integerListMapOfTemp)) {
            List<RealTimeValue> realTimeValues = integerListMapOfTemp.get(id);
            List<RealTimeValue> collect = realTimeValues.stream().filter(realTimeValue -> Objects.nonNull(realTimeValue.getValue())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                return collect.get(0).getValue();
            }

        }
        return null;
    }

    private List<RealTimeValue> realTimeValueList(Map<Integer, List<RealTimeValue>> integerListMapOfTemp, Integer id) {
        if (Objects.nonNull(integerListMapOfTemp)) {
            List<RealTimeValue> realTimeValues = integerListMapOfTemp.get(id);
            List<RealTimeValue> collect = realTimeValues.stream().filter(realTimeValue -> Objects.nonNull(realTimeValue.getValue())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                return collect;
            }

        }
        return Collections.emptyList();
    }

    private Double realTimeValueFilterWithNodes(Map<Integer, List<RealTimeValue>> integerListMapOfTemp, Integer id, List<BaseVo> baseVos) {
        if (Objects.nonNull(integerListMapOfTemp)) {
            List<RealTimeValue> realTimeValues = integerListMapOfTemp.get(id);
            List<RealTimeValue> collect = realTimeValues.stream().filter(realTimeValue -> Objects.nonNull(realTimeValue.getValue())
                    && baseVos.contains(new BaseVo(realTimeValue.getMonitoredId(), realTimeValue.getMonitoredLabel()))).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                return collect.get(0).getValue();
            }

        }
        return null;
    }

    private QuantitySearchVo getAPowerTransformer() {
        return new QuantitySearchVo(6000023,
                QuantityCategoryDef.TEMP,
                QuantityTypeDef.RMS,
                null,
                PhasorDef.AN,
                EnergyTypeDef.ELECTRIC);
    }

    private QuantitySearchVo getBPowerTransformer() {
        return new QuantitySearchVo(6000024,
                QuantityCategoryDef.TEMP,
                QuantityTypeDef.RMS,
                null,
                PhasorDef.BN,
                EnergyTypeDef.ELECTRIC);
    }

    private QuantitySearchVo getCPowerTransformer() {
        return new QuantitySearchVo(6000025,
                QuantityCategoryDef.TEMP,
                QuantityTypeDef.RMS,
                null,
                PhasorDef.CN,
                EnergyTypeDef.ELECTRIC);
    }


    private QuantitySearchVo getUab() {
        return new QuantitySearchVo(5,
                QuantityCategoryDef.VOLTAGE,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.PH_AB,
                EnergyTypeDef.ELECTRIC);
    }

    private QuantitySearchVo getUbc() {
        return new QuantitySearchVo(6,
                QuantityCategoryDef.VOLTAGE,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.PH_BC,
                EnergyTypeDef.ELECTRIC);
    }

    private QuantitySearchVo getUac() {
        return new QuantitySearchVo(7,
                QuantityCategoryDef.VOLTAGE,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.PH_CA,
                EnergyTypeDef.ELECTRIC);
    }

    private QuantitySearchVo getIa() {
        return new QuantitySearchVo(1000001,
                QuantityCategoryDef.CURRENT,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.AN,
                EnergyTypeDef.ELECTRIC);
    }

    private QuantitySearchVo getIb() {
        return new QuantitySearchVo(1000002,
                QuantityCategoryDef.CURRENT,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.BN,
                EnergyTypeDef.ELECTRIC);
    }

    private QuantitySearchVo getIc() {
        return new QuantitySearchVo(1000003,
                QuantityCategoryDef.CURRENT,
                QuantityTypeDef.RMS,
                FrequencyDef.NONE,
                PhasorDef.CN,
                EnergyTypeDef.ELECTRIC);
    }


    public QuantitySearchVo getActivePower() {
        return new QuantitySearchVo(2000004,
                QuantityCategoryDef.POWER,
                QuantityTypeDef.POWER,
                FrequencyDef.NONE,
                PhasorDef.TOTAL,
                EnergyTypeDef.ELECTRIC);
    }

    public QuantitySearchVo getReactivePower() {
        return new QuantitySearchVo(2000008,
                QuantityCategoryDef.POWER,
                56,
                FrequencyDef.NONE,
                PhasorDef.TOTAL,
                EnergyTypeDef.ELECTRIC);
    }

    public QuantitySearchVo getPositiveActiveElectric() {
        return new QuantitySearchVo(4000004,
                QuantityCategoryDef.ELECTRIC,
                QuantityTypeDef.P_INTG_POS,
                FrequencyDef.NONE,
                PhasorDef.TOTAL,
                EnergyTypeDef.ELECTRIC);
    }

    public QuantitySearchVo getReactivePowerElectric() {
        return new QuantitySearchVo(4000020,
                QuantityCategoryDef.ELECTRIC,
                QuantityTypeDef.Q_INTG_POS,
                FrequencyDef.NONE,
                PhasorDef.TOTAL,
                EnergyTypeDef.ELECTRIC);
    }


    public Long handleTime() {
        long now = System.currentTimeMillis();
        LocalDateTime localDateTime = TimeUtil.timestamp2LocalDateTime(now);
        LocalDate date = localDateTime.toLocalDate();
        LocalTime time = LocalTime.of(localDateTime.getHour(), localDateTime.getMinute());
        LocalDateTime of = LocalDateTime.of(date, time);
        return TimeUtil.localDateTime2timestamp(of);
    }

    public QuantityDataBatchSearchVo createQuantityDataBatchSearchVoWithRealTime(List<BaseVo> deviceNodes, Long st, Long et, List<QuantitySearchVo> quantitySettings) {
        QuantityDataBatchSearchVo aggregationDataBatch = new QuantityDataBatchSearchVo();
        aggregationDataBatch.setDataTypeId(EnumDataTypeId.REALTIME.getId());
        aggregationDataBatch.setStartTime(st);
        aggregationDataBatch.setEndTime(et);
        aggregationDataBatch.setQuantitySettings(quantitySettings);
        aggregationDataBatch.setNodes(deviceNodes);
        return aggregationDataBatch;
    }

    private PowerTransformerDto queryPowerTransformer(Long id) {
        QueryCondition queryCondition = new QueryConditionBuilder<>(NodeLabelDef.POWER_TRANS_FORMER, id).build();
        List<PowerTransformerDto> query = modelServiceUtils.query(queryCondition, PowerTransformerDto.class);
        if (CollectionUtils.isNotEmpty(query)) {
            return query.get(0);
        }
        return null;
    }

    /**
     * (p0+kq*Io%*sn)/(kt*(pk+kq*Uk%*sn))  开根号
     *
     * @param powerTransformerVo
     * @return
     */
    @Override
    public Double calculateOptimalLoadRate(PowerTransformerDto powerTransformerVo) {
        //空载损耗--p0
        Double noLoadLoss = powerTransformerVo.getNoloadloss();
        //短路损耗--pk
        Double shortCircuitLoss = powerTransformerVo.getShortcircuitloss();
        //无功经济当量--KQ
        Double aDouble = powerTransformerVo.getEquivalent();
        //空载电流（A）--Io%
        Double noLoadCurrent = powerTransformerVo.getNoloadcurrent();
        //短路电压百分比--Uk%
        Double shortCircuitImpedance = powerTransformerVo.getShortCircuitVoltage();
        //额定容量KVA--Sn
        Double ratedCapacity = powerTransformerVo.getRatedcapacity();
        if (Objects.isNull(noLoadLoss) || Objects.isNull(shortCircuitLoss) || Objects.isNull(aDouble)
                || Objects.isNull(noLoadCurrent) || Objects.isNull(shortCircuitImpedance) || Objects.isNull(ratedCapacity)) {
            return null;
        }
        Double kt = 1.05;
        Double top = calculateFourData(noLoadLoss, aDouble, noLoadCurrent / 100, ratedCapacity);
        Double down = CommonUtils.calcDouble(calculateFourData(shortCircuitLoss, aDouble, shortCircuitImpedance / 100, ratedCapacity),
                kt, EnumOperationType.MULTIPLICATION.getId());
        Double result = CommonUtils.calcDouble(top, down, EnumOperationType.DIVISION.getId());
        if (Objects.nonNull(result)) {
            return Math.sqrt(result);
        }
        return null;
    }

    @Override
    public Double calculateLoadRealTime(Long id, List<PointNode> nodes, List<LinkNode> links) {
        LoadInfoVo top = getLoadInfoVo(nodes, links, id, true);
        if (Objects.isNull(top.getLoadRealTime())) {
            top = getLoadInfoVo(nodes, links, id, false);
        }
        return top.getLoadRealTime();
    }

    @Override
    public Map<Long, Double> calculateLoadRealTimeBatch(List<Long> ids, List<PointNode> nodes, List<LinkNode> links) {
        List<BaseVo> topAndDownNodesBatch = getTopAndDownNodesBatch(nodes, links, ids);
        Long st = handleTime();
        Long et = TimeUtil.addDateTimeByCycle(st, AggregationCycle.ONE_MINUTE, 1);
        Long startTime1 = System.currentTimeMillis();
        Map<Integer, List<RealTimeValue>> integerListMap = quantityManageService.queryRealTimeBath(
                createQuantityDataBatchSearchVoWithRealTime(topAndDownNodesBatch, st, et,
                        Arrays.asList(getActivePower(), getReactivePower())));
        log.info("查询负载率定时记录时间为: {}s", (System.currentTimeMillis() - startTime1)/1000);
        Map<Long, Double> result = new HashMap<>();
        for (Long id : ids) {
            Double aDouble = assembleDataWithEachPower(integerListMap, id,
                    nodes, links);
            result.put(id, aDouble);
        }
        return result;
    }

    private Double assembleDataWithEachPower(Map<Integer, List<RealTimeValue>> integerListMap, Long id,
                                             List<PointNode> nodes, List<LinkNode> links) {
        Double loadInfoRealTime = getLoadInfoRealTime(nodes, links, id, true, integerListMap);
        if (Objects.isNull(loadInfoRealTime)) {
            loadInfoRealTime = getLoadInfoRealTime(nodes, links, id, false, integerListMap);
        }
        return loadInfoRealTime;
    }

    private Double getLoadInfoRealTime(List<PointNode> nodes, List<LinkNode> links, Long id, Boolean isTop, Map<Integer, List<RealTimeValue>> integerListMap) {
        List<LinkNode> target = getLinkNodes(nodes, links, id, isTop);
        List<BaseVo> queryNodes = getQueryNodes(target, nodes, isTop);
        Double activeData = realTimeValueFilterWithNodes(integerListMap, getActivePower().getId(), queryNodes);
        Double reactiveData = realTimeValueFilterWithNodes(integerListMap, getReactivePower().getId(), queryNodes);
        return calculateLoad(activeData, reactiveData);
    }

    private Double calculateFourData(Double value1, Double value2, Double value3, Double value4) {
        Double calcDouble = CommonUtils.calcDouble(CommonUtils.calcDouble(value2, value3,
                EnumOperationType.MULTIPLICATION.getId()), value4, EnumOperationType.MULTIPLICATION.getId());
        if (Objects.isNull(calcDouble)) {
            return value1;
        } else {
            if (Objects.isNull(value1)) {
                return calcDouble;
            }
            return CommonUtils.calcDouble(value1, calcDouble, EnumOperationType.ADD.getId());
        }
    }
}