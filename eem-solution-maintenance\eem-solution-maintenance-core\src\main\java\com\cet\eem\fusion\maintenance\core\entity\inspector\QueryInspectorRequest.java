package com.cet.eem.fusion.maintenance.core.entity.inspector;

import com.cet.eem.fusion.common.model.Page;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;

/**
 * @ClassName : QueryInspectorRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-02 09:33
 */
@Getter
@Setter
@ApiModel(value = "QueryInspectorRequest", description = "查询巡检人员")
public class QueryInspectorRequest {

    /**
     * 班组id
     */
    @NotNull(message = "班组ID不能为空")
    private Long id;

    /**
     * 分页参数
     */
    private Page page;

    public QueryInspectorRequest() {
    }

    public QueryInspectorRequest(Long id) {
        this.id = id;
    }
}
