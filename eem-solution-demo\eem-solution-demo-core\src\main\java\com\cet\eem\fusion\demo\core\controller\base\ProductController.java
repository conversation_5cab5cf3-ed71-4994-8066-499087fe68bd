package com.cet.eem.fusion.demo.core.controller.base;

import com.cet.eem.fusion.config.sdk.controller.BaseEemProductController;
import com.cet.eem.solution.common.def.common.PluginInfoDef;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName : ProductController
 * @Description : 产品管理
 * <AUTHOR> zhangh
 * @Date: 2020-09-09 10:41
 */
@Api(value = "ProductController", tags = {"产品类型管理接口"})
@RestController
@RequestMapping(value = PluginInfoDef.Demo.INTERFACE_PREFIX + "/v1/product")
public class ProductController extends BaseEemProductController {
}
