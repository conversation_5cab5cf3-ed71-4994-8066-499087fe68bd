#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版全局替换脚本 - 避免编码问题（rules目录下执行）
python simple_replace_script.py ..\eem-solution-group-energy\ --dry-run（预览替换结果，不执行）
python simple_replace_script.py ..\eem-solution-group-energy\（执行替换结果）
"""

import os
import sys
import argparse
import re
from pathlib import Path
from typing import List, Tuple


class SimpleReplacer:
    """简化版替换器"""
    
    def __init__(self, target_dir: str, rules_file: str = None, dry_run: bool = False):
        self.target_dir = Path(target_dir)
        self.rules_file = rules_file or "全局替换清单.md"
        self.dry_run = dry_run
        self.replacement_rules: List[Tuple[str, str]] = []
        self.processed_files = 0
        self.replaced_count = 0
        
    def load_replacement_rules(self) -> bool:
        """加载替换规则"""
        try:
            with open(self.rules_file, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f.readlines() if line.strip()]
            
            # 每两行为一组
            for i in range(0, len(lines), 2):
                if i + 1 < len(lines):
                    old_content = lines[i]
                    new_content = lines[i + 1]
                    self.replacement_rules.append((old_content, new_content))
            
            print(f"成功加载 {len(self.replacement_rules)} 条替换规则")
            return True
            
        except FileNotFoundError:
            print(f"错误：找不到替换规则文件 {self.rules_file}")
            return False
        except Exception as e:
            print(f"错误：加载替换规则失败 - {str(e)}")
            return False
    
    def comment_wildcard_imports_in_content(self, content: str) -> str:
        """注释掉以import com.cet开头并以.*结尾的导入语句"""
        lines = content.split('\n')
        modified = False
        
        for i, line in enumerate(lines):
            # 检查是否是以import com.cet开头并以.*结尾的导入语句
            if line.strip().startswith("import com.cet") and line.strip().endswith(".*;"):
                # 注释掉这一行
                lines[i] = "// " + line  # 添加注释
                modified = True
                print(f"    注释掉导入语句: {line.strip()}")
        
        if modified:
            return '\n'.join(lines)
        else:
            return content
    
    def replace_file_content(self, file_path: Path) -> int:
        """替换单个文件的内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            replace_count = 0
            
            # 强制注释掉以import com.cet开头并以.*结尾的导入语句
            content = self.comment_wildcard_imports_in_content(content)
            
            # 执行所有替换规则
            for old_text, new_text in self.replacement_rules:
                if old_text in content:
                    content = content.replace(old_text, new_text)
                    replace_count += 1
            
            # 如果内容有变化
            if content != original_content:
                if self.dry_run:
                    print(f"  [预览] {file_path.name} - 将执行 {replace_count} 次替换")
                else:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"  已处理: {file_path.name} - 执行了 {replace_count} 次替换")
                
                return replace_count
            
            return 0
            
        except Exception as e:
            print(f"  处理文件失败 {file_path.name}: {str(e)}")
            return 0
    
    def process_directory(self) -> None:
        """处理目录下的所有Java文件"""
        if not self.target_dir.exists():
            print(f"错误：目标目录不存在 {self.target_dir}")
            return
        
        print(f"开始处理目录: {self.target_dir}")
        print(f"替换规则数量: {len(self.replacement_rules)}")
        if self.dry_run:
            print("模式: 预览模式 (不会实际修改文件)")
        print("-" * 60)
        
        # 递归遍历所有Java文件
        java_files = list(self.target_dir.rglob("*.java"))
        
        if not java_files:
            print("未找到任何Java文件")
            return
        
        print(f"找到 {len(java_files)} 个Java文件")
        print("-" * 60)
        
        for java_file in java_files:
            replaced = self.replace_file_content(java_file)
            if replaced > 0:
                self.replaced_count += replaced
            elif not self.dry_run or replaced == 0:
                print(f"  无需替换: {java_file.name}")
            self.processed_files += 1
        
        print("-" * 60)
        print("处理完成!")
        print(f"处理文件数: {self.processed_files}")
        print(f"总替换次数: {self.replaced_count}")
    
    def run(self) -> None:
        """运行替换器"""
        print("简化版全局替换脚本启动")
        print("=" * 60)
        
        if not self.load_replacement_rules():
            return
        
        self.process_directory()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="简化版全局替换脚本")
    parser.add_argument("target_dir", help="目标文件夹路径")
    parser.add_argument("--rules-file", default="全局替换清单.md", help="替换清单文件路径")
    parser.add_argument("--dry-run", action="store_true", help="仅预览替换结果")
    
    args = parser.parse_args()
    
    replacer = SimpleReplacer(
        target_dir=args.target_dir,
        rules_file=args.rules_file,
        dry_run=args.dry_run
    )
    replacer.run()


if __name__ == "__main__":
    main()