package com.cet.eem.fusion.maintenance.core.schedule.strategy;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.PlanSheet;
import com.cet.eem.bll.maintenance.schedule.util.PlanSheetTimeParser;
import com.cet.eem.fusion.common.exception.ValidationException;
import org.quartz.CalendarIntervalScheduleBuilder;
import org.quartz.ScheduleBuilder;
import org.quartz.Trigger;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @ClassName : CustomTriggerStrategy
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-23 15:10
 */
@Component(PlanSheetTriggerStrategyKey.CUSTOM)
public class CustomTriggerStrategy implements PlanSheetTriggerStrategy<Trigger> {
    @Override
    public ScheduleBuilder<Trigger> buildSchedule(PlanSheet planSheet) {
        PlanSheetTimeParser planSheetTimeParser = new PlanSheetTimeParser(planSheet.getCycle(), planSheet.getExecuteStrategy());
        if (Objects.nonNull(planSheetTimeParser.getHours()) && planSheetTimeParser.getHours() > 0) {
            return (ScheduleBuilder) CalendarIntervalScheduleBuilder.calendarIntervalSchedule()
                    .withIntervalInHours(planSheetTimeParser.getHours())
                    .withMisfireHandlingInstructionDoNothing();
        } else if (Objects.nonNull(planSheetTimeParser.getDay()) && planSheetTimeParser.getDay() > 0) {
            return (ScheduleBuilder) CalendarIntervalScheduleBuilder.calendarIntervalSchedule()
                    .withIntervalInDays(planSheetTimeParser.getDay())
                    .withMisfireHandlingInstructionDoNothing();
        } else if (Objects.nonNull(planSheetTimeParser.getMonth()) && planSheetTimeParser.getMonth() > 0) {
            return CronUtils.handleMonthTrigger(planSheet, planSheetTimeParser.getMonth());
        } else {
            throw new ValidationException("读取执行巡检计划生成工单周期异常！");
        }
    }
}
