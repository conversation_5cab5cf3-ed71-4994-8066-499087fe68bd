package com.cet.eem.fusion.maintenance.core.entity.devicemanage.template;

import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : MeasureNode
 * @Description : 测点
 * <AUTHOR> Administrator
 * @Date: 2021-06-15 18:53
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.MEASURE_NODE)
public class MeasureNodeDto extends EntityWithName {
    @JsonProperty("dataid")
    private Long dataId;
    public MeasureNodeDto() {
        this.modelLabel = ModelLabelDef.MEASURE_NODE;
    }
}