﻿package com.cet.eem.fusion.refrigeration.core.service;

import com.cet.eem.fusion.refrigeration.core.model.dataentryquery.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @ClassName : DataEntryAndQueryService
 * @Description : 数据录入和查询-对接的接口
 * <AUTHOR> jiangzixuan
 * @Date: 2022-04-08 14:19
 */
public interface DataEntryAndQueryService {
    /**
     * 末端
     * @param queryParam
     * @return
     * @throws IllegalAccessException
     * @throws InstantiationException
     */
    List<EndColdDataEntry> queryEndColdData(DataQueryParam queryParam,Long projectId) throws IllegalAccessException, InstantiationException;

    /**
     * 管损
     * @param queryParam
     * @return
     */
    List<PipelineLossColdDataEntry> queryPipelineLossColdData(DataQueryParam queryParam,Long projectId);

    /**
     * 冷机出水温度
     * @param queryParam
     * @return
     */
    List<OptimizationOfRefrigeratorWaterEntry> queryOptimizationOfRefrigeratorWater(DataQueryParam queryParam,Long projectId) throws IllegalAccessException, InstantiationException;

    /**
     * 系统总功率需求预测查询
     * @param queryParam
     * @return
     */
    List<TotalSystemPowerEntry> queryTotalSystemPower(DataQueryParam queryParam,Long projectId) throws IllegalAccessException, InstantiationException;
    /**
     * 导出
     * @param queryParam
     * @param response
     */
    void exportWeatherDataCompare(DataQueryParam queryParam, HttpServletResponse response) throws Exception;
}
