package com.cet.eem.fusion.transformer.core.entity.dto;

import com.cet.eem.bll.common.model.domain.object.organization.Project;
import com.cet.eem.bll.common.model.domain.object.powersystem.PowerTransformerVo;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : PowerTransformerDto
 * @Description : 变压器数据，加字段后的
 * <AUTHOR> jiangzixuan
 * @Date: 2022-03-15 15:32
 */
@Getter
@Setter
public class PowerTransformerDto extends PowerTransformerVo {
    /**
     * 无功经济当量
     */
    @JsonProperty("reactiveeconomicequivalent")
    private Double equivalent;

    private Integer transformerlevel;

    private Double sectionlowlimit;

    private Double sectionupperlimit;
    /**
     * 短路电压百分比
     */
    @JsonProperty("shortcircuitvoltage")
    private Double shortCircuitVoltage;

    private List<Project> project_model;

    public PowerTransformerDto() {
        this.modelLabel = "powertransformer";
    }
}