package com.cet.eem.fusion.maintenance.core.entity.workorder.inspection.recordsheet;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @ClassName : QueryTemplateWithNodeName
 * @Description : 模板带上包括节点的节点名称
 * <AUTHOR> jiang<PERSON><PERSON>uan
 * @Date: 2022-10-19 09:34
 */
@Getter
@Setter
public class QueryTemplateWithNodeName extends QueryTemplateVo{
    @JsonProperty(RecordSheetModelLabel.LAYER)
    private List<QueryTemplateVo> queryTemplates;
}