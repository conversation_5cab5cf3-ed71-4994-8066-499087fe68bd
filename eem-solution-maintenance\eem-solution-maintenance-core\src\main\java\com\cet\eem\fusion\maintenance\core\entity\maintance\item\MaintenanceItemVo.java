package com.cet.eem.fusion.maintenance.core.entity.maintance.item;

import com.cet.eem.fusion.maintenance.core.entity.po.MaintenanceItem;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : MaintenanceItemVo
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-05-14 14:29
 */
@Getter
@Setter
public class MaintenanceItemVo extends MaintenanceItem {

    /**
     * 零部件名称
     */
    private String sparePartName;

    /**
     * 零部件单位
     */
    private String unit;

    /**
     * 维保方式名称
     */
    private String maintenanceTypeName;
    /**
     * 维保分组id
     */
    private Long groupId;
    /**
     * 维保分组名称
     */
    private String groupName;
}
