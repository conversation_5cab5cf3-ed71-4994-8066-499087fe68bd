package com.cet.eem.fusion.maintenance.core.service.singinpoint.impl;

import cn.hutool.json.JSONObject;
import com.cet.eem.auth.config.MobileNodeConfig;
import com.cet.eem.auth.def.InspectSignModeDef;
import com.cet.eem.auth.model.common.AppSystemInfo;
import com.cet.eem.auth.service.NodeAuthCheckService;
import com.cet.eem.auth.service.impl.CommonAuthService;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInEquipment;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInGroup;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInPoint;
import com.cet.eem.bll.common.model.domain.subject.powermaintenance.SignInPointSequence;
import com.cet.eem.bll.common.model.ext.modelentity.EemQueryCondition;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.SignInGroupWithAllSubLayer;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.SignInGroupWithEquipment;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.SignInGroupWithSubLayer;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.SignInPointWithSubLayer;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.bll.maintenance.dao.SignInGroupDao;
import com.cet.eem.bll.maintenance.dao.SignInPointDao;
import com.cet.eem.bll.maintenance.dao.SignInPointSequenceDao;
import com.cet.eem.bll.maintenance.model.devicemanage.QrInfo;
import com.cet.eem.bll.maintenance.model.sign.*;
import com.cet.eem.bll.maintenance.service.singinpoint.SignInService;
import com.cet.eem.bll.maintenance.service.singinpoint.SignInStatusRecordService;
import com.cet.eem.bll.maintenance.utils.QrCodeUtils;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.common.utils.ErrorUtils;
import com.cet.eem.fusion.common.def.base.ExcelType;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.common.definition.ContentTypeDef;
import com.cet.eem.common.definition.FileTypeDef;
import com.cet.eem.fusion.common.def.label.NodeLabelDef;
import com.cet.eem.fusion.common.exception.ValidationException;
import com.cet.eem.fusion.common.utils.file.FileUtils;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.common.model.node.ModelNodeWithParent;
import com.cet.eem.common.model.peccore.Node;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.fusion.common.utils.excel.PoiExcelUtils;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.cet.eem.model.model.IModel;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.fusion.common.modelutils.model.tool.QueryResultContentTaker;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName : SignInServiceImpl
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-03-12 14:17
 */
@Service
@Slf4j
public class SignInServiceImpl implements SignInService {

    @Autowired
    private SignInGroupDao signInGroupDao;

    @Autowired
    private SignInPointDao signInPointDao;

    @Autowired
    private SignInPointSequenceDao signInPointSequenceDao;

    @Autowired
    private ModelServiceUtils modelServiceUtils;

    @Autowired
    private CommonAuthService authManageService;

    @Autowired
    private SignInStatusRecordService signInStatusRecordService;

    @Autowired
    MobileNodeConfig mobileNodeConfig;

    @Autowired
    NodeAuthCheckService nodeAuthCheckService;

    @Override
    public List<SignInGroupWithSubLayer> queryCurrentProjectSignInGroup() {
        LambdaQueryWrapper<SignInGroup> queryWrapper = LambdaQueryWrapper.of(SignInGroup.class);
        queryWrapper.eq(SignInGroup::getProjectId, GlobalInfoUtils.getTenantId());
        List<SignInGroupWithSubLayer> signInGroupWithSubLayers = signInGroupDao.selectRelatedList(SignInGroupWithSubLayer.class, queryWrapper);
        for (SignInGroupWithSubLayer signInGroupWithSubLayer : signInGroupWithSubLayers) {
            if (CollectionUtils.isNotEmpty(signInGroupWithSubLayer.getSignInPointList())) {
                signInGroupWithSubLayer.setSignInPointList(getAfterSortSignInPoint(signInGroupWithSubLayer.getSignInPointList(), signInGroupWithSubLayer.getSignInPointSequenceList()));
            }
        }

        for (SignInGroupWithSubLayer signInGroupWithSubLayer : signInGroupWithSubLayers) {
            signInGroupWithSubLayer.setTreeId(String.format("%s_%s", signInGroupWithSubLayer.getModelLabel(), signInGroupWithSubLayer.getId()));
        }
        return signInGroupWithSubLayers;
    }

    @Override
    public List<JSONObject> querySignInEquipmentInPoint(Long signInPointId) {
        SignInPointWithSubLayer signInPointWithSubLayer = signInPointDao.selectRelatedById(SignInPointWithSubLayer.class, signInPointId);
        if (Objects.isNull(signInPointWithSubLayer)) {
            return Collections.emptyList();
        }
        List<SignInEquipment> equipmentList = signInPointWithSubLayer.getEquipmentList();
        if (CollectionUtils.isEmpty(equipmentList)) {
            return Collections.emptyList();
        }

        // 查询父节点，并处理成节点与其父节点路径的映射关系
        Set<BaseVo> nodes = equipmentList.stream().map(it -> new BaseVo(it.getObjectId(), it.getObjectLabel())).collect(Collectors.toSet());
        List<ModelNodeWithParent> modelNodeWithParents = nodeAuthCheckService.queryParentNodes(nodes);
        Map<BaseVo, String> nodeWithParentName = new HashMap<>();
        for (ModelNodeWithParent parent : modelNodeWithParents) {
            List<BaseVo> parentNodes = parent.getParentNodes();
            BaseVo node = new BaseVo(parent.getNodeId(), parent.getNodeLabel());
            if (CollectionUtils.isNotEmpty(parentNodes)) {
                List<String> names = parentNodes.stream().filter(it -> !Objects.equals(it, node)).map(BaseVo::getName).collect(Collectors.toList());
                nodeWithParentName.put(node, StringUtils.join(names, "/"));
            }
        }

        HashMap<String, Set<Long>> modelIdMap = equipmentList.stream().collect(Collectors.groupingBy(SignInEquipment::getObjectLabel, HashMap::new, Collectors.mapping(SignInEquipment::getObjectId, Collectors.toSet())));
        List<String> parentsNode = Arrays.asList(NodeLabelDef.BUILDING, NodeLabelDef.FLOOR, NodeLabelDef.ROOM);
        List<Map<String, Object>> resultNode = new ArrayList<>();
        for (Map.Entry<String, Set<Long>> entry : modelIdMap.entrySet()) {
            if (CollectionUtils.isEmpty(entry.getValue())) {
                continue;
            }
            resultNode.addAll(modelServiceUtils.queryAsTree(entry.getValue(), entry.getKey(), parentsNode));
        }
        List<JSONObject> jsonObjectList = new ArrayList<>();
        for (SignInEquipment signInEquipment : equipmentList) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("objectid", signInEquipment.getObjectId());
            jsonObject.put("objectlabel", signInEquipment.getObjectLabel());
            jsonObject.put(ColumnDef.PARENT_NAME, nodeWithParentName.get(new BaseVo(signInEquipment.getObjectId(), signInEquipment.getObjectLabel())));
            jsonObject.put("id", signInEquipment.getId());
            jsonObject.put("modelLabel", signInEquipment.getModelLabel());
            jsonObject.put("tree_id", signInEquipment.getObjectLabel() + "_" + signInEquipment.getObjectId());
            jsonObjectList.add(jsonObject);
            Map<String, Object> map = resultNode.stream().filter(s -> {
                Long id = QueryResultContentTaker.getId(s);
                String modelLabel = QueryResultContentTaker.getModelLabel(s);
                return signInEquipment.getObjectId().equals(id) && signInEquipment.getObjectLabel().equals(modelLabel);
            }).findAny().orElse(null);
            if (Objects.isNull(map)) {
                continue;
            }
            jsonObject.put(QueryResultContentTaker.NAME, QueryResultContentTaker.getName(map));
            List<BaseEntity> allSubLayerNode = QueryResultContentTaker.getAllSubLayerNode(Collections.singletonList(map), false);
            for (BaseEntity baseEntity : allSubLayerNode) {
                jsonObject.put(baseEntity.getModelLabel(), baseEntity.getName());
            }
        }
        return jsonObjectList;
    }

    @Override
    public SignInGroup createSignInGroup(CreateSignInGroupRequest createSignInGroupRequest) {
        checkSameNameGroupWhileCreate(createSignInGroupRequest.getName());
        SignInGroup signInGroup = new SignInGroup();
        signInGroup.setName(createSignInGroupRequest.getName());
        signInGroup.setProjectId(GlobalInfoUtils.getTenantId());
        signInGroupDao.insert(signInGroup);
        return signInGroup;
    }

    @Override
    public SignInGroup editSignInGroup(EditSignInGroupRequest editSignInGroupRequest) {
        checkSameNameGroupWhileEdit(editSignInGroupRequest.getId(), editSignInGroupRequest.getName());
        SignInGroup signInGroup = new SignInGroup();
        signInGroup.setId(editSignInGroupRequest.getId());
        signInGroup.setName(editSignInGroupRequest.getName());
        signInGroup.setProjectId(GlobalInfoUtils.getTenantId());
        signInGroupDao.updateById(signInGroup);
        return signInGroup;
    }

    @Override
    public void deleteSignInGroup(Collection<Long> ids) {
        LambdaQueryWrapper<SignInGroup> queryWrapper = LambdaQueryWrapper.of(SignInGroup.class);
        queryWrapper.in(SignInGroup::getId, ids);
        queryWrapper.eq(SignInGroup::getProjectId, GlobalInfoUtils.getTenantId());
        List<SignInGroupWithSubLayer> signInGroupWithSubLayers = signInGroupDao.selectRelatedList(SignInGroupWithSubLayer.class, queryWrapper);
        if (CollectionUtils.isEmpty(signInGroupWithSubLayers)) {
            return;
        }
        for (SignInGroupWithSubLayer signInGroupWithSubLayer : signInGroupWithSubLayers) {
            List<SignInPoint> signInPointList = signInGroupWithSubLayer.getSignInPointList();
            Assert.isTrue(CollectionUtils.isEmpty(signInPointList), "分组下存在签到点,无法删除");
        }
        List<Long> filterGroupIds = signInGroupWithSubLayers.stream().map(SignInGroupWithSubLayer::getId).distinct().collect(Collectors.toList());
        List<Long> filterSequenceIds = signInGroupWithSubLayers.stream()
                .filter(s -> CollectionUtils.isNotEmpty(s.getSignInPointSequenceList()))
                .flatMap(s -> s.getSignInPointSequenceList().stream()).map(SignInPointSequence::getId)
                .distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(filterGroupIds)) {
            signInGroupDao.deleteBatchIds(filterGroupIds);
        }
        if (CollectionUtils.isNotEmpty(filterSequenceIds)) {
            signInPointSequenceDao.deleteBatchIds(filterSequenceIds);
        }
    }

    @Override
    public synchronized Map<String, Object> createSignInPoint(CreateSignInPointRequest createSignInGroupRequest) {
        // 校验NFC是否允许为空
        checkAddNFC(createSignInGroupRequest);

        //1.查询指定的签到组是否存在
        LambdaQueryWrapper<SignInGroup> queryWrapper = LambdaQueryWrapper.of(SignInGroup.class);
        queryWrapper.in(SignInGroup::getId, createSignInGroupRequest.getSignInGroupIds());
        List<SignInGroupWithSubLayer> signInGroupWithSubLayers = signInGroupDao.selectRelatedList(SignInGroupWithSubLayer.class, queryWrapper);
        Assert.isTrue(CollectionUtils.isNotEmpty(signInGroupWithSubLayers), "找不到指定签到组");
        //2.签到点重名校验
        checkSameNamePointWhileCreate(createSignInGroupRequest.getName());
        //3.持久化到数据库
        SignInPointWithSubLayer signInPointWithSubLayer = new SignInPointWithSubLayer();
        signInPointWithSubLayer.setName(createSignInGroupRequest.getName());
        signInPointWithSubLayer.setImage(createSignInGroupRequest.getImage());
        signInPointWithSubLayer.setNfc(createSignInGroupRequest.getNfc());
        signInPointWithSubLayer.setAddress(createSignInGroupRequest.getAddress());
        signInPointWithSubLayer.setInterval(createSignInGroupRequest.getInterval());
        signInPointDao.saveOrUpdateBatch(Collections.singletonList(signInPointWithSubLayer));
        //4.写入签到点下的巡检设备
        signInPointDao.insertChild(signInPointWithSubLayer.getId(), createSignInGroupRequest.getChildren());
        //5.将签到点写入签到组下
        for (SignInGroupWithSubLayer signInGroupWithSubLayer : signInGroupWithSubLayers) {
            addSignInPointToGroup(signInPointWithSubLayer, signInGroupWithSubLayer);
        }
        //6.返回库里的数据
        Map<String, Object> signInNode = signInPointDao.selectRelatedTreeById(SignInPointWithSubLayer.class, signInPointWithSubLayer.getId());
        Long signPointId = CommonUtils.parseLong(signInNode.get(ColumnDef.ID));
        signInStatusRecordService.createSignInStatus(signPointId, createSignInGroupRequest.getSignInGroupIds());
        return signInNode;
    }

    @Override
    @Transactional
    public void createSignInPointBatch(List<CreateSignInPointRequest> createSignInPointRequests, Long projectId) {
        List<SignInPoint> newSignInPointData = new LinkedList<>();
        //存储本次添加的数据pointid和groupid的映射
        Map<Long, List<Long>> pointGroupIdMap = new HashMap<>();

        //存储group和point的映射
        Map<SignInGroupWithSubLayer, List<SignInPointWithSubLayer>> pointGroupMap = new HashMap<>();
        //保存签到点的数据
        saveSignInPoint(createSignInPointRequests, newSignInPointData, pointGroupMap);
        saveGroupAndEquipmentrelation(newSignInPointData, createSignInPointRequests, pointGroupIdMap, pointGroupMap);
        signInStatusRecordService.createSignInStatusBatch(pointGroupIdMap);
    }

    /**
     * 存储签到点的数据
     *
     * @param createSignInPointRequests   接口入参
     * @param signInPointWithSubLayerList
     * @param pointGroupMap
     */
    void saveSignInPoint(List<CreateSignInPointRequest> createSignInPointRequests,
                         List<SignInPoint> signInPointWithSubLayerList,
                         Map<SignInGroupWithSubLayer, List<SignInPointWithSubLayer>> pointGroupMap) {
        //用于校验签到点重复添加
        Set<String> signInPointSet = signInPointDao.queryAllSignInPointInProject(GlobalInfoUtils.getTenantId())
                .stream().map(SignInPoint::getName).collect(Collectors.toSet());
        //用于查询本次添加涉及的所有group
        List<Long> groupIds = new LinkedList<>();
        createSignInPointRequests.forEach(createSignInPointRequest -> {
            groupIds.addAll(createSignInPointRequest.getSignInGroupIds());
        });
        LambdaQueryWrapper<SignInGroup> queryWrapper = LambdaQueryWrapper.of(SignInGroup.class);
        queryWrapper.in(SignInGroup::getId, groupIds);
        List<SignInGroupWithSubLayer> newSignInGroupWithSubLayers =
                signInGroupDao.selectRelatedList(SignInGroupWithSubLayer.class, queryWrapper);
        Map<Long, SignInGroupWithSubLayer> signInGroupCache = newSignInGroupWithSubLayers
                .stream().collect(Collectors.toMap(SignInGroupWithSubLayer::getId, signInGroupWithSubLayer -> signInGroupWithSubLayer));
        List<Long> signInGroupIdCache = newSignInGroupWithSubLayers.stream().map(SignInGroup::getId).collect(Collectors.toList());

        for (CreateSignInPointRequest createSignInPointRequest : createSignInPointRequests) {
            if (signInPointSet.contains(createSignInPointRequest.getName())) {
                throw new ValidationException("已存在签到点：" + createSignInPointRequest.getName());
            } else {
                signInPointSet.add(createSignInPointRequest.getName());
            }
            if (!signInGroupIdCache.containsAll(createSignInPointRequest.getSignInGroupIds())) {
                throw new ValidationException("签到点-" + createSignInPointRequest.getName() + "-找不到指定签到组-");
            }
            checkAddNFC(createSignInPointRequest);
            SignInPointWithSubLayer signInPointWithSubLayer = new SignInPointWithSubLayer();
            signInPointWithSubLayer.setName(createSignInPointRequest.getName());
            signInPointWithSubLayer.setImage(createSignInPointRequest.getImage());
            signInPointWithSubLayer.setNfc(createSignInPointRequest.getNfc());
            signInPointWithSubLayer.setAddress(createSignInPointRequest.getAddress());
            signInPointWithSubLayer.setInterval(createSignInPointRequest.getInterval());
            signInPointWithSubLayerList.add(signInPointWithSubLayer);
            List<Long> requestSignInGroupIds = createSignInPointRequest.getSignInGroupIds();
            requestSignInGroupIds.forEach(groupId -> {
                List<SignInPointWithSubLayer> setSignInPointRequests = pointGroupMap.get(signInGroupCache.get(groupId));
                if (setSignInPointRequests == null) {
                    setSignInPointRequests = new LinkedList<>();
                }
                setSignInPointRequests.add(signInPointWithSubLayer);
                pointGroupMap.put(signInGroupCache.get(groupId), setSignInPointRequests);
            });
        }
        //存储签到点信息
        signInPointDao.saveOrUpdateBatch(signInPointWithSubLayerList);
    }

    void saveGroupAndEquipmentrelation(List<SignInPoint> newSignInPointData,
                                       List<CreateSignInPointRequest> createSignInPointRequests,
                                       Map<Long, List<Long>> pointGroupIdMap,
                                       Map<SignInGroupWithSubLayer, List<SignInPointWithSubLayer>> pointGroupMap) {
        //存储本次添加的数据pointid和equipment的映射
        Map<Long, List<? extends IModel>> signInEquipmentMap = new HashMap<>();
        for (int i = 0; i < newSignInPointData.size(); i++) {
            List childrens = createSignInPointRequests.get(i).getChildren();
            List<Long> groupIdList = createSignInPointRequests.get(i).getSignInGroupIds();
            Long pointId = newSignInPointData.get(i).getId();
            if (signInEquipmentMap.containsKey(pointId)) {
                List<? extends IModel> list = signInEquipmentMap.get(pointId);
                list.addAll(childrens);
                signInEquipmentMap.put(pointId, list);
            } else {
                signInEquipmentMap.put(pointId, childrens);
            }
            if (pointGroupIdMap.containsKey(pointId)) {
                List<Long> longs = pointGroupIdMap.get(pointId);
                longs.addAll(groupIdList);
                pointGroupIdMap.put(pointId, longs);
            } else {
                pointGroupIdMap.put(pointId, groupIdList);
            }
        }
        //存储签到点下设备信息
        signInPointDao.insertBatchChild(signInEquipmentMap);
        //存储签到点和分组的关联关系
        addSignInPointToGroupBatch(pointGroupMap);
    }

    private void checkAddNFC(CreateSignInPointRequest createSignInGroupRequest) {
        AppSystemInfo appSystemConfig = mobileNodeConfig.getAppSystemInfo();
        if ((Objects.isNull(appSystemConfig) || Objects.equals(appSystemConfig.getInspectSignMode(), InspectSignModeDef.NFC))
                && StringUtils.isBlank(createSignInGroupRequest.getNfc())) {
            throw new ValidationException("NFC信息不允许为空！");
        }
    }

    private void checkUpdateNFC(EditSignInPointRequest createSignInGroupRequest) {
        AppSystemInfo appSystemConfig = mobileNodeConfig.getAppSystemInfo();
        if ((Objects.isNull(appSystemConfig) || Objects.equals(appSystemConfig.getInspectSignMode(), InspectSignModeDef.NFC))
                && StringUtils.isBlank(createSignInGroupRequest.getNfc())) {
            throw new ValidationException("NFC信息不允许为空！");
        }
    }

    @Override
    public SignInPoint editSignInPoint(EditSignInPointRequest editSignInPointRequest) {
        checkUpdateNFC(editSignInPointRequest);

        List<Long> signInGroupId = editSignInPointRequest.getSignInGroupIds();
        LambdaQueryWrapper<SignInGroup> queryWrapper = LambdaQueryWrapper.of(SignInGroup.class);
        queryWrapper.in(SignInGroup::getId, signInGroupId);
        List<SignInGroupWithSubLayer> signInGroupWithSubLayers = signInGroupDao.selectRelatedList(SignInGroupWithSubLayer.class, queryWrapper);
        //1.重名检测
        checkSameNamePointWhileEdit(editSignInPointRequest.getId(), editSignInPointRequest.getName());
        //2.签到点模型更新
        SignInPoint signInPoint = updatePoint(editSignInPointRequest);
        //3.处理签到点下签到设备的更新操作
        updateSignInEquipmentsWhileEditSignInPoint(editSignInPointRequest);
        //4.处理签到点转移到其他签到组的情况
        transferPoint(signInPoint, signInGroupWithSubLayers);
        return signInPointDao.selectRelatedById(SignInPointWithSubLayer.class, editSignInPointRequest.getId());
    }

    @Override
    public void deleteSignInPoint(Long signInGroupId, Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        LambdaQueryWrapper<SignInPoint> queryWrapper = LambdaQueryWrapper.of(SignInPoint.class);
        queryWrapper.in(SignInPoint::getId, ids);
        List<SignInPointWithSubLayer> signInPointWithSubLayers = signInPointDao.selectRelatedList(SignInPointWithSubLayer.class, queryWrapper);
        for (SignInPointWithSubLayer signInPointWithSubLayer : signInPointWithSubLayers) {
            List<SignInEquipment> equipmentList = signInPointWithSubLayer.getEquipmentList();
            Assert.isTrue(CollectionUtils.isEmpty(equipmentList), "该签到点下仍然有设备,无法删除");
        }
        SignInGroupWithSubLayer signInGroupWithSubLayer = signInGroupDao.selectRelatedById(SignInGroupWithSubLayer.class, signInGroupId);
        for (SignInPointWithSubLayer signInPoint : signInPointWithSubLayers) {
            removeSignInPointFromGroup(signInPoint, signInGroupWithSubLayer);
        }

        signInStatusRecordService.deleteSignInStatusRecord(signInGroupId, ids);
    }

    @Override
    public List<SignInPoint> sortSignInPoint(Long signInGroupId, List<SortSignInPointRequest> sortSignInPointRequestList) {
        if (CollectionUtils.isEmpty(sortSignInPointRequestList)) {
            return Collections.emptyList();
        }
        //1.查询签到组下面的排序配置
        SignInGroupWithSubLayer signInGroupWithSubLayer = signInGroupDao.selectRelatedById(SignInGroupWithSubLayer.class, signInGroupId);
        List<SignInPointSequence> signInPointSequenceList = signInGroupWithSubLayer.getSignInPointSequenceList();
        //2.重组数据
        Map<Long, Integer> idSortMap = sortSignInPointRequestList.stream().collect(Collectors.toMap(SortSignInPointRequest::getId, SortSignInPointRequest::getSort));
        for (SignInPointSequence signInPointSequence : signInPointSequenceList) {
            Integer sort = idSortMap.get(signInPointSequence.getRegistrationPointId());
            if (Objects.nonNull(sort)) {
                signInPointSequence.setSort(sort);
            }
        }
        signInPointSequenceDao.saveOrUpdateBatch(signInPointSequenceList);
        return getAfterSortSignInPoint(signInGroupWithSubLayer.getSignInPointList(), signInGroupWithSubLayer.getSignInPointSequenceList());
    }

    @Override
    public List<Map<String, Object>> deleteSignInEquipment(Long signInPointId, List<BaseEntity> baseEntities) {
        return signInPointDao.deleteChild(signInPointId, baseEntities);
    }

    @Override
    public List<Map<String, Object>> querySignInEquipmentInGroup(Long signInGroupId, EemQueryCondition condition) {
        SignInGroupWithEquipment signInGroupWithEquipment = signInGroupDao.querySignInGroupWithEquipment(signInGroupId);
        if (Objects.isNull(signInGroupWithEquipment) || CollectionUtils.isEmpty(signInGroupWithEquipment.getSignInEquipmentList())) {
            return Collections.emptyList();
        }
        List<SignInEquipment> signInEquipmentList = signInGroupWithEquipment.getSignInEquipmentList();
        List<Map<String, Object>> maps = authManageService.queryNodeList(condition, GlobalInfoUtils.getUserId());
        removeOtherNode(maps, signInEquipmentList);
        return maps;
    }

    /**
     * 从 maps中移除非 signInEquipmentList 节点的数据
     *
     * @param maps
     * @param signInEquipmentList
     */
    private void removeOtherNode(List<Map<String, Object>> maps, List<SignInEquipment> signInEquipmentList) {
        Iterator<Map<String, Object>> iterator = maps.iterator();
        while (iterator.hasNext()) {
            Map<String, Object> map = iterator.next();
            List<Map<String, Object>> children = QueryResultContentTaker.getChildren(map);
            if (CollectionUtils.isNotEmpty(children)) {
                removeOtherNode(children, signInEquipmentList);
            }
            Optional<SignInEquipment> any = signInEquipmentList.stream().filter(s -> {
                Long id = QueryResultContentTaker.getId(map);
                String modelLabel = QueryResultContentTaker.getModelLabel(map);
                return s.getObjectId().equals(id) && s.getObjectLabel().equals(modelLabel);
            }).findAny();

            if (any.isPresent()) {
                map.put(ColumnDef.DISABLED, false);
                continue;
            }

            if (CollectionUtils.isEmpty(children)) {
                iterator.remove();
            } else {
                map.put(ColumnDef.DISABLED, true);
            }
        }
    }

    private List<SignInPoint> getAfterSortSignInPoint(List<SignInPoint> signInPointList, List<SignInPointSequence> signInPointSequenceList) {
        if (CollectionUtils.isEmpty(signInPointSequenceList)) {
            return signInPointList;
        }
        Map<Long, Integer> idSortMap = signInPointSequenceList.stream().collect(Collectors.toMap(SignInPointSequence::getRegistrationPointId, SignInPointSequence::getSort, (s1, s2) -> s2));
        class SignInPointSortHelper implements Comparable<SignInPointSortHelper> {
            @Getter
            private final SignInPoint signInPoint;
            private final Integer sort;

            public SignInPointSortHelper(SignInPoint signInPoint, Integer sort) {
                this.signInPoint = signInPoint;
                this.sort = sort;
            }

            @Override
            public int compareTo(SignInPointSortHelper o) {
                return this.sort - o.sort;
            }
        }
        return signInPointList.stream().map(s -> new SignInPointSortHelper(s, idSortMap.get(s.getId()))).sorted().map(SignInPointSortHelper::getSignInPoint).collect(Collectors.toList());
    }

    private void updateSignInEquipmentsWhileEditSignInPoint(EditSignInPointRequest editSignInPointRequest) {
        Long id = editSignInPointRequest.getId();
        SignInPointWithSubLayer signInPointWithSubLayer = signInPointDao.selectRelatedById(SignInPointWithSubLayer.class, id);
        List<SignInEquipment> newSignInEquipment = editSignInPointRequest.getChildren();
        List<SignInEquipment> oldSignInEquipment = signInPointWithSubLayer.getEquipmentList();
        handlerSignInEquipmentAdd(editSignInPointRequest.getId(), newSignInEquipment, oldSignInEquipment);
        handlerSignInEquipmentDelete(editSignInPointRequest.getId(), newSignInEquipment, oldSignInEquipment);
    }

    private void handlerSignInEquipmentAdd(Long signInPointId, List<SignInEquipment> newSignInEquipment, List<SignInEquipment> oldSignInEquipment) {
        if (CollectionUtils.isEmpty(newSignInEquipment)) {
            return;
        }
        List<SignInEquipment> waitAddSignInEquipment = newSignInEquipment.stream().filter(s -> {
                    if (CollectionUtils.isEmpty(oldSignInEquipment)) {
                        return true;
                    }
                    for (SignInEquipment signInEquipment : oldSignInEquipment) {
                        if (s.contentEquals(signInEquipment)) {
                            return false;
                        }
                    }
                    return true;
                }
        ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(waitAddSignInEquipment)) {
            return;
        }
        signInPointDao.insertChild(signInPointId, waitAddSignInEquipment);
    }

    private void handlerSignInEquipmentDelete(Long signInPointId, List<SignInEquipment> newSignInEquipment, List<SignInEquipment> oldSignInEquipment) {
        if (CollectionUtils.isEmpty(oldSignInEquipment)) {
            return;
        }
        List<SignInEquipment> waitDeleteSignInEquipment = oldSignInEquipment.stream().filter(s -> {
            if (CollectionUtils.isEmpty(newSignInEquipment)) {
                return true;
            }
            for (SignInEquipment signInEquipment : newSignInEquipment) {
                if (s.contentEquals(signInEquipment)) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(waitDeleteSignInEquipment)) {
            return;
        }
        signInPointDao.deleteChild(signInPointId, waitDeleteSignInEquipment);
    }


    /**
     * 转移签到点到其他签到组的逻辑
     *
     * @param signInPoint              签到点
     * @param signInGroupWithSubLayers 前端传递过来的签到组
     */
    private void transferPoint(SignInPoint signInPoint, List<SignInGroupWithSubLayer> signInGroupWithSubLayers) {
        //1.查询签到点之前在那些签到组下面
        LambdaQueryWrapper<SignInPoint> queryWrapper = LambdaQueryWrapper.of(SignInPoint.class);
        queryWrapper.eq(SignInPoint::getId, signInPoint.getId());
        List<SignInGroupWithSubLayer> hasThisPointGroups = signInGroupDao.selectRelatedList(SignInGroupWithSubLayer.class, null, Collections.singletonList(queryWrapper));
        handlerSignInGroupAdd(signInPoint, hasThisPointGroups, signInGroupWithSubLayers);
        handlerSignInGroupDelete(signInPoint, hasThisPointGroups, signInGroupWithSubLayers);
    }

    private void handlerSignInGroupDelete(SignInPoint signInPoint, List<SignInGroupWithSubLayer> hasThisPointGroups, List<SignInGroupWithSubLayer> signInGroupWithSubLayers) {
        if (CollectionUtils.isEmpty(hasThisPointGroups)) {
            return;
        }
        List<SignInGroupWithSubLayer> waitDeleteGroups = hasThisPointGroups.stream().filter(s -> {
            if (CollectionUtils.isEmpty(signInGroupWithSubLayers)) {
                return true;
            }
            for (SignInGroupWithSubLayer signInGroupWithSubLayer : signInGroupWithSubLayers) {
                if (signInGroupWithSubLayer.getId().equals(s.getId())) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());
        for (SignInGroupWithSubLayer waitDeleteGroup : waitDeleteGroups) {
            removeSignInPointFromGroup(signInPoint, waitDeleteGroup);
        }
    }

    private void handlerSignInGroupAdd(SignInPoint signInPoint, List<SignInGroupWithSubLayer> hasThisPointGroups, List<SignInGroupWithSubLayer> signInGroupWithSubLayers) {
        if (CollectionUtils.isEmpty(signInGroupWithSubLayers)) {
            return;
        }
        List<SignInGroupWithSubLayer> waitAddGroups = signInGroupWithSubLayers.stream().filter(s -> {
            if (CollectionUtils.isEmpty(hasThisPointGroups)) {
                return true;
            }
            for (SignInGroupWithSubLayer hasThisPointGroup : hasThisPointGroups) {
                if (hasThisPointGroup.getId().equals(s.getId())) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());
        for (SignInGroupWithSubLayer waitAddGroup : waitAddGroups) {
            addSignInPointToGroup(signInPoint, waitAddGroup);
        }
    }

    /**
     * 添加签到点到签到组内
     *
     * @param signInPoint
     * @param signInGroupWithSubLayer
     */
    private void addSignInPointToGroup(SignInPoint signInPoint, SignInGroupWithSubLayer signInGroupWithSubLayer) {
        SignInPointSequence signInPointSequence = new SignInPointSequence();
        signInPointSequence.setRegistrationPointId(signInPoint.getId());
        signInPointSequence.setSort(signInGroupWithSubLayer.getNextOrder());
        signInGroupDao.insertChild(signInGroupWithSubLayer.getId(), Arrays.asList(signInPoint, signInPointSequence));
    }

    private void addSignInPointToGroupBatch(Map<SignInGroupWithSubLayer, List<SignInPointWithSubLayer>> pointGroupMap) {
        Map<Long, List<? extends IModel>> childrens = new HashMap<>();
        int index = 0;
        for (Map.Entry<SignInGroupWithSubLayer, List<SignInPointWithSubLayer>> next : pointGroupMap.entrySet()) {
            SignInGroupWithSubLayer signInGroupWithSubLayer = next.getKey();
            List<SignInPointWithSubLayer> signInPointWithSubLayers = next.getValue();
            List<BaseEntity> children = new ArrayList<>();
            for (SignInPointWithSubLayer signInPointWithSubLayer : signInPointWithSubLayers) {
                SignInPointSequence signInPointSequence = new SignInPointSequence();
                signInPointSequence.setRegistrationPointId(signInPointWithSubLayer.getId());
                signInPointSequence.setSort(signInGroupWithSubLayer.getNextOrder() + index++);
                children.add(signInPointWithSubLayer);
                children.add(signInPointSequence);
            }
            childrens.put(signInGroupWithSubLayer.getId(), children);
        }
        signInGroupDao.insertBatchChild(childrens);
    }

    /**
     * 移除某个签到组内的签到点
     *
     * @param signInPoint
     * @param signInGroupWithSubLayer
     */
    private void removeSignInPointFromGroup(SignInPoint signInPoint, SignInGroupWithSubLayer signInGroupWithSubLayer) {
        List<IModel> waitRemoveModel = new ArrayList<>(2);
        waitRemoveModel.add(signInPoint);
        if (CollectionUtils.isNotEmpty(signInGroupWithSubLayer.getSignInPointSequenceList())) {
            Optional<SignInPointSequence> any = signInGroupWithSubLayer.getSignInPointSequenceList().stream().filter(s -> s.getRegistrationPointId().equals(signInPoint.getId())).findAny();
            any.ifPresent(waitRemoveModel::add);
        }
        signInGroupDao.moveChild(signInGroupWithSubLayer.getId(), waitRemoveModel);
    }

    private SignInPoint updatePoint(EditSignInPointRequest editSignInPointRequest) {
        SignInPoint signInPoint = new SignInPoint();
        signInPoint.setId(editSignInPointRequest.getId());
        signInPoint.setName(editSignInPointRequest.getName());
        signInPoint.setAddress(editSignInPointRequest.getAddress());
        signInPoint.setImage(editSignInPointRequest.getImage());
        signInPoint.setInterval(editSignInPointRequest.getInterval());
        signInPoint.setNfc(editSignInPointRequest.getNfc());
        signInPointDao.updateById(signInPoint);
        return signInPoint;
    }

    private void checkSameNameGroupWhileCreate(String name) {
        LambdaQueryWrapper<SignInGroup> queryWrapper = LambdaQueryWrapper.of(SignInGroup.class);
        queryWrapper.eq(SignInGroup::getName, name);
        queryWrapper.eq(SignInGroup::getProjectId, GlobalInfoUtils.getTenantId());
        SignInGroup existSignInGroup = signInGroupDao.selectOne(queryWrapper);
        Assert.isNull(existSignInGroup, "该项目下已存在同名签到组");
    }

    private void checkSameNameGroupWhileEdit(Long id, String name) {
        LambdaQueryWrapper<SignInGroup> queryWrapper = LambdaQueryWrapper.of(SignInGroup.class);
        queryWrapper.eq(SignInGroup::getName, name);
        queryWrapper.eq(SignInGroup::getProjectId, GlobalInfoUtils.getTenantId());
        queryWrapper.ne(SignInGroup::getId, id);
        SignInGroup existSignInGroup = signInGroupDao.selectOne(queryWrapper);
        Assert.isNull(existSignInGroup, "该项目下已存在同名签到组");
    }

    private void checkSameNamePointWhileCreate(String name) {
        List<SignInPoint> signInPointList = signInPointDao.queryAllSignInPointInProject(GlobalInfoUtils.getTenantId());
        List<String> nameList = signInPointList.stream().map(SignInPoint::getName).collect(Collectors.toList());
        Assert.isTrue(!nameList.contains(name), "该项目下已存在同名签到点");
    }

    private void checkSameNamePointWhileEdit(Long id, String name) {
        List<SignInPoint> signInPointList = signInPointDao.queryAllSignInPointInProject(GlobalInfoUtils.getTenantId());
        Optional<SignInPoint> any = signInPointList.stream().filter(s -> {
            if (s.getId().equals(id)) {
                return false;
            }
            if (name.equals(s.getName())) {
                return true;
            }
            return false;
        }).findAny();
        Assert.isTrue(!any.isPresent(), "该项目下已存在同名签到点");
    }

    @Override
    public SignInPointWithSubLayer querySignInPoint(@NotNull Long signInPointGroupId, BaseVo node) {
        Assert.notNull(signInPointGroupId, "签到点分组id不允许为空！");
        List<SignInGroupWithAllSubLayer> signGroups = signInGroupDao.querySignInGroupWithAllSubLayer(Collections.singletonList(signInPointGroupId));
        if (CollectionUtils.isEmpty(signGroups)) {
            throw new ValidationException("签到点分组不存在！");
        }

        Map<BaseVo, SignInPointWithSubLayer> baseVoSignInPointWithSubLayerMap = getBaseVoSignInPointWithSubLayerMap(signGroups.get(0).getSignInPointWithSubLayerList());
        SignInPointWithSubLayer result = baseVoSignInPointWithSubLayerMap.get(node);
        if (result == null) {
            log.warn("Signin point record is null: signInPointGroupId={}, node={}", signInPointGroupId, JsonTransferUtils.toJSONString(node));
        }
        return result;
    }

    private Map<BaseVo, SignInPointWithSubLayer> getBaseVoSignInPointWithSubLayerMap(List<SignInPointWithSubLayer> signInPointWithSubLayerList) {
        Map<BaseVo, SignInPointWithSubLayer> map = new HashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        if (CollectionUtils.isEmpty(signInPointWithSubLayerList)) {
            return map;
        }

        for (SignInPointWithSubLayer signInPointWithSubLayer : signInPointWithSubLayerList) {
            List<SignInEquipment> equipmentList = signInPointWithSubLayer.getEquipmentList();
            if (CollectionUtils.isEmpty(equipmentList)) {
                continue;
            }

            for (SignInEquipment signInEquipment : equipmentList) {
                map.put(new BaseVo(signInEquipment.getObjectId(), signInEquipment.getObjectLabel()), signInPointWithSubLayer);
            }
        }
        return map;
    }

    @Override
    public List<BaseVo> querySignInPointByProjectId(String name) {
        List<SignInGroupWithSubLayer> signInGroupWithSubLayers = signInGroupDao.querySignInGroupWithSubLayerByProjectId(GlobalInfoUtils.getTenantId());
        if (CollectionUtils.isEmpty(signInGroupWithSubLayers)) {
            return Collections.emptyList();
        }

        return signInGroupWithSubLayers.stream()
                .filter(it -> CollectionUtils.isNotEmpty(it.getSignInPointList()))
                .flatMap(it -> it.getSignInPointList().stream())
                .map(it -> new BaseVo(it.getId(), it.getModelLabel(), it.getName()))
                .distinct()
                .filter(it -> {
                    if (StringUtils.isEmpty(name)) {
                        return true;
                    }

                    return it.getName().contains(name);
                })
                .sorted((v1, v2) -> CommonUtils.sort(v1.getId(), v2.getId(), true))
                .collect(Collectors.toList());
    }

    @Override
    public void relateGraph(Long signGroupId, Node node) {
        SignInGroup signInGroup = signInGroupDao.selectById(signGroupId);
        Assert.notNull(signInGroup, "签到点分组不存在！");
        signInGroup.setRelatedGraph(JsonTransferUtils.toJSONString(node));
        modelServiceUtils.writeData(Collections.singletonList(signInGroup));
    }

    @Override
    public void downloadSignPointQrCode(Long signPointId, HttpServletResponse response) throws IOException {
        // 查询节点
        SignInPoint signInPoint = signInPointDao.selectById(signPointId);
        if (Objects.isNull(signInPoint)) throw new ValidationException("待查询签到点不存在");
        response.setContentType(ContentTypeDef.IMAGE_JPEG);
        FileUtils.setHeaderNoCache(response);
        OutputStream stream = response.getOutputStream();

        // 生成二维码，并写入输出流
        getQrCodeBytes(signInPoint, 200, stream);
    }

    @Override
    public void downloadSignGroupQrCode(Long signGroupId, HttpServletResponse response) {
        SignInGroupWithSubLayer signInGroupWithSubLayer = signInGroupDao.querySignInGroupWithSubLayer(signGroupId);
        if (Objects.isNull(signInGroupWithSubLayer)) {
            return;
        }

        try (Workbook wb = PoiExcelUtils.createWorkBook(ExcelType.XLS_X)) {
            exportSingleSignGroup(wb, signInGroupWithSubLayer);
            String fileName = "签到点二维码（" + TimeUtil.format(LocalDateTime.now(), TimeUtil.LONG_TIME_FORMAT) + "）";
            FileUtils.downloadExcel(response, wb, fileName, ContentTypeDef.APPLICATION_MS_EXCEL_07);
        } catch (Exception e) {
            ErrorUtils.exportError("签到点二维码", e);
        }
    }

    @Override
    public void downProjectLoadQrCode(Long projectId, HttpServletResponse response) {
        List<SignInGroupWithSubLayer> signInGroupWithSubLayers = signInGroupDao.querySignInGroupWithSubLayerByProjectId(projectId);
        if (CollectionUtils.isEmpty(signInGroupWithSubLayers)) {
            return;
        }

        try (Workbook wb = PoiExcelUtils.createWorkBook(ExcelType.XLS_X)) {
            for (SignInGroupWithSubLayer group : signInGroupWithSubLayers) {
                exportSingleSignGroup(wb, group);
            }

            String fileName = "签到点二维码（" + TimeUtil.format(LocalDateTime.now(), TimeUtil.LONG_TIME_FORMAT) + "）";
            FileUtils.downloadExcel(response, wb, fileName, ContentTypeDef.APPLICATION_MS_EXCEL_07);
        } catch (Exception e) {
            ErrorUtils.exportError("签到点二维码", e);
        }
    }

    private void exportSingleSignGroup(Workbook wb, SignInGroupWithSubLayer group) {
        List<SignInPoint> pointList = group.getSignInPointList();
        if (CollectionUtils.isEmpty(pointList)) {
            return;
        }

        pointList = getAfterSortSignInPoint(pointList, group.getSignInPointSequenceList());
        Sheet sheet = wb.createSheet(group.getName());
        Drawing drawing = sheet.createDrawingPatriarch();
        int row = 1;
        List<SignInPoint> singleRowPointList = new ArrayList<>();
        for (int i = 0; i < pointList.size(); i++) {
            if (i % 4 == 0 && i != 0) {
                writeImageToExcel(singleRowPointList, drawing, wb, sheet, row);
                row += 14;
                singleRowPointList.clear();
            }

            singleRowPointList.add(pointList.get(i));
        }

        writeImageToExcel(singleRowPointList, drawing, wb, sheet, row);
    }

    private void writeImageToExcel(List<SignInPoint> singleRowPointList, Drawing drawing, Workbook wb, Sheet sheet, int row) {
        if (CollectionUtils.isEmpty(singleRowPointList)) {
            return;
        }

        int startRow = row;
        int endRow = row + 8;
        int startCol = 1;

        for (int i = 0; i < singleRowPointList.size(); i++) {
            ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
            getQrCodeBytes(singleRowPointList.get(i), 233, byteArrayOut);
            XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 223, 223, (short) startCol, startRow, (short) startCol + 2, endRow);
            drawing.createPicture(anchor, wb.addPicture(byteArrayOut.toByteArray(), Workbook.PICTURE_TYPE_JPEG));
            String content = String.format("签到点名称：%s", singleRowPointList.get(i).getName());
            addCodeDescription(sheet, content, startCol, endRow + 1);
            startCol = startCol + 4;
        }
    }

    private void addCodeDescription(Sheet sheet, String content, int colNum, int rowNum) {
        Row row = PoiExcelUtils.createRow(sheet, rowNum);
        Cell cell = row.createCell(colNum);
        cell.setCellValue(content);
    }

    private void getQrCodeBytes(SignInPoint point, int size, OutputStream stream) {
        String qrCode = JsonTransferUtils.toJSONString(new QrInfo(point.getId(), point.getModelLabel()));

        try {
            BitMatrix bitMatrix = QrCodeUtils.createCode(qrCode, size, size);
            MatrixToImageWriter.writeToStream(bitMatrix, FileTypeDef.JPG, stream);
        } catch (Exception e) {
            log.error("生成二维码发生异常：", e);
        }
    }
}
