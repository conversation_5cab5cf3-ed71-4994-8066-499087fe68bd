package com.cet.eem.fusion.maintenance.core.controller.device;

import com.cet.eem.bll.common.model.node.EemNodeFieldInfo;
import com.cet.eem.bll.maintenance.service.device.TemplateService;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.electric.commons.ApiResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/7/17
 * @deprecated 已废弃
 */
@Deprecated
@Api(value = "ManageNodeMobileController", tags = "移动端：带模板的管网对象查询")
@RequestMapping(value = "/eem/v2/manageNode/mobile")
@RestController
@Validated
public class DeviceNodeController {
    @Autowired
    TemplateService templateService;

    @ApiOperation(value = "查询节点信息带有模板信息")
    @PostMapping(value = "/nodeByNodeFieldDefWithTemplate")
    public ApiResult<List<EemNodeFieldInfo>> queryNodeInfoByNodeFieldDefWithTemplate(@RequestBody BaseVo node) {
        return Result.ok(templateService.queryNodeInfoByNodeFieldDefWithTemplate(node));
    }
}
