package com.cet.eem.fusion.maintenance.core;

import com.cet.eem.fusion.maintenance.core.config.EemFusionMaintenanceBeanNameGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 描述：运维插件自动配置
 * <AUTHOR> (2025-08-15)
 */
@Slf4j
@Configuration
@EnableFeignClients(value = "com.cet.eem.fusion.common.feign.feign")
@ComponentScan(value = {"com.cet.eem.fusion.maintenance"},
        nameGenerator = EemFusionMaintenanceBeanNameGenerator.class)
public class MaintenanceConfigAutoConfiguration {
    public MaintenanceConfigAutoConfiguration(){
        log.info("Load eem solution 运维插件.");
    }
}
