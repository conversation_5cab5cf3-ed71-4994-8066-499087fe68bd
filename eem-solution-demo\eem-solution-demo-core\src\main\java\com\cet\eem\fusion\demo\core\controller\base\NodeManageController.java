package com.cet.eem.fusion.demo.core.controller.base;

import com.cet.eem.fusion.config.sdk.controller.EemBaseNodeManageController;
import com.cet.eem.solution.common.def.common.PluginInfoDef;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>  (2025/8/26 10:00)
 */
@Api(value = PluginInfoDef.Demo.INTERFACE_PREFIX + "/v1/node", tags = "项目节点管理")
@RequestMapping(value = PluginInfoDef.Demo.INTERFACE_PREFIX + "/v1/node")
@RestController
@Validated
public class NodeManageController extends EemBaseNodeManageController {
}