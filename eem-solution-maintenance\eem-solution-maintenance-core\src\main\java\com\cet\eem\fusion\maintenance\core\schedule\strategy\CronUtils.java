package com.cet.eem.fusion.maintenance.core.schedule.strategy;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.PlanSheet;
import com.cet.eem.bll.maintenance.schedule.util.PlanTimeUtils;
import com.cet.eem.fusion.common.exception.ValidationException;
import com.cet.eem.fusion.common.model.objective.physicalquantity.AggregationCycle;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import org.quartz.CalendarIntervalScheduleBuilder;
import org.quartz.CronScheduleBuilder;
import org.quartz.ScheduleBuilder;
import org.quartz.Trigger;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 描述：
 *
 * <AUTHOR>
 * @date 2023/2/14
 */
public class CronUtils {
    /**
     * 秒 分钟 小时 日 月 周 年
     */
    private static final String MONTH_FORMAT = "%s %s %s %s 1/%s ? *";

    /**
     * 创建cron，目前仅支持月度
     *
     * @param firstTime
     * @param cycle
     * @param step
     * @param planSheetId
     * @return
     */
    public static String createCron(Long firstTime, int cycle, Integer step, Long planSheetId) {
        if (Objects.isNull(step)) {
            step = 1;
        }

        LocalDateTime dt = TimeUtil.timestamp2LocalDateTime(firstTime);
        if (Objects.isNull(dt)) {
            throw new ValidationException(String.format("id=%s的计划无首次开始时间！", planSheetId));
        }
        String cron = null;
        if (Objects.equals(cycle, AggregationCycle.ONE_MONTH)) {
            cron = String.format(MONTH_FORMAT, dt.getSecond(), dt.getMinute(), dt.getHour(), dt.getDayOfMonth(), step);
        } else {
            throw new ValidationException(String.format("构造cron时无法处理聚合周期=%s的情况！", cycle));
        }

        return cron;
    }

    /**
     * 处理月周期触发器
     *
     * @param planSheet 巡检计划
     * @param month     月周期间隔
     * @return
     */
    public static ScheduleBuilder<Trigger> handleMonthTrigger(PlanSheet planSheet, Integer month) {
        if (month == 1) {
            Long realStartTime = PlanTimeUtils.getRealStartTime(planSheet);
            String cron = CronUtils.createCron(realStartTime, AggregationCycle.ONE_MONTH, month,
                    planSheet.getId());
            return (ScheduleBuilder) CronScheduleBuilder.cronSchedule(cron).withMisfireHandlingInstructionDoNothing();
        } else {
            return (ScheduleBuilder) CalendarIntervalScheduleBuilder
                    .calendarIntervalSchedule()
                    .withIntervalInMonths(month)
                    .withMisfireHandlingInstructionDoNothing();
        }
    }
}
