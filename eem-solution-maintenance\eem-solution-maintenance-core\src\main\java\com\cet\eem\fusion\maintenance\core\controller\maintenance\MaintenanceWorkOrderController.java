package com.cet.eem.fusion.maintenance.core.controller.maintenance;

import com.cet.eem.maintenanceservice.controller.bff.maintenance.MaintenanceWorkOrderBffController;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName : MaintenanceWorkOrderController
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-05-27 15:20
 */
@Api(value = "/eem/v1/maintenance/plan", tags = "工单：维保工单")
@RequestMapping(value = "/eem/v1/workorder/maintenance")
@RestController
@Validated
public class MaintenanceWorkOrderController extends MaintenanceWorkOrderBffController {

}
