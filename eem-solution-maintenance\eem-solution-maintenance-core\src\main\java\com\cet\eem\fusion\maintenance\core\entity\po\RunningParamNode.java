package com.cet.eem.fusion.maintenance.core.entity.po;

import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-14
 */
@Data
@ApiModel("运行参数节点树表")
@ModelLabel(ModelLabelDef.MEASURE_NODE_GROUP)
public class RunningParamNode extends EntityWithName {
    private String name;
    @JsonProperty("parentid")
    private Long parentId;


    public RunningParamNode() {
        this.modelLabel = ModelLabelDef.MEASURE_NODE_GROUP;
    }

    public RunningParamNode(String name, Long parentId) {
        this.name = name;
        this.parentId = parentId;
    }
}
