package com.cet.eem.fusion.maintenance.core.entity.devicemanage.component.vo;

import com.cet.eem.fusion.maintenance.core.entity.po.SparePartsDevice;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : DeviceVo
 * @Description : 返回设备时，前端回显需要设备类型的id
 * <AUTHOR> Administrator
 * @Date: 2021-05-24 18:10
 */
@Getter
@Setter
public class SparePartsDeviceVo extends SparePartsDevice {
private Long objectLabelId;

}