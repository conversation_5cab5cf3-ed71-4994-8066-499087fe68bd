package com.cet.eem.fusion.maintenance.core.dao.impl;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.InspectionScheme;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.InspectionSchemeWithSubLayer;
import com.cet.eem.bll.maintenance.dao.InspectionSchemeDao;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.fusion.common.modelutils.dao.ModelDaoImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/13
 */
@Repository
public class InspectionSchemeDaoImpl extends ModelDaoImpl<InspectionScheme> implements InspectionSchemeDao {
    @Override
    public List<InspectionSchemeWithSubLayer> query(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<InspectionScheme> wrapper=LambdaQueryWrapper.of(InspectionScheme.class)
                .in(InspectionScheme::getId,ids);
        return this.selectRelatedList(InspectionSchemeWithSubLayer.class, wrapper);
    }
}
