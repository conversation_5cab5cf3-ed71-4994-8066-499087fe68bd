package com.cet.eem.solution.common.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * <AUTHOR>
 * @date 2025/3/21 17:02
 */
@Configuration
public class FlinkRedisConfig {
    @Value("${spring.redis.host}")
    private String redisHostName;

    @Value("${spring.redis.port}")
    private Integer redisPort;

    @Value("${spring.redis.password}")
    private String redisPassword;

    @Value("${cet.eem.flink.redis.db:4}")
    private Integer flinkdb;

    @Bean
    public RedisStandaloneConfiguration flinkRedisStandaloneConfiguration() {
        RedisStandaloneConfiguration configuration = new RedisStandaloneConfiguration(redisHostName, redisPort);
        configuration.setPassword(redisPassword);
        configuration.setDatabase(flinkdb);
        return configuration;
    }

    @Bean
    public JedisConnectionFactory flinkJedisConnectionFactory() {
        JedisConnectionFactory factory = new JedisConnectionFactory(flinkRedisStandaloneConfiguration());
        factory.afterPropertiesSet(); // 确保属性加载完成
        return factory;
    }

    @Bean
    public RedisTemplate<String, Object> flinkRedisTemplate() {
        RedisTemplate<String, Object> flinkRedisTemplate = new RedisTemplate<>();
        flinkRedisTemplate.setConnectionFactory(flinkJedisConnectionFactory());
        flinkRedisTemplate.setKeySerializer(new StringRedisSerializer());
        flinkRedisTemplate.setHashKeySerializer(new StringRedisSerializer());
        flinkRedisTemplate.setHashValueSerializer(new StringRedisSerializer());
        return flinkRedisTemplate;
    }
}
