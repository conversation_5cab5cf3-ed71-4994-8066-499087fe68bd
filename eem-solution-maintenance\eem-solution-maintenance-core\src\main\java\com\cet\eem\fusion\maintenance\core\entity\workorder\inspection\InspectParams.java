package com.cet.eem.fusion.maintenance.core.entity.workorder.inspection;

import lombok.Getter;
import lombok.Setter;

/**
 * 巡检参数结果
 *
 * <AUTHOR>
 * @date 2021/5/7
 */
@Getter
@Setter
public class InspectParams {
    private Long paramId;
    private Boolean status;
    private Double value;
    private String textValue;

    public InspectParams(Long paramId, Boolean status) {
        this.paramId = paramId;
        this.status = status;
    }

    public InspectParams(Long paramId, Double value) {
        this.paramId = paramId;
        this.value = value;
    }

    public InspectParams() {
    }
}
