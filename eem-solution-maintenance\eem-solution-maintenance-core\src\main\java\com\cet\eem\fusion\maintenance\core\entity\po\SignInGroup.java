package com.cet.eem.fusion.maintenance.core.entity.po;

import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : SignInGroup
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-03-12 11:01
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.REGISTRATION_GROUP)
public class SignInGroup extends EntityWithName {

    /**
     * 项目id
     */
    @JsonProperty(ColumnDef.PROJECT_ID)
    private Long projectId;

    @JsonProperty(ColumnDef.RELATED_GRAPH)
    private String relatedGraph;

    public SignInGroup() {
        this.modelLabel = ModelLabelDef.REGISTRATION_GROUP;
    }
}
