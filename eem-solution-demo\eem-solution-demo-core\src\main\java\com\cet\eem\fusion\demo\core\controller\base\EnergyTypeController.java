package com.cet.eem.fusion.demo.core.controller.base;

import com.cet.eem.fusion.config.sdk.controller.BaseEemEnergyTypeController;
import com.cet.eem.solution.common.def.common.PluginInfoDef;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>  (2025/8/26 9:57)
 */
@Api(value = "能源类型相关操作", tags = "基础配置包-能源类型相关操作")
@RequestMapping(value = PluginInfoDef.Demo.INTERFACE_PREFIX + "/v1/energy-type")
@RestController
@Validated
public class EnergyTypeController extends BaseEemEnergyTypeController  {
}
