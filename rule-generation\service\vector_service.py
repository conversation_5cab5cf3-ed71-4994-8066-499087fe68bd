"""
向量服务
封装向量库相关的业务逻辑
"""

import os
import logging
from typing import List, Dict, Any, Tuple

from utils.code_embedding import CodeEmbedding
from utils.file_utils import FileUtils
from utils.milvus_utils import MilvusUtils
# 不再导入DEFAULT_PROJECT_PATH，项目路径必须通过参数传入

logger = logging.getLogger(__name__)


class VectorService:
    """向量服务类"""
    
    def __init__(self):
        """初始化服务"""
        self.code_embedding = CodeEmbedding()
        self.milvus_utils = MilvusUtils()
        self.file_utils = FileUtils()
        self._collection_initialized = False
    
    def initialize_vector_database(self, project_path: str = None, recreate: bool = False, collection_name: str = None) -> Dict[str, Any]:
        """
        初始化向量库

        Args:
            project_path (str): 项目路径，默认使用配置中的路径
            recreate (bool): 是否重新创建向量库，默认False
            collection_name (str): 指定集合名称，默认使用配置中的名称

        Returns:
            Dict[str, Any]: 初始化结果
        """
        if project_path is None:
            raise ValueError("项目路径不能为空，请通过参数指定项目路径")
        
        logger.info("=" * 50)
        logger.info("开始初始化向量库")
        logger.info(f"项目路径: {project_path}")
        logger.info(f"重新创建: {recreate}")
        logger.info(f"集合名称: {collection_name or '使用默认配置'}")
        logger.info("=" * 50)
        
        try:
            # 1. 创建或获取集合
            logger.info("正在创建或获取Milvus集合...")
            collection = self.milvus_utils.create_collection(recreate=recreate, collection_name=collection_name)
            
            # 2. 扫描Java文件
            java_files = self.file_utils.scan_java_files(project_path)
            if not java_files:
                logger.warning("未找到Java文件，初始化终止")
                return {"success": False, "message": "未找到Java文件"}
            
            # 2.5. 预分析文件大小分布
            logger.info("分析文件大小分布...")
            self._analyze_file_sizes(java_files)
            
            # 3. 批量读取文件内容
            logger.info("开始读取文件内容...")
            file_contents = self.file_utils.batch_read_files(java_files)
            
            if not file_contents:
                logger.warning("未能读取到任何文件内容，初始化终止")
                return {"success": False, "message": "未能读取到任何文件内容"}
            
            # 4. 生成向量
            logger.info("开始生成代码向量...")
            class_names = [item[0] for item in file_contents]
            contents = [item[1] for item in file_contents]
            file_paths = [item[2] for item in file_contents]
            
            # 获取相对路径用于存储
            base_path = os.path.abspath(project_path)
            relative_paths = [
                self.file_utils.get_relative_path(fp, base_path) 
                for fp in file_paths
            ]
            
            # 批量生成向量
            embeddings = self.code_embedding.batch_get_embeddings(contents)
            vector_lists = [
                self.code_embedding.embedding_to_list(emb) 
                for emb in embeddings
            ]
            
            # 5. 插入向量库
            logger.info("开始插入向量数据...")
            inserted_ids = self.milvus_utils.insert_vectors(
                class_names, contents, vector_lists
            )
            
            # 标记集合已初始化
            self._collection_initialized = True
            
            logger.info("=" * 50)
            logger.info("向量库初始化完成")
            logger.info(f"成功处理文件数: {len(inserted_ids)}")
            stats = self.milvus_utils.get_collection_stats()
            logger.info(f"向量库统计: {stats}")
            logger.info("=" * 50)
            
            return {
                "success": True,
                "message": "向量库初始化成功",
                "processed_files": len(inserted_ids),
                "stats": stats
            }
            
        except Exception as e:
            logger.error(f"初始化向量库失败: {e}")
            
            # 如果是字段长度错误，建议重新创建集合
            if "exceeds max length" in str(e):
                logger.error("检测到字段长度超限错误，建议使用 recreate=True 重新创建集合")
                logger.error("或者检查config.py中的max_content_length配置")
            
            return {
                "success": False,
                "message": f"初始化向量库失败: {e}",
                "error": str(e)
            }
    
    def _analyze_file_sizes(self, file_paths: List[str]):
        """
        分析文件大小分布
        
        Args:
            file_paths (List[str]): 文件路径列表
        """
        try:
            sizes = []
            large_files = []
            very_large_files = []
            
            for file_path in file_paths:
                try:
                    size = os.path.getsize(file_path)
                    sizes.append(size)
                    
                    if size > 2 * 1024 * 1024:  # >2MB
                        very_large_files.append((file_path, size))
                    elif size > 500 * 1024:  # >500KB
                        large_files.append((file_path, size))
                        
                except Exception:
                    continue
            
            if sizes:
                avg_size = sum(sizes) / len(sizes)
                max_size = max(sizes)
                
                logger.info(f"文件大小统计:")
                logger.info(f"  总文件数: {len(sizes)}")
                logger.info(f"  平均大小: {avg_size/1024:.1f}KB")
                logger.info(f"  最大文件: {max_size/1024:.1f}KB")
                logger.info(f"  大文件(>500KB): {len(large_files)}")
                logger.info(f"  超大文件(>2MB): {len(very_large_files)}")
                
                if very_large_files:
                    logger.warning("发现超大文件，将被跳过:")
                    for file_path, size in very_large_files[:3]:
                        logger.warning(f"  {os.path.basename(file_path)}: {size/1024/1024:.1f}MB")
                        
        except Exception as e:
            logger.warning(f"文件大小分析失败: {e}")
    
    def get_collection_status(self) -> Dict[str, Any]:
        """
        获取集合状态
        
        Returns:
            Dict[str, Any]: 集合状态信息
        """
        try:
            if self.milvus_utils.collection_exists():
                stats = self.milvus_utils.get_collection_stats()
                logger.info(f"集合存在，包含 {stats.get('row_count', 0)} 条记录")
                return {"exists": True, "stats": stats}
            else:
                logger.info("集合不存在")
                return {"exists": False, "stats": {}}
        except Exception as e:
            logger.error(f"检查集合状态失败: {e}")
            return {"exists": False, "error": str(e)}
    
    def cleanup(self):
        """清理资源"""
        try:
            self.milvus_utils.release_collection()
            logger.info("资源清理完成")
        except Exception as e:
            logger.warning(f"资源清理失败: {e}")
