package com.cet.eem.fusion.maintenance.core.entity.devicemanage.component;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName : QueryImport
 * @Description : 导入零件时选择的节点接收类
 * <AUTHOR> jiangzixuan
 * @Date: 2021-05-25 16:56
 */
@Data
public class QueryImport {
    @NotNull(message = "选择的节点id不能为空")
    private Long id;
    @NotNull(message = "选择的节点类型不能为空")
    private String modelLabel;
}