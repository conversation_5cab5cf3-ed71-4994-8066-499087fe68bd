package com.cet.eem.fusion.maintenance.core.dao.devicemanager;

import com.cet.eem.bll.maintenance.model.devicemanage.template.AttributeTemplate;
import com.cet.eem.bll.maintenance.model.devicemanage.template.EquipmentNodeTreeDto;
import com.cet.eem.bll.maintenance.model.devicemanage.template.NodeWithTemplate;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-05-11
 */
public interface TemplateNodeTreeDao extends BaseModelDao<EquipmentNodeTreeDto> {

    List<NodeWithTemplate> queryNodeTemplate(List<Long> ids);


    List<NodeWithTemplate> queryNodeWithTemplates(Long id);

    ResultWithTotal<List<NodeWithTemplate>> queryNodeWithTemplates(Long id, Page page);

    NodeWithTemplate NodeWithTemplateName(AttributeTemplate template, Long parentId);


    EquipmentNodeTreeDto queryNodeByIdAndName(Long id, String name);


    EquipmentNodeTreeDto selectByNameAndParentId(EquipmentNodeTreeDto data);


    List<EquipmentNodeTreeDto> selectByParentIds(List<Long> parentIds);


    List<EquipmentNodeTreeDto> selectByProjectId(Long projectId);


    NodeWithTemplate checkRepeat(AttributeTemplate update, Long parentId);

    /**
     * 根据项目查询设备模板分组以及设备模板
     *
     * @param projectId
     * @return
     */
    List<NodeWithTemplate> queryNodeTemplateByProject(@NotNull Long projectId);

    /**
     * 根据名称，id和父id进行编辑
     * @param data
     * @return
     */
    EquipmentNodeTreeDto selectByNameAndParentIdAndId(EquipmentNodeTreeDto data);
}
