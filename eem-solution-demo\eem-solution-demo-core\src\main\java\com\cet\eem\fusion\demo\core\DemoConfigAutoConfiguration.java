package com.cet.eem.fusion.demo.core;

import com.cet.eem.fusion.demo.core.config.EemFusionDemoBeanNameGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * 描述：自动配置
 * <AUTHOR>  (2025/7/17 15:16)
 */
@Slf4j
@Configuration
@EnableFeignClients(value = "com.cet.eem.fusion.common.feign.feign")
@ComponentScan(value = {"com.cet.eem.fusion.demo"},
        nameGenerator = EemFusionDemoBeanNameGenerator.class)
public class DemoConfigAutoConfiguration {
    public DemoConfigAutoConfiguration(){
        log.info("Load eem solution demo.");
    }
}
