package com.cet.eem.fusion.maintenance.core.entity.inspector;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @ClassName : InspectorPasswordChangeRequest
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-04-02 09:33
 */
@Getter
@Setter
@ApiModel(value = "InspectorPasswordCheckRequest", description = "巡检人员密码修改")
public class InspectorPasswordCheckRequest {

    /**
     * 巡检人员ID
     */
    @NotNull(message = "巡检人员ID")
    private Long id;

    /**
     * 密码
     */
    @NotEmpty(message = "密码不能为空")
    @ApiModelProperty("使用特殊字符串#密码#unix时间戳进行md5签名后的字符串, 如Test#mima123456#1587018761000。\\n\" +\n" +
            "            \"特殊字符串和开发人员确定, 时间戳需要和timestamps一致")
    private String password;

    @ApiModelProperty("时间戳")
    private Long timestamps;
}
