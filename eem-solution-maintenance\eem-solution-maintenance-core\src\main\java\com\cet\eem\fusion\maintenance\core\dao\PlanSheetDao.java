package com.cet.eem.fusion.maintenance.core.dao;

import com.cet.eem.bll.common.model.domain.subject.powermaintenance.PlanSheet;
import com.cet.eem.bll.common.model.ext.subject.powermaintenance.PlanSheetWithSubLayer;
import com.cet.eem.bll.maintenance.model.plan.QueryInspectionPlanRequest;
import com.cet.eem.bll.maintenance.model.plan.QueryMaintenancePlanRequest;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.modelutils.dao.BaseModelDao;

import java.util.List;

public interface PlanSheetDao extends BaseModelDao<PlanSheet> {

    /**
     * 分页查询子级节点
     *
     * @param queryInspectionPlanRequest
     * @return
     */
    ResultWithTotal<List<PlanSheetWithSubLayer>> queryInspectionPlanSheetSubLayerWithPage(QueryInspectionPlanRequest queryInspectionPlanRequest);

    /**
     * 查询维保计划
     *
     * @param queryMaintenancePlanRequest
     * @return
     */
    ResultWithTotal<List<PlanSheetWithSubLayer>> queryMaintenancePlanSheetSubLayerWithPage(QueryMaintenancePlanRequest queryMaintenancePlanRequest);

    /**
     * 查询未结束的巡检计划
     *
     * @return
     */
    List<PlanSheet> queryUnFinishedPlan();

    /**
     * 删除计划
     *
     * @param ids
     */
    void deletePlanSheets(List<Long> ids);

    /**
     * 根据执行策略查询巡检计划
     *
     * @param strategyType
     * @return
     */
    List<PlanSheetWithSubLayer> queryMaintenancePlanSheetByStrategyType(Integer strategyType);

    /**
     * 定时任务查询维保计划，针对不能根据GlobalInfoUtils获取项目id的情况
     * @param queryMaintenancePlanRequest
     * @return
     */
    ResultWithTotal<List<PlanSheetWithSubLayer>> queryMaintenancePlanBySchedule(QueryMaintenancePlanRequest queryMaintenancePlanRequest);

    /**
     * 查询项目下的巡检计划（全部
     * @param projectId
     * @return
     */
    List<PlanSheetWithSubLayer> queryPlanSheet(Long projectId);
}
