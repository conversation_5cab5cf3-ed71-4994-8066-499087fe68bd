﻿package com.cet.eem.fusion.refrigeration.core.controller;

import com.cet.eem.fusion.refrigeration.core.handle.optimizationstrategy.OptimizationStrategyHandle;
import com.cet.eem.common.model.Result;
import com.cet.eem.common.utils.TimeUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@Api(value = "OptimizationStrategyController", tags = {"冷机优化策略接口"})
@RestController
@RequestMapping(value = "/eem/v1/optimizationStrategy")
public class OptimizationStrategyController {
    @Autowired
    OptimizationStrategyHandle optimizationStrategyHandle;

    @ApiOperation(value = "手动执行冷机优化策略计算")
    @PostMapping(value = "/calcOptimizationStrategy", produces = "application/json")
    public Result<Object> calcOptimizationStrategy(@RequestParam Long time) {
        optimizationStrategyHandle.calcOptimizationStrategy(TimeUtil.timestamp2LocalDateTime(time));
        return Result.ok();
    }

}

