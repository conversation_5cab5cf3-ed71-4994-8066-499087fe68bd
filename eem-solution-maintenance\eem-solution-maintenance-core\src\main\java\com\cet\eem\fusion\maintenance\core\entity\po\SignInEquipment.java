package com.cet.eem.fusion.maintenance.core.entity.po;

import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.modelutils.annotation.ModelLabel;
import com.cet.eem.fusion.common.modelutils.model.model.EntityWithName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName : SingInEquipment
 * @Description :
 * <AUTHOR> zhangh
 * @Date: 2021-03-12 11:02
 */
@Getter
@Setter
@ModelLabel(ModelLabelDef.REGISTRATION_EQUIPMENT)
public class SignInEquipment extends EntityWithName {

    /**
     * 设备或房间Id
     */
    @JsonProperty("objectid")
    private Long objectId;

    /**
     * 设备或房间模型
     */
    @JsonProperty("objectlabel")
    private String objectLabel;

    public SignInEquipment() {
        this.modelLabel = ModelLabelDef.REGISTRATION_EQUIPMENT;
    }

    public boolean contentEquals(SignInEquipment signInEquipment) {
        return this.getObjectLabel().equals(signInEquipment.getObjectLabel()) && this.getObjectId().equals(signInEquipment.getObjectId());
    }
}
