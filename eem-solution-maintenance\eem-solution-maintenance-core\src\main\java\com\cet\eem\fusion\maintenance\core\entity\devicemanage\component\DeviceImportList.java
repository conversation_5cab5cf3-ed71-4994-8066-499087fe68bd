package com.cet.eem.fusion.maintenance.core.entity.devicemanage.component;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName : DeviceImportList
 * @Description : 从零件导入到备件时，接收前端传来的数据
 * <AUTHOR> jiangzixuan
 * @Date: 2021-06-07 15:15
 */
@Data
public class DeviceImportList {
    @NotNull
    private String model;
    @NotNull
    private Long id;
    @NotNull
    private String modelLabel;

    public DeviceImportList() {

    }

    public DeviceImportList(String modelLabel, String model) {
        this.modelLabel = modelLabel;
        this.model = model;
    }
}