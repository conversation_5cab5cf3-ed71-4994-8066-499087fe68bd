﻿package com.cet.eem.fusion.refrigeration.core.impl;

import com.cet.eem.fusion.refrigeration.core.dao.weather.RefrigeratingRunningStrategyDao;
import com.cet.eem.bll.common.model.domain.subject.energysaving.RefrigeratingRunningStrategy;
import com.cet.eem.conditions.query.LambdaQueryWrapper;
import com.cet.eem.dao.ModelDaoImpl;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName : RefrigeratingRunningStrategyDaoImpl
 * @Description :
 * <AUTHOR> jiangzixuan
 * @Date: 2021-12-16 18:57
 */
@Repository
public class RefrigeratingRunningStrategyDaoImpl extends ModelDaoImpl<RefrigeratingRunningStrategy> implements RefrigeratingRunningStrategyDao {


    @Override
    public List<RefrigeratingRunningStrategy> queryStrategy(LocalDateTime st, LocalDateTime et, Integer cycle, Long roomId, Integer type) {
        LambdaQueryWrapper<RefrigeratingRunningStrategy> wrapper = LambdaQueryWrapper.of(RefrigeratingRunningStrategy.class);
        wrapper.ge(RefrigeratingRunningStrategy::getLogTime, st)
                .lt(RefrigeratingRunningStrategy::getLogTime, et)
                .eq(RefrigeratingRunningStrategy::getAggregationCycle, cycle)
                .eq(RefrigeratingRunningStrategy::getRoomId, roomId);
        if (Objects.nonNull(type)){
            wrapper.eq(RefrigeratingRunningStrategy::getDeviceOperationType, type);
        }

        return this.selectList(wrapper);
    }
}
