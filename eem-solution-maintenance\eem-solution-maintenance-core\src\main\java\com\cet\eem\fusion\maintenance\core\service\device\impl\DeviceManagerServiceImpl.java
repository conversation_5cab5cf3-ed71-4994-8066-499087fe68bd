package com.cet.eem.fusion.maintenance.core.service.device.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.cet.eem.auth.service.impl.CommonAuthService;
import com.cet.eem.fusion.config.sdk.service.EemNodeService;
import com.cet.eem.bll.common.dao.pecdeviceevent.PecDeviceExtendDao;
import com.cet.eem.bll.common.dao.powersystem.DeviceCommonInfoDao;
import com.cet.eem.fusion.config.sdk.def.OperationLogType;
import com.cet.eem.bll.common.log.service.CommonUtilsService;
import com.cet.eem.bll.common.model.domain.object.physicalquantity.MeasuredbyVo;
import com.cet.eem.bll.common.model.domain.object.powersystem.DeviceCommonInfo;
import com.cet.eem.bll.common.model.domain.perception.logicaldevice.PecDeviceExtendVo;
import com.cet.eem.fusion.config.sdk.utils.GlobalInfoUtils;
import com.cet.eem.bll.demand.constant.TableColumnNameDef;
import com.cet.eem.bll.maintenance.dao.devicemanager.*;
import com.cet.eem.bll.maintenance.model.devicemanage.*;
import com.cet.eem.bll.maintenance.model.devicemanage.template.*;
import com.cet.eem.bll.maintenance.service.device.DeviceManagerService;
import com.cet.eem.bll.maintenance.service.device.TemplateService;
import com.cet.eem.bll.maintenance.utils.ExcelExportFactory;
import com.cet.eem.bll.maintenance.utils.QrCodeUtils;
import com.cet.eem.fusion.common.utils.CommonUtils;
import com.cet.eem.fusion.common.utils.ErrorUtils;
import com.cet.eem.fusion.common.def.base.ExcelType;
import com.cet.eem.fusion.common.def.common.ColumnDef;
import com.cet.eem.common.definition.ContentTypeDef;
import com.cet.eem.fusion.common.def.label.ModelLabelDef;
import com.cet.eem.fusion.common.def.label.NodeLabelDef;
import com.cet.eem.fusion.common.exception.ValidationException;
import com.cet.eem.fusion.common.utils.file.FileUtils;
import com.cet.eem.fusion.common.model.BaseVo;
import com.cet.eem.fusion.common.model.Page;
import com.cet.electric.commons.ApiResult;
import com.cet.electric.commons.ApiResult;
import com.cet.eem.fusion.common.utils.page.PageUtils;
import com.cet.eem.fusion.common.utils.JsonTransferUtils;
import com.cet.eem.fusion.common.utils.excel.PoiExcelUtils;
import com.cet.electric.baseconfig.sdk.common.utils.TimeUtil;
import com.cet.eem.fusion.common.modelutils.conditions.query.LambdaQueryWrapper;
import com.cet.eem.conditions.query.QueryWrapper;
import com.cet.eem.fusion.common.modelutils.model.base.ConditionBlock;
import com.cet.eem.fusion.common.modelutils.model.base.QueryCondition;
import com.cet.eem.model.base.SingleModelConditionDTO;
import com.cet.eem.fusion.common.modelutils.model.tool.ModelServiceUtils;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;
import com.cet.eem.fusion.common.modelutils.model.tool.ParentQueryConditionBuilder;
import com.cet.eem.fusion.common.modelutils.model.tool.QueryResultContentTaker;
import com.cet.eem.service.EemModelDataService;
import com.cet.electric.modelservice.common.entity.IdTextPair;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.hssf.usermodel.HSSFPatriarch;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: fyl
 * @Description:
 * @Data: Created in 2021-04-14
 */
@Service
@Slf4j
public class DeviceManagerServiceImpl implements DeviceManagerService {
    @Autowired
    private DeviceManagerServiceDao deviceDao;
    @Autowired
    private TechParamValueDao techParamValueDao;
    @Autowired
    private TemplateDao templateDao;
    @Autowired
    private TemplateService templateService;
    @Autowired
    private TemplateGroupDao templateGroupDao;
    @Autowired
    private PowerDisCabinetDao powerDisCabinetDao;
    @Autowired
    private ArrayCabiNetDao arrayCabiNetDao;
    @Autowired
    private DeviceCommonInfoDao deviceCommonInfoDao;
    @Autowired
    EemModelDataService modelService;
    @Autowired
    PecDeviceExtendDao pecDeviceExtendDao;
    @Autowired
    NodeDao nodeDao;
    @Autowired
    CommonUtilsService commonUtilsService;
    @Autowired
    ModelServiceUtils modelServiceUtils;
    @Autowired
    protected CommonAuthService authManageService;
    private static String REGEX = "-";

    @Override
    public ApiResult<String> qrCode(Long userId, EquipmentSearchDto dto, HttpServletResponse response) throws IOException {
        QueryCondition queryCondition = new QueryCondition(dto.getEquipmentLabel(), dto.getEquipmentId());
        List<Map<String, Object>> result = deviceDao.queryDevice(queryCondition);
        Assert.isTrue(CollectionUtils.isNotEmpty(result), "未查询到设备！");
        Long deviceIdForQr = QueryResultContentTaker.getId(result.get(0));
        response.setContentType(ContentTypeDef.IMAGE_JPEG);
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);
        OutputStream stream = response.getOutputStream();

        String qrContent = JsonTransferUtils.toJSONString(new QrInfo(deviceIdForQr, dto.getEquipmentLabel()));
        BitMatrix bitMatrix = QrCodeUtils.createCode(qrContent, 200, 200);
        MatrixToImageWriter.writeToStream(bitMatrix, "jpg", stream);
        return new Result<>();
    }

    @Override
    public ApiResult<String> exportQrCodees(Long userId, EquipmentExportInfo info, HttpServletResponse response) {
        List<Map<String, Object>> equipmentUnderSelected = getEquipmentUnderSelected(info.getId(), info.getModelLabel(), userId);
        if (CollectionUtils.isEmpty(equipmentUnderSelected)) {
            throw new ValidationException("请选择项目或者房间节点进行二维码导出");
        }
        Assert.isTrue(info.getModelLabel().equals(NodeLabelDef.ROOM) || info.getModelLabel().equals(NodeLabelDef.PROJECT), "仅支持导出管理层级下设备二维码！");
        List<String> colLst = new ArrayList<>();
        ExcelExportFactory excelExportFactory = new ExcelExportFactory();
        colLst.addAll(Arrays.asList(new String[]{"name", "code", "roomname"}));
        excelExportFactory.setExcelType("device");
        excelExportFactory.setColLst(colLst);
        try (HSSFWorkbook wb = new HSSFWorkbook()) {
            HSSFSheet sheet1 = wb.createSheet("sheet1");
            HSSFPatriarch patriarch = sheet1.createDrawingPatriarch();
            List<Map<String, Object>> onePageLst = new ArrayList<>();
            int n = 0;
            for (int i = 0; i < equipmentUnderSelected.size(); i++) {
                onePageLst.add(equipmentUnderSelected.get(i));
                if (equipmentUnderSelected.size() == i + 1) {
                    excelExportFactory.imageIntoExcel(n, onePageLst, patriarch, wb, sheet1, info);
                    break;
                }
                if ((i + 1) % 4 == 0) {
                    excelExportFactory.imageIntoExcel(n, onePageLst, patriarch, wb, sheet1, info);
                    n++;
                    onePageLst.clear();
                }
            }
            String fileName = "导出二维码-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8") + ".xls");
            response.setCharacterEncoding("UTF-8");
            wb.write(response.getOutputStream());
            return null;
        } catch (Exception e) {
            log.error("导出二维码异常 ->{}", e.getMessage());
            return new Result<>();
        }
    }


    @Override
    public List<TechParamValue> queryTechParam(EquipmentSearchDto searchDto) {
        DeviceCommonInfo deviceCommonInfo = deviceCommonInfoDao.queryDeviceInfoByObject(searchDto.getEquipmentId(), searchDto.getEquipmentLabel());
        if (Objects.isNull(deviceCommonInfo) || Objects.isNull(deviceCommonInfo.getNodetemplateid())) {
            return Collections.EMPTY_LIST;
        }
        List<TechParamValue> list = techParamValueDao.queryTechParam(searchDto);
        List<AttributeTemplate> templates = templateDao.getTemplates(Collections.singletonList(deviceCommonInfo.getNodetemplateid()));
        //如果模板为空或者模板下技术参数为空 返回空值
        if (CollectionUtils.isEmpty(templates)) {
            return Collections.EMPTY_LIST;
        }
        if (CollectionUtils.isEmpty(templates.get(0).getTechParams())) {
            return Collections.EMPTY_LIST;
        }
        if (CollectionUtils.isNotEmpty(list)) {
            List<TechParamValue> techParamValues = packageTechParamValueResult(list, templates);
            //技术参数增加一个value字段，类型是字符串，兼容以前的查询，number有值，value没值的情况，value赋值number的内容，number入库的类型是int
            assembleStringValue(techParamValues);
            return techParamValues;
        }
        List<TechParamValue> techParamValues = packageTechParamValue(templates.get(0).getTechParams());
        assembleStringValue(techParamValues);
        return techParamValues;
    }

    /**
     * 根据模板中技术参数信息来拼接返回值信息
     *
     * @param list
     * @param templates
     * @return
     */
    private List<TechParamValue> packageTechParamValueResult(List<TechParamValue> list, List<AttributeTemplate> templates) {
        List<TechParamValue> result = new ArrayList<>();
        List<TechParam> techParams = templates.get(0).getTechParams();
        if (CollectionUtils.isEmpty(techParams)) {
            return Collections.emptyList();
        }
        techParams.forEach(techParam -> {
            List<TechParamValue> filter = list.stream().filter(tparam -> tparam.getTechParamTemplateId().equals(techParam.getId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filter)) {
                filter.forEach(techParamValue -> {
                    techParamValue.setName(techParam.getName());
                    techParamValue.setUnit(techParam.getUnit());
                    techParamValue.setTechParamTemplateId(techParam.getId());
                    //如果value字段没数据，那么赋值number字段，number字段入库是int类型
                    if (Objects.isNull(techParamValue.getValue()) && Objects.nonNull(techParamValue.getNumber())) {
                        techParamValue.setValue(String.valueOf(techParamValue.getNumber().intValue()));
                    }
                });
                result.addAll(filter);
            } else {
                result.add(new TechParamValue(techParam.getName(), techParam.getUnit(), techParam.getId()));
            }
        });
        return result;
    }

    private void assembleStringValue(List<TechParamValue> list) {
        for (TechParamValue value : list) {
            if (Objects.isNull(value.getValue()) && Objects.nonNull(value.getNumber())) {
                //原先入库的都是int类型
                value.setValue(String.valueOf(value.getNumber().intValue()));
            }
        }
    }

    @Override
    public void writeTechParamValue(List<TechParamValue> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        checkTechParamValue(list);
        writeImportTechParamValue(list);
        commonUtilsService.writeAddOperationLogs(EEMOperationLogType.DEVICE_MANAGE, "创建技术参数值", list);
    }

    private void checkTechParamValue(List<TechParamValue> list) {
        Long count = list.stream().map(TechParamValue::getName).distinct().count();
        Assert.isTrue(count.intValue() == list.size(), "写入技术参数有同名数据");
    }

    @Override
    public List<RunningParamGroup> queryEquipmentRunningParam(EquipmentSearchDto searchDto) {
        //模型修改后需要变更
        DeviceCommonInfo deviceCommonInfo = deviceCommonInfoDao.queryDeviceInfoByObject(searchDto.getEquipmentId(), searchDto.getEquipmentLabel());
        Long templateId = deviceCommonInfo.getNodetemplateid();
        if (Objects.isNull(templateId)) {
            return Collections.EMPTY_LIST;
        }
        AttributeTemplate template = templateService.getTemplate(templateId);
        return template.getRunningParam();
    }

    @Override
    public List<BaseWithTemplate> queryTemplateName(BaseVo baseVo) {
        List<BaseVo> baseVos = queryBaseVo(baseVo);
        if (CollectionUtils.isEmpty(baseVos)) {
            return Collections.emptyList();
        }

        List<DeviceCommonInfo> deviceCommonInfos = deviceCommonInfoDao.queryDeviceInfoByObjects(baseVos);
        // 将查询的节点放在第一个
        if (baseVos.size() > 1) {
            List<BaseVo> collect = baseVos.stream().filter(baseVo1 -> Objects.equals(baseVo.getId(), baseVo1.getId()) && baseVo1.getModelLabel().equals(baseVo.getModelLabel())).collect(Collectors.toList());
            BaseVo baseVo1 = collect.get(0);
            baseVos.remove(baseVo1);
            baseVos.add(0, baseVo1);
        }
        if (CollectionUtils.isNotEmpty(deviceCommonInfos)) {
            List<Long> collect = deviceCommonInfos.stream().map(DeviceCommonInfo::getNodetemplateid).filter(Objects::nonNull).collect(Collectors.toList());
            Map<Long, String> nodeTemplateAndParentNode = templateService.queryNodeTemplateParentPath(GlobalInfoUtils.getTenantId(), collect);
            if (CollectionUtils.isNotEmpty(collect)) {
                List<AttributeTemplate> templates = templateDao.queryNodeTemplates(collect);
                return packageBaseWithTemplate(baseVos, templates, deviceCommonInfos, nodeTemplateAndParentNode);
            }
        }

        return packageBaseWithTemplate(baseVos, null, null, null);
    }

    private List<BaseVo> packageBaseVo(List<LineSegmentVo> list) {
        List<BaseVo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.EMPTY_LIST;
        }
        list.forEach(lineSegmentVo -> {
            result.add(new BaseVo(lineSegmentVo.getId(), lineSegmentVo.getModelLabel(), lineSegmentVo.getName()));
        });
        return result;
    }


    @Override
    public List<RunningParamValue> dataServiceParam(Long groupId, List<MeasuredbyVo> measuredby) {
        if (CollectionUtils.isEmpty(measuredby)) {
            return Collections.EMPTY_LIST;
        }
        List<Long> deviceIds = measuredby.stream().map(MeasuredbyVo::getMeasuredby).collect(Collectors.toList());
        //查询metertype枚举值
        List<IdTextPair> meterTypes = modelService.getEnumrationByModel(ModelLabelDef.METER_TYPE).getData();
        //通过模板分组名称匹配metertypeid 获取对应的deviceid
        TemplateGroupDto templateGroupDto = templateGroupDao.selectById(groupId);
        if (Objects.isNull(templateGroupDto)) {
            return Collections.EMPTY_LIST;
        }
        Optional<Integer> firstTypeOption = meterTypes.stream().filter(enumItem -> enumItem.getId().equals(templateGroupDto.getMeterType())).map(IdTextPair::getId).findFirst();
        Integer meterType = null;
        if (firstTypeOption.isPresent()) {
            meterType = firstTypeOption.get();
        }
        List<PecDeviceExtendVo> pecDeviceExtendVos = pecDeviceExtendDao.queryPecDeviceExtList(deviceIds, Collections.singletonList(meterType));
        GroupWithRunningParam runningParam = templateGroupDao.getRunningParam(groupId);
        if (Objects.isNull(runningParam)) {
            return Collections.EMPTY_LIST;
        }
        List<RunningParam> runningParams = runningParam.getList();
        if (CollectionUtils.isEmpty(runningParams)) {
            return Collections.EMPTY_LIST;
        }
        return packageRunningParamResult(pecDeviceExtendVos, runningParams);
    }

    @Override
    public ResultWithTotal<List<Map<String, Object>>> getNodeData(DeviceInfoRequest request, Long userId) {
        List<Map<String, Object>> results;
        if (Objects.isNull(request.getSelected())) {
            return ResultWithTotal.ok();
        }
        List<IdTextPair> data = modelService.getEnumrationByModel(ModelLabelDef.DEVICE_CLASS).getData();
        List<String> subLabels = data.stream().map(IdTextPair::getPropertyLabel).collect(Collectors.toList());
        QueryCondition queryCondition = ParentQueryConditionBuilder.of(request.getSelected().getModelLabel())
                .where(ColumnDef.ID, ConditionBlock.OPERATOR_EQ, request.getSelected().getId())
                .leftJoin(subLabels)
                .queryAsTree(true)
                .build();
        if (request.getSelected().getModelLabel().equals(ModelLabelDef.PROJECT) || request.getSelected().getModelLabel().equals(ModelLabelDef.ROOM)) {
            results = getEquipmentUnderSelected(queryCondition, userId);
        } else {
            results = nodeDao.queryNodes(Collections.singletonList(request.getSelected()));
        }
        return handleNodeData(results, request.getKeyword(), request.getPage());
    }

    @Override
    public ResultWithTotal<List<Map<String, Object>>> getNodeData(DeviceInfoRequestControlByFront deviceInfoRequestControlByFront, Long userId) {
        List<Map<String, Object>> results;
        Page page = deviceInfoRequestControlByFront.getPage();
        QueryCondition queryCondition = deviceInfoRequestControlByFront.getQueryCondition();
        List<SingleModelConditionDTO> subLayerConditions = queryCondition.getSubLayerConditions();
        subLayerConditions.removeIf(singleModelConditionDTO -> Objects.equals(ModelLabelDef.ROOM, singleModelConditionDTO.getModelLabel()));
        String keyword = deviceInfoRequestControlByFront.getKeyword();
        log.info("入参：queryCondition={}, userId={}", JsonTransferUtils.toJSONString(queryCondition), userId);
        String rootLabel = queryCondition.getRootLabel();
        Long rootID = queryCondition.getRootID();

        if (Objects.equals(rootLabel, ModelLabelDef.PROJECT) || Objects.equals(rootLabel, ModelLabelDef.ROOM)) {
            results = getEquipmentUnderSelected(queryCondition, userId);
        } else {
            // 查设备返回本身
            results = nodeDao.queryNodes(Collections.singletonList(new BaseVo(rootLabel, rootID)));
        }

        return handleNodeData(results, keyword, page);
    }

    /**
     * 模型服务查询返回的nodeData
     *
     * @param nodeData
     * @return
     */
    private ResultWithTotal<List<Map<String, Object>>> handleNodeData(List<Map<String, Object>> nodeData, String keyword, Page page) {
        if (CollectionUtils.isEmpty(nodeData)) {
            return ResultWithTotal.ok();
        }
        if (!Objects.isNull(keyword)) {
            nodeData = nodeData.stream().
                    filter(object -> object.get(TableColumnNameDef.COLUMN_NAME).toString().contains(keyword)).collect(Collectors.toList());
        }
        ResultWithTotal<List<Map<String, Object>>> resultWithTotal = new ResultWithTotal<>();
        resultWithTotal.setTotal(nodeData.size());
        sortNode(nodeData);
        if (Objects.nonNull(page)) {
            nodeData = PageUtils.subList(page.getIndex(), page.getLimit(), nodeData);
        }
        handleResultNull(nodeData);
        resultWithTotal.setData(nodeData);
        return resultWithTotal;
    }

    private void handleResultNull(List<Map<String, Object>> result) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        for (Map<String, Object> map : result) {
            map.entrySet().removeIf(entry -> entry.getValue() == null);
        }
    }

    private void sortNode(List<Map<String, Object>> results) {
        if (CollectionUtils.isEmpty(results)) {
            return;
        }
        // 对当前层级进行排序
        results.sort((v1, v2) -> {
            String m1 = (String) v1.get(ColumnDef.MODEL_LABEL);
            String m2 = (String) v2.get(ColumnDef.MODEL_LABEL);

            int result = m1.compareTo(m2);
            if (result != 0) {
                return result;
            }

            long id1 = CommonUtils.parseLong(v1.get(ColumnDef.ID));
            long id2 = CommonUtils.parseLong(v2.get(ColumnDef.ID));
            return CommonUtils.sort(id1, id2, true);
        });
    }

    private List<Map<String, Object>> getEquipmentUnderSelected(Long id, String modelLabel, Long userId) {
        String label = "deviceclass";
        List<IdTextPair> data = modelService.getEnumrationByModel(label).getData();
        List<String> subLabels = data.stream().map(IdTextPair::getPropertyLabel).collect(Collectors.toList());
        QueryCondition queryCondition = ParentQueryConditionBuilder.of(modelLabel)
                .where(ColumnDef.ID, ConditionBlock.OPERATOR_EQ, id)
                .selectChildByLabels(subLabels)
                .queryAsTree(true)
                .build();
        List<Map<String, Object>> maps = authManageService.queryNodeList(queryCondition, userId);
        if (CollectionUtils.isEmpty(maps)) {
            return Collections.emptyList();
        }

        List<Map<String, Object>> nodeList = QueryResultContentTaker.getChildrenAtFirstElement(maps);
        if (CollectionUtils.isEmpty(nodeList)) {
            return nodeList;
        }

        Map<String, Map<String, Object>> commonInfoMap = nodeDao.queryDeviceCommonInfoMap(nodeList);
        nodeDao.assemblyNodeInfo(nodeList, commonInfoMap);
        return nodeList;
    }

    private List<Map<String, Object>> getEquipmentUnderSelected(QueryCondition queryCondition, Long userId) {
        List<Map<String, Object>> maps = authManageService.queryNodeList(queryCondition, userId);
        if (CollectionUtils.isEmpty(maps)) {
            return Collections.emptyList();
        }

        List<Map<String, Object>> nodeList = QueryResultContentTaker.getChildrenAtFirstElement(maps);
        if (CollectionUtils.isEmpty(nodeList)) {
            return nodeList;
        }

        Map<String, Map<String, Object>> commonInfoMap = nodeDao.queryDeviceCommonInfoMap(nodeList);
        nodeDao.assemblyNodeInfo(nodeList, commonInfoMap);
        return nodeList;
    }

    @Override
    public List<BaseVo> queryBaseVo(BaseVo baseVo) {
        List<BaseVo> netWithLineSegment = new ArrayList<>();
        if (baseVo.getModelLabel().equals(ModelLabelDef.POWER_DIS_CABINET)) {
            PowerDisCabinetWithLayer powerDisCabinetWithLayer = powerDisCabinetDao.
                    selectRelatedById(PowerDisCabinetWithLayer.class, baseVo.getId(), Collections.singletonList(QueryWrapper.of(LineSegmentVo.class)));
            if (Objects.isNull(powerDisCabinetWithLayer)) {
                return Collections.emptyList();
            }
            baseVo.setName(powerDisCabinetWithLayer.getName());
            return getBaseVos(baseVo, netWithLineSegment, powerDisCabinetWithLayer.getList());
        } else if (baseVo.getModelLabel().equals(ModelLabelDef.ARRAY_CABINET)) {
            ArrayCabiNetWithLayer arrayCabiNetWithLayer = arrayCabiNetDao.
                    selectRelatedById(ArrayCabiNetWithLayer.class, baseVo.getId(), Collections.singletonList(QueryWrapper.of(LineSegmentVo.class)));
            if (Objects.isNull(arrayCabiNetWithLayer)) {
                return Collections.emptyList();
            }
            baseVo.setName(arrayCabiNetWithLayer.getName());
            return getBaseVos(baseVo, netWithLineSegment, arrayCabiNetWithLayer.getList());
        }
        return Collections.singletonList(baseVo);
    }

    @Override
    public void exportTechParamByChoose(BaseVo baseVo, HttpServletResponse response) {
        Assert.isTrue(baseVo.getModelLabel().equals(NodeLabelDef.ROOM) || baseVo.getModelLabel().equals(NodeLabelDef.PROJECT), "仅支持导出管理层级下设备二维码");
        List<Map<String, Object>> equipmentUnderSelected = getEquipmentUnderSelected(baseVo.getId(), baseVo.getModelLabel(), GlobalInfoUtils.getUserId());
        String fileName = "设备技术参数" + LocalDateTime.now().format(TimeUtil.SECONDTIMEFORMAT);
        try (Workbook workBook = PoiExcelUtils.createWorkBook(ExcelType.XLS_X)) {
            List<Integer> colWidth = Arrays.asList(38, 18, 18, 18, 18, 18);
            if (CollectionUtils.isNotEmpty(equipmentUnderSelected)) {
                equipmentUnderSelected = equipmentUnderSelected.stream().filter(vo -> !Objects.isNull(vo.get(ColumnDef.NODE_TEMPLATE_ID))).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(equipmentUnderSelected)) {
                    List<OutputTechParamInfo> outputTechParamInfos = packageTechParamResult(equipmentUnderSelected);
                    Map<AttributeTemplate, List<OutputTechParamInfo>> infoByTemplate = outputTechParamInfos.stream().filter(outputTechParamInfo -> !Objects.isNull(outputTechParamInfo.getTemplate())).collect(Collectors.groupingBy(OutputTechParamInfo::getTemplate));
                    // 创建excel中的sheet表
                    infoByTemplate.forEach((template, infos) -> {
                        try {
                            PoiExcelUtils.createSheet(workBook, template.getName(), (sheet, baseCellStyle, rowIndex) -> {
                                int rowNum = 0;
                                // 写入导出连接关系表数据
                                LinkedList<Long> techIds = writeHeader(workBook, sheet, baseCellStyle, rowNum++, template);
                                writeRecord(sheet, baseCellStyle, rowNum, infos, techIds);
                            }, colWidth);
                        } catch (Exception e) {
                            log.error("导出错误：", e);
                        }
                    });
                    FileUtils.downloadExcel(response, workBook, fileName, ContentTypeDef.APPLICATION_MS_EXCEL_07);
                } else {
                    PoiExcelUtils.createSheet(workBook, fileName, (sheet, baseCellStyle, rowIndex) -> {
                    }, colWidth);
                    FileUtils.downloadExcel(response, workBook, fileName, ContentTypeDef.APPLICATION_MS_EXCEL_07);
                }
            }
        } catch (Exception e) {
            ErrorUtils.exportError("导出异常", e);
        }
    }

    @Override
    public void importTechParams(MultipartFile file) throws IOException {
        List<TechParamValue> techParamValues = new ArrayList<>();
        EasyExcelFactory.read(file.getInputStream(), new TechParamListener(techParamValues)).doReadAll();
        if (CollectionUtils.isEmpty(techParamValues)) {
            return;
        }
        checkDataRepeat(techParamValues);
        writeImportTechParamValue(techParamValues);
    }

    private List<ImportTemplateDto> handeImportData(List<TechParamValue> techParamValues) {
        if (CollectionUtils.isEmpty(techParamValues)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<DeviceCommonInfo> wrapper = LambdaQueryWrapper.of(DeviceCommonInfo.class);
        List<ImportTemplateDto> importTemplateDtos = new ArrayList<>();
        Map<BaseVo, List<TechParamValue>> nodeMap = techParamValues.stream().collect(Collectors.groupingBy(techParamValue -> new BaseVo(techParamValue.getObjectId(), techParamValue.getObjectLabel())));
        nodeMap.forEach((key, val) -> {
            wrapper.or(it -> it.eq(DeviceCommonInfo::getObjectid, key.getId()).eq(DeviceCommonInfo::getObjectlabel, key.getModelLabel()));
        });
        List<DeviceCommonInfo> deviceCommonInfos = deviceCommonInfoDao.selectList(wrapper);
        for (TechParamValue techParamValue : techParamValues) {
            ImportTemplateDto importTemplateDto = new ImportTemplateDto();
            for (DeviceCommonInfo deviceCommonInfo : deviceCommonInfos) {
                if (deviceCommonInfo.getObjectid().equals(techParamValue.getObjectId()) && deviceCommonInfo.getObjectlabel().equals(techParamValue.getObjectLabel()) && null != deviceCommonInfo.getNodetemplateid()) {
                    importTemplateDto.setNodeTemplateId(deviceCommonInfo.getNodetemplateid());
                    BeanUtils.copyProperties(techParamValue, importTemplateDto);
                    break;
                }
            }
            importTemplateDtos.add(importTemplateDto);
        }
        List<ImportTemplateDto> collect1 = importTemplateDtos.stream().filter(importTemplateDto -> null != importTemplateDto.getNodeTemplateId()).collect(Collectors.toList());
        Set<Long> nodeTemplateIds = deviceCommonInfos.stream().map(DeviceCommonInfo::getNodetemplateid).collect(Collectors.toSet());
        QueryCondition queryCondition = ParentQueryConditionBuilder.of(ModelLabelDef.NODE_TEMPLATE).where(ColumnDef.ID, ConditionBlock.OPERATOR_IN, nodeTemplateIds).selectChildByLabels(Collections.singletonList(ModelLabelDef.TECH_PARAM_TEMPLATE)).queryAsTree().build();
        List<Map<String, Object>> query = modelServiceUtils.query(queryCondition);
        List<BaseVo> baseVos = JsonTransferUtils.transferList(query, BaseVo.class);
        for (BaseVo baseVo : baseVos) {
            for (ImportTemplateDto importTemplateDto : collect1) {
                if (importTemplateDto.getNodeTemplateId().equals(baseVo.getId())) {
                    List<Long> collect = baseVo.getChildren().stream().map(BaseVo::getId).collect(Collectors.toList());
                    importTemplateDto.setIds(collect);
                }
            }
        }
        return collect1;
    }

    private void writeImportTechParamValue(List<TechParamValue> techParamValues) {
        List<TechParamValue> original = techParamValueDao.queryTechParamWith(techParamValues);
        List<ImportTemplateDto> importTemplateDtos = handeImportData(techParamValues);
        List<TechParamValue> result = new ArrayList<>();
        for (ImportTemplateDto importTemplateDto : importTemplateDtos) {
            boolean flag = true;
            for (TechParamValue origin : original) {
                if (importTemplateDto.getTechParamTemplateId().equals(origin.getTechParamTemplateId()) && importTemplateDto.getObjectId().equals(origin.getObjectId()) && importTemplateDto.getObjectLabel().equals(origin.getObjectLabel())) {
                    flag = false;
                    //参数赋值现在都赋值到value字段
                    origin.setValue(importTemplateDto.getValue());
                    result.add(origin);
                    break;
                }
            }
            if (flag && importTemplateDto.getIds().contains(importTemplateDto.getTechParamTemplateId())) {
                TechParamValue techParamValue = new TechParamValue();
                BeanUtils.copyProperties(importTemplateDto, techParamValue);
                result.add(techParamValue);
            }
        }
        result.stream().distinct().collect(Collectors.toList());
        techParamValueDao.saveOrUpdateBatch(result);
    }

    private void checkDataRepeat(List<TechParamValue> techParamValues) {
        List<TechParamValue> checkRepeat = new ArrayList<>();
        for (TechParamValue value : techParamValues) {
            TechParamValue value1 = new TechParamValue();
            value1.setObjectId(value.getObjectId());
            value1.setObjectLabel(value.getObjectLabel());
            value1.setTechParamTemplateId(value.getTechParamTemplateId());
            checkRepeat.add(value1);
        }
        Long count1 = checkRepeat.stream().distinct().count();
        Assert.isTrue(count1.intValue() == techParamValues.size(), "存在重复数据");
    }

    private void writeRecord(Sheet sheet, CellStyle baseCellStyle, int rowNum, List<OutputTechParamInfo> outputTechParamInfos, LinkedList<Long> techIds) {
        int col;
        for (OutputTechParamInfo log : outputTechParamInfos) {
            col = 0;
            Row row = PoiExcelUtils.createRow(sheet, rowNum);
            PoiExcelUtils.createCell(row, col++, baseCellStyle, log.getName() + "(" + String.join(REGEX, log.getModelLabel(), String.valueOf(log.getId())) + ")");
            PoiExcelUtils.createCell(row, col++, baseCellStyle, log.getCode().equals("null") ? "--" : String.valueOf(log.getCode()));
            for (Long id : techIds) {
                TechParamValue value1 = log.getValues().stream().filter(techParamValue -> log.getId().equals(techParamValue.getObjectId()))
                        .filter(techParamValue -> log.getModelLabel().equals(techParamValue.getObjectLabel()))
                        .filter(techParamValue -> techParamValue.getTechParamTemplateId().equals(id)).findFirst().orElse(null);
                if (null != value1) {
                    PoiExcelUtils.createCell(row, col++, baseCellStyle, value1.getValue());
                } else {
                    PoiExcelUtils.createCell(row, col++, baseCellStyle, (Boolean) null);
                }

            }
            rowNum++;
        }
    }

    private CellStyle createRequiredStyle(Workbook workbook, CellStyle baseCellStyle) {
        CellStyle requiredCellStyle = workbook.createCellStyle();
        requiredCellStyle.cloneStyleFrom(baseCellStyle);
        Font font = PoiExcelUtils.createFont(workbook, true, null, null, HSSFColor.HSSFColorPredefined.RED.getIndex());
        requiredCellStyle.setFont(font);
        return requiredCellStyle;
    }

    /**
     * 返回技术参数模板id
     *
     * @param sheet
     * @param baseCellStyle
     * @param startRow
     * @param template
     * @return
     */
    private LinkedList<Long> writeHeader(Workbook workbook, Sheet sheet, CellStyle baseCellStyle, int startRow, AttributeTemplate template) {
        LinkedHashMap<String, CellStyle> headerMap = new LinkedHashMap<>(CommonUtils.MAP_INIT_SIZE_16);
        LinkedList<Long> list = new LinkedList<>();
        CellStyle requiredStyle = createRequiredStyle(workbook, baseCellStyle);
        headerMap.put("设备名称", requiredStyle);
        headerMap.put("设备编号", baseCellStyle);
        if (!Objects.isNull(template) && CollectionUtils.isNotEmpty(template.getTechParams())) {
            for (TechParam techParam : template.getTechParams()) {
                headerMap.put(techParam.getName() + "（" + techParam.getUnit() + "）" + "（" + techParam.getId() + "）", baseCellStyle);
                list.add(techParam.getId());
            }
        }
        PoiExcelUtils.createHeaderName(sheet, startRow, headerMap);
        return list;
    }

    private List<OutputTechParamInfo> packageTechParamResult(List<Map<String, Object>> equipmentUnderSelected) {
        List<BaseVo> baseVos = JsonTransferUtils.transferList(equipmentUnderSelected, BaseVo.class);
        List<TechParamValue> techParamValues = techParamValueDao.queryTechParam(baseVos);
        //设备对应
        List<Long> templateIds = equipmentUnderSelected.stream().map(vo -> Long.parseLong(vo.get(ColumnDef.NODE_TEMPLATE_ID).toString())).distinct().collect(Collectors.toList());
        List<AttributeTemplate> templates = templateDao.getTemplates(templateIds);
        return parseToOutputTechParamInfo(equipmentUnderSelected, templates, techParamValues);

    }

    private List<OutputTechParamInfo> parseToOutputTechParamInfo(List<Map<String, Object>> equipmentUnderSelected, List<AttributeTemplate> templates, List<TechParamValue> techParamValues) {
        List<OutputTechParamInfo> outputTechParamInfos = new ArrayList<>();
        equipmentUnderSelected.forEach(map -> {
            List<TechParamValue> techParamValueList = new ArrayList<>();
            OutputTechParamInfo info = new OutputTechParamInfo(Long.parseLong(String.valueOf(map.get(ColumnDef.ID))),
                    String.valueOf(map.get(ColumnDef.MODEL_LABEL)),
                    String.valueOf(map.get(ColumnDef.NAME)),
                    String.valueOf(map.get(ColumnDef.CODE)));
            AttributeTemplate template1 = templates.stream().filter(template -> Long.valueOf(String.valueOf(map.get(ColumnDef.NODE_TEMPLATE_ID))).equals(template.getId())).findFirst().orElse(null);
            if (Objects.isNull(template1)) {
                return;
            }
            packageTechParam(info, template1, techParamValueList, techParamValues);
            info.setTemplate(template1);
            info.setValues(techParamValueList);
            outputTechParamInfos.add(info);
        });
        return outputTechParamInfos;
    }

    private void packageTechParam(OutputTechParamInfo info, AttributeTemplate template1, List<TechParamValue> result, List<TechParamValue> techParamValues) {
        List<TechParamValue> collect = techParamValues.stream().filter(tech -> tech.getObjectId().equals(info.getId()) && tech.getObjectLabel().equals(info.getModelLabel())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            result.addAll(packageTechParamValueResult(techParamValues, Collections.singletonList(template1)));
            return;
        }
        if (!Objects.isNull(template1) && CollectionUtils.isNotEmpty(template1.getTechParams())) {
            template1.getTechParams().forEach(techParam -> {
                result.add(new TechParamValue(techParam.getName(), techParam.getUnit(), techParam.getId()));
            });
        }

    }


    private List<BaseVo> getBaseVos(BaseVo baseVo, List<BaseVo> netWithLineSegment, List<LineSegmentVo> lines) {
        if (CollectionUtils.isNotEmpty(lines)) {
            netWithLineSegment = packageBaseVo(lines);
        }
        netWithLineSegment.add(baseVo);
        return netWithLineSegment;
    }

    private List<BaseWithTemplate> packageBaseWithTemplate(List<BaseVo> nodeList, List<AttributeTemplate> templates, List<DeviceCommonInfo> collect, Map<Long, String> nodeTemplateAndParentNode) {
        List<BaseWithTemplate> result = new ArrayList<>();
        if (templates == null) {
            templates = Collections.emptyList();
        }

        List<AttributeTemplate> finalTemplates = templates;
        nodeList.forEach(baseVo -> {
            BaseWithTemplate baseWithTemplate = new BaseWithTemplate(baseVo.getId(), baseVo.getModelLabel(), baseVo.getName());
            result.add(baseWithTemplate);

            if (CollectionUtils.isEmpty(collect)) {
                return;
            }

            Long templateId = collect.stream().filter(deviceCommonInfo -> deviceCommonInfo.getObjectid().equals(baseVo.getId()) && deviceCommonInfo.getObjectlabel().equals(baseVo.getModelLabel()))
                    .map(DeviceCommonInfo::getNodetemplateid)
                    .filter(Objects::nonNull).findFirst().orElse(null);

            if (Objects.nonNull(templateId)) {
                AttributeTemplate template1 = finalTemplates.stream().filter(template -> template.getId().equals(templateId)).findFirst().orElse(null);
                baseWithTemplate.setTemplate(template1);
                baseWithTemplate.setNodeTemplatePath(nodeTemplateAndParentNode.get(templateId));
            }
        });

        return result;
    }

    private void packageBaseWithTemplate(List<BaseWithTemplate> result, List<BaseVo> list, List<AttributeTemplate> templates, List<DeviceCommonInfo> collect) {
        list.forEach(baseVo -> {
            BaseWithTemplate baseWithTemplate = new BaseWithTemplate(baseVo.getId(), baseVo.getModelLabel(), baseVo.getName());
            if (CollectionUtils.isNotEmpty(collect)) {
                Long templateId = collect.stream().filter(deviceCommonInfo -> deviceCommonInfo.getObjectid().equals(baseVo.getId()) && deviceCommonInfo.getObjectlabel().equals(baseVo.getModelLabel())).filter(deviceCommonInfo -> deviceCommonInfo.getNodetemplateid() != null)
                        .map(DeviceCommonInfo::getNodetemplateid).findFirst().orElse(null);
                if (!Objects.isNull(templateId)) {
                    AttributeTemplate template1 = templates.stream().filter(template -> template.getId().equals(templateId)).findFirst().orElse(null);
                    if (!Objects.isNull(template1)) {
                        baseWithTemplate.setTemplate(template1);
                    }
                }
            }
            result.add(baseWithTemplate);
        });
    }

    private List<RunningParamValue> packageRunningParamResult(List<PecDeviceExtendVo> pecDeviceExtendVos, List<RunningParam> runningParams) {
        List<RunningParamValue> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pecDeviceExtendVos)) {
            runningParams.forEach(runningParam -> {
                result.add(new RunningParamValue(runningParam.getName(), pecDeviceExtendVos.get(0).getDeviceid(), runningParam.getDataId()));
            });
        }
        return result;
    }


    private List<TechParamValue> packageTechParamValue(List<TechParam> techParamList) {
        if (CollectionUtils.isEmpty(techParamList)) {
            return Collections.EMPTY_LIST;
        }
        List<TechParamValue> result = new ArrayList<>();
        techParamList.forEach(techParam -> {
            result.add(new TechParamValue(techParam.getName(), techParam.getUnit(), techParam.getId()));
        });
        return result;
    }


}
